msgid ""
msgstr ""
"Project-Id-Version: CTFd\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2023-10-07 03:17-0400\n"
"PO-Revision-Date: 2024-06-20 07:46\n"
"Last-Translator: \n"
"Language-Team: Catalan\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: CTFd\n"
"X-Crowdin-Project-ID: 659064\n"
"X-Crowdin-Language: ca\n"
"X-Crowdin-File: /master/messages.pot\n"
"X-Crowdin-File-ID: 188\n"
"Language: ca_ES\n"

#: CTFd/forms/auth.py:19 CTFd/forms/self.py:16
#: CTFd/themes/core-beta/templates/teams/private.html:319
#: CTFd/themes/core-beta/templates/teams/public.html:85
msgid "User Name"
msgstr "Nom d'usuari"

#: CTFd/forms/auth.py:21 CTFd/forms/auth.py:53 CTFd/forms/self.py:17
#: CTFd/forms/teams.py:203
msgid "Email"
msgstr "Correu electrònic"

#: CTFd/forms/auth.py:22 CTFd/forms/auth.py:43 CTFd/forms/auth.py:60
#: CTFd/forms/self.py:19 CTFd/forms/teams.py:204
msgid "Password"
msgstr "Contrasenya"

#: CTFd/forms/auth.py:23 CTFd/forms/auth.py:44 CTFd/forms/auth.py:55
#: CTFd/forms/auth.py:62 CTFd/forms/self.py:24 CTFd/forms/teams.py:127
#: CTFd/forms/teams.py:210 CTFd/themes/core-beta/templates/challenge.html:147
msgid "Submit"
msgstr "Envia"

#: CTFd/forms/auth.py:39
msgid "User Name or Email"
msgstr "Nom d'usuari o correu electrònic"

#: CTFd/forms/auth.py:48
msgid "Resend Confirmation Email"
msgstr "Reenvia el correu de confirmació"

#: CTFd/forms/self.py:18 CTFd/forms/users.py:155
msgid "Language"
msgstr "Idioma"

#: CTFd/forms/self.py:20
msgid "Current Password"
msgstr "Contrasenya actual"

#: CTFd/forms/self.py:21 CTFd/forms/teams.py:113 CTFd/forms/teams.py:191
#: CTFd/forms/teams.py:206 CTFd/forms/users.py:138
#: CTFd/themes/core-beta/templates/teams/teams.html:51
#: CTFd/themes/core-beta/templates/users/users.html:48
msgid "Affiliation"
msgstr "Afiliació"

#: CTFd/forms/self.py:22 CTFd/forms/teams.py:119 CTFd/forms/teams.py:192
#: CTFd/forms/teams.py:205 CTFd/forms/users.py:139
#: CTFd/themes/core-beta/templates/teams/teams.html:50
#: CTFd/themes/core-beta/templates/users/users.html:47
msgid "Website"
msgstr "Lloc web"

#: CTFd/forms/self.py:23 CTFd/forms/teams.py:123 CTFd/forms/teams.py:207
#: CTFd/themes/core-beta/templates/teams/teams.html:52
#: CTFd/themes/core-beta/templates/users/users.html:49
msgid "Country"
msgstr "País"

#: CTFd/forms/self.py:52 CTFd/themes/core-beta/templates/settings.html:225
msgid "Expiration"
msgstr "Venciment"

#: CTFd/forms/self.py:54
msgid "Generate"
msgstr "Genera"

#: CTFd/forms/setup.py:29
msgid "Event Name"
msgstr "Nom de l'esdeveniment"

#: CTFd/forms/setup.py:29
msgid "The name of your CTF event/workshop"
msgstr "El nom del teu esdeveniment/taller CTF"

#: CTFd/forms/setup.py:32
msgid "Event Description"
msgstr "Descripció de l'esdeveniment"

#: CTFd/forms/setup.py:32
msgid "Description for the CTF"
msgstr "Descripció del CTF"

#: CTFd/forms/setup.py:35 CTFd/forms/setup.py:36
msgid "User Mode"
msgstr "Mode Usuari"

#: CTFd/forms/setup.py:36
msgid "Team Mode"
msgstr "Mode Equip"

#: CTFd/forms/setup.py:38
msgid "Controls whether users join together in teams to play (Team Mode) or play as themselves (User Mode)"
msgstr "Controla si els usuaris es reuneixen en equips per jugar (Mode Equip) o juguen com a individus (Mode Usuari)"

#: CTFd/forms/setup.py:45
msgid "Admin Username"
msgstr "Nom d'usuari de l'administrador"

#: CTFd/forms/setup.py:46
msgid "Your username for the administration account"
msgstr "El teu nom d'usuari per al compte d'administració"

#: CTFd/forms/setup.py:50
msgid "Admin Email"
msgstr "Correu electrònic de l'administrador"

#: CTFd/forms/setup.py:51
msgid "Your email address for the administration account"
msgstr "La teva adreça de correu electrònic per al compte d'administració"

#: CTFd/forms/setup.py:55
msgid "Admin Password"
msgstr "Contrasenya de l'administrador"

#: CTFd/forms/setup.py:56
msgid "Your password for the administration account"
msgstr "La teva contrasenya per al compte d'administració"

#: CTFd/forms/setup.py:61
msgid "Logo"
msgstr "Logotip"

#: CTFd/forms/setup.py:62
msgid "Logo to use for the website instead of a CTF name. Used as the home page button. Optional."
msgstr "Logotip a utilitzar per al lloc web en lloc del nom del CTF. Utilitzat com a botó de la pàgina principal. Opcional."

#: CTFd/forms/setup.py:67
msgid "Banner"
msgstr "Banner"

#: CTFd/forms/setup.py:67
msgid "Banner to use for the homepage. Optional."
msgstr "Banner a utilitzar per a la pàgina d'inici. Opcional."

#: CTFd/forms/setup.py:70
msgid "Small Icon"
msgstr "Icona Petita"

#: CTFd/forms/setup.py:71
msgid "favicon used in user's browsers. Only PNGs accepted. Must be 32x32px. Optional."
msgstr "favicon utilitzat als navegadors dels usuaris. Només s'accepten PNGs. Ha de ser de 32x32px. Opcional."

#: CTFd/forms/setup.py:76
msgid "Theme"
msgstr "Tema"

#: CTFd/forms/setup.py:77
msgid "CTFd Theme to use. Can be changed later."
msgstr "Tema de CTFd a utilitzar. Es pot canviar més endavant."

#: CTFd/forms/setup.py:84
msgid "Theme Color"
msgstr "Color del Tema"

#: CTFd/forms/setup.py:85
msgid "Color used by theme to control aesthetics. Requires theme support. Optional."
msgstr "Color utilitzat pel tema per controlar l'estètica. Requereix suport del tema. Opcional."

#: CTFd/forms/setup.py:91
msgid "Verify Emails"
msgstr "Verificar Correus Electrònics"

#: CTFd/forms/setup.py:143
msgid "Start Time"
msgstr "Hora d'Inici"

#: CTFd/forms/setup.py:144
msgid "Time when your CTF is scheduled to start. Optional."
msgstr "Hora en què està programat que comenci el teu CTF. Opcional."

#: CTFd/forms/setup.py:147
msgid "End Time"
msgstr "Hola de Fi"

#: CTFd/forms/setup.py:148
msgid "Time when your CTF is scheduled to end. Optional."
msgstr "Hora en què està programat que finalitzi el teu CTF. Opcional."

#: CTFd/forms/setup.py:150
msgid "Finish"
msgstr "Finalitzar"

#: CTFd/forms/teams.py:76 CTFd/forms/teams.py:83 CTFd/forms/teams.py:100
#: CTFd/forms/teams.py:202
msgid "Team Name"
msgstr "Nom de l'Equip"

#: CTFd/forms/teams.py:77 CTFd/forms/teams.py:84
msgid "Team Password"
msgstr "Contrasenya de l'Equip"

#: CTFd/forms/teams.py:78 CTFd/forms/teams.py:258
msgid "Join"
msgstr "Uneix-te"

#: CTFd/forms/teams.py:85
msgid "Create"
msgstr "Crea"

#: CTFd/forms/teams.py:101
msgid "Your team's public name shown to other competitors"
msgstr "El nom públic del teu equip mostrat a altres competidors"

#: CTFd/forms/teams.py:104
msgid "New Team Password"
msgstr "Nova contrasenya de l'equip"

#: CTFd/forms/teams.py:104
msgid "Set a new team join password"
msgstr "Estableix una nova contrasenya per unir-se a l'equip"

#: CTFd/forms/teams.py:107
msgid "Confirm Current Team Password"
msgstr "Confirma la contrasenya actual de l'equip"

#: CTFd/forms/teams.py:108
msgid "Provide your current team password (or your password) to update your team's password"
msgstr "Proporciona la contrasenya actual de l'equip (o la teva) per actualitzar la contrasenya de l'equip"

#: CTFd/forms/teams.py:114
msgid "Your team's affiliation publicly shown to other competitors"
msgstr "L'afiliació del teu equip mostrada públicament a altres competidors"

#: CTFd/forms/teams.py:120
msgid "Your team's website publicly shown to other competitors"
msgstr "El lloc web del teu equip mostrat públicament a altres competidors"

#: CTFd/forms/teams.py:125
msgid "Your team's country publicly shown to other competitors"
msgstr "El país del teu equip mostrat públicament a altres competidors"

#: CTFd/forms/teams.py:165
msgid "Team Captain"
msgstr "Capità de l'equip"

#: CTFd/forms/teams.py:188 CTFd/forms/users.py:135
msgid "Search Field"
msgstr "Camp de cerca"

#: CTFd/forms/teams.py:190 CTFd/forms/users.py:137
#: CTFd/themes/core-beta/templates/challenge.html:183
msgid "Name"
msgstr "Nom"

#: CTFd/forms/teams.py:197 CTFd/forms/users.py:145
msgid "Parameter"
msgstr "Paràmetre"

#: CTFd/forms/teams.py:198 CTFd/forms/users.py:149
msgid "Search"
msgstr "Cerca"

#: CTFd/forms/teams.py:208
msgid "Hidden"
msgstr "Ocult"

#: CTFd/forms/teams.py:209
msgid "Banned"
msgstr "Prohibit"

#: CTFd/forms/teams.py:254
msgid "Invite Link"
msgstr "Enllaç d'invitació"

#: CTFd/forms/users.py:146
msgid "Search for matching users"
msgstr "Cerca usuaris coincidents"

#: CTFd/themes/core-beta/templates/base.html:49
msgid "Powered by CTFd"
msgstr "Impulsat per CTFd"

#: CTFd/themes/core-beta/templates/challenge.html:11
#: CTFd/themes/core-beta/templates/teams/private.html:375
#: CTFd/themes/core-beta/templates/teams/public.html:141
#: CTFd/themes/core-beta/templates/users/private.html:114
#: CTFd/themes/core-beta/templates/users/public.html:113
msgid "Challenge"
msgstr "Desafiament"

#: CTFd/themes/core-beta/templates/challenge.html:19
#, python-format
msgid "%(num)d Solve"
msgid_plural "%(num)d Solves"
msgstr[0] "%(num)d Solució"
msgstr[1] "%(num)d Solucions"

#: CTFd/themes/core-beta/templates/challenge.html:73
msgid "View Hint"
msgstr "Veure Pista"

#: CTFd/themes/core-beta/templates/challenge.html:135
msgid "Flag"
msgstr "Bandera"

#: CTFd/themes/core-beta/templates/challenge.html:167
msgid "Next Challenge"
msgstr "Pròxim Desafiament"

#: CTFd/themes/core-beta/templates/challenge.html:186
#: CTFd/themes/core-beta/templates/setup.html:305
#: CTFd/themes/core-beta/templates/setup.html:326
msgid "Date"
msgstr "Data"

#: CTFd/themes/core-beta/templates/challenges.html:7
#: CTFd/themes/core-beta/templates/components/navbar.html:57
msgid "Challenges"
msgstr "Reptes"

#: CTFd/themes/core-beta/templates/confirm.html:7
msgid "Confirm"
msgstr "Confirma"

#: CTFd/themes/core-beta/templates/confirm.html:18
msgid "We've sent a confirmation email to your email address."
msgstr "Hem enviat un correu electrònic de confirmació a la teva adreça."

#: CTFd/themes/core-beta/templates/confirm.html:24
msgid "Please click the link in that email to confirm your account."
msgstr "Si us plau, fes clic a l'enllaç d'aquest correu per confirmar el teu compte."

#: CTFd/themes/core-beta/templates/confirm.html:30
msgid "If the email doesn’t arrive, check your spam folder or contact an administrator to manually verify your account."
msgstr "Si el correu no arriba, revisa la carpeta de correu brossa o contacta amb un administrador per verificar manualment el teu compte."

#: CTFd/themes/core-beta/templates/confirm.html:43
msgid "Change Email Address"
msgstr "Canvia l'adreça de correu electrònic"

#: CTFd/themes/core-beta/templates/components/navbar.html:178
#: CTFd/themes/core-beta/templates/login.html:7
msgid "Login"
msgstr "Inicia sessió"

#: CTFd/themes/core-beta/templates/login.html:40
msgid "Forgot your password?"
msgstr "Has oblidat la teva contrasenya?"

#: CTFd/themes/core-beta/templates/components/navbar.html:97
#: CTFd/themes/core-beta/templates/notifications.html:7
msgid "Notifications"
msgstr "Notificacions"

#: CTFd/themes/core-beta/templates/notifications.html:14
msgid "There are no notifications yet"
msgstr "Encara no hi ha notificacions"

#: CTFd/themes/core-beta/templates/components/navbar.html:165
#: CTFd/themes/core-beta/templates/register.html:7
msgid "Register"
msgstr "Registra't"

#: CTFd/themes/core-beta/templates/register.html:35
msgid "Your username on the site"
msgstr "El teu nom d'usuari al lloc"

#: CTFd/themes/core-beta/templates/register.html:43
msgid "Never shown to the public"
msgstr "Mai es mostra al públic"

#: CTFd/themes/core-beta/templates/register.html:51
msgid "Password used to log into your account"
msgstr "Contrasenya utilitzada per iniciar sessió al teu compte"

#: CTFd/themes/core-beta/templates/reset_password.html:7
msgid "Reset Password"
msgstr "Restableix la contrasenya"

#: CTFd/themes/core-beta/templates/reset_password.html:21
msgid "You can now reset the password for your account and log in. Please enter in a new password below."
msgstr "Ara pots restablir la contrasenya del teu compte i iniciar sessió. Si us plau, introdueix una nova contrasenya a continuació."

#: CTFd/themes/core-beta/templates/reset_password.html:44
msgid "Please provide the email address associated with your account below."
msgstr "Si us plau, proporciona a continuació l'adreça de correu electrònic associada al teu compte."

#: CTFd/themes/core-beta/templates/components/navbar.html:135
#: CTFd/themes/core-beta/templates/settings.html:8
#: CTFd/themes/core-beta/templates/setup.html:50
msgid "Settings"
msgstr "Configuració"

#: CTFd/themes/core-beta/templates/components/navbar.html:123
#: CTFd/themes/core-beta/templates/settings.html:21
msgid "Profile"
msgstr "Perfil"

#: CTFd/themes/core-beta/templates/settings.html:26
msgid "Access Tokens"
msgstr "Tokens d'accés"

#: CTFd/themes/core-beta/templates/settings.html:95
msgid "Your profile has been updated"
msgstr "El teu perfil ha estat actualitzat"

#: CTFd/themes/core-beta/templates/settings.html:103
#: CTFd/themes/core-beta/templates/teams/private.html:83
#: CTFd/themes/core-beta/templates/teams/private.html:198
msgid "Error:"
msgstr "Error:"

#: CTFd/themes/core-beta/templates/settings.html:129
msgid "API Key Generated"
msgstr "Clau API generada"

#: CTFd/themes/core-beta/templates/settings.html:137
msgid "Please copy your API Key, it won't be shown again!"
msgstr "Si us plau, copia la teva clau API, no es tornarà a mostrar!"

#: CTFd/themes/core-beta/templates/settings.html:183
msgid "Active Tokens"
msgstr "Tokens actius"

#: CTFd/themes/core-beta/templates/settings.html:195
msgid "Delete Token"
msgstr "Elimina Token"

#: CTFd/themes/core-beta/templates/settings.html:204
msgid "Are you sure you want to delete this token?"
msgstr "Estàs segur que vols eliminar aquest token?"

#: CTFd/themes/core-beta/templates/settings.html:224
msgid "Created"
msgstr "Creat"

#: CTFd/themes/core-beta/templates/settings.html:226
msgid "Description"
msgstr "Descripció"

#: CTFd/themes/core-beta/templates/settings.html:227
msgid "Delete"
msgstr "Elimina"

#: CTFd/themes/core-beta/templates/setup.html:24
msgid "Setup"
msgstr "Configuració"

#: CTFd/themes/core-beta/templates/setup.html:44
msgid "General"
msgstr "General"

#: CTFd/themes/core-beta/templates/setup.html:47
msgid "Mode"
msgstr "Mode"

#: CTFd/themes/core-beta/templates/setup.html:53
msgid "Administration"
msgstr "Administració"

#: CTFd/themes/core-beta/templates/setup.html:56
msgid "Style"
msgstr "Estil"

#: CTFd/themes/core-beta/templates/setup.html:59
msgid "Date &amp; Time"
msgstr "Data &amp; hora"

#: CTFd/themes/core-beta/templates/setup.html:62
msgid "Integrations"
msgstr "Integracions"

#: CTFd/themes/core-beta/templates/setup.html:86
#: CTFd/themes/core-beta/templates/setup.html:138
#: CTFd/themes/core-beta/templates/setup.html:202
#: CTFd/themes/core-beta/templates/setup.html:238
#: CTFd/themes/core-beta/templates/setup.html:296
#: CTFd/themes/core-beta/templates/setup.html:345
msgid "Next"
msgstr "Següent"

#: CTFd/themes/core-beta/templates/setup.html:112
msgid "Participants register accounts and form teams"
msgstr "Els participants registren comptes i formen equips"

#: CTFd/themes/core-beta/templates/setup.html:113
msgid "If a team member solves a challenge, the entire team receives credit"
msgstr "Si un membre de l'equip resol un desafiament, tot l'equip rep crèdit"

#: CTFd/themes/core-beta/templates/setup.html:115
msgid "Easier to see which team member solved a challenge"
msgstr "És més fàcil veure quin membre de l'equip ha resolt un desafiament"

#: CTFd/themes/core-beta/templates/setup.html:116
msgid "May be slightly more difficult to administer"
msgstr "Pot ser una mica més difícil d'administrar"

#: CTFd/themes/core-beta/templates/setup.html:120
msgid "Participants only register an individual account"
msgstr "Els participants només registren un compte individual"

#: CTFd/themes/core-beta/templates/setup.html:121
msgid "Players can share accounts to form pseudo-teams"
msgstr "Els jugadors poden compartir comptes per formar pseudo-equips"

#: CTFd/themes/core-beta/templates/setup.html:123
msgid "Easier to organize"
msgstr "Més fàcil d'organitzar"

#: CTFd/themes/core-beta/templates/setup.html:124
msgid "Difficult to attribute solutions to individual team members"
msgstr "Difícil atribuir les solucions als membres individuals de l'equip"

#: CTFd/themes/core-beta/templates/setup.html:143
msgid "Visibility Settings"
msgstr "Configuració de Visibilitat"

#: CTFd/themes/core-beta/templates/setup.html:146
msgid "Control the visibility of different sections of CTFd"
msgstr "Controla la visibilitat de diferents seccions de CTFd"

#: CTFd/themes/core-beta/templates/setup.html:232
msgid "Subscribe email address to the CTFd LLC Newsletter for news and updates"
msgstr "Subscriu l'adreça de correu electrònic al butlletí de CTFd LLC per a notícies i actualitzacions"

#: CTFd/themes/core-beta/templates/setup.html:309
#: CTFd/themes/core-beta/templates/setup.html:330
#: CTFd/themes/core-beta/templates/teams/private.html:378
#: CTFd/themes/core-beta/templates/teams/public.html:144
#: CTFd/themes/core-beta/templates/users/private.html:117
#: CTFd/themes/core-beta/templates/users/public.html:116
msgid "Time"
msgstr "Hora"

#: CTFd/themes/core-beta/templates/setup.html:334
msgid "UTC Preview"
msgstr "Previsualització UTC"

#: CTFd/themes/core-beta/templates/components/navbar.html:34
#: CTFd/themes/core-beta/templates/users/users.html:6
msgid "Users"
msgstr "Usuaris"

#: CTFd/themes/core-beta/templates/components/navbar.html:41
#: CTFd/themes/core-beta/templates/teams/teams.html:6
msgid "Teams"
msgstr "Equips"

#: CTFd/themes/core-beta/templates/components/navbar.html:50
msgid "Scoreboard"
msgstr "Marcador"

#: CTFd/themes/core-beta/templates/components/navbar.html:80
msgid "Admin Panel"
msgstr "Panell d'Administració"

#: CTFd/themes/core-beta/templates/components/navbar.html:110
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:6
#: CTFd/themes/core-beta/templates/teams/teams.html:49
msgid "Team"
msgstr "Equip"

#: CTFd/themes/core-beta/templates/components/navbar.html:147
msgid "Logout"
msgstr "Sortir"

#: CTFd/themes/core-beta/templates/errors/403.html:9
msgid "Forbidden"
msgstr "Prohibit"

#: CTFd/themes/core-beta/templates/errors/404.html:11
msgid "File not found"
msgstr "Fitxer no trobat"

#: CTFd/themes/core-beta/templates/errors/404.html:12
msgid "Sorry about that"
msgstr "Ho sentim per això"

#: CTFd/themes/core-beta/templates/errors/429.html:11
msgid "Too many requests"
msgstr "Massa peticions"

#: CTFd/themes/core-beta/templates/errors/429.html:12
msgid "Please slow down!"
msgstr "Si us plau, afluixa el ritme!"

#: CTFd/themes/core-beta/templates/errors/502.html:11
msgid "Bad Gateway"
msgstr "Gateway incorrecta"

#: CTFd/themes/core-beta/templates/macros/forms.html:13
#: CTFd/themes/core-beta/templates/macros/forms.html:36
msgid "(Optional)"
msgstr "(Opcional)"

#: CTFd/themes/core-beta/templates/teams/invite.html:6
#: CTFd/themes/core-beta/templates/teams/join_team.html:6
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:36
msgid "Join Team"
msgstr "Uneix-te a l'equip"

#: CTFd/themes/core-beta/templates/teams/invite.html:15
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:12
msgid "Welcome to"
msgstr "Benvingut a"

#: CTFd/themes/core-beta/templates/teams/invite.html:19
msgid "Click the button below to join the team!"
msgstr "Fes clic al botó de sota per unir-te a l'equip!"

#: CTFd/themes/core-beta/templates/teams/new_team.html:6
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:39
msgid "Create Team"
msgstr "Crea l'equip"

#: CTFd/themes/core-beta/templates/teams/private.html:8
msgid "Edit Team"
msgstr "Edita l'equip"

#: CTFd/themes/core-beta/templates/teams/private.html:76
msgid "Your team's profile has been updated"
msgstr "El perfil del teu equip s'ha actualitzat"

#: CTFd/themes/core-beta/templates/teams/private.html:104
msgid "Choose Captain"
msgstr "Tria Capità"

#: CTFd/themes/core-beta/templates/teams/private.html:127
msgid "Your captain rights have been transferred"
msgstr "Els teus drets de capità han estat transferits"

#: CTFd/themes/core-beta/templates/teams/private.html:152
msgid "Invite Users"
msgstr "Convida usuaris"

#: CTFd/themes/core-beta/templates/teams/private.html:171
msgid "Share this link with your team members for them to join your team"
msgstr "Comparteix aquest enllaç amb els membres del teu equip perquè s'uneixin"

#: CTFd/themes/core-beta/templates/teams/private.html:174
msgid "Invite links can be re-used and expire after 1 day"
msgstr "Els enllaços d'invitació es poden reutilitzar i caduquen després d'un dia"

#: CTFd/themes/core-beta/templates/teams/private.html:188
msgid "Disband Team"
msgstr "Dissol l'equip"

#: CTFd/themes/core-beta/templates/teams/private.html:193
msgid "Are you sure you want to disband your team?"
msgstr "Estàs segur que vols dissoldre el teu equip?"

#: CTFd/themes/core-beta/templates/teams/private.html:206
msgid "No"
msgstr "No"

#: CTFd/themes/core-beta/templates/teams/private.html:207
msgid "Yes"
msgstr "Si"

#: CTFd/themes/core-beta/templates/teams/private.html:218
#: CTFd/themes/core-beta/templates/teams/public.html:10
#: CTFd/themes/core-beta/templates/users/private.html:21
#: CTFd/themes/core-beta/templates/users/public.html:21
#: CTFd/themes/core-beta/templates/users/users.html:66
msgid "Official"
msgstr "Oficial"

#: CTFd/themes/core-beta/templates/teams/private.html:250
msgid "place"
msgstr "lloc"

#: CTFd/themes/core-beta/templates/teams/private.html:258
msgid "points"
msgstr "punts"

#: CTFd/themes/core-beta/templates/teams/private.html:315
#: CTFd/themes/core-beta/templates/teams/public.html:81
msgid "Members"
msgstr "Membres"

#: CTFd/themes/core-beta/templates/teams/private.html:320
#: CTFd/themes/core-beta/templates/teams/public.html:86
msgid "Score"
msgstr "Puntuació"

#: CTFd/themes/core-beta/templates/teams/private.html:332
msgid "Captain"
msgstr "Capità"

#: CTFd/themes/core-beta/templates/teams/private.html:348
#: CTFd/themes/core-beta/templates/teams/public.html:109
#: CTFd/themes/core-beta/templates/users/private.html:88
#: CTFd/themes/core-beta/templates/users/public.html:88
msgid "Awards"
msgstr "Premis"

#: CTFd/themes/core-beta/templates/teams/private.html:371
#: CTFd/themes/core-beta/templates/teams/public.html:137
#: CTFd/themes/core-beta/templates/users/private.html:110
msgid "Solves"
msgstr "Solucions"

#: CTFd/themes/core-beta/templates/teams/private.html:376
#: CTFd/themes/core-beta/templates/teams/public.html:142
#: CTFd/themes/core-beta/templates/users/private.html:115
#: CTFd/themes/core-beta/templates/users/public.html:114
msgid "Category"
msgstr "Categoria"

#: CTFd/themes/core-beta/templates/teams/private.html:377
#: CTFd/themes/core-beta/templates/teams/public.html:143
#: CTFd/themes/core-beta/templates/users/private.html:116
#: CTFd/themes/core-beta/templates/users/public.html:115
msgid "Value"
msgstr "Valor"

#: CTFd/themes/core-beta/templates/teams/private.html:468
#: CTFd/themes/core-beta/templates/teams/public.html:232
#: CTFd/themes/core-beta/templates/users/private.html:203
#: CTFd/themes/core-beta/templates/users/public.html:204
msgid "No solves yet"
msgstr "Encara no hi ha solucions"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:14
msgid "In order to participate you must either join or create a team."
msgstr "Per participar cal que t'uneixis o crear un equip."

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:22
msgid "Play with Official Team"
msgstr "Juga amb l'Equip Oficial"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:27
msgid "Join Unofficial Team"
msgstr "Uneix-te a un Equip No Oficial"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:30
msgid "Create Unofficial Team"
msgstr "Crea un Equip No Oficial"

#: CTFd/themes/core-beta/templates/teams/teams.html:124
#: CTFd/themes/core-beta/templates/users/users.html:114
msgid "Page"
msgstr "Pàgina"

#: CTFd/themes/core-beta/templates/users/users.html:46
msgid "User"
msgstr "Usuari"

#: CTFd/utils/modes/__init__.py:35
msgid "user"
msgid_plural "users"
msgstr[0] "usuari"
msgstr[1] "usuaris"

#: CTFd/utils/modes/__init__.py:37
msgid "team"
msgid_plural "teams"
msgstr[0] "equip"
msgstr[1] "equips"

