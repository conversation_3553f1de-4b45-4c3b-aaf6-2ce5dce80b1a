msgid ""
msgstr ""
"Project-Id-Version: CTFd\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2023-10-07 03:17-0400\n"
"PO-Revision-Date: 2024-11-11 09:40\n"
"Last-Translator: \n"
"Language-Team: French\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.12.1\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Crowdin-Project: CTFd\n"
"X-Crowdin-Project-ID: 659064\n"
"X-Crowdin-Language: fr\n"
"X-Crowdin-File: /master/messages.pot\n"
"X-Crowdin-File-ID: 188\n"
"Language: fr_FR\n"

#: CTFd/forms/auth.py:19 CTFd/forms/self.py:16
#: CTFd/themes/core-beta/templates/teams/private.html:319
#: CTFd/themes/core-beta/templates/teams/public.html:85
msgid "User Name"
msgstr "Nom d'utilisateur"

#: CTFd/forms/auth.py:21 CTFd/forms/auth.py:53 CTFd/forms/self.py:17
#: CTFd/forms/teams.py:203
msgid "Email"
msgstr "E-mail"

#: CTFd/forms/auth.py:22 CTFd/forms/auth.py:43 CTFd/forms/auth.py:60
#: CTFd/forms/self.py:19 CTFd/forms/teams.py:204
msgid "Password"
msgstr "Mot de passe"

#: CTFd/forms/auth.py:23 CTFd/forms/auth.py:44 CTFd/forms/auth.py:55
#: CTFd/forms/auth.py:62 CTFd/forms/self.py:24 CTFd/forms/teams.py:127
#: CTFd/forms/teams.py:210 CTFd/themes/core-beta/templates/challenge.html:147
msgid "Submit"
msgstr "Soumettre"

#: CTFd/forms/auth.py:39
msgid "User Name or Email"
msgstr "Nom d'utilisateur ou adresse e-mail"

#: CTFd/forms/auth.py:48
msgid "Resend Confirmation Email"
msgstr "Renvoyer un mail de confirmation"

#: CTFd/forms/self.py:18 CTFd/forms/users.py:155
msgid "Language"
msgstr "Langue"

#: CTFd/forms/self.py:20
msgid "Current Password"
msgstr "Mot de passe actuel"

#: CTFd/forms/self.py:21 CTFd/forms/teams.py:113 CTFd/forms/teams.py:191
#: CTFd/forms/teams.py:206 CTFd/forms/users.py:138
#: CTFd/themes/core-beta/templates/teams/teams.html:51
#: CTFd/themes/core-beta/templates/users/users.html:48
msgid "Affiliation"
msgstr "Affiliation"

#: CTFd/forms/self.py:22 CTFd/forms/teams.py:119 CTFd/forms/teams.py:192
#: CTFd/forms/teams.py:205 CTFd/forms/users.py:139
#: CTFd/themes/core-beta/templates/teams/teams.html:50
#: CTFd/themes/core-beta/templates/users/users.html:47
msgid "Website"
msgstr "Site web"

#: CTFd/forms/self.py:23 CTFd/forms/teams.py:123 CTFd/forms/teams.py:207
#: CTFd/themes/core-beta/templates/teams/teams.html:52
#: CTFd/themes/core-beta/templates/users/users.html:49
msgid "Country"
msgstr "Pays"

#: CTFd/forms/self.py:52 CTFd/themes/core-beta/templates/settings.html:225
msgid "Expiration"
msgstr "Expiration"

#: CTFd/forms/self.py:54
msgid "Generate"
msgstr "Générer"

#: CTFd/forms/setup.py:29
msgid "Event Name"
msgstr "Nom de l'évènement"

#: CTFd/forms/setup.py:29
msgid "The name of your CTF event/workshop"
msgstr "Le nom de votre CTF/atelier"

#: CTFd/forms/setup.py:32
msgid "Event Description"
msgstr "Description de l'évènement"

#: CTFd/forms/setup.py:32
msgid "Description for the CTF"
msgstr "Description du CTF"

#: CTFd/forms/setup.py:35 CTFd/forms/setup.py:36
msgid "User Mode"
msgstr "Mode Utilisateur"

#: CTFd/forms/setup.py:36
msgid "Team Mode"
msgstr "Mode Équipe"

#: CTFd/forms/setup.py:38
msgid "Controls whether users join together in teams to play (Team Mode) or play as themselves (User Mode)"
msgstr "Contrôle si des utilisateurs se rejoignent pour jouer en équipe (Mode Équipe) ou jouent pour eux-mêmes (Mode Utilisateur)"

#: CTFd/forms/setup.py:45
msgid "Admin Username"
msgstr "Nom de l'administrateur"

#: CTFd/forms/setup.py:46
msgid "Your username for the administration account"
msgstr "Votre nom pour le compte d'administration"

#: CTFd/forms/setup.py:50
msgid "Admin Email"
msgstr "E-mail de l'administrateur"

#: CTFd/forms/setup.py:51
msgid "Your email address for the administration account"
msgstr "Votre adresse e-mail pour le compte d'administration"

#: CTFd/forms/setup.py:55
msgid "Admin Password"
msgstr "Mot de passe de l'administrateur"

#: CTFd/forms/setup.py:56
msgid "Your password for the administration account"
msgstr "Votre mot de passe pour le compte d'administration"

#: CTFd/forms/setup.py:61
msgid "Logo"
msgstr "Logo"

#: CTFd/forms/setup.py:62
msgid "Logo to use for the website instead of a CTF name. Used as the home page button. Optional."
msgstr "Logo à utiliser pour le site web plutôt que le nom de l'évènement. Utilisé comme bouton d'accueil. Optionnel."

#: CTFd/forms/setup.py:67
msgid "Banner"
msgstr "Bannière"

#: CTFd/forms/setup.py:67
msgid "Banner to use for the homepage. Optional."
msgstr "Bannière à utiliser pour la page d'accueil. Optionnel."

#: CTFd/forms/setup.py:70
msgid "Small Icon"
msgstr "Petite icône"

#: CTFd/forms/setup.py:71
msgid "favicon used in user's browsers. Only PNGs accepted. Must be 32x32px. Optional."
msgstr "Favicon utilisé dans les navigateurs des utilisateurs. Seuls les PNGs sont acceptés. Doit être de dimensions 32x32px. Optionnel."

#: CTFd/forms/setup.py:76
msgid "Theme"
msgstr "Thème"

#: CTFd/forms/setup.py:77
msgid "CTFd Theme to use. Can be changed later."
msgstr "Thème de CTFd à utiliser. Peut être changé ultérieurement."

#: CTFd/forms/setup.py:84
msgid "Theme Color"
msgstr "Couleur du thème"

#: CTFd/forms/setup.py:85
msgid "Color used by theme to control aesthetics. Requires theme support. Optional."
msgstr "Couleur utilisée par le thème pour contrôler l'esthétique. Requiert le support du thème. Optionnel."

#: CTFd/forms/setup.py:91
msgid "Verify Emails"
msgstr "Vérifier les courriels"

#: CTFd/forms/setup.py:143
msgid "Start Time"
msgstr "Date de lancement"

#: CTFd/forms/setup.py:144
msgid "Time when your CTF is scheduled to start. Optional."
msgstr "Date planifiée du lancement du CTF. Optionnel."

#: CTFd/forms/setup.py:147
msgid "End Time"
msgstr "Date de fin"

#: CTFd/forms/setup.py:148
msgid "Time when your CTF is scheduled to end. Optional."
msgstr "Date planifiée de fin du CTF. Optionnel."

#: CTFd/forms/setup.py:150
msgid "Finish"
msgstr "Fin"

#: CTFd/forms/teams.py:76 CTFd/forms/teams.py:83 CTFd/forms/teams.py:100
#: CTFd/forms/teams.py:202
msgid "Team Name"
msgstr "Nom d'équipe"

#: CTFd/forms/teams.py:77 CTFd/forms/teams.py:84
msgid "Team Password"
msgstr "Mot de passe d'équipe"

#: CTFd/forms/teams.py:78 CTFd/forms/teams.py:258
msgid "Join"
msgstr "Rejoindre"

#: CTFd/forms/teams.py:85
msgid "Create"
msgstr "Créer"

#: CTFd/forms/teams.py:101
msgid "Your team's public name shown to other competitors"
msgstr "Votre nom d'équipe public à montrer aux autres compétiteurs"

#: CTFd/forms/teams.py:104
msgid "New Team Password"
msgstr "Nouveau mot de passe d'équipe"

#: CTFd/forms/teams.py:104
msgid "Set a new team join password"
msgstr "Régler un nouveau mot de passe d'équipe pour rejoindre"

#: CTFd/forms/teams.py:107
msgid "Confirm Current Team Password"
msgstr "Confirmer le mot de passe d'équipe actuel"

#: CTFd/forms/teams.py:108
msgid "Provide your current team password (or your password) to update your team's password"
msgstr "Saisissez votre mot de passe d'équipe actuel (ou votre mot de passe) pour mettre à jour celui de l'équipe"

#: CTFd/forms/teams.py:114
msgid "Your team's affiliation publicly shown to other competitors"
msgstr "L'affiliation de votre équipe visible aux autres compétiteurs"

#: CTFd/forms/teams.py:120
msgid "Your team's website publicly shown to other competitors"
msgstr "Le site web de votre équipe visible aux autres compétiteurs"

#: CTFd/forms/teams.py:125
msgid "Your team's country publicly shown to other competitors"
msgstr "Le pays de votre équipe visible aux autres compétiteurs"

#: CTFd/forms/teams.py:165
msgid "Team Captain"
msgstr "Capitaine de l'équipe"

#: CTFd/forms/teams.py:188 CTFd/forms/users.py:135
msgid "Search Field"
msgstr "Champ de recherche"

#: CTFd/forms/teams.py:190 CTFd/forms/users.py:137
#: CTFd/themes/core-beta/templates/challenge.html:183
msgid "Name"
msgstr "Nom"

#: CTFd/forms/teams.py:197 CTFd/forms/users.py:145
msgid "Parameter"
msgstr "Paramètre"

#: CTFd/forms/teams.py:198 CTFd/forms/users.py:149
msgid "Search"
msgstr "Recherche"

#: CTFd/forms/teams.py:208
msgid "Hidden"
msgstr "Caché"

#: CTFd/forms/teams.py:209
msgid "Banned"
msgstr "Banni"

#: CTFd/forms/teams.py:254
msgid "Invite Link"
msgstr "Lien d'invitation"

#: CTFd/forms/users.py:146
msgid "Search for matching users"
msgstr "Rechercher des utilisateurs correspondants"

#: CTFd/themes/core-beta/templates/base.html:49
msgid "Powered by CTFd"
msgstr "Propulsé par CTFd"

#: CTFd/themes/core-beta/templates/challenge.html:11
#: CTFd/themes/core-beta/templates/teams/private.html:375
#: CTFd/themes/core-beta/templates/teams/public.html:141
#: CTFd/themes/core-beta/templates/users/private.html:114
#: CTFd/themes/core-beta/templates/users/public.html:113
msgid "Challenge"
msgstr "Challenge"

#: CTFd/themes/core-beta/templates/challenge.html:19
#, python-format
msgid "%(num)d Solve"
msgid_plural "%(num)d Solves"
msgstr[0] "%(num)d résolution"
msgstr[1] "%(num)d résolutions"

#: CTFd/themes/core-beta/templates/challenge.html:73
msgid "View Hint"
msgstr "Voir l'indice"

#: CTFd/themes/core-beta/templates/challenge.html:135
msgid "Flag"
msgstr "Flag"

#: CTFd/themes/core-beta/templates/challenge.html:167
msgid "Next Challenge"
msgstr "Challenge suivant"

#: CTFd/themes/core-beta/templates/challenge.html:186
#: CTFd/themes/core-beta/templates/setup.html:305
#: CTFd/themes/core-beta/templates/setup.html:326
msgid "Date"
msgstr "Date"

#: CTFd/themes/core-beta/templates/challenges.html:7
#: CTFd/themes/core-beta/templates/components/navbar.html:57
msgid "Challenges"
msgstr "Challenges"

#: CTFd/themes/core-beta/templates/confirm.html:7
msgid "Confirm"
msgstr "Confirmer"

#: CTFd/themes/core-beta/templates/confirm.html:18
msgid "We've sent a confirmation email to your email address."
msgstr "Nous avons envoyé un mail de confirmation à votre adresse e-mail."

#: CTFd/themes/core-beta/templates/confirm.html:24
msgid "Please click the link in that email to confirm your account."
msgstr "Merci de cliquer sur le lien d'invitation envoyé par mail pour confirmer votre compte."

#: CTFd/themes/core-beta/templates/confirm.html:30
msgid "If the email doesn’t arrive, check your spam folder or contact an administrator to manually verify your account."
msgstr "Si le mail n'arrive pas, vérifier votre dossier spam ou contactez un administrateur pour vérifier manuellement votre compte."

#: CTFd/themes/core-beta/templates/confirm.html:43
msgid "Change Email Address"
msgstr "Changer l'adresse e-mail"

#: CTFd/themes/core-beta/templates/components/navbar.html:178
#: CTFd/themes/core-beta/templates/login.html:7
msgid "Login"
msgstr "S'identifier"

#: CTFd/themes/core-beta/templates/login.html:40
msgid "Forgot your password?"
msgstr "Mot de passe oublié ?"

#: CTFd/themes/core-beta/templates/components/navbar.html:97
#: CTFd/themes/core-beta/templates/notifications.html:7
msgid "Notifications"
msgstr "Notifications"

#: CTFd/themes/core-beta/templates/notifications.html:14
msgid "There are no notifications yet"
msgstr "Il n'y a pas encore de notifications"

#: CTFd/themes/core-beta/templates/components/navbar.html:165
#: CTFd/themes/core-beta/templates/register.html:7
msgid "Register"
msgstr "S'inscrire"

#: CTFd/themes/core-beta/templates/register.html:35
msgid "Your username on the site"
msgstr "Votre nom d'utilisateur sur le site"

#: CTFd/themes/core-beta/templates/register.html:43
msgid "Never shown to the public"
msgstr "Jamais montré publiquement"

#: CTFd/themes/core-beta/templates/register.html:51
msgid "Password used to log into your account"
msgstr "Mot de passe utilisé pour vous connecter"

#: CTFd/themes/core-beta/templates/reset_password.html:7
msgid "Reset Password"
msgstr "Réinitialiser le mot de passe"

#: CTFd/themes/core-beta/templates/reset_password.html:21
msgid "You can now reset the password for your account and log in. Please enter in a new password below."
msgstr "Vous pouvez désormais réinitialiser votre mot de passe et vous connecter. Merci d'entrer votre nouveau mot de passe ci-dessous."

#: CTFd/themes/core-beta/templates/reset_password.html:44
msgid "Please provide the email address associated with your account below."
msgstr "Merci de saisir l'adresse e-mail associée à votre compte ci-dessous."

#: CTFd/themes/core-beta/templates/components/navbar.html:135
#: CTFd/themes/core-beta/templates/settings.html:8
#: CTFd/themes/core-beta/templates/setup.html:50
msgid "Settings"
msgstr "Paramètres"

#: CTFd/themes/core-beta/templates/components/navbar.html:123
#: CTFd/themes/core-beta/templates/settings.html:21
msgid "Profile"
msgstr "Compte"

#: CTFd/themes/core-beta/templates/settings.html:26
msgid "Access Tokens"
msgstr "Clé d'Accès"

#: CTFd/themes/core-beta/templates/settings.html:95
msgid "Your profile has been updated"
msgstr "Votre compte a été mis à jour"

#: CTFd/themes/core-beta/templates/settings.html:103
#: CTFd/themes/core-beta/templates/teams/private.html:83
#: CTFd/themes/core-beta/templates/teams/private.html:198
msgid "Error:"
msgstr "Erreur :"

#: CTFd/themes/core-beta/templates/settings.html:129
msgid "API Key Generated"
msgstr "Clé d'API générée"

#: CTFd/themes/core-beta/templates/settings.html:137
msgid "Please copy your API Key, it won't be shown again!"
msgstr "Merci d'enregistrer votre clé d'API, elle ne sera plus montrée !"

#: CTFd/themes/core-beta/templates/settings.html:183
msgid "Active Tokens"
msgstr "Clés actives"

#: CTFd/themes/core-beta/templates/settings.html:195
msgid "Delete Token"
msgstr "Supprimer la clé"

#: CTFd/themes/core-beta/templates/settings.html:204
msgid "Are you sure you want to delete this token?"
msgstr "Confirmez-vous la suppression de cette clé ?"

#: CTFd/themes/core-beta/templates/settings.html:224
msgid "Created"
msgstr "Créé"

#: CTFd/themes/core-beta/templates/settings.html:226
msgid "Description"
msgstr "Description"

#: CTFd/themes/core-beta/templates/settings.html:227
msgid "Delete"
msgstr "Supprimer"

#: CTFd/themes/core-beta/templates/setup.html:24
msgid "Setup"
msgstr "Régler"

#: CTFd/themes/core-beta/templates/setup.html:44
msgid "General"
msgstr "Général"

#: CTFd/themes/core-beta/templates/setup.html:47
msgid "Mode"
msgstr "Mode"

#: CTFd/themes/core-beta/templates/setup.html:53
msgid "Administration"
msgstr "Administration"

#: CTFd/themes/core-beta/templates/setup.html:56
msgid "Style"
msgstr "Style"

#: CTFd/themes/core-beta/templates/setup.html:59
msgid "Date &amp; Time"
msgstr "Date & Heure"

#: CTFd/themes/core-beta/templates/setup.html:62
msgid "Integrations"
msgstr "Intégrations"

#: CTFd/themes/core-beta/templates/setup.html:86
#: CTFd/themes/core-beta/templates/setup.html:138
#: CTFd/themes/core-beta/templates/setup.html:202
#: CTFd/themes/core-beta/templates/setup.html:238
#: CTFd/themes/core-beta/templates/setup.html:296
#: CTFd/themes/core-beta/templates/setup.html:345
msgid "Next"
msgstr "Suivant"

#: CTFd/themes/core-beta/templates/setup.html:112
msgid "Participants register accounts and form teams"
msgstr "Les participants s'enregistrent et forment des équipes"

#: CTFd/themes/core-beta/templates/setup.html:113
msgid "If a team member solves a challenge, the entire team receives credit"
msgstr "Si un membre de l'équipe résout un challenge, l'équipe entière reçoit le crédit"

#: CTFd/themes/core-beta/templates/setup.html:115
msgid "Easier to see which team member solved a challenge"
msgstr "Plus aisé de voir quel membre de l'équipe a résolu un challenge"

#: CTFd/themes/core-beta/templates/setup.html:116
msgid "May be slightly more difficult to administer"
msgstr "Pourrait être un peu plus difficile à administrer"

#: CTFd/themes/core-beta/templates/setup.html:120
msgid "Participants only register an individual account"
msgstr "Les participants n'enregistrent qu'un compte individuel"

#: CTFd/themes/core-beta/templates/setup.html:121
msgid "Players can share accounts to form pseudo-teams"
msgstr "Les compétiteurs peuvent partager des comptes pour former des pseudo-équipes"

#: CTFd/themes/core-beta/templates/setup.html:123
msgid "Easier to organize"
msgstr "Plus facile à organiser"

#: CTFd/themes/core-beta/templates/setup.html:124
msgid "Difficult to attribute solutions to individual team members"
msgstr "Difficile d'attribuer des solutions aux membres individuels de l'équipe"

#: CTFd/themes/core-beta/templates/setup.html:143
msgid "Visibility Settings"
msgstr "Paramètres de visibilité"

#: CTFd/themes/core-beta/templates/setup.html:146
msgid "Control the visibility of different sections of CTFd"
msgstr "Contrôler la visibilité des différentes sections du CTFd"

#: CTFd/themes/core-beta/templates/setup.html:232
msgid "Subscribe email address to the CTFd LLC Newsletter for news and updates"
msgstr "S'abonner par e-mail aux actualités de CTFd LLC"

#: CTFd/themes/core-beta/templates/setup.html:309
#: CTFd/themes/core-beta/templates/setup.html:330
#: CTFd/themes/core-beta/templates/teams/private.html:378
#: CTFd/themes/core-beta/templates/teams/public.html:144
#: CTFd/themes/core-beta/templates/users/private.html:117
#: CTFd/themes/core-beta/templates/users/public.html:116
msgid "Time"
msgstr "Temps"

#: CTFd/themes/core-beta/templates/setup.html:334
msgid "UTC Preview"
msgstr "Prévisualisation UTC"

#: CTFd/themes/core-beta/templates/components/navbar.html:34
#: CTFd/themes/core-beta/templates/users/users.html:6
msgid "Users"
msgstr "Utilisateurs"

#: CTFd/themes/core-beta/templates/components/navbar.html:41
#: CTFd/themes/core-beta/templates/teams/teams.html:6
msgid "Teams"
msgstr "Équipes"

#: CTFd/themes/core-beta/templates/components/navbar.html:50
msgid "Scoreboard"
msgstr "Classement"

#: CTFd/themes/core-beta/templates/components/navbar.html:80
msgid "Admin Panel"
msgstr "Administration"

#: CTFd/themes/core-beta/templates/components/navbar.html:110
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:6
#: CTFd/themes/core-beta/templates/teams/teams.html:49
msgid "Team"
msgstr "Équipe"

#: CTFd/themes/core-beta/templates/components/navbar.html:147
msgid "Logout"
msgstr "Se déconnecter"

#: CTFd/themes/core-beta/templates/errors/403.html:9
msgid "Forbidden"
msgstr "Non autorisé"

#: CTFd/themes/core-beta/templates/errors/404.html:11
msgid "File not found"
msgstr "Fichier non trouvé"

#: CTFd/themes/core-beta/templates/errors/404.html:12
msgid "Sorry about that"
msgstr "Désolé"

#: CTFd/themes/core-beta/templates/errors/429.html:11
msgid "Too many requests"
msgstr "Trop de requêtes"

#: CTFd/themes/core-beta/templates/errors/429.html:12
msgid "Please slow down!"
msgstr "Merci de ralentir !"

#: CTFd/themes/core-beta/templates/errors/502.html:11
msgid "Bad Gateway"
msgstr "Mauvaise passerelle"

#: CTFd/themes/core-beta/templates/macros/forms.html:13
#: CTFd/themes/core-beta/templates/macros/forms.html:36
msgid "(Optional)"
msgstr "(Optionnel)"

#: CTFd/themes/core-beta/templates/teams/invite.html:6
#: CTFd/themes/core-beta/templates/teams/join_team.html:6
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:36
msgid "Join Team"
msgstr "Rejoindre l'équipe"

#: CTFd/themes/core-beta/templates/teams/invite.html:15
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:12
msgid "Welcome to"
msgstr "Bienvenue sur"

#: CTFd/themes/core-beta/templates/teams/invite.html:19
msgid "Click the button below to join the team!"
msgstr "Cliquer sur le bouton ci-dessous pour rejoindre l'équipe !"

#: CTFd/themes/core-beta/templates/teams/new_team.html:6
#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:39
msgid "Create Team"
msgstr "Créer une équipe"

#: CTFd/themes/core-beta/templates/teams/private.html:8
msgid "Edit Team"
msgstr "Équipe de rédaction"

#: CTFd/themes/core-beta/templates/teams/private.html:76
msgid "Your team's profile has been updated"
msgstr "Le profil de votre équipe a été mis à jour"

#: CTFd/themes/core-beta/templates/teams/private.html:104
msgid "Choose Captain"
msgstr "Choisir le capitaine"

#: CTFd/themes/core-beta/templates/teams/private.html:127
msgid "Your captain rights have been transferred"
msgstr "Vos droits de capitaine ont été transférés"

#: CTFd/themes/core-beta/templates/teams/private.html:152
msgid "Invite Users"
msgstr "Inviter des utilisateurs"

#: CTFd/themes/core-beta/templates/teams/private.html:171
msgid "Share this link with your team members for them to join your team"
msgstr "Partagez ce lien avec les membres de votre équipe pour qu'ils rejoignent votre équipe"

#: CTFd/themes/core-beta/templates/teams/private.html:174
msgid "Invite links can be re-used and expire after 1 day"
msgstr "Les liens d'invitation peuvent être réutilisés et expirent au bout d'un jour"

#: CTFd/themes/core-beta/templates/teams/private.html:188
msgid "Disband Team"
msgstr "Dissolution de l'équipe"

#: CTFd/themes/core-beta/templates/teams/private.html:193
msgid "Are you sure you want to disband your team?"
msgstr "Êtes-vous sûr de vouloir dissoudre votre équipe ?"

#: CTFd/themes/core-beta/templates/teams/private.html:206
msgid "No"
msgstr "Non"

#: CTFd/themes/core-beta/templates/teams/private.html:207
msgid "Yes"
msgstr "Oui"

#: CTFd/themes/core-beta/templates/teams/private.html:218
#: CTFd/themes/core-beta/templates/teams/public.html:10
#: CTFd/themes/core-beta/templates/users/private.html:21
#: CTFd/themes/core-beta/templates/users/public.html:21
#: CTFd/themes/core-beta/templates/users/users.html:66
msgid "Official"
msgstr "Officiel"

#: CTFd/themes/core-beta/templates/teams/private.html:250
msgid "place"
msgstr "position"

#: CTFd/themes/core-beta/templates/teams/private.html:258
msgid "points"
msgstr "points"

#: CTFd/themes/core-beta/templates/teams/private.html:315
#: CTFd/themes/core-beta/templates/teams/public.html:81
msgid "Members"
msgstr "Les membres"

#: CTFd/themes/core-beta/templates/teams/private.html:320
#: CTFd/themes/core-beta/templates/teams/public.html:86
msgid "Score"
msgstr "Score"

#: CTFd/themes/core-beta/templates/teams/private.html:332
msgid "Captain"
msgstr "Capitaine"

#: CTFd/themes/core-beta/templates/teams/private.html:348
#: CTFd/themes/core-beta/templates/teams/public.html:109
#: CTFd/themes/core-beta/templates/users/private.html:88
#: CTFd/themes/core-beta/templates/users/public.html:88
msgid "Awards"
msgstr "Trophées"

#: CTFd/themes/core-beta/templates/teams/private.html:371
#: CTFd/themes/core-beta/templates/teams/public.html:137
#: CTFd/themes/core-beta/templates/users/private.html:110
msgid "Solves"
msgstr "Résolutions"

#: CTFd/themes/core-beta/templates/teams/private.html:376
#: CTFd/themes/core-beta/templates/teams/public.html:142
#: CTFd/themes/core-beta/templates/users/private.html:115
#: CTFd/themes/core-beta/templates/users/public.html:114
msgid "Category"
msgstr "Catégorie"

#: CTFd/themes/core-beta/templates/teams/private.html:377
#: CTFd/themes/core-beta/templates/teams/public.html:143
#: CTFd/themes/core-beta/templates/users/private.html:116
#: CTFd/themes/core-beta/templates/users/public.html:115
msgid "Value"
msgstr "Valeur"

#: CTFd/themes/core-beta/templates/teams/private.html:468
#: CTFd/themes/core-beta/templates/teams/public.html:232
#: CTFd/themes/core-beta/templates/users/private.html:203
#: CTFd/themes/core-beta/templates/users/public.html:204
msgid "No solves yet"
msgstr "Aucune résolution pour le moment"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:14
msgid "In order to participate you must either join or create a team."
msgstr "Vous devez créer ou rejoindre une équipe pour participer."

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:22
msgid "Play with Official Team"
msgstr "Participer avec l'Équipe Officielle"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:27
msgid "Join Unofficial Team"
msgstr "Rejoindre une Équipe Non-officielle"

#: CTFd/themes/core-beta/templates/teams/team_enrollment.html:30
msgid "Create Unofficial Team"
msgstr "Créer une Équipe Non-officielle"

#: CTFd/themes/core-beta/templates/teams/teams.html:124
#: CTFd/themes/core-beta/templates/users/users.html:114
msgid "Page"
msgstr "Page"

#: CTFd/themes/core-beta/templates/users/users.html:46
msgid "User"
msgstr "Utilisateur"

#: CTFd/utils/modes/__init__.py:35
msgid "user"
msgid_plural "users"
msgstr[0] "utilisateur"
msgstr[1] "utilisateurs"

#: CTFd/utils/modes/__init__.py:37
msgid "team"
msgid_plural "teams"
msgstr[0] "équipe"
msgstr[1] "équipes"

