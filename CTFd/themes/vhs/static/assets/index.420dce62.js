var te=typeof globalThis<"u"&&globalThis||typeof self<"u"&&self||typeof te<"u"&&te,ae={searchParams:"URLSearchParams"in te,iterable:"Symbol"in te&&"iterator"in Symbol,blob:"FileReader"in te&&"Blob"in te&&function(){try{return new Blob,!0}catch{return!1}}(),formData:"FormData"in te,arrayBuffer:"ArrayBuffer"in te};function hl(e){return e&&DataView.prototype.isPrototypeOf(e)}if(ae.arrayBuffer)var pl=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],_l=ArrayBuffer.isView||function(e){return e&&pl.indexOf(Object.prototype.toString.call(e))>-1};function nn(e){if(typeof e!="string"&&(e=String(e)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(e)||e==="")throw new TypeError('Invalid character in header field name: "'+e+'"');return e.toLowerCase()}function ai(e){return typeof e!="string"&&(e=String(e)),e}function ci(e){var t={next:function(){var n=e.shift();return{done:n===void 0,value:n}}};return ae.iterable&&(t[Symbol.iterator]=function(){return t}),t}function Y(e){this.map={},e instanceof Y?e.forEach(function(t,n){this.append(n,t)},this):Array.isArray(e)?e.forEach(function(t){this.append(t[0],t[1])},this):e&&Object.getOwnPropertyNames(e).forEach(function(t){this.append(t,e[t])},this)}Y.prototype.append=function(e,t){e=nn(e),t=ai(t);var n=this.map[e];this.map[e]=n?n+", "+t:t};Y.prototype.delete=function(e){delete this.map[nn(e)]};Y.prototype.get=function(e){return e=nn(e),this.has(e)?this.map[e]:null};Y.prototype.has=function(e){return this.map.hasOwnProperty(nn(e))};Y.prototype.set=function(e,t){this.map[nn(e)]=ai(t)};Y.prototype.forEach=function(e,t){for(var n in this.map)this.map.hasOwnProperty(n)&&e.call(t,this.map[n],n,this)};Y.prototype.keys=function(){var e=[];return this.forEach(function(t,n){e.push(n)}),ci(e)};Y.prototype.values=function(){var e=[];return this.forEach(function(t){e.push(t)}),ci(e)};Y.prototype.entries=function(){var e=[];return this.forEach(function(t,n){e.push([n,t])}),ci(e)};ae.iterable&&(Y.prototype[Symbol.iterator]=Y.prototype.entries);function pr(e){if(e.bodyUsed)return Promise.reject(new TypeError("Already read"));e.bodyUsed=!0}function fo(e){return new Promise(function(t,n){e.onload=function(){t(e.result)},e.onerror=function(){n(e.error)}})}function ml(e){var t=new FileReader,n=fo(t);return t.readAsArrayBuffer(e),n}function gl(e){var t=new FileReader,n=fo(t);return t.readAsText(e),n}function vl(e){for(var t=new Uint8Array(e),n=new Array(t.length),r=0;r<t.length;r++)n[r]=String.fromCharCode(t[r]);return n.join("")}function ds(e){if(e.slice)return e.slice(0);var t=new Uint8Array(e.byteLength);return t.set(new Uint8Array(e)),t.buffer}function ho(){return this.bodyUsed=!1,this._initBody=function(e){this.bodyUsed=this.bodyUsed,this._bodyInit=e,e?typeof e=="string"?this._bodyText=e:ae.blob&&Blob.prototype.isPrototypeOf(e)?this._bodyBlob=e:ae.formData&&FormData.prototype.isPrototypeOf(e)?this._bodyFormData=e:ae.searchParams&&URLSearchParams.prototype.isPrototypeOf(e)?this._bodyText=e.toString():ae.arrayBuffer&&ae.blob&&hl(e)?(this._bodyArrayBuffer=ds(e.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):ae.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(e)||_l(e))?this._bodyArrayBuffer=ds(e):this._bodyText=e=Object.prototype.toString.call(e):this._bodyText="",this.headers.get("content-type")||(typeof e=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):ae.searchParams&&URLSearchParams.prototype.isPrototypeOf(e)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},ae.blob&&(this.blob=function(){var e=pr(this);if(e)return e;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){if(this._bodyArrayBuffer){var e=pr(this);return e||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}else return this.blob().then(ml)}),this.text=function(){var e=pr(this);if(e)return e;if(this._bodyBlob)return gl(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(vl(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},ae.formData&&(this.formData=function(){return this.text().then(El)}),this.json=function(){return this.text().then(JSON.parse)},this}var yl=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function bl(e){var t=e.toUpperCase();return yl.indexOf(t)>-1?t:e}function ct(e,t){if(!(this instanceof ct))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');t=t||{};var n=t.body;if(e instanceof ct){if(e.bodyUsed)throw new TypeError("Already read");this.url=e.url,this.credentials=e.credentials,t.headers||(this.headers=new Y(e.headers)),this.method=e.method,this.mode=e.mode,this.signal=e.signal,!n&&e._bodyInit!=null&&(n=e._bodyInit,e.bodyUsed=!0)}else this.url=String(e);if(this.credentials=t.credentials||this.credentials||"same-origin",(t.headers||!this.headers)&&(this.headers=new Y(t.headers)),this.method=bl(t.method||this.method||"GET"),this.mode=t.mode||this.mode||null,this.signal=t.signal||this.signal,this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&n)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(n),(this.method==="GET"||this.method==="HEAD")&&(t.cache==="no-store"||t.cache==="no-cache")){var r=/([?&])_=[^&]*/;if(r.test(this.url))this.url=this.url.replace(r,"$1_="+new Date().getTime());else{var i=/\?/;this.url+=(i.test(this.url)?"&":"?")+"_="+new Date().getTime()}}}ct.prototype.clone=function(){return new ct(this,{body:this._bodyInit})};function El(e){var t=new FormData;return e.trim().split("&").forEach(function(n){if(n){var r=n.split("="),i=r.shift().replace(/\+/g," "),o=r.join("=").replace(/\+/g," ");t.append(decodeURIComponent(i),decodeURIComponent(o))}}),t}function wl(e){var t=new Y,n=e.replace(/\r?\n[\t ]+/g," ");return n.split("\r").map(function(r){return r.indexOf(`
`)===0?r.substr(1,r.length):r}).forEach(function(r){var i=r.split(":"),o=i.shift().trim();if(o){var a=i.join(":").trim();t.append(o,a)}}),t}ho.call(ct.prototype);function xe(e,t){if(!(this instanceof xe))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');t||(t={}),this.type="default",this.status=t.status===void 0?200:t.status,this.ok=this.status>=200&&this.status<300,this.statusText=t.statusText===void 0?"":""+t.statusText,this.headers=new Y(t.headers),this.url=t.url||"",this._initBody(e)}ho.call(xe.prototype);xe.prototype.clone=function(){return new xe(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new Y(this.headers),url:this.url})};xe.error=function(){var e=new xe(null,{status:0,statusText:""});return e.type="error",e};var Al=[301,302,303,307,308];xe.redirect=function(e,t){if(Al.indexOf(t)===-1)throw new RangeError("Invalid status code");return new xe(null,{status:t,headers:{location:e}})};var tt=te.DOMException;try{new tt}catch{tt=function(t,n){this.message=t,this.name=n;var r=Error(t);this.stack=r.stack},tt.prototype=Object.create(Error.prototype),tt.prototype.constructor=tt}function po(e,t){return new Promise(function(n,r){var i=new ct(e,t);if(i.signal&&i.signal.aborted)return r(new tt("Aborted","AbortError"));var o=new XMLHttpRequest;function a(){o.abort()}o.onload=function(){var f={status:o.status,statusText:o.statusText,headers:wl(o.getAllResponseHeaders()||"")};f.url="responseURL"in o?o.responseURL:f.headers.get("X-Request-URL");var d="response"in o?o.response:o.responseText;setTimeout(function(){n(new xe(d,f))},0)},o.onerror=function(){setTimeout(function(){r(new TypeError("Network request failed"))},0)},o.ontimeout=function(){setTimeout(function(){r(new TypeError("Network request failed"))},0)},o.onabort=function(){setTimeout(function(){r(new tt("Aborted","AbortError"))},0)};function u(f){try{return f===""&&te.location.href?te.location.href:f}catch{return f}}o.open(i.method,u(i.url),!0),i.credentials==="include"?o.withCredentials=!0:i.credentials==="omit"&&(o.withCredentials=!1),"responseType"in o&&(ae.blob?o.responseType="blob":ae.arrayBuffer&&i.headers.get("Content-Type")&&i.headers.get("Content-Type").indexOf("application/octet-stream")!==-1&&(o.responseType="arraybuffer")),t&&typeof t.headers=="object"&&!(t.headers instanceof Y)?Object.getOwnPropertyNames(t.headers).forEach(function(f){o.setRequestHeader(f,ai(t.headers[f]))}):i.headers.forEach(function(f,d){o.setRequestHeader(d,f)}),i.signal&&(i.signal.addEventListener("abort",a),o.onreadystatechange=function(){o.readyState===4&&i.signal.removeEventListener("abort",a)}),o.send(typeof i._bodyInit>"u"?null:i._bodyInit)})}po.polyfill=!0;te.fetch||(te.fetch=po,te.Headers=Y,te.Request=ct,te.Response=xe);const G={urlRoot:"",csrfNonce:"",userMode:"",userName:"",userEmail:"",start:null,end:null,themeSettings:{},eventSounds:["/themes/core/static/sounds/notification.webm","/themes/core/static/sounds/notification.mp3"]},Tl=window.fetch,Sl=(e,t)=>(t===void 0&&(t={method:"GET",credentials:"same-origin",headers:{}}),e=G.urlRoot+e,t.headers===void 0&&(t.headers={}),t.credentials="same-origin",t.headers.Accept="application/json",t.headers["Content-Type"]="application/json",t.headers["CSRF-Token"]=G.csrfNonce,Tl(e,t));var De=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},_o={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(De,function(){var n=1e3,r=6e4,i=36e5,o="millisecond",a="second",u="minute",f="hour",d="day",g="week",s="month",c="quarter",l="year",h="date",m="Invalid Date",p=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,_=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,v={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_")},b=function($,S,w){var N=String($);return!N||N.length>=S?$:""+Array(S+1-N.length).join(w)+$},T={s:b,z:function($){var S=-$.utcOffset(),w=Math.abs(S),N=Math.floor(w/60),A=w%60;return(S<=0?"+":"-")+b(N,2,"0")+":"+b(A,2,"0")},m:function $(S,w){if(S.date()<w.date())return-$(w,S);var N=12*(w.year()-S.year())+(w.month()-S.month()),A=S.clone().add(N,s),P=w-A<0,M=S.clone().add(N+(P?-1:1),s);return+(-(N+(w-A)/(P?A-M:M-A))||0)},a:function($){return $<0?Math.ceil($)||0:Math.floor($)},p:function($){return{M:s,y:l,w:g,d,D:h,h:f,m:u,s:a,ms:o,Q:c}[$]||String($||"").toLowerCase().replace(/s$/,"")},u:function($){return $===void 0}},C="en",D={};D[C]=v;var R=function($){return $ instanceof F},B=function $(S,w,N){var A;if(!S)return C;if(typeof S=="string"){var P=S.toLowerCase();D[P]&&(A=P),w&&(D[P]=w,A=P);var M=S.split("-");if(!A&&M.length>1)return $(M[0])}else{var H=S.name;D[H]=S,A=H}return!N&&A&&(C=A),A||!N&&C},x=function($,S){if(R($))return $.clone();var w=typeof S=="object"?S:{};return w.date=$,w.args=arguments,new F(w)},I=T;I.l=B,I.i=R,I.w=function($,S){return x($,{locale:S.$L,utc:S.$u,x:S.$x,$offset:S.$offset})};var F=function(){function $(w){this.$L=B(w.locale,null,!0),this.parse(w)}var S=$.prototype;return S.parse=function(w){this.$d=function(N){var A=N.date,P=N.utc;if(A===null)return new Date(NaN);if(I.u(A))return new Date;if(A instanceof Date)return new Date(A);if(typeof A=="string"&&!/Z$/i.test(A)){var M=A.match(p);if(M){var H=M[2]-1||0,j=(M[7]||"0").substring(0,3);return P?new Date(Date.UTC(M[1],H,M[3]||1,M[4]||0,M[5]||0,M[6]||0,j)):new Date(M[1],H,M[3]||1,M[4]||0,M[5]||0,M[6]||0,j)}}return new Date(A)}(w),this.$x=w.x||{},this.init()},S.init=function(){var w=this.$d;this.$y=w.getFullYear(),this.$M=w.getMonth(),this.$D=w.getDate(),this.$W=w.getDay(),this.$H=w.getHours(),this.$m=w.getMinutes(),this.$s=w.getSeconds(),this.$ms=w.getMilliseconds()},S.$utils=function(){return I},S.isValid=function(){return this.$d.toString()!==m},S.isSame=function(w,N){var A=x(w);return this.startOf(N)<=A&&A<=this.endOf(N)},S.isAfter=function(w,N){return x(w)<this.startOf(N)},S.isBefore=function(w,N){return this.endOf(N)<x(w)},S.$g=function(w,N,A){return I.u(w)?this[N]:this.set(A,w)},S.unix=function(){return Math.floor(this.valueOf()/1e3)},S.valueOf=function(){return this.$d.getTime()},S.startOf=function(w,N){var A=this,P=!!I.u(N)||N,M=I.p(w),H=function(oe,X){var we=I.w(A.$u?Date.UTC(A.$y,X,oe):new Date(A.$y,X,oe),A);return P?we:we.endOf(d)},j=function(oe,X){return I.w(A.toDate()[oe].apply(A.toDate("s"),(P?[0,0,0,0]:[23,59,59,999]).slice(X)),A)},V=this.$W,U=this.$M,ne=this.$D,z="set"+(this.$u?"UTC":"");switch(M){case l:return P?H(1,0):H(31,11);case s:return P?H(1,U):H(0,U+1);case g:var ve=this.$locale().weekStart||0,Ee=(V<ve?V+7:V)-ve;return H(P?ne-Ee:ne+(6-Ee),U);case d:case h:return j(z+"Hours",0);case f:return j(z+"Minutes",1);case u:return j(z+"Seconds",2);case a:return j(z+"Milliseconds",3);default:return this.clone()}},S.endOf=function(w){return this.startOf(w,!1)},S.$set=function(w,N){var A,P=I.p(w),M="set"+(this.$u?"UTC":""),H=(A={},A[d]=M+"Date",A[h]=M+"Date",A[s]=M+"Month",A[l]=M+"FullYear",A[f]=M+"Hours",A[u]=M+"Minutes",A[a]=M+"Seconds",A[o]=M+"Milliseconds",A)[P],j=P===d?this.$D+(N-this.$W):N;if(P===s||P===l){var V=this.clone().set(h,1);V.$d[H](j),V.init(),this.$d=V.set(h,Math.min(this.$D,V.daysInMonth())).$d}else H&&this.$d[H](j);return this.init(),this},S.set=function(w,N){return this.clone().$set(w,N)},S.get=function(w){return this[I.p(w)]()},S.add=function(w,N){var A,P=this;w=Number(w);var M=I.p(N),H=function(U){var ne=x(P);return I.w(ne.date(ne.date()+Math.round(U*w)),P)};if(M===s)return this.set(s,this.$M+w);if(M===l)return this.set(l,this.$y+w);if(M===d)return H(1);if(M===g)return H(7);var j=(A={},A[u]=r,A[f]=i,A[a]=n,A)[M]||1,V=this.$d.getTime()+w*j;return I.w(V,this)},S.subtract=function(w,N){return this.add(-1*w,N)},S.format=function(w){var N=this,A=this.$locale();if(!this.isValid())return A.invalidDate||m;var P=w||"YYYY-MM-DDTHH:mm:ssZ",M=I.z(this),H=this.$H,j=this.$m,V=this.$M,U=A.weekdays,ne=A.months,z=function(X,we,Je,yt){return X&&(X[we]||X(N,P))||Je[we].substr(0,yt)},ve=function(X){return I.s(H%12||12,X,"0")},Ee=A.meridiem||function(X,we,Je){var yt=X<12?"AM":"PM";return Je?yt.toLowerCase():yt},oe={YY:String(this.$y).slice(-2),YYYY:this.$y,M:V+1,MM:I.s(V+1,2,"0"),MMM:z(A.monthsShort,V,ne,3),MMMM:z(ne,V),D:this.$D,DD:I.s(this.$D,2,"0"),d:String(this.$W),dd:z(A.weekdaysMin,this.$W,U,2),ddd:z(A.weekdaysShort,this.$W,U,3),dddd:U[this.$W],H:String(H),HH:I.s(H,2,"0"),h:ve(1),hh:ve(2),a:Ee(H,j,!0),A:Ee(H,j,!1),m:String(j),mm:I.s(j,2,"0"),s:String(this.$s),ss:I.s(this.$s,2,"0"),SSS:I.s(this.$ms,3,"0"),Z:M};return P.replace(_,function(X,we){return we||oe[X]||M.replace(":","")})},S.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},S.diff=function(w,N,A){var P,M=I.p(N),H=x(w),j=(H.utcOffset()-this.utcOffset())*r,V=this-H,U=I.m(this,H);return U=(P={},P[l]=U/12,P[s]=U,P[c]=U/3,P[g]=(V-j)/6048e5,P[d]=(V-j)/864e5,P[f]=V/i,P[u]=V/r,P[a]=V/n,P)[M]||V,A?U:I.a(U)},S.daysInMonth=function(){return this.endOf(s).$D},S.$locale=function(){return D[this.$L]},S.locale=function(w,N){if(!w)return this.$L;var A=this.clone(),P=B(w,N,!0);return P&&(A.$L=P),A},S.clone=function(){return I.w(this.$d,this)},S.toDate=function(){return new Date(this.valueOf())},S.toJSON=function(){return this.isValid()?this.toISOString():null},S.toISOString=function(){return this.$d.toISOString()},S.toString=function(){return this.$d.toUTCString()},$}(),ee=F.prototype;return x.prototype=ee,[["$ms",o],["$s",a],["$m",u],["$H",f],["$W",d],["$M",s],["$y",l],["$D",h]].forEach(function($){ee[$[1]]=function(S){return this.$g(S,$[0],$[1])}}),x.extend=function($,S){return $.$i||($(S,F,x),$.$i=!0),x},x.locale=B,x.isDayjs=R,x.unix=function($){return x(1e3*$)},x.en=D[C],x.Ls=D,x.p={},x})})(_o);const Ge=_o.exports;var mo={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(De,function(){return function(n,r,i){var o=r.prototype,a=o.format;i.en.ordinal=function(u){var f=["th","st","nd","rd"],d=u%100;return"["+u+(f[(d-20)%10]||f[d]||f[0])+"]"},o.format=function(u){var f=this,d=this.$locale();if(!this.isValid())return a.bind(this)(u);var g=this.$utils(),s=(u||"YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,function(c){switch(c){case"Q":return Math.ceil((f.$M+1)/3);case"Do":return d.ordinal(f.$D);case"gggg":return f.weekYear();case"GGGG":return f.isoWeekYear();case"wo":return d.ordinal(f.week(),"W");case"w":case"ww":return g.s(f.week(),c==="w"?1:2,"0");case"W":case"WW":return g.s(f.isoWeek(),c==="W"?1:2,"0");case"k":case"kk":return g.s(String(f.$H===0?24:f.$H),c==="k"?1:2,"0");case"X":return Math.floor(f.$d.getTime()/1e3);case"x":return f.$d.getTime();case"z":return"["+f.offsetName()+"]";case"zzz":return"["+f.offsetName("long")+"]";default:return c}});return a.bind(this)(s)}}})})(mo);const Vn=mo.exports,Me=document,$n=window,go=Me.documentElement,pt=Me.createElement.bind(Me),vo=pt("div"),_r=pt("table"),Ol=pt("tbody"),hs=pt("tr"),{isArray:Fn,prototype:yo}=Array,{concat:xl,filter:li,indexOf:bo,map:Eo,push:Cl,slice:wo,some:ui,splice:$l}=yo,Nl=/^#(?:[\w-]|\\.|[^\x00-\xa0])*$/,Dl=/^\.(?:[\w-]|\\.|[^\x00-\xa0])*$/,Ll=/<.+>/,Il=/^\w+$/;function fi(e,t){const n=Ml(t);return!e||!n&&!$t(t)&&!J(t)?[]:!n&&Dl.test(e)?t.getElementsByClassName(e.slice(1).replace(/\\/g,"")):!n&&Il.test(e)?t.getElementsByTagName(e):t.querySelectorAll(e)}class jn{constructor(t,n){if(!t)return;if(Rr(t))return t;let r=t;if(le(t)){const i=(Rr(n)?n[0]:n)||Me;if(r=Nl.test(t)&&"getElementById"in i?i.getElementById(t.slice(1).replace(/\\/g,"")):Ll.test(t)?So(t):fi(t,i),!r)return}else if(_t(t))return this.ready(t);(r.nodeType||r===$n)&&(r=[r]),this.length=r.length;for(let i=0,o=this.length;i<o;i++)this[i]=r[i]}init(t,n){return new jn(t,n)}}const E=jn.prototype,k=E.init;k.fn=k.prototype=E;E.length=0;E.splice=$l;typeof Symbol=="function"&&(E[Symbol.iterator]=yo[Symbol.iterator]);function Rr(e){return e instanceof jn}function Ct(e){return!!e&&e===e.window}function $t(e){return!!e&&e.nodeType===9}function Ml(e){return!!e&&e.nodeType===11}function J(e){return!!e&&e.nodeType===1}function kl(e){return!!e&&e.nodeType===3}function Pl(e){return typeof e=="boolean"}function _t(e){return typeof e=="function"}function le(e){return typeof e=="string"}function ue(e){return e===void 0}function en(e){return e===null}function Ao(e){return!isNaN(parseFloat(e))&&isFinite(e)}function di(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return t===null||t===Object.prototype}k.isWindow=Ct;k.isFunction=_t;k.isArray=Fn;k.isNumeric=Ao;k.isPlainObject=di;function Z(e,t,n){if(n){let r=e.length;for(;r--;)if(t.call(e[r],r,e[r])===!1)return e}else if(di(e)){const r=Object.keys(e);for(let i=0,o=r.length;i<o;i++){const a=r[i];if(t.call(e[a],a,e[a])===!1)return e}}else for(let r=0,i=e.length;r<i;r++)if(t.call(e[r],r,e[r])===!1)return e;return e}k.each=Z;E.each=function(e){return Z(this,e)};E.empty=function(){return this.each((e,t)=>{for(;t.firstChild;)t.removeChild(t.firstChild)})};function Nn(...e){const t=Pl(e[0])?e.shift():!1,n=e.shift(),r=e.length;if(!n)return{};if(!r)return Nn(t,k,n);for(let i=0;i<r;i++){const o=e[i];for(const a in o)t&&(Fn(o[a])||di(o[a]))?((!n[a]||n[a].constructor!==o[a].constructor)&&(n[a]=new o[a].constructor),Nn(t,n[a],o[a])):n[a]=o[a]}return n}k.extend=Nn;E.extend=function(e){return Nn(E,e)};const Rl=/\S+/g;function Wn(e){return le(e)?e.match(Rl)||[]:[]}E.toggleClass=function(e,t){const n=Wn(e),r=!ue(t);return this.each((i,o)=>{!J(o)||Z(n,(a,u)=>{r?t?o.classList.add(u):o.classList.remove(u):o.classList.toggle(u)})})};E.addClass=function(e){return this.toggleClass(e,!0)};E.removeAttr=function(e){const t=Wn(e);return this.each((n,r)=>{!J(r)||Z(t,(i,o)=>{r.removeAttribute(o)})})};function Hl(e,t){if(!!e){if(le(e)){if(arguments.length<2){if(!this[0]||!J(this[0]))return;const n=this[0].getAttribute(e);return en(n)?void 0:n}return ue(t)?this:en(t)?this.removeAttr(e):this.each((n,r)=>{!J(r)||r.setAttribute(e,t)})}for(const n in e)this.attr(n,e[n]);return this}}E.attr=Hl;E.removeClass=function(e){return arguments.length?this.toggleClass(e,!1):this.attr("class","")};E.hasClass=function(e){return!!e&&ui.call(this,t=>J(t)&&t.classList.contains(e))};E.get=function(e){return ue(e)?wo.call(this):(e=Number(e),this[e<0?e+this.length:e])};E.eq=function(e){return k(this.get(e))};E.first=function(){return this.eq(0)};E.last=function(){return this.eq(-1)};function Bl(e){return ue(e)?this.get().map(t=>J(t)||kl(t)?t.textContent:"").join(""):this.each((t,n)=>{!J(n)||(n.textContent=e)})}E.text=Bl;function ke(e,t,n){if(!J(e))return;const r=$n.getComputedStyle(e,null);return n?r.getPropertyValue(t)||void 0:r[t]||e.style[t]}function Te(e,t){return parseInt(ke(e,t),10)||0}function ps(e,t){return Te(e,`border${t?"Left":"Top"}Width`)+Te(e,`padding${t?"Left":"Top"}`)+Te(e,`padding${t?"Right":"Bottom"}`)+Te(e,`border${t?"Right":"Bottom"}Width`)}const mr={};function Vl(e){if(mr[e])return mr[e];const t=pt(e);Me.body.insertBefore(t,null);const n=ke(t,"display");return Me.body.removeChild(t),mr[e]=n!=="none"?n:"block"}function _s(e){return ke(e,"display")==="none"}function To(e,t){const n=e&&(e.matches||e.webkitMatchesSelector||e.msMatchesSelector);return!!n&&!!t&&n.call(e,t)}function Kn(e){return le(e)?(t,n)=>To(n,e):_t(e)?e:Rr(e)?(t,n)=>e.is(n):e?(t,n)=>n===e:()=>!1}E.filter=function(e){const t=Kn(e);return k(li.call(this,(n,r)=>t.call(n,r,n)))};function Ye(e,t){return t?e.filter(t):e}E.detach=function(e){return Ye(this,e).each((t,n)=>{n.parentNode&&n.parentNode.removeChild(n)}),this};const Fl=/^\s*<(\w+)[^>]*>/,jl=/^<(\w+)\s*\/?>(?:<\/\1>)?$/,ms={"*":vo,tr:Ol,td:hs,th:hs,thead:_r,tbody:_r,tfoot:_r};function So(e){if(!le(e))return[];if(jl.test(e))return[pt(RegExp.$1)];const t=Fl.test(e)&&RegExp.$1,n=ms[t]||ms["*"];return n.innerHTML=e,k(n.childNodes).detach().get()}k.parseHTML=So;E.has=function(e){const t=le(e)?(n,r)=>fi(e,r).length:(n,r)=>r.contains(e);return this.filter(t)};E.not=function(e){const t=Kn(e);return this.filter((n,r)=>(!le(e)||J(r))&&!t.call(r,n,r))};function Re(e,t,n,r){const i=[],o=_t(t),a=r&&Kn(r);for(let u=0,f=e.length;u<f;u++)if(o){const d=t(e[u]);d.length&&Cl.apply(i,d)}else{let d=e[u][t];for(;d!=null&&!(r&&a(-1,d));)i.push(d),d=n?d[t]:null}return i}function Oo(e){return e.multiple&&e.options?Re(li.call(e.options,t=>t.selected&&!t.disabled&&!t.parentNode.disabled),"value"):e.value||""}function Wl(e){return arguments.length?this.each((t,n)=>{const r=n.multiple&&n.options;if(r||Mo.test(n.type)){const i=Fn(e)?Eo.call(e,String):en(e)?[]:[String(e)];r?Z(n.options,(o,a)=>{a.selected=i.indexOf(a.value)>=0},!0):n.checked=i.indexOf(n.value)>=0}else n.value=ue(e)||en(e)?"":e}):this[0]&&Oo(this[0])}E.val=Wl;E.is=function(e){const t=Kn(e);return ui.call(this,(n,r)=>t.call(n,r,n))};k.guid=1;function $e(e){return e.length>1?li.call(e,(t,n,r)=>bo.call(r,t)===n):e}k.unique=$e;E.add=function(e,t){return k($e(this.get().concat(k(e,t).get())))};E.children=function(e){return Ye(k($e(Re(this,t=>t.children))),e)};E.parent=function(e){return Ye(k($e(Re(this,"parentNode"))),e)};E.index=function(e){const t=e?k(e)[0]:this[0],n=e?this:k(t).parent().children();return bo.call(n,t)};E.closest=function(e){const t=this.filter(e);if(t.length)return t;const n=this.parent();return n.length?n.closest(e):t};E.siblings=function(e){return Ye(k($e(Re(this,t=>k(t).parent().children().not(t)))),e)};E.find=function(e){return k($e(Re(this,t=>fi(e,t))))};const Kl=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Ul=/^$|^module$|\/(java|ecma)script/i,Gl=["type","src","nonce","noModule"];function Yl(e,t){const n=k(e);n.filter("script").add(n.find("script")).each((r,i)=>{if(Ul.test(i.type)&&go.contains(i)){const o=pt("script");o.text=i.textContent.replace(Kl,""),Z(Gl,(a,u)=>{i[u]&&(o[u]=i[u])}),t.head.insertBefore(o,null),t.head.removeChild(o)}})}function ql(e,t,n,r,i){r?e.insertBefore(t,n?e.firstChild:null):e.nodeName==="HTML"?e.parentNode.replaceChild(t,e):e.parentNode.insertBefore(t,n?e:e.nextSibling),i&&Yl(t,e.ownerDocument)}function qe(e,t,n,r,i,o,a,u){return Z(e,(f,d)=>{Z(k(d),(g,s)=>{Z(k(t),(c,l)=>{const h=n?s:l,m=n?l:s,p=n?g:c;ql(h,p?m.cloneNode(!0):m,r,i,!p)},u)},a)},o),t}E.after=function(){return qe(arguments,this,!1,!1,!1,!0,!0)};E.append=function(){return qe(arguments,this,!1,!1,!0)};function zl(e){if(!arguments.length)return this[0]&&this[0].innerHTML;if(ue(e))return this;const t=/<script[\s>]/.test(e);return this.each((n,r)=>{!J(r)||(t?k(r).empty().append(e):r.innerHTML=e)})}E.html=zl;E.appendTo=function(e){return qe(arguments,this,!0,!1,!0)};E.wrapInner=function(e){return this.each((t,n)=>{const r=k(n),i=r.contents();i.length?i.wrapAll(e):r.append(e)})};E.before=function(){return qe(arguments,this,!1,!0)};E.wrapAll=function(e){let t=k(e),n=t[0];for(;n.children.length;)n=n.firstElementChild;return this.first().before(t),this.appendTo(n)};E.wrap=function(e){return this.each((t,n)=>{const r=k(e)[0];k(n).wrapAll(t?r.cloneNode(!0):r)})};E.insertAfter=function(e){return qe(arguments,this,!0,!1,!1,!1,!1,!0)};E.insertBefore=function(e){return qe(arguments,this,!0,!0)};E.prepend=function(){return qe(arguments,this,!1,!0,!0,!0,!0)};E.prependTo=function(e){return qe(arguments,this,!0,!0,!0,!1,!1,!0)};E.contents=function(){return k($e(Re(this,e=>e.tagName==="IFRAME"?[e.contentDocument]:e.tagName==="TEMPLATE"?e.content.childNodes:e.childNodes)))};E.next=function(e,t,n){return Ye(k($e(Re(this,"nextElementSibling",t,n))),e)};E.nextAll=function(e){return this.next(e,!0)};E.nextUntil=function(e,t){return this.next(t,!0,e)};E.parents=function(e,t){return Ye(k($e(Re(this,"parentElement",!0,t))),e)};E.parentsUntil=function(e,t){return this.parents(t,e)};E.prev=function(e,t,n){return Ye(k($e(Re(this,"previousElementSibling",t,n))),e)};E.prevAll=function(e){return this.prev(e,!0)};E.prevUntil=function(e,t){return this.prev(t,!0,e)};E.map=function(e){return k(xl.apply([],Eo.call(this,(t,n)=>e.call(t,n,t))))};E.clone=function(){return this.map((e,t)=>t.cloneNode(!0))};E.offsetParent=function(){return this.map((e,t)=>{let n=t.offsetParent;for(;n&&ke(n,"position")==="static";)n=n.offsetParent;return n||go})};E.slice=function(e,t){return k(wo.call(this,e,t))};const Xl=/-([a-z])/g;function hi(e){return e.replace(Xl,(t,n)=>n.toUpperCase())}E.ready=function(e){const t=()=>setTimeout(e,0,k);return Me.readyState!=="loading"?t():Me.addEventListener("DOMContentLoaded",t),this};E.unwrap=function(){return this.parent().each((e,t)=>{if(t.tagName==="BODY")return;const n=k(t);n.replaceWith(n.children())}),this};E.offset=function(){const e=this[0];if(!e)return;const t=e.getBoundingClientRect();return{top:t.top+$n.pageYOffset,left:t.left+$n.pageXOffset}};E.position=function(){const e=this[0];if(!e)return;const t=ke(e,"position")==="fixed",n=t?e.getBoundingClientRect():this.offset();if(!t){const r=e.ownerDocument;let i=e.offsetParent||r.documentElement;for(;(i===r.body||i===r.documentElement)&&ke(i,"position")==="static";)i=i.parentNode;if(i!==e&&J(i)){const o=k(i).offset();n.top-=o.top+Te(i,"borderTopWidth"),n.left-=o.left+Te(i,"borderLeftWidth")}}return{top:n.top-Te(e,"marginTop"),left:n.left-Te(e,"marginLeft")}};const xo={class:"className",contenteditable:"contentEditable",for:"htmlFor",readonly:"readOnly",maxlength:"maxLength",tabindex:"tabIndex",colspan:"colSpan",rowspan:"rowSpan",usemap:"useMap"};E.prop=function(e,t){if(!!e){if(le(e))return e=xo[e]||e,arguments.length<2?this[0]&&this[0][e]:this.each((n,r)=>{r[e]=t});for(const n in e)this.prop(n,e[n]);return this}};E.removeProp=function(e){return this.each((t,n)=>{delete n[xo[e]||e]})};const Ql=/^--/;function pi(e){return Ql.test(e)}const gr={},{style:Jl}=vo,Zl=["webkit","moz","ms"];function eu(e,t=pi(e)){if(t)return e;if(!gr[e]){const n=hi(e),r=`${n[0].toUpperCase()}${n.slice(1)}`,i=`${n} ${Zl.join(`${r} `)}${r}`.split(" ");Z(i,(o,a)=>{if(a in Jl)return gr[e]=a,!1})}return gr[e]}const tu={animationIterationCount:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0};function Co(e,t,n=pi(e)){return!n&&!tu[e]&&Ao(t)?`${t}px`:t}function nu(e,t){if(le(e)){const n=pi(e);return e=eu(e,n),arguments.length<2?this[0]&&ke(this[0],e,n):e?(t=Co(e,t,n),this.each((r,i)=>{!J(i)||(n?i.style.setProperty(e,t):i.style[e]=t)})):this}for(const n in e)this.css(n,e[n]);return this}E.css=nu;function $o(e,t){try{return e(t)}catch{return t}}const ru=/^\s+|\s+$/;function gs(e,t){const n=e.dataset[t]||e.dataset[hi(t)];return ru.test(n)?n:$o(JSON.parse,n)}function iu(e,t,n){n=$o(JSON.stringify,n),e.dataset[hi(t)]=n}function su(e,t){if(!e){if(!this[0])return;const n={};for(const r in this[0].dataset)n[r]=gs(this[0],r);return n}if(le(e))return arguments.length<2?this[0]&&gs(this[0],e):ue(t)?this:this.each((n,r)=>{iu(r,e,t)});for(const n in e)this.data(n,e[n]);return this}E.data=su;function No(e,t){const n=e.documentElement;return Math.max(e.body[`scroll${t}`],n[`scroll${t}`],e.body[`offset${t}`],n[`offset${t}`],n[`client${t}`])}Z([!0,!1],(e,t)=>{Z(["Width","Height"],(n,r)=>{const i=`${t?"outer":"inner"}${r}`;E[i]=function(o){if(!!this[0])return Ct(this[0])?t?this[0][`inner${r}`]:this[0].document.documentElement[`client${r}`]:$t(this[0])?No(this[0],r):this[0][`${t?"offset":"client"}${r}`]+(o&&t?Te(this[0],`margin${n?"Top":"Left"}`)+Te(this[0],`margin${n?"Bottom":"Right"}`):0)}})});Z(["Width","Height"],(e,t)=>{const n=t.toLowerCase();E[n]=function(r){if(!this[0])return ue(r)?void 0:this;if(!arguments.length)return Ct(this[0])?this[0].document.documentElement[`client${t}`]:$t(this[0])?No(this[0],t):this[0].getBoundingClientRect()[n]-ps(this[0],!e);const i=parseInt(r,10);return this.each((o,a)=>{if(!J(a))return;const u=ke(a,"boxSizing");a.style[n]=Co(n,i+(u==="border-box"?ps(a,!e):0))})}});const vs="___cd";E.toggle=function(e){return this.each((t,n)=>{if(!J(n))return;(ue(e)?_s(n):e)?(n.style.display=n[vs]||"",_s(n)&&(n.style.display=Vl(n.tagName))):(n[vs]=ke(n,"display"),n.style.display="none")})};E.hide=function(){return this.toggle(!1)};E.show=function(){return this.toggle(!0)};const ys="___ce",_i=".",mi={focus:"focusin",blur:"focusout"},Do={mouseenter:"mouseover",mouseleave:"mouseout"},ou=/^(mouse|pointer|contextmenu|drag|drop|click|dblclick)/i;function gi(e){return Do[e]||mi[e]||e}function vi(e){const t=e.split(_i);return[t[0],t.slice(1).sort()]}E.trigger=function(e,t){if(le(e)){const[r,i]=vi(e),o=gi(r);if(!o)return this;const a=ou.test(o)?"MouseEvents":"HTMLEvents";e=Me.createEvent(a),e.initEvent(o,!0,!0),e.namespace=i.join(_i),e.___ot=r}e.___td=t;const n=e.___ot in mi;return this.each((r,i)=>{n&&_t(i[e.___ot])&&(i[`___i${e.type}`]=!0,i[e.___ot](),i[`___i${e.type}`]=!1),i.dispatchEvent(e)})};function Lo(e){return e[ys]=e[ys]||{}}function au(e,t,n,r,i){const o=Lo(e);o[t]=o[t]||[],o[t].push([n,r,i]),e.addEventListener(t,i)}function Io(e,t){return!t||!ui.call(t,n=>e.indexOf(n)<0)}function Dn(e,t,n,r,i){const o=Lo(e);if(t)o[t]&&(o[t]=o[t].filter(([a,u,f])=>{if(i&&f.guid!==i.guid||!Io(a,n)||r&&r!==u)return!0;e.removeEventListener(t,f)}));else for(t in o)Dn(e,t,n,r,i)}E.off=function(e,t,n){if(ue(e))this.each((r,i)=>{!J(i)&&!$t(i)&&!Ct(i)||Dn(i)});else if(le(e))_t(t)&&(n=t,t=""),Z(Wn(e),(r,i)=>{const[o,a]=vi(i),u=gi(o);this.each((f,d)=>{!J(d)&&!$t(d)&&!Ct(d)||Dn(d,u,a,t,n)})});else for(const r in e)this.off(r,e[r]);return this};E.remove=function(e){return Ye(this,e).detach().off(),this};E.replaceWith=function(e){return this.before(e).remove()};E.replaceAll=function(e){return k(e).replaceWith(this),this};function cu(e,t,n,r,i){if(!le(e)){for(const o in e)this.on(o,t,n,e[o],i);return this}return le(t)||(ue(t)||en(t)?t="":ue(n)?(n=t,t=""):(r=n,n=t,t="")),_t(r)||(r=n,n=void 0),r?(Z(Wn(e),(o,a)=>{const[u,f]=vi(a),d=gi(u),g=u in Do,s=u in mi;!d||this.each((c,l)=>{if(!J(l)&&!$t(l)&&!Ct(l))return;const h=function(m){if(m.target[`___i${m.type}`])return m.stopImmediatePropagation();if(m.namespace&&!Io(f,m.namespace.split(_i))||!t&&(s&&(m.target!==l||m.___ot===d)||g&&m.relatedTarget&&l.contains(m.relatedTarget)))return;let p=l;if(t){let v=m.target;for(;!To(v,t);)if(v===l||(v=v.parentNode,!v))return;p=v}Object.defineProperty(m,"currentTarget",{configurable:!0,get(){return p}}),Object.defineProperty(m,"delegateTarget",{configurable:!0,get(){return l}}),Object.defineProperty(m,"data",{configurable:!0,get(){return n}});const _=r.call(p,m,m.___td);i&&Dn(l,d,f,t,h),_===!1&&(m.preventDefault(),m.stopPropagation())};h.guid=r.guid=r.guid||k.guid++,au(l,d,f,t,h)})}),this):this}E.on=cu;function lu(e,t,n,r){return this.on(e,t,n,r,!0)}E.one=lu;const uu=/\r?\n/g;function fu(e,t){return`&${encodeURIComponent(e)}=${encodeURIComponent(t.replace(uu,`\r
`))}`}const du=/file|reset|submit|button|image/i,Mo=/radio|checkbox/i;E.serialize=function(){let e="";return this.each((t,n)=>{Z(n.elements||[n],(r,i)=>{if(i.disabled||!i.name||i.tagName==="FIELDSET"||du.test(i.type)||Mo.test(i.type)&&!i.checked)return;const o=Oo(i);if(!ue(o)){const a=Fn(o)?o:[o];Z(a,(u,f)=>{e+=fu(i.name,f)})}})}),e.slice(1)};Ge.extend(Vn);function hu(){let e=document.querySelectorAll("[data-time]");for(const t of e){let n=t.dataset.time,r=t.dataset.timeFormat;t.innerText=Ge(n).format(r)}}function pu(e,t){k(t).select(),document.execCommand("copy"),k(e.target).tooltip({title:"Copied!",trigger:"manual"}),k(e.target).tooltip("show"),setTimeout(function(){k(e.target).tooltip("hide")},1500)}function _u(e){let t=0,n,r,i;if(e.length===0)return t;for(n=0,i=e.length;n<i;n++)r=e.charCodeAt(n),t=(t<<5)-t+r,t|=0;return t}function mu(e){let t=0;for(let o=0;o<e.length;o++)t=e.charCodeAt(o)+((t<<5)-t),t=t&t;let n=(t%360+360)%360,r=(t%25+25)%25+75,i=(t%20+20)%20+40;return`hsl(${n}, ${r}%, ${i}%)`}async function ko(e={}){let t="/api/v1/challenges";if(Object.keys(e).length!==0){let o=new URLSearchParams(e).toString();t=`${t}?${o}`}let i=(await(await O.fetch(t,{method:"GET"})).json()).data;return O._functions.challenges.sortChallenges&&(i=O._functions.challenges.sortChallenges(i)),i}async function Po(e){return(await(await O.fetch(`/api/v1/challenges/${e}`,{method:"GET"})).json()).data}async function gu(){let e=await ko();O._functions.challenges.displayChallenges&&O._functions.challenges.displayChallenges(e)}const Ro=e=>new Promise((t,n)=>{const r=document.querySelector(`script[src='${e}']`);r&&r.remove();const i=document.createElement("script");document.body.appendChild(i),i.onload=t,i.onerror=n,i.async=!0,i.src=e});async function vu(e,t){O._internal.challenge={};let n=O.config,r=await Po(e);O._functions.challenge.displayChallenge&&O._functions.challenge.displayChallenge(r),Ro(n.urlRoot+r.type_data.scripts.view).then(()=>{const o=O._internal.challenge;o.data=r,o.preRender(),O._functions.challenge.renderChallenge?O._functions.challenge.renderChallenge(o):t&&t(o),o.postRender()})}async function yu(e,t,n=!1){if(O._functions.challenge.submitChallenge){O._functions.challenge.submitChallenge(e,t);return}let r="/api/v1/challenges/attempt";(n===!0||O.config.preview===!0)&&(r+="?preview=true");const o=await(await O.fetch(r,{method:"POST",body:JSON.stringify({challenge_id:e,submission:t})})).json();return O._functions.challenge.displaySubmissionResponse&&O._functions.challenge.displaySubmissionResponse(o),o}async function Ho(e){return await(await O.fetch(`/api/v1/hints/${e}`,{method:"GET"})).json()}async function yi(e,t="hints"){return await(await O.fetch("/api/v1/unlocks",{method:"POST",body:JSON.stringify({target:e,type:t})})).json()}async function Bo(e){let n=(await Ho(e)).data;if(n.content){O._functions.challenge.displayHint(n);return}if(await Vo(n)){let i=await yi(e);i.success?await Bo(e):O._functions.challenge.displayUnlockError(i)}}async function bu(e){return O._functions.challenge.displayUnlock(e)}async function Vo(e){return O._functions.challenge.displayHintUnlock(e)}async function Fo(e){return O._functions.challenge.displaySolutionUnlock(e)}async function jo(e){return(await(await O.fetch(`/api/v1/challenges/${e}/solves`,{method:"GET"})).json()).data}async function Eu(e){let t=await jo(e);O._functions.challenge.displaySolves&&O._functions.challenge.displaySolves(t)}async function Wo(e){return(await(await O.fetch(`/api/v1/solutions/${e}`,{method:"GET"})).json()).data}async function Ko(e){let t=await Wo(e);if(t.content&&O._functions.challenge.displaySolution){O._functions.challenge.displaySolution(t);return}if(await Fo(t)){let r=await yi(t.id,"solutions");r.success?await Ko(e):O._functions.challenge.displayUnlockError(r)}}async function wu(e=null){let t="/api/v1/scoreboard";return e&&(t=`${t}?bracket_id=${e}`),(await(await O.fetch(t,{method:"GET"})).json()).data}async function Au(e,t=null){let n=`/api/v1/scoreboard/top/${e}`;return t&&(n=`${n}?bracket_id=${t}`),(await(await O.fetch(n,{method:"GET"})).json()).data}async function Tu(e){return(await(await O.fetch(`/api/v1/brackets?type=${e}`,{method:"GET"})).json()).data}async function Su(e){return await(await O.fetch("/api/v1/users/me",{method:"PATCH",body:JSON.stringify(e)})).json()}async function Ou(e){return await(await O.fetch("/api/v1/tokens",{method:"POST",body:JSON.stringify(e)})).json()}async function xu(e){return await(await O.fetch(`/api/v1/tokens/${e}`,{method:"DELETE"})).json()}async function Cu(e="me",t){return await(await O.fetch(`/api/v1/users/${e}/submissions?challenge_id=${t}`,{method:"GET"})).json()}async function $u(e){return await(await O.fetch(`/api/v1/users/${e}/solves`,{method:"GET"})).json()}async function Nu(e){return await(await O.fetch(`/api/v1/users/${e}/fails`,{method:"GET"})).json()}async function Du(e){return await(await O.fetch(`/api/v1/users/${e}/awards`,{method:"GET"})).json()}async function Lu(){return await(await O.fetch("/api/v1/teams/me/members",{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}})).json()}async function Iu(){return await(await O.fetch("/api/v1/teams/me",{method:"DELETE"})).json()}async function Mu(e){return await(await O.fetch("/api/v1/teams/me",{method:"PATCH",body:JSON.stringify(e)})).json()}async function ku(e){return await(await O.fetch(`/api/v1/teams/${e}/solves`,{method:"GET"})).json()}async function Pu(e){return await(await O.fetch(`/api/v1/teams/${e}/fails`,{method:"GET"})).json()}async function Ru(e){return await(await O.fetch(`/api/v1/teams/${e}/awards`,{method:"GET"})).json()}function Hu(e){const t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild}function Uo(e){const t=document.createElement("div");return t.innerText=e,t.innerHTML}class Bu{constructor(){this.id=Math.random(),this.isMaster=!1,this.others={},window.addEventListener("storage",this),window.addEventListener("unload",this),this.broadcast("hello"),setTimeout(this.check.bind(this),500),this._checkInterval=setInterval(this.check.bind(this),9e3),this._pingInterval=setInterval(this.sendPing.bind(this),17e3)}destroy(){clearInterval(this._pingInterval),clearInterval(this._checkInterval),window.removeEventListener("storage",this),window.removeEventListener("unload",this),this.broadcast("bye")}handleEvent(t){if(t.type==="unload"){this.destroy();return}if(t.type==="broadcast")try{const n=JSON.parse(t.newValue);n.id!==this.id&&this[n.type](n)}catch(n){console.error(n)}}sendPing(){this.broadcast("ping")}hello(t){if(this.ping(t),t.id<this.id){this.check();return}this.sendPing()}ping(t){this.others[t.id]=Date.now()}bye(t){delete this.others[t.id],this.check()}check(){const t=Date.now();let n=!0;for(const r in this.others)this.others[r]+23e3<t?delete this.others[r]:r<this.id&&(n=!1);this.isMaster!==n&&(this.isMaster=n,this.masterDidChange())}masterDidChange(){}broadcast(t,n){const r={id:this.id,type:t,...n};try{localStorage.setItem("broadcast",JSON.stringify(r))}catch(i){console.error(i)}}}var Go={};/*!
 *  howler.js v2.2.3
 *  howlerjs.com
 *
 *  (c) 2013-2020, James Simpson of GoldFire Studios
 *  goldfirestudios.com
 *
 *  MIT License
 */(function(e){(function(){var t=function(){this.init()};t.prototype={init:function(){var s=this||n;return s._counter=1e3,s._html5AudioPool=[],s.html5PoolSize=10,s._codecs={},s._howls=[],s._muted=!1,s._volume=1,s._canPlayEvent="canplaythrough",s._navigator=typeof window<"u"&&window.navigator?window.navigator:null,s.masterGain=null,s.noAudio=!1,s.usingWebAudio=!0,s.autoSuspend=!0,s.ctx=null,s.autoUnlock=!0,s._setup(),s},volume:function(s){var c=this||n;if(s=parseFloat(s),c.ctx||g(),typeof s<"u"&&s>=0&&s<=1){if(c._volume=s,c._muted)return c;c.usingWebAudio&&c.masterGain.gain.setValueAtTime(s,n.ctx.currentTime);for(var l=0;l<c._howls.length;l++)if(!c._howls[l]._webAudio)for(var h=c._howls[l]._getSoundIds(),m=0;m<h.length;m++){var p=c._howls[l]._soundById(h[m]);p&&p._node&&(p._node.volume=p._volume*s)}return c}return c._volume},mute:function(s){var c=this||n;c.ctx||g(),c._muted=s,c.usingWebAudio&&c.masterGain.gain.setValueAtTime(s?0:c._volume,n.ctx.currentTime);for(var l=0;l<c._howls.length;l++)if(!c._howls[l]._webAudio)for(var h=c._howls[l]._getSoundIds(),m=0;m<h.length;m++){var p=c._howls[l]._soundById(h[m]);p&&p._node&&(p._node.muted=s?!0:p._muted)}return c},stop:function(){for(var s=this||n,c=0;c<s._howls.length;c++)s._howls[c].stop();return s},unload:function(){for(var s=this||n,c=s._howls.length-1;c>=0;c--)s._howls[c].unload();return s.usingWebAudio&&s.ctx&&typeof s.ctx.close<"u"&&(s.ctx.close(),s.ctx=null,g()),s},codecs:function(s){return(this||n)._codecs[s.replace(/^x-/,"")]},_setup:function(){var s=this||n;if(s.state=s.ctx&&s.ctx.state||"suspended",s._autoSuspend(),!s.usingWebAudio)if(typeof Audio<"u")try{var c=new Audio;typeof c.oncanplaythrough>"u"&&(s._canPlayEvent="canplay")}catch{s.noAudio=!0}else s.noAudio=!0;try{var c=new Audio;c.muted&&(s.noAudio=!0)}catch{}return s.noAudio||s._setupCodecs(),s},_setupCodecs:function(){var s=this||n,c=null;try{c=typeof Audio<"u"?new Audio:null}catch{return s}if(!c||typeof c.canPlayType!="function")return s;var l=c.canPlayType("audio/mpeg;").replace(/^no$/,""),h=s._navigator?s._navigator.userAgent:"",m=h.match(/OPR\/([0-6].)/g),p=m&&parseInt(m[0].split("/")[1],10)<33,_=h.indexOf("Safari")!==-1&&h.indexOf("Chrome")===-1,v=h.match(/Version\/(.*?) /),b=_&&v&&parseInt(v[1],10)<15;return s._codecs={mp3:!!(!p&&(l||c.canPlayType("audio/mp3;").replace(/^no$/,""))),mpeg:!!l,opus:!!c.canPlayType('audio/ogg; codecs="opus"').replace(/^no$/,""),ogg:!!c.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),oga:!!c.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),wav:!!(c.canPlayType('audio/wav; codecs="1"')||c.canPlayType("audio/wav")).replace(/^no$/,""),aac:!!c.canPlayType("audio/aac;").replace(/^no$/,""),caf:!!c.canPlayType("audio/x-caf;").replace(/^no$/,""),m4a:!!(c.canPlayType("audio/x-m4a;")||c.canPlayType("audio/m4a;")||c.canPlayType("audio/aac;")).replace(/^no$/,""),m4b:!!(c.canPlayType("audio/x-m4b;")||c.canPlayType("audio/m4b;")||c.canPlayType("audio/aac;")).replace(/^no$/,""),mp4:!!(c.canPlayType("audio/x-mp4;")||c.canPlayType("audio/mp4;")||c.canPlayType("audio/aac;")).replace(/^no$/,""),weba:!!(!b&&c.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/,"")),webm:!!(!b&&c.canPlayType('audio/webm; codecs="vorbis"').replace(/^no$/,"")),dolby:!!c.canPlayType('audio/mp4; codecs="ec-3"').replace(/^no$/,""),flac:!!(c.canPlayType("audio/x-flac;")||c.canPlayType("audio/flac;")).replace(/^no$/,"")},s},_unlockAudio:function(){var s=this||n;if(!(s._audioUnlocked||!s.ctx)){s._audioUnlocked=!1,s.autoUnlock=!1,!s._mobileUnloaded&&s.ctx.sampleRate!==44100&&(s._mobileUnloaded=!0,s.unload()),s._scratchBuffer=s.ctx.createBuffer(1,1,22050);var c=function(l){for(;s._html5AudioPool.length<s.html5PoolSize;)try{var h=new Audio;h._unlocked=!0,s._releaseHtml5Audio(h)}catch{s.noAudio=!0;break}for(var m=0;m<s._howls.length;m++)if(!s._howls[m]._webAudio)for(var p=s._howls[m]._getSoundIds(),_=0;_<p.length;_++){var v=s._howls[m]._soundById(p[_]);v&&v._node&&!v._node._unlocked&&(v._node._unlocked=!0,v._node.load())}s._autoResume();var b=s.ctx.createBufferSource();b.buffer=s._scratchBuffer,b.connect(s.ctx.destination),typeof b.start>"u"?b.noteOn(0):b.start(0),typeof s.ctx.resume=="function"&&s.ctx.resume(),b.onended=function(){b.disconnect(0),s._audioUnlocked=!0,document.removeEventListener("touchstart",c,!0),document.removeEventListener("touchend",c,!0),document.removeEventListener("click",c,!0),document.removeEventListener("keydown",c,!0);for(var T=0;T<s._howls.length;T++)s._howls[T]._emit("unlock")}};return document.addEventListener("touchstart",c,!0),document.addEventListener("touchend",c,!0),document.addEventListener("click",c,!0),document.addEventListener("keydown",c,!0),s}},_obtainHtml5Audio:function(){var s=this||n;if(s._html5AudioPool.length)return s._html5AudioPool.pop();var c=new Audio().play();return c&&typeof Promise<"u"&&(c instanceof Promise||typeof c.then=="function")&&c.catch(function(){console.warn("HTML5 Audio pool exhausted, returning potentially locked audio object.")}),new Audio},_releaseHtml5Audio:function(s){var c=this||n;return s._unlocked&&c._html5AudioPool.push(s),c},_autoSuspend:function(){var s=this;if(!(!s.autoSuspend||!s.ctx||typeof s.ctx.suspend>"u"||!n.usingWebAudio)){for(var c=0;c<s._howls.length;c++)if(s._howls[c]._webAudio){for(var l=0;l<s._howls[c]._sounds.length;l++)if(!s._howls[c]._sounds[l]._paused)return s}return s._suspendTimer&&clearTimeout(s._suspendTimer),s._suspendTimer=setTimeout(function(){if(!!s.autoSuspend){s._suspendTimer=null,s.state="suspending";var h=function(){s.state="suspended",s._resumeAfterSuspend&&(delete s._resumeAfterSuspend,s._autoResume())};s.ctx.suspend().then(h,h)}},3e4),s}},_autoResume:function(){var s=this;if(!(!s.ctx||typeof s.ctx.resume>"u"||!n.usingWebAudio))return s.state==="running"&&s.ctx.state!=="interrupted"&&s._suspendTimer?(clearTimeout(s._suspendTimer),s._suspendTimer=null):s.state==="suspended"||s.state==="running"&&s.ctx.state==="interrupted"?(s.ctx.resume().then(function(){s.state="running";for(var c=0;c<s._howls.length;c++)s._howls[c]._emit("resume")}),s._suspendTimer&&(clearTimeout(s._suspendTimer),s._suspendTimer=null)):s.state==="suspending"&&(s._resumeAfterSuspend=!0),s}};var n=new t,r=function(s){var c=this;if(!s.src||s.src.length===0){console.error("An array of source files must be passed with any new Howl.");return}c.init(s)};r.prototype={init:function(s){var c=this;return n.ctx||g(),c._autoplay=s.autoplay||!1,c._format=typeof s.format!="string"?s.format:[s.format],c._html5=s.html5||!1,c._muted=s.mute||!1,c._loop=s.loop||!1,c._pool=s.pool||5,c._preload=typeof s.preload=="boolean"||s.preload==="metadata"?s.preload:!0,c._rate=s.rate||1,c._sprite=s.sprite||{},c._src=typeof s.src!="string"?s.src:[s.src],c._volume=s.volume!==void 0?s.volume:1,c._xhr={method:s.xhr&&s.xhr.method?s.xhr.method:"GET",headers:s.xhr&&s.xhr.headers?s.xhr.headers:null,withCredentials:s.xhr&&s.xhr.withCredentials?s.xhr.withCredentials:!1},c._duration=0,c._state="unloaded",c._sounds=[],c._endTimers={},c._queue=[],c._playLock=!1,c._onend=s.onend?[{fn:s.onend}]:[],c._onfade=s.onfade?[{fn:s.onfade}]:[],c._onload=s.onload?[{fn:s.onload}]:[],c._onloaderror=s.onloaderror?[{fn:s.onloaderror}]:[],c._onplayerror=s.onplayerror?[{fn:s.onplayerror}]:[],c._onpause=s.onpause?[{fn:s.onpause}]:[],c._onplay=s.onplay?[{fn:s.onplay}]:[],c._onstop=s.onstop?[{fn:s.onstop}]:[],c._onmute=s.onmute?[{fn:s.onmute}]:[],c._onvolume=s.onvolume?[{fn:s.onvolume}]:[],c._onrate=s.onrate?[{fn:s.onrate}]:[],c._onseek=s.onseek?[{fn:s.onseek}]:[],c._onunlock=s.onunlock?[{fn:s.onunlock}]:[],c._onresume=[],c._webAudio=n.usingWebAudio&&!c._html5,typeof n.ctx<"u"&&n.ctx&&n.autoUnlock&&n._unlockAudio(),n._howls.push(c),c._autoplay&&c._queue.push({event:"play",action:function(){c.play()}}),c._preload&&c._preload!=="none"&&c.load(),c},load:function(){var s=this,c=null;if(n.noAudio){s._emit("loaderror",null,"No audio support.");return}typeof s._src=="string"&&(s._src=[s._src]);for(var l=0;l<s._src.length;l++){var h,m;if(s._format&&s._format[l])h=s._format[l];else{if(m=s._src[l],typeof m!="string"){s._emit("loaderror",null,"Non-string found in selected audio sources - ignoring.");continue}h=/^data:audio\/([^;,]+);/i.exec(m),h||(h=/\.([^.]+)$/.exec(m.split("?",1)[0])),h&&(h=h[1].toLowerCase())}if(h||console.warn('No file extension was found. Consider using the "format" property or specify an extension.'),h&&n.codecs(h)){c=s._src[l];break}}if(!c){s._emit("loaderror",null,"No codec support for selected audio sources.");return}return s._src=c,s._state="loading",window.location.protocol==="https:"&&c.slice(0,5)==="http:"&&(s._html5=!0,s._webAudio=!1),new i(s),s._webAudio&&a(s),s},play:function(s,c){var l=this,h=null;if(typeof s=="number")h=s,s=null;else{if(typeof s=="string"&&l._state==="loaded"&&!l._sprite[s])return null;if(typeof s>"u"&&(s="__default",!l._playLock)){for(var m=0,p=0;p<l._sounds.length;p++)l._sounds[p]._paused&&!l._sounds[p]._ended&&(m++,h=l._sounds[p]._id);m===1?s=null:h=null}}var _=h?l._soundById(h):l._inactiveSound();if(!_)return null;if(h&&!s&&(s=_._sprite||"__default"),l._state!=="loaded"){_._sprite=s,_._ended=!1;var v=_._id;return l._queue.push({event:"play",action:function(){l.play(v)}}),v}if(h&&!_._paused)return c||l._loadQueue("play"),_._id;l._webAudio&&n._autoResume();var b=Math.max(0,_._seek>0?_._seek:l._sprite[s][0]/1e3),T=Math.max(0,(l._sprite[s][0]+l._sprite[s][1])/1e3-b),C=T*1e3/Math.abs(_._rate),D=l._sprite[s][0]/1e3,R=(l._sprite[s][0]+l._sprite[s][1])/1e3;_._sprite=s,_._ended=!1;var B=function(){_._paused=!1,_._seek=b,_._start=D,_._stop=R,_._loop=!!(_._loop||l._sprite[s][2])};if(b>=R){l._ended(_);return}var x=_._node;if(l._webAudio){var I=function(){l._playLock=!1,B(),l._refreshBuffer(_);var S=_._muted||l._muted?0:_._volume;x.gain.setValueAtTime(S,n.ctx.currentTime),_._playStart=n.ctx.currentTime,typeof x.bufferSource.start>"u"?_._loop?x.bufferSource.noteGrainOn(0,b,86400):x.bufferSource.noteGrainOn(0,b,T):_._loop?x.bufferSource.start(0,b,86400):x.bufferSource.start(0,b,T),C!==1/0&&(l._endTimers[_._id]=setTimeout(l._ended.bind(l,_),C)),c||setTimeout(function(){l._emit("play",_._id),l._loadQueue()},0)};n.state==="running"&&n.ctx.state!=="interrupted"?I():(l._playLock=!0,l.once("resume",I),l._clearTimer(_._id))}else{var F=function(){x.currentTime=b,x.muted=_._muted||l._muted||n._muted||x.muted,x.volume=_._volume*n.volume(),x.playbackRate=_._rate;try{var S=x.play();if(S&&typeof Promise<"u"&&(S instanceof Promise||typeof S.then=="function")?(l._playLock=!0,B(),S.then(function(){l._playLock=!1,x._unlocked=!0,c?l._loadQueue():l._emit("play",_._id)}).catch(function(){l._playLock=!1,l._emit("playerror",_._id,"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction."),_._ended=!0,_._paused=!0})):c||(l._playLock=!1,B(),l._emit("play",_._id)),x.playbackRate=_._rate,x.paused){l._emit("playerror",_._id,"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction.");return}s!=="__default"||_._loop?l._endTimers[_._id]=setTimeout(l._ended.bind(l,_),C):(l._endTimers[_._id]=function(){l._ended(_),x.removeEventListener("ended",l._endTimers[_._id],!1)},x.addEventListener("ended",l._endTimers[_._id],!1))}catch(w){l._emit("playerror",_._id,w)}};x.src==="data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA"&&(x.src=l._src,x.load());var ee=window&&window.ejecta||!x.readyState&&n._navigator.isCocoonJS;if(x.readyState>=3||ee)F();else{l._playLock=!0,l._state="loading";var $=function(){l._state="loaded",F(),x.removeEventListener(n._canPlayEvent,$,!1)};x.addEventListener(n._canPlayEvent,$,!1),l._clearTimer(_._id)}}return _._id},pause:function(s){var c=this;if(c._state!=="loaded"||c._playLock)return c._queue.push({event:"pause",action:function(){c.pause(s)}}),c;for(var l=c._getSoundIds(s),h=0;h<l.length;h++){c._clearTimer(l[h]);var m=c._soundById(l[h]);if(m&&!m._paused&&(m._seek=c.seek(l[h]),m._rateSeek=0,m._paused=!0,c._stopFade(l[h]),m._node))if(c._webAudio){if(!m._node.bufferSource)continue;typeof m._node.bufferSource.stop>"u"?m._node.bufferSource.noteOff(0):m._node.bufferSource.stop(0),c._cleanBuffer(m._node)}else(!isNaN(m._node.duration)||m._node.duration===1/0)&&m._node.pause();arguments[1]||c._emit("pause",m?m._id:null)}return c},stop:function(s,c){var l=this;if(l._state!=="loaded"||l._playLock)return l._queue.push({event:"stop",action:function(){l.stop(s)}}),l;for(var h=l._getSoundIds(s),m=0;m<h.length;m++){l._clearTimer(h[m]);var p=l._soundById(h[m]);p&&(p._seek=p._start||0,p._rateSeek=0,p._paused=!0,p._ended=!0,l._stopFade(h[m]),p._node&&(l._webAudio?p._node.bufferSource&&(typeof p._node.bufferSource.stop>"u"?p._node.bufferSource.noteOff(0):p._node.bufferSource.stop(0),l._cleanBuffer(p._node)):(!isNaN(p._node.duration)||p._node.duration===1/0)&&(p._node.currentTime=p._start||0,p._node.pause(),p._node.duration===1/0&&l._clearSound(p._node))),c||l._emit("stop",p._id))}return l},mute:function(s,c){var l=this;if(l._state!=="loaded"||l._playLock)return l._queue.push({event:"mute",action:function(){l.mute(s,c)}}),l;if(typeof c>"u")if(typeof s=="boolean")l._muted=s;else return l._muted;for(var h=l._getSoundIds(c),m=0;m<h.length;m++){var p=l._soundById(h[m]);p&&(p._muted=s,p._interval&&l._stopFade(p._id),l._webAudio&&p._node?p._node.gain.setValueAtTime(s?0:p._volume,n.ctx.currentTime):p._node&&(p._node.muted=n._muted?!0:s),l._emit("mute",p._id))}return l},volume:function(){var s=this,c=arguments,l,h;if(c.length===0)return s._volume;if(c.length===1||c.length===2&&typeof c[1]>"u"){var m=s._getSoundIds(),p=m.indexOf(c[0]);p>=0?h=parseInt(c[0],10):l=parseFloat(c[0])}else c.length>=2&&(l=parseFloat(c[0]),h=parseInt(c[1],10));var _;if(typeof l<"u"&&l>=0&&l<=1){if(s._state!=="loaded"||s._playLock)return s._queue.push({event:"volume",action:function(){s.volume.apply(s,c)}}),s;typeof h>"u"&&(s._volume=l),h=s._getSoundIds(h);for(var v=0;v<h.length;v++)_=s._soundById(h[v]),_&&(_._volume=l,c[2]||s._stopFade(h[v]),s._webAudio&&_._node&&!_._muted?_._node.gain.setValueAtTime(l,n.ctx.currentTime):_._node&&!_._muted&&(_._node.volume=l*n.volume()),s._emit("volume",_._id))}else return _=h?s._soundById(h):s._sounds[0],_?_._volume:0;return s},fade:function(s,c,l,h){var m=this;if(m._state!=="loaded"||m._playLock)return m._queue.push({event:"fade",action:function(){m.fade(s,c,l,h)}}),m;s=Math.min(Math.max(0,parseFloat(s)),1),c=Math.min(Math.max(0,parseFloat(c)),1),l=parseFloat(l),m.volume(s,h);for(var p=m._getSoundIds(h),_=0;_<p.length;_++){var v=m._soundById(p[_]);if(v){if(h||m._stopFade(p[_]),m._webAudio&&!v._muted){var b=n.ctx.currentTime,T=b+l/1e3;v._volume=s,v._node.gain.setValueAtTime(s,b),v._node.gain.linearRampToValueAtTime(c,T)}m._startFadeInterval(v,s,c,l,p[_],typeof h>"u")}}return m},_startFadeInterval:function(s,c,l,h,m,p){var _=this,v=c,b=l-c,T=Math.abs(b/.01),C=Math.max(4,T>0?h/T:h),D=Date.now();s._fadeTo=l,s._interval=setInterval(function(){var R=(Date.now()-D)/h;D=Date.now(),v+=b*R,v=Math.round(v*100)/100,b<0?v=Math.max(l,v):v=Math.min(l,v),_._webAudio?s._volume=v:_.volume(v,s._id,!0),p&&(_._volume=v),(l<c&&v<=l||l>c&&v>=l)&&(clearInterval(s._interval),s._interval=null,s._fadeTo=null,_.volume(l,s._id),_._emit("fade",s._id))},C)},_stopFade:function(s){var c=this,l=c._soundById(s);return l&&l._interval&&(c._webAudio&&l._node.gain.cancelScheduledValues(n.ctx.currentTime),clearInterval(l._interval),l._interval=null,c.volume(l._fadeTo,s),l._fadeTo=null,c._emit("fade",s)),c},loop:function(){var s=this,c=arguments,l,h,m;if(c.length===0)return s._loop;if(c.length===1)if(typeof c[0]=="boolean")l=c[0],s._loop=l;else return m=s._soundById(parseInt(c[0],10)),m?m._loop:!1;else c.length===2&&(l=c[0],h=parseInt(c[1],10));for(var p=s._getSoundIds(h),_=0;_<p.length;_++)m=s._soundById(p[_]),m&&(m._loop=l,s._webAudio&&m._node&&m._node.bufferSource&&(m._node.bufferSource.loop=l,l&&(m._node.bufferSource.loopStart=m._start||0,m._node.bufferSource.loopEnd=m._stop,s.playing(p[_])&&(s.pause(p[_],!0),s.play(p[_],!0)))));return s},rate:function(){var s=this,c=arguments,l,h;if(c.length===0)h=s._sounds[0]._id;else if(c.length===1){var m=s._getSoundIds(),p=m.indexOf(c[0]);p>=0?h=parseInt(c[0],10):l=parseFloat(c[0])}else c.length===2&&(l=parseFloat(c[0]),h=parseInt(c[1],10));var _;if(typeof l=="number"){if(s._state!=="loaded"||s._playLock)return s._queue.push({event:"rate",action:function(){s.rate.apply(s,c)}}),s;typeof h>"u"&&(s._rate=l),h=s._getSoundIds(h);for(var v=0;v<h.length;v++)if(_=s._soundById(h[v]),_){s.playing(h[v])&&(_._rateSeek=s.seek(h[v]),_._playStart=s._webAudio?n.ctx.currentTime:_._playStart),_._rate=l,s._webAudio&&_._node&&_._node.bufferSource?_._node.bufferSource.playbackRate.setValueAtTime(l,n.ctx.currentTime):_._node&&(_._node.playbackRate=l);var b=s.seek(h[v]),T=(s._sprite[_._sprite][0]+s._sprite[_._sprite][1])/1e3-b,C=T*1e3/Math.abs(_._rate);(s._endTimers[h[v]]||!_._paused)&&(s._clearTimer(h[v]),s._endTimers[h[v]]=setTimeout(s._ended.bind(s,_),C)),s._emit("rate",_._id)}}else return _=s._soundById(h),_?_._rate:s._rate;return s},seek:function(){var s=this,c=arguments,l,h;if(c.length===0)s._sounds.length&&(h=s._sounds[0]._id);else if(c.length===1){var m=s._getSoundIds(),p=m.indexOf(c[0]);p>=0?h=parseInt(c[0],10):s._sounds.length&&(h=s._sounds[0]._id,l=parseFloat(c[0]))}else c.length===2&&(l=parseFloat(c[0]),h=parseInt(c[1],10));if(typeof h>"u")return 0;if(typeof l=="number"&&(s._state!=="loaded"||s._playLock))return s._queue.push({event:"seek",action:function(){s.seek.apply(s,c)}}),s;var _=s._soundById(h);if(_)if(typeof l=="number"&&l>=0){var v=s.playing(h);v&&s.pause(h,!0),_._seek=l,_._ended=!1,s._clearTimer(h),!s._webAudio&&_._node&&!isNaN(_._node.duration)&&(_._node.currentTime=l);var b=function(){v&&s.play(h,!0),s._emit("seek",h)};if(v&&!s._webAudio){var T=function(){s._playLock?setTimeout(T,0):b()};setTimeout(T,0)}else b()}else if(s._webAudio){var C=s.playing(h)?n.ctx.currentTime-_._playStart:0,D=_._rateSeek?_._rateSeek-_._seek:0;return _._seek+(D+C*Math.abs(_._rate))}else return _._node.currentTime;return s},playing:function(s){var c=this;if(typeof s=="number"){var l=c._soundById(s);return l?!l._paused:!1}for(var h=0;h<c._sounds.length;h++)if(!c._sounds[h]._paused)return!0;return!1},duration:function(s){var c=this,l=c._duration,h=c._soundById(s);return h&&(l=c._sprite[h._sprite][1]/1e3),l},state:function(){return this._state},unload:function(){for(var s=this,c=s._sounds,l=0;l<c.length;l++)c[l]._paused||s.stop(c[l]._id),s._webAudio||(s._clearSound(c[l]._node),c[l]._node.removeEventListener("error",c[l]._errorFn,!1),c[l]._node.removeEventListener(n._canPlayEvent,c[l]._loadFn,!1),c[l]._node.removeEventListener("ended",c[l]._endFn,!1),n._releaseHtml5Audio(c[l]._node)),delete c[l]._node,s._clearTimer(c[l]._id);var h=n._howls.indexOf(s);h>=0&&n._howls.splice(h,1);var m=!0;for(l=0;l<n._howls.length;l++)if(n._howls[l]._src===s._src||s._src.indexOf(n._howls[l]._src)>=0){m=!1;break}return o&&m&&delete o[s._src],n.noAudio=!1,s._state="unloaded",s._sounds=[],s=null,null},on:function(s,c,l,h){var m=this,p=m["_on"+s];return typeof c=="function"&&p.push(h?{id:l,fn:c,once:h}:{id:l,fn:c}),m},off:function(s,c,l){var h=this,m=h["_on"+s],p=0;if(typeof c=="number"&&(l=c,c=null),c||l)for(p=0;p<m.length;p++){var _=l===m[p].id;if(c===m[p].fn&&_||!c&&_){m.splice(p,1);break}}else if(s)h["_on"+s]=[];else{var v=Object.keys(h);for(p=0;p<v.length;p++)v[p].indexOf("_on")===0&&Array.isArray(h[v[p]])&&(h[v[p]]=[])}return h},once:function(s,c,l){var h=this;return h.on(s,c,l,1),h},_emit:function(s,c,l){for(var h=this,m=h["_on"+s],p=m.length-1;p>=0;p--)(!m[p].id||m[p].id===c||s==="load")&&(setTimeout(function(_){_.call(this,c,l)}.bind(h,m[p].fn),0),m[p].once&&h.off(s,m[p].fn,m[p].id));return h._loadQueue(s),h},_loadQueue:function(s){var c=this;if(c._queue.length>0){var l=c._queue[0];l.event===s&&(c._queue.shift(),c._loadQueue()),s||l.action()}return c},_ended:function(s){var c=this,l=s._sprite;if(!c._webAudio&&s._node&&!s._node.paused&&!s._node.ended&&s._node.currentTime<s._stop)return setTimeout(c._ended.bind(c,s),100),c;var h=!!(s._loop||c._sprite[l][2]);if(c._emit("end",s._id),!c._webAudio&&h&&c.stop(s._id,!0).play(s._id),c._webAudio&&h){c._emit("play",s._id),s._seek=s._start||0,s._rateSeek=0,s._playStart=n.ctx.currentTime;var m=(s._stop-s._start)*1e3/Math.abs(s._rate);c._endTimers[s._id]=setTimeout(c._ended.bind(c,s),m)}return c._webAudio&&!h&&(s._paused=!0,s._ended=!0,s._seek=s._start||0,s._rateSeek=0,c._clearTimer(s._id),c._cleanBuffer(s._node),n._autoSuspend()),!c._webAudio&&!h&&c.stop(s._id,!0),c},_clearTimer:function(s){var c=this;if(c._endTimers[s]){if(typeof c._endTimers[s]!="function")clearTimeout(c._endTimers[s]);else{var l=c._soundById(s);l&&l._node&&l._node.removeEventListener("ended",c._endTimers[s],!1)}delete c._endTimers[s]}return c},_soundById:function(s){for(var c=this,l=0;l<c._sounds.length;l++)if(s===c._sounds[l]._id)return c._sounds[l];return null},_inactiveSound:function(){var s=this;s._drain();for(var c=0;c<s._sounds.length;c++)if(s._sounds[c]._ended)return s._sounds[c].reset();return new i(s)},_drain:function(){var s=this,c=s._pool,l=0,h=0;if(!(s._sounds.length<c)){for(h=0;h<s._sounds.length;h++)s._sounds[h]._ended&&l++;for(h=s._sounds.length-1;h>=0;h--){if(l<=c)return;s._sounds[h]._ended&&(s._webAudio&&s._sounds[h]._node&&s._sounds[h]._node.disconnect(0),s._sounds.splice(h,1),l--)}}},_getSoundIds:function(s){var c=this;if(typeof s>"u"){for(var l=[],h=0;h<c._sounds.length;h++)l.push(c._sounds[h]._id);return l}else return[s]},_refreshBuffer:function(s){var c=this;return s._node.bufferSource=n.ctx.createBufferSource(),s._node.bufferSource.buffer=o[c._src],s._panner?s._node.bufferSource.connect(s._panner):s._node.bufferSource.connect(s._node),s._node.bufferSource.loop=s._loop,s._loop&&(s._node.bufferSource.loopStart=s._start||0,s._node.bufferSource.loopEnd=s._stop||0),s._node.bufferSource.playbackRate.setValueAtTime(s._rate,n.ctx.currentTime),c},_cleanBuffer:function(s){var c=this,l=n._navigator&&n._navigator.vendor.indexOf("Apple")>=0;if(n._scratchBuffer&&s.bufferSource&&(s.bufferSource.onended=null,s.bufferSource.disconnect(0),l))try{s.bufferSource.buffer=n._scratchBuffer}catch{}return s.bufferSource=null,c},_clearSound:function(s){var c=/MSIE |Trident\//.test(n._navigator&&n._navigator.userAgent);c||(s.src="data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA")}};var i=function(s){this._parent=s,this.init()};i.prototype={init:function(){var s=this,c=s._parent;return s._muted=c._muted,s._loop=c._loop,s._volume=c._volume,s._rate=c._rate,s._seek=0,s._paused=!0,s._ended=!0,s._sprite="__default",s._id=++n._counter,c._sounds.push(s),s.create(),s},create:function(){var s=this,c=s._parent,l=n._muted||s._muted||s._parent._muted?0:s._volume;return c._webAudio?(s._node=typeof n.ctx.createGain>"u"?n.ctx.createGainNode():n.ctx.createGain(),s._node.gain.setValueAtTime(l,n.ctx.currentTime),s._node.paused=!0,s._node.connect(n.masterGain)):n.noAudio||(s._node=n._obtainHtml5Audio(),s._errorFn=s._errorListener.bind(s),s._node.addEventListener("error",s._errorFn,!1),s._loadFn=s._loadListener.bind(s),s._node.addEventListener(n._canPlayEvent,s._loadFn,!1),s._endFn=s._endListener.bind(s),s._node.addEventListener("ended",s._endFn,!1),s._node.src=c._src,s._node.preload=c._preload===!0?"auto":c._preload,s._node.volume=l*n.volume(),s._node.load()),s},reset:function(){var s=this,c=s._parent;return s._muted=c._muted,s._loop=c._loop,s._volume=c._volume,s._rate=c._rate,s._seek=0,s._rateSeek=0,s._paused=!0,s._ended=!0,s._sprite="__default",s._id=++n._counter,s},_errorListener:function(){var s=this;s._parent._emit("loaderror",s._id,s._node.error?s._node.error.code:0),s._node.removeEventListener("error",s._errorFn,!1)},_loadListener:function(){var s=this,c=s._parent;c._duration=Math.ceil(s._node.duration*10)/10,Object.keys(c._sprite).length===0&&(c._sprite={__default:[0,c._duration*1e3]}),c._state!=="loaded"&&(c._state="loaded",c._emit("load"),c._loadQueue()),s._node.removeEventListener(n._canPlayEvent,s._loadFn,!1)},_endListener:function(){var s=this,c=s._parent;c._duration===1/0&&(c._duration=Math.ceil(s._node.duration*10)/10,c._sprite.__default[1]===1/0&&(c._sprite.__default[1]=c._duration*1e3),c._ended(s)),s._node.removeEventListener("ended",s._endFn,!1)}};var o={},a=function(s){var c=s._src;if(o[c]){s._duration=o[c].duration,d(s);return}if(/^data:[^;]+;base64,/.test(c)){for(var l=atob(c.split(",")[1]),h=new Uint8Array(l.length),m=0;m<l.length;++m)h[m]=l.charCodeAt(m);f(h.buffer,s)}else{var p=new XMLHttpRequest;p.open(s._xhr.method,c,!0),p.withCredentials=s._xhr.withCredentials,p.responseType="arraybuffer",s._xhr.headers&&Object.keys(s._xhr.headers).forEach(function(_){p.setRequestHeader(_,s._xhr.headers[_])}),p.onload=function(){var _=(p.status+"")[0];if(_!=="0"&&_!=="2"&&_!=="3"){s._emit("loaderror",null,"Failed loading audio file with status: "+p.status+".");return}f(p.response,s)},p.onerror=function(){s._webAudio&&(s._html5=!0,s._webAudio=!1,s._sounds=[],delete o[c],s.load())},u(p)}},u=function(s){try{s.send()}catch{s.onerror()}},f=function(s,c){var l=function(){c._emit("loaderror",null,"Decoding audio data failed.")},h=function(m){m&&c._sounds.length>0?(o[c._src]=m,d(c,m)):l()};typeof Promise<"u"&&n.ctx.decodeAudioData.length===1?n.ctx.decodeAudioData(s).then(h).catch(l):n.ctx.decodeAudioData(s,h,l)},d=function(s,c){c&&!s._duration&&(s._duration=c.duration),Object.keys(s._sprite).length===0&&(s._sprite={__default:[0,s._duration*1e3]}),s._state!=="loaded"&&(s._state="loaded",s._emit("load"),s._loadQueue())},g=function(){if(!!n.usingWebAudio){try{typeof AudioContext<"u"?n.ctx=new AudioContext:typeof webkitAudioContext<"u"?n.ctx=new webkitAudioContext:n.usingWebAudio=!1}catch{n.usingWebAudio=!1}n.ctx||(n.usingWebAudio=!1);var s=/iP(hone|od|ad)/.test(n._navigator&&n._navigator.platform),c=n._navigator&&n._navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/),l=c?parseInt(c[1],10):null;if(s&&l&&l<9){var h=/safari/.test(n._navigator&&n._navigator.userAgent.toLowerCase());n._navigator&&!h&&(n.usingWebAudio=!1)}n.usingWebAudio&&(n.masterGain=typeof n.ctx.createGain>"u"?n.ctx.createGainNode():n.ctx.createGain(),n.masterGain.gain.setValueAtTime(n._muted?0:n._volume,n.ctx.currentTime),n.masterGain.connect(n.ctx.destination)),n._setup()}};e.Howler=n,e.Howl=r,typeof De<"u"?(De.HowlerGlobal=t,De.Howler=n,De.Howl=r,De.Sound=i):typeof window<"u"&&(window.HowlerGlobal=t,window.Howler=n,window.Howl=r,window.Sound=i)})();/*!
 *  Spatial Plugin - Adds support for stereo and 3D audio where Web Audio is supported.
 *  
 *  howler.js v2.2.3
 *  howlerjs.com
 *
 *  (c) 2013-2020, James Simpson of GoldFire Studios
 *  goldfirestudios.com
 *
 *  MIT License
 */(function(){HowlerGlobal.prototype._pos=[0,0,0],HowlerGlobal.prototype._orientation=[0,0,-1,0,1,0],HowlerGlobal.prototype.stereo=function(n){var r=this;if(!r.ctx||!r.ctx.listener)return r;for(var i=r._howls.length-1;i>=0;i--)r._howls[i].stereo(n);return r},HowlerGlobal.prototype.pos=function(n,r,i){var o=this;if(!o.ctx||!o.ctx.listener)return o;if(r=typeof r!="number"?o._pos[1]:r,i=typeof i!="number"?o._pos[2]:i,typeof n=="number")o._pos=[n,r,i],typeof o.ctx.listener.positionX<"u"?(o.ctx.listener.positionX.setTargetAtTime(o._pos[0],Howler.ctx.currentTime,.1),o.ctx.listener.positionY.setTargetAtTime(o._pos[1],Howler.ctx.currentTime,.1),o.ctx.listener.positionZ.setTargetAtTime(o._pos[2],Howler.ctx.currentTime,.1)):o.ctx.listener.setPosition(o._pos[0],o._pos[1],o._pos[2]);else return o._pos;return o},HowlerGlobal.prototype.orientation=function(n,r,i,o,a,u){var f=this;if(!f.ctx||!f.ctx.listener)return f;var d=f._orientation;if(r=typeof r!="number"?d[1]:r,i=typeof i!="number"?d[2]:i,o=typeof o!="number"?d[3]:o,a=typeof a!="number"?d[4]:a,u=typeof u!="number"?d[5]:u,typeof n=="number")f._orientation=[n,r,i,o,a,u],typeof f.ctx.listener.forwardX<"u"?(f.ctx.listener.forwardX.setTargetAtTime(n,Howler.ctx.currentTime,.1),f.ctx.listener.forwardY.setTargetAtTime(r,Howler.ctx.currentTime,.1),f.ctx.listener.forwardZ.setTargetAtTime(i,Howler.ctx.currentTime,.1),f.ctx.listener.upX.setTargetAtTime(o,Howler.ctx.currentTime,.1),f.ctx.listener.upY.setTargetAtTime(a,Howler.ctx.currentTime,.1),f.ctx.listener.upZ.setTargetAtTime(u,Howler.ctx.currentTime,.1)):f.ctx.listener.setOrientation(n,r,i,o,a,u);else return d;return f},Howl.prototype.init=function(n){return function(r){var i=this;return i._orientation=r.orientation||[1,0,0],i._stereo=r.stereo||null,i._pos=r.pos||null,i._pannerAttr={coneInnerAngle:typeof r.coneInnerAngle<"u"?r.coneInnerAngle:360,coneOuterAngle:typeof r.coneOuterAngle<"u"?r.coneOuterAngle:360,coneOuterGain:typeof r.coneOuterGain<"u"?r.coneOuterGain:0,distanceModel:typeof r.distanceModel<"u"?r.distanceModel:"inverse",maxDistance:typeof r.maxDistance<"u"?r.maxDistance:1e4,panningModel:typeof r.panningModel<"u"?r.panningModel:"HRTF",refDistance:typeof r.refDistance<"u"?r.refDistance:1,rolloffFactor:typeof r.rolloffFactor<"u"?r.rolloffFactor:1},i._onstereo=r.onstereo?[{fn:r.onstereo}]:[],i._onpos=r.onpos?[{fn:r.onpos}]:[],i._onorientation=r.onorientation?[{fn:r.onorientation}]:[],n.call(this,r)}}(Howl.prototype.init),Howl.prototype.stereo=function(n,r){var i=this;if(!i._webAudio)return i;if(i._state!=="loaded")return i._queue.push({event:"stereo",action:function(){i.stereo(n,r)}}),i;var o=typeof Howler.ctx.createStereoPanner>"u"?"spatial":"stereo";if(typeof r>"u")if(typeof n=="number")i._stereo=n,i._pos=[n,0,0];else return i._stereo;for(var a=i._getSoundIds(r),u=0;u<a.length;u++){var f=i._soundById(a[u]);if(f)if(typeof n=="number")f._stereo=n,f._pos=[n,0,0],f._node&&(f._pannerAttr.panningModel="equalpower",(!f._panner||!f._panner.pan)&&t(f,o),o==="spatial"?typeof f._panner.positionX<"u"?(f._panner.positionX.setValueAtTime(n,Howler.ctx.currentTime),f._panner.positionY.setValueAtTime(0,Howler.ctx.currentTime),f._panner.positionZ.setValueAtTime(0,Howler.ctx.currentTime)):f._panner.setPosition(n,0,0):f._panner.pan.setValueAtTime(n,Howler.ctx.currentTime)),i._emit("stereo",f._id);else return f._stereo}return i},Howl.prototype.pos=function(n,r,i,o){var a=this;if(!a._webAudio)return a;if(a._state!=="loaded")return a._queue.push({event:"pos",action:function(){a.pos(n,r,i,o)}}),a;if(r=typeof r!="number"?0:r,i=typeof i!="number"?-.5:i,typeof o>"u")if(typeof n=="number")a._pos=[n,r,i];else return a._pos;for(var u=a._getSoundIds(o),f=0;f<u.length;f++){var d=a._soundById(u[f]);if(d)if(typeof n=="number")d._pos=[n,r,i],d._node&&((!d._panner||d._panner.pan)&&t(d,"spatial"),typeof d._panner.positionX<"u"?(d._panner.positionX.setValueAtTime(n,Howler.ctx.currentTime),d._panner.positionY.setValueAtTime(r,Howler.ctx.currentTime),d._panner.positionZ.setValueAtTime(i,Howler.ctx.currentTime)):d._panner.setPosition(n,r,i)),a._emit("pos",d._id);else return d._pos}return a},Howl.prototype.orientation=function(n,r,i,o){var a=this;if(!a._webAudio)return a;if(a._state!=="loaded")return a._queue.push({event:"orientation",action:function(){a.orientation(n,r,i,o)}}),a;if(r=typeof r!="number"?a._orientation[1]:r,i=typeof i!="number"?a._orientation[2]:i,typeof o>"u")if(typeof n=="number")a._orientation=[n,r,i];else return a._orientation;for(var u=a._getSoundIds(o),f=0;f<u.length;f++){var d=a._soundById(u[f]);if(d)if(typeof n=="number")d._orientation=[n,r,i],d._node&&(d._panner||(d._pos||(d._pos=a._pos||[0,0,-.5]),t(d,"spatial")),typeof d._panner.orientationX<"u"?(d._panner.orientationX.setValueAtTime(n,Howler.ctx.currentTime),d._panner.orientationY.setValueAtTime(r,Howler.ctx.currentTime),d._panner.orientationZ.setValueAtTime(i,Howler.ctx.currentTime)):d._panner.setOrientation(n,r,i)),a._emit("orientation",d._id);else return d._orientation}return a},Howl.prototype.pannerAttr=function(){var n=this,r=arguments,i,o,a;if(!n._webAudio)return n;if(r.length===0)return n._pannerAttr;if(r.length===1)if(typeof r[0]=="object")i=r[0],typeof o>"u"&&(i.pannerAttr||(i.pannerAttr={coneInnerAngle:i.coneInnerAngle,coneOuterAngle:i.coneOuterAngle,coneOuterGain:i.coneOuterGain,distanceModel:i.distanceModel,maxDistance:i.maxDistance,refDistance:i.refDistance,rolloffFactor:i.rolloffFactor,panningModel:i.panningModel}),n._pannerAttr={coneInnerAngle:typeof i.pannerAttr.coneInnerAngle<"u"?i.pannerAttr.coneInnerAngle:n._coneInnerAngle,coneOuterAngle:typeof i.pannerAttr.coneOuterAngle<"u"?i.pannerAttr.coneOuterAngle:n._coneOuterAngle,coneOuterGain:typeof i.pannerAttr.coneOuterGain<"u"?i.pannerAttr.coneOuterGain:n._coneOuterGain,distanceModel:typeof i.pannerAttr.distanceModel<"u"?i.pannerAttr.distanceModel:n._distanceModel,maxDistance:typeof i.pannerAttr.maxDistance<"u"?i.pannerAttr.maxDistance:n._maxDistance,refDistance:typeof i.pannerAttr.refDistance<"u"?i.pannerAttr.refDistance:n._refDistance,rolloffFactor:typeof i.pannerAttr.rolloffFactor<"u"?i.pannerAttr.rolloffFactor:n._rolloffFactor,panningModel:typeof i.pannerAttr.panningModel<"u"?i.pannerAttr.panningModel:n._panningModel});else return a=n._soundById(parseInt(r[0],10)),a?a._pannerAttr:n._pannerAttr;else r.length===2&&(i=r[0],o=parseInt(r[1],10));for(var u=n._getSoundIds(o),f=0;f<u.length;f++)if(a=n._soundById(u[f]),a){var d=a._pannerAttr;d={coneInnerAngle:typeof i.coneInnerAngle<"u"?i.coneInnerAngle:d.coneInnerAngle,coneOuterAngle:typeof i.coneOuterAngle<"u"?i.coneOuterAngle:d.coneOuterAngle,coneOuterGain:typeof i.coneOuterGain<"u"?i.coneOuterGain:d.coneOuterGain,distanceModel:typeof i.distanceModel<"u"?i.distanceModel:d.distanceModel,maxDistance:typeof i.maxDistance<"u"?i.maxDistance:d.maxDistance,refDistance:typeof i.refDistance<"u"?i.refDistance:d.refDistance,rolloffFactor:typeof i.rolloffFactor<"u"?i.rolloffFactor:d.rolloffFactor,panningModel:typeof i.panningModel<"u"?i.panningModel:d.panningModel};var g=a._panner;g?(g.coneInnerAngle=d.coneInnerAngle,g.coneOuterAngle=d.coneOuterAngle,g.coneOuterGain=d.coneOuterGain,g.distanceModel=d.distanceModel,g.maxDistance=d.maxDistance,g.refDistance=d.refDistance,g.rolloffFactor=d.rolloffFactor,g.panningModel=d.panningModel):(a._pos||(a._pos=n._pos||[0,0,-.5]),t(a,"spatial"))}return n},Sound.prototype.init=function(n){return function(){var r=this,i=r._parent;r._orientation=i._orientation,r._stereo=i._stereo,r._pos=i._pos,r._pannerAttr=i._pannerAttr,n.call(this),r._stereo?i.stereo(r._stereo):r._pos&&i.pos(r._pos[0],r._pos[1],r._pos[2],r._id)}}(Sound.prototype.init),Sound.prototype.reset=function(n){return function(){var r=this,i=r._parent;return r._orientation=i._orientation,r._stereo=i._stereo,r._pos=i._pos,r._pannerAttr=i._pannerAttr,r._stereo?i.stereo(r._stereo):r._pos?i.pos(r._pos[0],r._pos[1],r._pos[2],r._id):r._panner&&(r._panner.disconnect(0),r._panner=void 0,i._refreshBuffer(r)),n.call(this)}}(Sound.prototype.reset);var t=function(n,r){r=r||"spatial",r==="spatial"?(n._panner=Howler.ctx.createPanner(),n._panner.coneInnerAngle=n._pannerAttr.coneInnerAngle,n._panner.coneOuterAngle=n._pannerAttr.coneOuterAngle,n._panner.coneOuterGain=n._pannerAttr.coneOuterGain,n._panner.distanceModel=n._pannerAttr.distanceModel,n._panner.maxDistance=n._pannerAttr.maxDistance,n._panner.refDistance=n._pannerAttr.refDistance,n._panner.rolloffFactor=n._pannerAttr.rolloffFactor,n._panner.panningModel=n._pannerAttr.panningModel,typeof n._panner.positionX<"u"?(n._panner.positionX.setValueAtTime(n._pos[0],Howler.ctx.currentTime),n._panner.positionY.setValueAtTime(n._pos[1],Howler.ctx.currentTime),n._panner.positionZ.setValueAtTime(n._pos[2],Howler.ctx.currentTime)):n._panner.setPosition(n._pos[0],n._pos[1],n._pos[2]),typeof n._panner.orientationX<"u"?(n._panner.orientationX.setValueAtTime(n._orientation[0],Howler.ctx.currentTime),n._panner.orientationY.setValueAtTime(n._orientation[1],Howler.ctx.currentTime),n._panner.orientationZ.setValueAtTime(n._orientation[2],Howler.ctx.currentTime)):n._panner.setOrientation(n._orientation[0],n._orientation[1],n._orientation[2])):(n._panner=Howler.ctx.createStereoPanner(),n._panner.pan.setValueAtTime(n._stereo,Howler.ctx.currentTime)),n._panner.connect(n._node),n._paused||n._parent.pause(n._id,!0).play(n._id,!0)}})()})(Go);const Yo=(e,t=[])=>JSON.parse(localStorage.getItem(`CTFd:${e}`))||t,qo=(e,t)=>{localStorage.setItem(`CTFd:${e}`,JSON.stringify(t))};function Un(){return Yo("read_notifications")}function Gn(){return Yo("unread_notifications")}function bi(e){qo("read_notifications",e)}function Yn(e){qo("unread_notifications",e)}function Vu(e){const t=[...Un(),e];return bi(t),zo(e),t}function Fu(e){const t=[...Gn(),e];return Yn(t),t}function bs(){const e=Un();return e.length===0?0:Math.max(...e)}function zo(e){const n=Gn().filter(r=>r!==e);Yn(n)}function ju(){const e=Gn(),t=Un();bi(t.concat(e)),Yn([])}const K={init:(e,t)=>{K.source=new EventSource(e+"/events");for(let r=0;r<t.length;r++)t[r]=`${e}${t[r]}`;K.howl=new Go.Howl({src:t});let n=bs();O.fetch(`/api/v1/notifications?since_id=${n}`,{method:"HEAD"}).then(r=>{let i=r.headers.get("result-count");i&&(K.controller.broadcast("counter",{count:i}),O._functions.events.eventCount(i))})},controller:new Bu,source:null,howl:null,connect:()=>{K.source.addEventListener("notification",function(e){let t=JSON.parse(e.data);K.controller.broadcast("notification",t),O.events.counter.unread.add(t.id);let n=O.events.counter.unread.getAll().length;K.controller.broadcast("counter",{count:n}),O._functions.events.eventCount(n),K.render(t),t.sound&&K.howl.play()},!1)},disconnect:()=>{K.source&&K.source.close()},render:e=>{switch(e.type){case"toast":{O._functions.events.eventToast(e);break}case"alert":{O._functions.events.eventAlert(e);break}case"background":{O._functions.events.eventBackground(e);break}default:{console.log(e),alert(e);break}}},counter:{read:{getAll:Un,setAll:bi,add:Vu,getLast:bs},unread:{getAll:Gn,setAll:Yn,add:Fu,remove:zo,readAll:ju}}};K.controller.alert=function(e){K.render(e)};K.controller.toast=function(e){K.render(e)};K.controller.background=function(e){K.render(e)};K.controller.counter=function(e){O._functions.events.eventCount(e.count)};K.controller.masterDidChange=function(){this.isMaster?K.connect():K.disconnect()};var Xo={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(De,function(){return function(n,r,i){n=n||{};var o=r.prototype,a={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function u(d,g,s,c){return o.fromToBase(d,g,s,c)}i.en.relativeTime=a,o.fromToBase=function(d,g,s,c,l){for(var h,m,p,_=s.$locale().relativeTime||a,v=n.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],b=v.length,T=0;T<b;T+=1){var C=v[T];C.d&&(h=c?i(d).diff(s,C.d,!0):s.diff(d,C.d,!0));var D=(n.rounding||Math.round)(Math.abs(h));if(p=h>0,D<=C.r||!C.r){D<=1&&T>0&&(C=v[T-1]);var R=_[C.l];l&&(D=l(""+D)),m=typeof R=="string"?R.replace("%d",D):R(D,g,C.l,p);break}}if(g)return m;var B=p?_.future:_.past;return typeof B=="function"?B(m):B.replace("%s",m)},o.to=function(d,g){return u(d,g,this,!0)},o.from=function(d,g){return u(d,g,this)};var f=function(d){return d.$u?i.utc():i()};o.toNow=function(d){return this.to(f(this),d)},o.fromNow=function(d){return this.from(f(this),d)}}})})(Xo);const Wu=Xo.exports;Ge.extend(Vn);Ge.extend(Wu);const wt={id:null,name:null,email:null},Sn={id:null,name:null},Ku={},Uu={challenge:{displayChallenge:null,renderChallenge:null,displayHint(e){alert(e.content)},displayUnlock(e){return confirm("Are you sure you'd like to unlock this hint?")},displayHintUnlock(e){return confirm("Are you sure you'd like to unlock this hint?")},displaySolutionUnlock(e){return confirm("Are you sure you'd like to unlock this solution?")},displayUnlockError(e){const t=[];Object.keys(e.errors).map(r=>{t.push(e.errors[r])});const n=t.join(`
`);alert(n)},submitChallenge:null,displaySubmissionResponse:null,displaySolves:null},challenges:{displayChallenges:null,sortChallenges:null},events:{eventAlert:null,eventToast:null,eventBackground:null,eventRead:null,eventCount:null}},Gu={htmlEntities:Uo,colorHash:mu,copyToClipboard:pu,hashCode:_u,renderTimes:hu},Yu={ajax:{getScript:Ro},html:{createHtmlNode:Hu,htmlEntities:Uo}},qu={challenge:{displayChallenge:vu,submitChallenge:yu,loadSolves:jo,displaySolves:Eu,loadHint:Ho,loadUnlock:yi,displayUnlock:bu,displayHintUnlock:Vo,displaySolutionUnlock:Fo,displayHint:Bo,loadSolution:Wo,displaySolution:Ko},challenges:{getChallenges:ko,getChallenge:Po,displayChallenges:gu},scoreboard:{getScoreboard:wu,getScoreboardDetail:Au,getBrackets:Tu},settings:{updateSettings:Su,generateToken:Ou,deleteToken:xu},users:{userSolves:$u,userFails:Nu,userAwards:Du,userSubmissions:Cu},teams:{getInviteToken:Lu,disbandTeam:Iu,updateTeamSettings:Mu,teamSolves:ku,teamFails:Pu,teamAwards:Ru}},zu={$:k,dayjs:Ge};let Es=!1;const Xu=e=>{Es||(Es=!0,G.urlRoot=e.urlRoot||G.urlRoot,G.csrfNonce=e.csrfNonce||G.csrfNonce,G.userMode=e.userMode||G.userMode,G.start=e.start||G.start,G.end=e.end||G.end,G.themeSettings=e.themeSettings||G.themeSettings,G.eventSounds=e.eventSounds||G.eventSounds,G.preview=!1,wt.id=e.userId,wt.name=e.userName||wt.name,wt.email=e.userEmail||wt.email,Sn.id=e.teamId,Sn.name=e.teamName||Sn.name,K.init(G.urlRoot,G.eventSounds))},Qu={run(e){e(Ei)}},Ei={init:Xu,config:G,fetch:Sl,user:wt,team:Sn,ui:Gu,utils:Yu,pages:qu,events:K,_internal:Ku,_functions:Uu,plugin:Qu,lib:zu};window.CTFd=Ei;const O=Ei;Ge.extend(Vn);const Ju=()=>{document.querySelectorAll("[data-time]").forEach(e=>{const t=e.getAttribute("data-time"),n=e.getAttribute("data-time-format")||"MMMM Do, h:mm:ss A";e.innerText=Ge(t).format(n)})},Zu=()=>{document.querySelectorAll(".form-control").forEach(e=>{e.addEventListener("onfocus",()=>{e.classList.remove("input-filled-invalid"),e.classList.add("input-filled-valid")}),e.addEventListener("onblur",()=>{e.nodeValue===""&&(e.classList.remove("input-filled-valid"),e.classList.remove("input-filled-invalid"))}),e.nodeValue&&e.classList.add("input-filled-valid")}),document.querySelectorAll(".page-select").forEach(e=>{e.addEventListener("change",t=>{var r;const n=new URL(window.location);n.searchParams.set("page",(r=t.target.value)!=null?r:"1"),window.location.href=n.toString()})})};var Qo={exports:{}};/*! lolight v1.4.0 - https://larsjung.de/lolight/ */(function(e,t){(function(n,r){e.exports=r()})(De,function(){function n(c){if(typeof c!="string")throw new Error("tok: no string");for(var l=[],h=s.length,m=!1;c;)for(var p=0;p<h;p+=1){var _=s[p][1].exec(c);if(_&&_.index===0){var v=s[p][0];if(v!=="rex"||!m){var b=_[0];v===d&&u.test(b)&&(v="key"),v==="spc"?0<=b.indexOf(`
`)&&(m=!1):m=v===g||v===d,c=c.slice(b.length),l.push([v,b]);break}}}return l}function r(c,l){if(typeof document<"u")l(document);else if(c)throw new Error("no doc")}function i(c){r(!0,function(l){var h=n(c.textContent);c.innerHTML="",h.forEach(function(m){var p=l.createElement("span");p.className="ll-"+m[0],p.textContent=m[1],c.appendChild(p)})})}function o(c){r(!0,function(l){[].forEach.call(l.querySelectorAll(c||".lolight"),function(h){i(h)})})}var a="_nam#2196f3}_num#ec407a}_str#43a047}_rex#ef6c00}_pct#666}_key#555;font-weight:bold}_com#aaa;font-style:italic}".replace(/_/g,".ll-").replace(/#/g,"{color:#"),u=/^(a(bstract|lias|nd|rguments|rray|s(m|sert)?|uto)|b(ase|egin|ool(ean)?|reak|yte)|c(ase|atch|har|hecked|lass|lone|ompl|onst|ontinue)|de(bugger|cimal|clare|f(ault|er)?|init|l(egate|ete)?)|do|double|e(cho|ls?if|lse(if)?|nd|nsure|num|vent|x(cept|ec|p(licit|ort)|te(nds|nsion|rn)))|f(allthrough|alse|inal(ly)?|ixed|loat|or(each)?|riend|rom|unc(tion)?)|global|goto|guard|i(f|mp(lements|licit|ort)|n(it|clude(_once)?|line|out|stanceof|t(erface|ernal)?)?|s)|l(ambda|et|ock|ong)|m(odule|utable)|NaN|n(amespace|ative|ext|ew|il|ot|ull)|o(bject|perator|r|ut|verride)|p(ackage|arams|rivate|rotected|rotocol|ublic)|r(aise|e(adonly|do|f|gister|peat|quire(_once)?|scue|strict|try|turn))|s(byte|ealed|elf|hort|igned|izeof|tatic|tring|truct|ubscript|uper|ynchronized|witch)|t(emplate|hen|his|hrows?|ransient|rue|ry|ype(alias|def|id|name|of))|u(n(checked|def(ined)?|ion|less|signed|til)|se|sing)|v(ar|irtual|oid|olatile)|w(char_t|hen|here|hile|ith)|xor|yield)$/,f="com",d="nam",g="num",s=[[g,/#([0-9a-f]{6}|[0-9a-f]{3})\b/],[f,/(\/\/|#).*?(?=\n|$)/],[f,/\/\*[\s\S]*?\*\//],[f,/<!--[\s\S]*?-->/],["rex",/\/(\\\/|[^\n])*?\//],["str",/(['"`])(\\\1|[\s\S])*?\1/],[g,/[+-]?([0-9]*\.?[0-9]+|[0-9]+\.?[0-9]*)([eE][+-]?[0-9]+)?/],["pct",/[\\.,:;+\-*\/=<>()[\]{}|?!&@~]/],["spc",/\s+/],[d,/[\w$]+/],["unk",/./]];return r(!1,function(c){var l=c.querySelector("head"),h=c.createElement("style");h.textContent=a,l.insertBefore(h,l.firstChild),/^(i|c|loade)/.test(c.readyState)?o():c.addEventListener("DOMContentLoaded",function(){o()})}),o.tok=n,o.el=i,o})})(Qo);const ef=Qo.exports,tf=()=>{(!O.config.themeSettings.hasOwnProperty("use_builtin_code_highlighter")||O.config.themeSettings.use_builtin_code_highlighter===!0)&&ef("pre code")};var re="top",fe="bottom",de="right",ie="left",qn="auto",Bt=[re,fe,de,ie],lt="start",Nt="end",Jo="clippingParents",wi="viewport",At="popper",Zo="reference",Hr=Bt.reduce(function(e,t){return e.concat([t+"-"+lt,t+"-"+Nt])},[]),Ai=[].concat(Bt,[qn]).reduce(function(e,t){return e.concat([t,t+"-"+lt,t+"-"+Nt])},[]),ea="beforeRead",ta="read",na="afterRead",ra="beforeMain",ia="main",sa="afterMain",oa="beforeWrite",aa="write",ca="afterWrite",la=[ea,ta,na,ra,ia,sa,oa,aa,ca];function Ce(e){return e?(e.nodeName||"").toLowerCase():null}function he(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function ut(e){var t=he(e).Element;return e instanceof t||e instanceof Element}function pe(e){var t=he(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function Ti(e){if(typeof ShadowRoot>"u")return!1;var t=he(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}function nf(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var r=t.styles[n]||{},i=t.attributes[n]||{},o=t.elements[n];!pe(o)||!Ce(o)||(Object.assign(o.style,r),Object.keys(i).forEach(function(a){var u=i[a];u===!1?o.removeAttribute(a):o.setAttribute(a,u===!0?"":u)}))})}function rf(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(r){var i=t.elements[r],o=t.attributes[r]||{},a=Object.keys(t.styles.hasOwnProperty(r)?t.styles[r]:n[r]),u=a.reduce(function(f,d){return f[d]="",f},{});!pe(i)||!Ce(i)||(Object.assign(i.style,u),Object.keys(o).forEach(function(f){i.removeAttribute(f)}))})}}const Si={name:"applyStyles",enabled:!0,phase:"write",fn:nf,effect:rf,requires:["computeStyles"]};function Se(e){return e.split("-")[0]}var it=Math.max,Ln=Math.min,Dt=Math.round;function Br(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function ua(){return!/^((?!chrome|android).)*safari/i.test(Br())}function Lt(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var r=e.getBoundingClientRect(),i=1,o=1;t&&pe(e)&&(i=e.offsetWidth>0&&Dt(r.width)/e.offsetWidth||1,o=e.offsetHeight>0&&Dt(r.height)/e.offsetHeight||1);var a=ut(e)?he(e):window,u=a.visualViewport,f=!ua()&&n,d=(r.left+(f&&u?u.offsetLeft:0))/i,g=(r.top+(f&&u?u.offsetTop:0))/o,s=r.width/i,c=r.height/o;return{width:s,height:c,top:g,right:d+s,bottom:g+c,left:d,x:d,y:g}}function Oi(e){var t=Lt(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function fa(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Ti(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function Pe(e){return he(e).getComputedStyle(e)}function sf(e){return["table","td","th"].indexOf(Ce(e))>=0}function ze(e){return((ut(e)?e.ownerDocument:e.document)||window.document).documentElement}function zn(e){return Ce(e)==="html"?e:e.assignedSlot||e.parentNode||(Ti(e)?e.host:null)||ze(e)}function ws(e){return!pe(e)||Pe(e).position==="fixed"?null:e.offsetParent}function of(e){var t=/firefox/i.test(Br()),n=/Trident/i.test(Br());if(n&&pe(e)){var r=Pe(e);if(r.position==="fixed")return null}var i=zn(e);for(Ti(i)&&(i=i.host);pe(i)&&["html","body"].indexOf(Ce(i))<0;){var o=Pe(i);if(o.transform!=="none"||o.perspective!=="none"||o.contain==="paint"||["transform","perspective"].indexOf(o.willChange)!==-1||t&&o.willChange==="filter"||t&&o.filter&&o.filter!=="none")return i;i=i.parentNode}return null}function rn(e){for(var t=he(e),n=ws(e);n&&sf(n)&&Pe(n).position==="static";)n=ws(n);return n&&(Ce(n)==="html"||Ce(n)==="body"&&Pe(n).position==="static")?t:n||of(e)||t}function xi(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function zt(e,t,n){return it(e,Ln(t,n))}function af(e,t,n){var r=zt(e,t,n);return r>n?n:r}function da(){return{top:0,right:0,bottom:0,left:0}}function ha(e){return Object.assign({},da(),e)}function pa(e,t){return t.reduce(function(n,r){return n[r]=e,n},{})}var cf=function(t,n){return t=typeof t=="function"?t(Object.assign({},n.rects,{placement:n.placement})):t,ha(typeof t!="number"?t:pa(t,Bt))};function lf(e){var t,n=e.state,r=e.name,i=e.options,o=n.elements.arrow,a=n.modifiersData.popperOffsets,u=Se(n.placement),f=xi(u),d=[ie,de].indexOf(u)>=0,g=d?"height":"width";if(!(!o||!a)){var s=cf(i.padding,n),c=Oi(o),l=f==="y"?re:ie,h=f==="y"?fe:de,m=n.rects.reference[g]+n.rects.reference[f]-a[f]-n.rects.popper[g],p=a[f]-n.rects.reference[f],_=rn(o),v=_?f==="y"?_.clientHeight||0:_.clientWidth||0:0,b=m/2-p/2,T=s[l],C=v-c[g]-s[h],D=v/2-c[g]/2+b,R=zt(T,D,C),B=f;n.modifiersData[r]=(t={},t[B]=R,t.centerOffset=R-D,t)}}function uf(e){var t=e.state,n=e.options,r=n.element,i=r===void 0?"[data-popper-arrow]":r;i!=null&&(typeof i=="string"&&(i=t.elements.popper.querySelector(i),!i)||!fa(t.elements.popper,i)||(t.elements.arrow=i))}const _a={name:"arrow",enabled:!0,phase:"main",fn:lf,effect:uf,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function It(e){return e.split("-")[1]}var ff={top:"auto",right:"auto",bottom:"auto",left:"auto"};function df(e,t){var n=e.x,r=e.y,i=t.devicePixelRatio||1;return{x:Dt(n*i)/i||0,y:Dt(r*i)/i||0}}function As(e){var t,n=e.popper,r=e.popperRect,i=e.placement,o=e.variation,a=e.offsets,u=e.position,f=e.gpuAcceleration,d=e.adaptive,g=e.roundOffsets,s=e.isFixed,c=a.x,l=c===void 0?0:c,h=a.y,m=h===void 0?0:h,p=typeof g=="function"?g({x:l,y:m}):{x:l,y:m};l=p.x,m=p.y;var _=a.hasOwnProperty("x"),v=a.hasOwnProperty("y"),b=ie,T=re,C=window;if(d){var D=rn(n),R="clientHeight",B="clientWidth";if(D===he(n)&&(D=ze(n),Pe(D).position!=="static"&&u==="absolute"&&(R="scrollHeight",B="scrollWidth")),D=D,i===re||(i===ie||i===de)&&o===Nt){T=fe;var x=s&&D===C&&C.visualViewport?C.visualViewport.height:D[R];m-=x-r.height,m*=f?1:-1}if(i===ie||(i===re||i===fe)&&o===Nt){b=de;var I=s&&D===C&&C.visualViewport?C.visualViewport.width:D[B];l-=I-r.width,l*=f?1:-1}}var F=Object.assign({position:u},d&&ff),ee=g===!0?df({x:l,y:m},he(n)):{x:l,y:m};if(l=ee.x,m=ee.y,f){var $;return Object.assign({},F,($={},$[T]=v?"0":"",$[b]=_?"0":"",$.transform=(C.devicePixelRatio||1)<=1?"translate("+l+"px, "+m+"px)":"translate3d("+l+"px, "+m+"px, 0)",$))}return Object.assign({},F,(t={},t[T]=v?m+"px":"",t[b]=_?l+"px":"",t.transform="",t))}function hf(e){var t=e.state,n=e.options,r=n.gpuAcceleration,i=r===void 0?!0:r,o=n.adaptive,a=o===void 0?!0:o,u=n.roundOffsets,f=u===void 0?!0:u,d={placement:Se(t.placement),variation:It(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,As(Object.assign({},d,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:f})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,As(Object.assign({},d,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:f})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}const Ci={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:hf,data:{}};var mn={passive:!0};function pf(e){var t=e.state,n=e.instance,r=e.options,i=r.scroll,o=i===void 0?!0:i,a=r.resize,u=a===void 0?!0:a,f=he(t.elements.popper),d=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&d.forEach(function(g){g.addEventListener("scroll",n.update,mn)}),u&&f.addEventListener("resize",n.update,mn),function(){o&&d.forEach(function(g){g.removeEventListener("scroll",n.update,mn)}),u&&f.removeEventListener("resize",n.update,mn)}}const $i={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:pf,data:{}};var _f={left:"right",right:"left",bottom:"top",top:"bottom"};function On(e){return e.replace(/left|right|bottom|top/g,function(t){return _f[t]})}var mf={start:"end",end:"start"};function Ts(e){return e.replace(/start|end/g,function(t){return mf[t]})}function Ni(e){var t=he(e),n=t.pageXOffset,r=t.pageYOffset;return{scrollLeft:n,scrollTop:r}}function Di(e){return Lt(ze(e)).left+Ni(e).scrollLeft}function gf(e,t){var n=he(e),r=ze(e),i=n.visualViewport,o=r.clientWidth,a=r.clientHeight,u=0,f=0;if(i){o=i.width,a=i.height;var d=ua();(d||!d&&t==="fixed")&&(u=i.offsetLeft,f=i.offsetTop)}return{width:o,height:a,x:u+Di(e),y:f}}function vf(e){var t,n=ze(e),r=Ni(e),i=(t=e.ownerDocument)==null?void 0:t.body,o=it(n.scrollWidth,n.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),a=it(n.scrollHeight,n.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),u=-r.scrollLeft+Di(e),f=-r.scrollTop;return Pe(i||n).direction==="rtl"&&(u+=it(n.clientWidth,i?i.clientWidth:0)-o),{width:o,height:a,x:u,y:f}}function Li(e){var t=Pe(e),n=t.overflow,r=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+i+r)}function ma(e){return["html","body","#document"].indexOf(Ce(e))>=0?e.ownerDocument.body:pe(e)&&Li(e)?e:ma(zn(e))}function Xt(e,t){var n;t===void 0&&(t=[]);var r=ma(e),i=r===((n=e.ownerDocument)==null?void 0:n.body),o=he(r),a=i?[o].concat(o.visualViewport||[],Li(r)?r:[]):r,u=t.concat(a);return i?u:u.concat(Xt(zn(a)))}function Vr(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function yf(e,t){var n=Lt(e,!1,t==="fixed");return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}function Ss(e,t,n){return t===wi?Vr(gf(e,n)):ut(t)?yf(t,n):Vr(vf(ze(e)))}function bf(e){var t=Xt(zn(e)),n=["absolute","fixed"].indexOf(Pe(e).position)>=0,r=n&&pe(e)?rn(e):e;return ut(r)?t.filter(function(i){return ut(i)&&fa(i,r)&&Ce(i)!=="body"}):[]}function Ef(e,t,n,r){var i=t==="clippingParents"?bf(e):[].concat(t),o=[].concat(i,[n]),a=o[0],u=o.reduce(function(f,d){var g=Ss(e,d,r);return f.top=it(g.top,f.top),f.right=Ln(g.right,f.right),f.bottom=Ln(g.bottom,f.bottom),f.left=it(g.left,f.left),f},Ss(e,a,r));return u.width=u.right-u.left,u.height=u.bottom-u.top,u.x=u.left,u.y=u.top,u}function ga(e){var t=e.reference,n=e.element,r=e.placement,i=r?Se(r):null,o=r?It(r):null,a=t.x+t.width/2-n.width/2,u=t.y+t.height/2-n.height/2,f;switch(i){case re:f={x:a,y:t.y-n.height};break;case fe:f={x:a,y:t.y+t.height};break;case de:f={x:t.x+t.width,y:u};break;case ie:f={x:t.x-n.width,y:u};break;default:f={x:t.x,y:t.y}}var d=i?xi(i):null;if(d!=null){var g=d==="y"?"height":"width";switch(o){case lt:f[d]=f[d]-(t[g]/2-n[g]/2);break;case Nt:f[d]=f[d]+(t[g]/2-n[g]/2);break}}return f}function Mt(e,t){t===void 0&&(t={});var n=t,r=n.placement,i=r===void 0?e.placement:r,o=n.strategy,a=o===void 0?e.strategy:o,u=n.boundary,f=u===void 0?Jo:u,d=n.rootBoundary,g=d===void 0?wi:d,s=n.elementContext,c=s===void 0?At:s,l=n.altBoundary,h=l===void 0?!1:l,m=n.padding,p=m===void 0?0:m,_=ha(typeof p!="number"?p:pa(p,Bt)),v=c===At?Zo:At,b=e.rects.popper,T=e.elements[h?v:c],C=Ef(ut(T)?T:T.contextElement||ze(e.elements.popper),f,g,a),D=Lt(e.elements.reference),R=ga({reference:D,element:b,strategy:"absolute",placement:i}),B=Vr(Object.assign({},b,R)),x=c===At?B:D,I={top:C.top-x.top+_.top,bottom:x.bottom-C.bottom+_.bottom,left:C.left-x.left+_.left,right:x.right-C.right+_.right},F=e.modifiersData.offset;if(c===At&&F){var ee=F[i];Object.keys(I).forEach(function($){var S=[de,fe].indexOf($)>=0?1:-1,w=[re,fe].indexOf($)>=0?"y":"x";I[$]+=ee[w]*S})}return I}function wf(e,t){t===void 0&&(t={});var n=t,r=n.placement,i=n.boundary,o=n.rootBoundary,a=n.padding,u=n.flipVariations,f=n.allowedAutoPlacements,d=f===void 0?Ai:f,g=It(r),s=g?u?Hr:Hr.filter(function(h){return It(h)===g}):Bt,c=s.filter(function(h){return d.indexOf(h)>=0});c.length===0&&(c=s);var l=c.reduce(function(h,m){return h[m]=Mt(e,{placement:m,boundary:i,rootBoundary:o,padding:a})[Se(m)],h},{});return Object.keys(l).sort(function(h,m){return l[h]-l[m]})}function Af(e){if(Se(e)===qn)return[];var t=On(e);return[Ts(e),t,Ts(t)]}function Tf(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var i=n.mainAxis,o=i===void 0?!0:i,a=n.altAxis,u=a===void 0?!0:a,f=n.fallbackPlacements,d=n.padding,g=n.boundary,s=n.rootBoundary,c=n.altBoundary,l=n.flipVariations,h=l===void 0?!0:l,m=n.allowedAutoPlacements,p=t.options.placement,_=Se(p),v=_===p,b=f||(v||!h?[On(p)]:Af(p)),T=[p].concat(b).reduce(function(ne,z){return ne.concat(Se(z)===qn?wf(t,{placement:z,boundary:g,rootBoundary:s,padding:d,flipVariations:h,allowedAutoPlacements:m}):z)},[]),C=t.rects.reference,D=t.rects.popper,R=new Map,B=!0,x=T[0],I=0;I<T.length;I++){var F=T[I],ee=Se(F),$=It(F)===lt,S=[re,fe].indexOf(ee)>=0,w=S?"width":"height",N=Mt(t,{placement:F,boundary:g,rootBoundary:s,altBoundary:c,padding:d}),A=S?$?de:ie:$?fe:re;C[w]>D[w]&&(A=On(A));var P=On(A),M=[];if(o&&M.push(N[ee]<=0),u&&M.push(N[A]<=0,N[P]<=0),M.every(function(ne){return ne})){x=F,B=!1;break}R.set(F,M)}if(B)for(var H=h?3:1,j=function(z){var ve=T.find(function(Ee){var oe=R.get(Ee);if(oe)return oe.slice(0,z).every(function(X){return X})});if(ve)return x=ve,"break"},V=H;V>0;V--){var U=j(V);if(U==="break")break}t.placement!==x&&(t.modifiersData[r]._skip=!0,t.placement=x,t.reset=!0)}}const va={name:"flip",enabled:!0,phase:"main",fn:Tf,requiresIfExists:["offset"],data:{_skip:!1}};function Os(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function xs(e){return[re,de,fe,ie].some(function(t){return e[t]>=0})}function Sf(e){var t=e.state,n=e.name,r=t.rects.reference,i=t.rects.popper,o=t.modifiersData.preventOverflow,a=Mt(t,{elementContext:"reference"}),u=Mt(t,{altBoundary:!0}),f=Os(a,r),d=Os(u,i,o),g=xs(f),s=xs(d);t.modifiersData[n]={referenceClippingOffsets:f,popperEscapeOffsets:d,isReferenceHidden:g,hasPopperEscaped:s},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":g,"data-popper-escaped":s})}const ya={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Sf};function Of(e,t,n){var r=Se(e),i=[ie,re].indexOf(r)>=0?-1:1,o=typeof n=="function"?n(Object.assign({},t,{placement:e})):n,a=o[0],u=o[1];return a=a||0,u=(u||0)*i,[ie,de].indexOf(r)>=0?{x:u,y:a}:{x:a,y:u}}function xf(e){var t=e.state,n=e.options,r=e.name,i=n.offset,o=i===void 0?[0,0]:i,a=Ai.reduce(function(g,s){return g[s]=Of(s,t.rects,o),g},{}),u=a[t.placement],f=u.x,d=u.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=f,t.modifiersData.popperOffsets.y+=d),t.modifiersData[r]=a}const ba={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:xf};function Cf(e){var t=e.state,n=e.name;t.modifiersData[n]=ga({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}const Ii={name:"popperOffsets",enabled:!0,phase:"read",fn:Cf,data:{}};function $f(e){return e==="x"?"y":"x"}function Nf(e){var t=e.state,n=e.options,r=e.name,i=n.mainAxis,o=i===void 0?!0:i,a=n.altAxis,u=a===void 0?!1:a,f=n.boundary,d=n.rootBoundary,g=n.altBoundary,s=n.padding,c=n.tether,l=c===void 0?!0:c,h=n.tetherOffset,m=h===void 0?0:h,p=Mt(t,{boundary:f,rootBoundary:d,padding:s,altBoundary:g}),_=Se(t.placement),v=It(t.placement),b=!v,T=xi(_),C=$f(T),D=t.modifiersData.popperOffsets,R=t.rects.reference,B=t.rects.popper,x=typeof m=="function"?m(Object.assign({},t.rects,{placement:t.placement})):m,I=typeof x=="number"?{mainAxis:x,altAxis:x}:Object.assign({mainAxis:0,altAxis:0},x),F=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,ee={x:0,y:0};if(!!D){if(o){var $,S=T==="y"?re:ie,w=T==="y"?fe:de,N=T==="y"?"height":"width",A=D[T],P=A+p[S],M=A-p[w],H=l?-B[N]/2:0,j=v===lt?R[N]:B[N],V=v===lt?-B[N]:-R[N],U=t.elements.arrow,ne=l&&U?Oi(U):{width:0,height:0},z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:da(),ve=z[S],Ee=z[w],oe=zt(0,R[N],ne[N]),X=b?R[N]/2-H-oe-ve-I.mainAxis:j-oe-ve-I.mainAxis,we=b?-R[N]/2+H+oe+Ee+I.mainAxis:V+oe+Ee+I.mainAxis,Je=t.elements.arrow&&rn(t.elements.arrow),yt=Je?T==="y"?Je.clientTop||0:Je.clientLeft||0:0,rs=($=F==null?void 0:F[T])!=null?$:0,ll=A+X-rs-yt,ul=A+we-rs,is=zt(l?Ln(P,ll):P,A,l?it(M,ul):M);D[T]=is,ee[T]=is-A}if(u){var ss,fl=T==="x"?re:ie,dl=T==="x"?fe:de,Ze=D[C],_n=C==="y"?"height":"width",os=Ze+p[fl],as=Ze-p[dl],hr=[re,ie].indexOf(_)!==-1,cs=(ss=F==null?void 0:F[C])!=null?ss:0,ls=hr?os:Ze-R[_n]-B[_n]-cs+I.altAxis,us=hr?Ze+R[_n]+B[_n]-cs-I.altAxis:as,fs=l&&hr?af(ls,Ze,us):zt(l?ls:os,Ze,l?us:as);D[C]=fs,ee[C]=fs-Ze}t.modifiersData[r]=ee}}const Ea={name:"preventOverflow",enabled:!0,phase:"main",fn:Nf,requiresIfExists:["offset"]};function Df(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function Lf(e){return e===he(e)||!pe(e)?Ni(e):Df(e)}function If(e){var t=e.getBoundingClientRect(),n=Dt(t.width)/e.offsetWidth||1,r=Dt(t.height)/e.offsetHeight||1;return n!==1||r!==1}function Mf(e,t,n){n===void 0&&(n=!1);var r=pe(t),i=pe(t)&&If(t),o=ze(t),a=Lt(e,i,n),u={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(r||!r&&!n)&&((Ce(t)!=="body"||Li(o))&&(u=Lf(t)),pe(t)?(f=Lt(t,!0),f.x+=t.clientLeft,f.y+=t.clientTop):o&&(f.x=Di(o))),{x:a.left+u.scrollLeft-f.x,y:a.top+u.scrollTop-f.y,width:a.width,height:a.height}}function kf(e){var t=new Map,n=new Set,r=[];e.forEach(function(o){t.set(o.name,o)});function i(o){n.add(o.name);var a=[].concat(o.requires||[],o.requiresIfExists||[]);a.forEach(function(u){if(!n.has(u)){var f=t.get(u);f&&i(f)}}),r.push(o)}return e.forEach(function(o){n.has(o.name)||i(o)}),r}function Pf(e){var t=kf(e);return la.reduce(function(n,r){return n.concat(t.filter(function(i){return i.phase===r}))},[])}function Rf(e){var t;return function(){return t||(t=new Promise(function(n){Promise.resolve().then(function(){t=void 0,n(e())})})),t}}function Hf(e){var t=e.reduce(function(n,r){var i=n[r.name];return n[r.name]=i?Object.assign({},i,r,{options:Object.assign({},i.options,r.options),data:Object.assign({},i.data,r.data)}):r,n},{});return Object.keys(t).map(function(n){return t[n]})}var Cs={placement:"bottom",modifiers:[],strategy:"absolute"};function $s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(r){return!(r&&typeof r.getBoundingClientRect=="function")})}function Xn(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,r=n===void 0?[]:n,i=t.defaultOptions,o=i===void 0?Cs:i;return function(u,f,d){d===void 0&&(d=o);var g={placement:"bottom",orderedModifiers:[],options:Object.assign({},Cs,o),modifiersData:{},elements:{reference:u,popper:f},attributes:{},styles:{}},s=[],c=!1,l={state:g,setOptions:function(_){var v=typeof _=="function"?_(g.options):_;m(),g.options=Object.assign({},o,g.options,v),g.scrollParents={reference:ut(u)?Xt(u):u.contextElement?Xt(u.contextElement):[],popper:Xt(f)};var b=Pf(Hf([].concat(r,g.options.modifiers)));return g.orderedModifiers=b.filter(function(T){return T.enabled}),h(),l.update()},forceUpdate:function(){if(!c){var _=g.elements,v=_.reference,b=_.popper;if(!!$s(v,b)){g.rects={reference:Mf(v,rn(b),g.options.strategy==="fixed"),popper:Oi(b)},g.reset=!1,g.placement=g.options.placement,g.orderedModifiers.forEach(function(I){return g.modifiersData[I.name]=Object.assign({},I.data)});for(var T=0;T<g.orderedModifiers.length;T++){if(g.reset===!0){g.reset=!1,T=-1;continue}var C=g.orderedModifiers[T],D=C.fn,R=C.options,B=R===void 0?{}:R,x=C.name;typeof D=="function"&&(g=D({state:g,options:B,name:x,instance:l})||g)}}}},update:Rf(function(){return new Promise(function(p){l.forceUpdate(),p(g)})}),destroy:function(){m(),c=!0}};if(!$s(u,f))return l;l.setOptions(d).then(function(p){!c&&d.onFirstUpdate&&d.onFirstUpdate(p)});function h(){g.orderedModifiers.forEach(function(p){var _=p.name,v=p.options,b=v===void 0?{}:v,T=p.effect;if(typeof T=="function"){var C=T({state:g,name:_,instance:l,options:b}),D=function(){};s.push(C||D)}})}function m(){s.forEach(function(p){return p()}),s=[]}return l}}var Bf=Xn(),Vf=[$i,Ii,Ci,Si],Ff=Xn({defaultModifiers:Vf}),jf=[$i,Ii,Ci,Si,ba,va,Ea,_a,ya],Mi=Xn({defaultModifiers:jf});const wa=Object.freeze(Object.defineProperty({__proto__:null,popperGenerator:Xn,detectOverflow:Mt,createPopperBase:Bf,createPopper:Mi,createPopperLite:Ff,top:re,bottom:fe,right:de,left:ie,auto:qn,basePlacements:Bt,start:lt,end:Nt,clippingParents:Jo,viewport:wi,popper:At,reference:Zo,variationPlacements:Hr,placements:Ai,beforeRead:ea,read:ta,afterRead:na,beforeMain:ra,main:ia,afterMain:sa,beforeWrite:oa,write:aa,afterWrite:ca,modifierPhases:la,applyStyles:Si,arrow:_a,computeStyles:Ci,eventListeners:$i,flip:va,hide:ya,offset:ba,popperOffsets:Ii,preventOverflow:Ea},Symbol.toStringTag,{value:"Module"}));/*!
  * Bootstrap v5.3.3 (https://getbootstrap.com/)
  * Copyright 2011-2024 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */const Be=new Map,vr={set(e,t,n){Be.has(e)||Be.set(e,new Map);const r=Be.get(e);if(!r.has(t)&&r.size!==0){console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(r.keys())[0]}.`);return}r.set(t,n)},get(e,t){return Be.has(e)&&Be.get(e).get(t)||null},remove(e,t){if(!Be.has(e))return;const n=Be.get(e);n.delete(t),n.size===0&&Be.delete(e)}},Wf=1e6,Kf=1e3,Fr="transitionend",Aa=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,(t,n)=>`#${CSS.escape(n)}`)),e),Uf=e=>e==null?`${e}`:Object.prototype.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase(),Gf=e=>{do e+=Math.floor(Math.random()*Wf);while(document.getElementById(e));return e},Yf=e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:n}=window.getComputedStyle(e);const r=Number.parseFloat(t),i=Number.parseFloat(n);return!r&&!i?0:(t=t.split(",")[0],n=n.split(",")[0],(Number.parseFloat(t)+Number.parseFloat(n))*Kf)},Ta=e=>{e.dispatchEvent(new Event(Fr))},Le=e=>!e||typeof e!="object"?!1:(typeof e.jquery<"u"&&(e=e[0]),typeof e.nodeType<"u"),Fe=e=>Le(e)?e.jquery?e[0]:e:typeof e=="string"&&e.length>0?document.querySelector(Aa(e)):null,Vt=e=>{if(!Le(e)||e.getClientRects().length===0)return!1;const t=getComputedStyle(e).getPropertyValue("visibility")==="visible",n=e.closest("details:not([open])");if(!n)return t;if(n!==e){const r=e.closest("summary");if(r&&r.parentNode!==n||r===null)return!1}return t},je=e=>!e||e.nodeType!==Node.ELEMENT_NODE||e.classList.contains("disabled")?!0:typeof e.disabled<"u"?e.disabled:e.hasAttribute("disabled")&&e.getAttribute("disabled")!=="false",Sa=e=>{if(!document.documentElement.attachShadow)return null;if(typeof e.getRootNode=="function"){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?Sa(e.parentNode):null},In=()=>{},sn=e=>{e.offsetHeight},Oa=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,yr=[],qf=e=>{document.readyState==="loading"?(yr.length||document.addEventListener("DOMContentLoaded",()=>{for(const t of yr)t()}),yr.push(e)):e()},_e=()=>document.documentElement.dir==="rtl",ge=e=>{qf(()=>{const t=Oa();if(t){const n=e.NAME,r=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=()=>(t.fn[n]=r,e.jQueryInterface)}})},ce=(e,t=[],n=e)=>typeof e=="function"?e(...t):n,xa=(e,t,n=!0)=>{if(!n){ce(e);return}const r=5,i=Yf(t)+r;let o=!1;const a=({target:u})=>{u===t&&(o=!0,t.removeEventListener(Fr,a),ce(e))};t.addEventListener(Fr,a),setTimeout(()=>{o||Ta(t)},i)},ki=(e,t,n,r)=>{const i=e.length;let o=e.indexOf(t);return o===-1?!n&&r?e[i-1]:e[0]:(o+=n?1:-1,r&&(o=(o+i)%i),e[Math.max(0,Math.min(o,i-1))])},zf=/[^.]*(?=\..*)\.|.*/,Xf=/\..*/,Qf=/::\d+$/,br={};let Ns=1;const Ca={mouseenter:"mouseover",mouseleave:"mouseout"},Jf=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function $a(e,t){return t&&`${t}::${Ns++}`||e.uidEvent||Ns++}function Na(e){const t=$a(e);return e.uidEvent=t,br[t]=br[t]||{},br[t]}function Zf(e,t){return function n(r){return Pi(r,{delegateTarget:e}),n.oneOff&&y.off(e,r.type,t),t.apply(e,[r])}}function ed(e,t,n){return function r(i){const o=e.querySelectorAll(t);for(let{target:a}=i;a&&a!==this;a=a.parentNode)for(const u of o)if(u===a)return Pi(i,{delegateTarget:a}),r.oneOff&&y.off(e,i.type,t,n),n.apply(a,[i])}}function Da(e,t,n=null){return Object.values(e).find(r=>r.callable===t&&r.delegationSelector===n)}function La(e,t,n){const r=typeof t=="string",i=r?n:t||n;let o=Ia(e);return Jf.has(o)||(o=e),[r,i,o]}function Ds(e,t,n,r,i){if(typeof t!="string"||!e)return;let[o,a,u]=La(t,n,r);t in Ca&&(a=(h=>function(m){if(!m.relatedTarget||m.relatedTarget!==m.delegateTarget&&!m.delegateTarget.contains(m.relatedTarget))return h.call(this,m)})(a));const f=Na(e),d=f[u]||(f[u]={}),g=Da(d,a,o?n:null);if(g){g.oneOff=g.oneOff&&i;return}const s=$a(a,t.replace(zf,"")),c=o?ed(e,n,a):Zf(e,a);c.delegationSelector=o?n:null,c.callable=a,c.oneOff=i,c.uidEvent=s,d[s]=c,e.addEventListener(u,c,o)}function jr(e,t,n,r,i){const o=Da(t[n],r,i);!o||(e.removeEventListener(n,o,Boolean(i)),delete t[n][o.uidEvent])}function td(e,t,n,r){const i=t[n]||{};for(const[o,a]of Object.entries(i))o.includes(r)&&jr(e,t,n,a.callable,a.delegationSelector)}function Ia(e){return e=e.replace(Xf,""),Ca[e]||e}const y={on(e,t,n,r){Ds(e,t,n,r,!1)},one(e,t,n,r){Ds(e,t,n,r,!0)},off(e,t,n,r){if(typeof t!="string"||!e)return;const[i,o,a]=La(t,n,r),u=a!==t,f=Na(e),d=f[a]||{},g=t.startsWith(".");if(typeof o<"u"){if(!Object.keys(d).length)return;jr(e,f,a,o,i?n:null);return}if(g)for(const s of Object.keys(f))td(e,f,s,t.slice(1));for(const[s,c]of Object.entries(d)){const l=s.replace(Qf,"");(!u||t.includes(l))&&jr(e,f,a,c.callable,c.delegationSelector)}},trigger(e,t,n){if(typeof t!="string"||!e)return null;const r=Oa(),i=Ia(t),o=t!==i;let a=null,u=!0,f=!0,d=!1;o&&r&&(a=r.Event(t,n),r(e).trigger(a),u=!a.isPropagationStopped(),f=!a.isImmediatePropagationStopped(),d=a.isDefaultPrevented());const g=Pi(new Event(t,{bubbles:u,cancelable:!0}),n);return d&&g.preventDefault(),f&&e.dispatchEvent(g),g.defaultPrevented&&a&&a.preventDefault(),g}};function Pi(e,t={}){for(const[n,r]of Object.entries(t))try{e[n]=r}catch{Object.defineProperty(e,n,{configurable:!0,get(){return r}})}return e}function Ls(e){if(e==="true")return!0;if(e==="false")return!1;if(e===Number(e).toString())return Number(e);if(e===""||e==="null")return null;if(typeof e!="string")return e;try{return JSON.parse(decodeURIComponent(e))}catch{return e}}function Er(e){return e.replace(/[A-Z]/g,t=>`-${t.toLowerCase()}`)}const Ie={setDataAttribute(e,t,n){e.setAttribute(`data-bs-${Er(t)}`,n)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${Er(t)}`)},getDataAttributes(e){if(!e)return{};const t={},n=Object.keys(e.dataset).filter(r=>r.startsWith("bs")&&!r.startsWith("bsConfig"));for(const r of n){let i=r.replace(/^bs/,"");i=i.charAt(0).toLowerCase()+i.slice(1,i.length),t[i]=Ls(e.dataset[r])}return t},getDataAttribute(e,t){return Ls(e.getAttribute(`data-bs-${Er(t)}`))}};class on{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,n){const r=Le(n)?Ie.getDataAttribute(n,"config"):{};return{...this.constructor.Default,...typeof r=="object"?r:{},...Le(n)?Ie.getDataAttributes(n):{},...typeof t=="object"?t:{}}}_typeCheckConfig(t,n=this.constructor.DefaultType){for(const[r,i]of Object.entries(n)){const o=t[r],a=Le(o)?"element":Uf(o);if(!new RegExp(i).test(a))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${r}" provided type "${a}" but expected type "${i}".`)}}}const nd="5.3.3";class be extends on{constructor(t,n){super(),t=Fe(t),t&&(this._element=t,this._config=this._getConfig(n),vr.set(this._element,this.constructor.DATA_KEY,this))}dispose(){vr.remove(this._element,this.constructor.DATA_KEY),y.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,n,r=!0){xa(t,n,r)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return vr.get(Fe(t),this.DATA_KEY)}static getOrCreateInstance(t,n={}){return this.getInstance(t)||new this(t,typeof n=="object"?n:null)}static get VERSION(){return nd}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const wr=e=>{let t=e.getAttribute("data-bs-target");if(!t||t==="#"){let n=e.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),t=n&&n!=="#"?n.trim():null}return t?t.split(",").map(n=>Aa(n)).join(","):null},L={find(e,t=document.documentElement){return[].concat(...Element.prototype.querySelectorAll.call(t,e))},findOne(e,t=document.documentElement){return Element.prototype.querySelector.call(t,e)},children(e,t){return[].concat(...e.children).filter(n=>n.matches(t))},parents(e,t){const n=[];let r=e.parentNode.closest(t);for(;r;)n.push(r),r=r.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(e){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map(n=>`${n}:not([tabindex^="-"])`).join(",");return this.find(t,e).filter(n=>!je(n)&&Vt(n))},getSelectorFromElement(e){const t=wr(e);return t&&L.findOne(t)?t:null},getElementFromSelector(e){const t=wr(e);return t?L.findOne(t):null},getMultipleElementsFromSelector(e){const t=wr(e);return t?L.find(t):[]}},Qn=(e,t="hide")=>{const n=`click.dismiss${e.EVENT_KEY}`,r=e.NAME;y.on(document,n,`[data-bs-dismiss="${r}"]`,function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),je(this))return;const o=L.getElementFromSelector(this)||this.closest(`.${r}`);e.getOrCreateInstance(o)[t]()})},rd="alert",id="bs.alert",Ma=`.${id}`,sd=`close${Ma}`,od=`closed${Ma}`,ad="fade",cd="show";class an extends be{static get NAME(){return rd}close(){if(y.trigger(this._element,sd).defaultPrevented)return;this._element.classList.remove(cd);const n=this._element.classList.contains(ad);this._queueCallback(()=>this._destroyElement(),this._element,n)}_destroyElement(){this._element.remove(),y.trigger(this._element,od),this.dispose()}static jQueryInterface(t){return this.each(function(){const n=an.getOrCreateInstance(this);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t](this)}})}}Qn(an,"close");ge(an);const ld="button",ud="bs.button",fd=`.${ud}`,dd=".data-api",hd="active",Is='[data-bs-toggle="button"]',pd=`click${fd}${dd}`;class Jn extends be{static get NAME(){return ld}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle(hd))}static jQueryInterface(t){return this.each(function(){const n=Jn.getOrCreateInstance(this);t==="toggle"&&n[t]()})}}y.on(document,pd,Is,e=>{e.preventDefault();const t=e.target.closest(Is);Jn.getOrCreateInstance(t).toggle()});ge(Jn);const _d="swipe",Ft=".bs.swipe",md=`touchstart${Ft}`,gd=`touchmove${Ft}`,vd=`touchend${Ft}`,yd=`pointerdown${Ft}`,bd=`pointerup${Ft}`,Ed="touch",wd="pen",Ad="pointer-event",Td=40,Sd={endCallback:null,leftCallback:null,rightCallback:null},Od={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class Mn extends on{constructor(t,n){super(),this._element=t,!(!t||!Mn.isSupported())&&(this._config=this._getConfig(n),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return Sd}static get DefaultType(){return Od}static get NAME(){return _d}dispose(){y.off(this._element,Ft)}_start(t){if(!this._supportPointerEvents){this._deltaX=t.touches[0].clientX;return}this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX)}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),ce(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=Td)return;const n=t/this._deltaX;this._deltaX=0,n&&ce(n>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(y.on(this._element,yd,t=>this._start(t)),y.on(this._element,bd,t=>this._end(t)),this._element.classList.add(Ad)):(y.on(this._element,md,t=>this._start(t)),y.on(this._element,gd,t=>this._move(t)),y.on(this._element,vd,t=>this._end(t)))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&(t.pointerType===wd||t.pointerType===Ed)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const xd="carousel",Cd="bs.carousel",Xe=`.${Cd}`,ka=".data-api",$d="ArrowLeft",Nd="ArrowRight",Dd=500,Kt="next",bt="prev",Tt="left",xn="right",Ld=`slide${Xe}`,Ar=`slid${Xe}`,Id=`keydown${Xe}`,Md=`mouseenter${Xe}`,kd=`mouseleave${Xe}`,Pd=`dragstart${Xe}`,Rd=`load${Xe}${ka}`,Hd=`click${Xe}${ka}`,Pa="carousel",gn="active",Bd="slide",Vd="carousel-item-end",Fd="carousel-item-start",jd="carousel-item-next",Wd="carousel-item-prev",Ra=".active",Ha=".carousel-item",Kd=Ra+Ha,Ud=".carousel-item img",Gd=".carousel-indicators",Yd="[data-bs-slide], [data-bs-slide-to]",qd='[data-bs-ride="carousel"]',zd={[$d]:xn,[Nd]:Tt},Xd={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Qd={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class cn extends be{constructor(t,n){super(t,n),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=L.findOne(Gd,this._element),this._addEventListeners(),this._config.ride===Pa&&this.cycle()}static get Default(){return Xd}static get DefaultType(){return Qd}static get NAME(){return xd}next(){this._slide(Kt)}nextWhenVisible(){!document.hidden&&Vt(this._element)&&this.next()}prev(){this._slide(bt)}pause(){this._isSliding&&Ta(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval(()=>this.nextWhenVisible(),this._config.interval)}_maybeEnableCycle(){if(!!this._config.ride){if(this._isSliding){y.one(this._element,Ar,()=>this.cycle());return}this.cycle()}}to(t){const n=this._getItems();if(t>n.length-1||t<0)return;if(this._isSliding){y.one(this._element,Ar,()=>this.to(t));return}const r=this._getItemIndex(this._getActive());if(r===t)return;const i=t>r?Kt:bt;this._slide(i,n[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&y.on(this._element,Id,t=>this._keydown(t)),this._config.pause==="hover"&&(y.on(this._element,Md,()=>this.pause()),y.on(this._element,kd,()=>this._maybeEnableCycle())),this._config.touch&&Mn.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const r of L.find(Ud,this._element))y.on(r,Pd,i=>i.preventDefault());const n={leftCallback:()=>this._slide(this._directionToOrder(Tt)),rightCallback:()=>this._slide(this._directionToOrder(xn)),endCallback:()=>{this._config.pause==="hover"&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout(()=>this._maybeEnableCycle(),Dd+this._config.interval))}};this._swipeHelper=new Mn(this._element,n)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const n=zd[t.key];n&&(t.preventDefault(),this._slide(this._directionToOrder(n)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const n=L.findOne(Ra,this._indicatorsElement);n.classList.remove(gn),n.removeAttribute("aria-current");const r=L.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);r&&(r.classList.add(gn),r.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const n=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=n||this._config.defaultInterval}_slide(t,n=null){if(this._isSliding)return;const r=this._getActive(),i=t===Kt,o=n||ki(this._getItems(),r,i,this._config.wrap);if(o===r)return;const a=this._getItemIndex(o),u=l=>y.trigger(this._element,l,{relatedTarget:o,direction:this._orderToDirection(t),from:this._getItemIndex(r),to:a});if(u(Ld).defaultPrevented||!r||!o)return;const d=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(a),this._activeElement=o;const g=i?Fd:Vd,s=i?jd:Wd;o.classList.add(s),sn(o),r.classList.add(g),o.classList.add(g);const c=()=>{o.classList.remove(g,s),o.classList.add(gn),r.classList.remove(gn,s,g),this._isSliding=!1,u(Ar)};this._queueCallback(c,r,this._isAnimated()),d&&this.cycle()}_isAnimated(){return this._element.classList.contains(Bd)}_getActive(){return L.findOne(Kd,this._element)}_getItems(){return L.find(Ha,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return _e()?t===Tt?bt:Kt:t===Tt?Kt:bt}_orderToDirection(t){return _e()?t===bt?Tt:xn:t===bt?xn:Tt}static jQueryInterface(t){return this.each(function(){const n=cn.getOrCreateInstance(this,t);if(typeof t=="number"){n.to(t);return}if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t]()}})}}y.on(document,Hd,Yd,function(e){const t=L.getElementFromSelector(this);if(!t||!t.classList.contains(Pa))return;e.preventDefault();const n=cn.getOrCreateInstance(t),r=this.getAttribute("data-bs-slide-to");if(r){n.to(r),n._maybeEnableCycle();return}if(Ie.getDataAttribute(this,"slide")==="next"){n.next(),n._maybeEnableCycle();return}n.prev(),n._maybeEnableCycle()});y.on(window,Rd,()=>{const e=L.find(qd);for(const t of e)cn.getOrCreateInstance(t)});ge(cn);const Jd="collapse",Zd="bs.collapse",ln=`.${Zd}`,eh=".data-api",th=`show${ln}`,nh=`shown${ln}`,rh=`hide${ln}`,ih=`hidden${ln}`,sh=`click${ln}${eh}`,Tr="show",Ot="collapse",vn="collapsing",oh="collapsed",ah=`:scope .${Ot} .${Ot}`,ch="collapse-horizontal",lh="width",uh="height",fh=".collapse.show, .collapse.collapsing",Wr='[data-bs-toggle="collapse"]',dh={parent:null,toggle:!0},hh={parent:"(null|element)",toggle:"boolean"};class kt extends be{constructor(t,n){super(t,n),this._isTransitioning=!1,this._triggerArray=[];const r=L.find(Wr);for(const i of r){const o=L.getSelectorFromElement(i),a=L.find(o).filter(u=>u===this._element);o!==null&&a.length&&this._triggerArray.push(i)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return dh}static get DefaultType(){return hh}static get NAME(){return Jd}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(fh).filter(u=>u!==this._element).map(u=>kt.getOrCreateInstance(u,{toggle:!1}))),t.length&&t[0]._isTransitioning||y.trigger(this._element,th).defaultPrevented)return;for(const u of t)u.hide();const r=this._getDimension();this._element.classList.remove(Ot),this._element.classList.add(vn),this._element.style[r]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const i=()=>{this._isTransitioning=!1,this._element.classList.remove(vn),this._element.classList.add(Ot,Tr),this._element.style[r]="",y.trigger(this._element,nh)},a=`scroll${r[0].toUpperCase()+r.slice(1)}`;this._queueCallback(i,this._element,!0),this._element.style[r]=`${this._element[a]}px`}hide(){if(this._isTransitioning||!this._isShown()||y.trigger(this._element,rh).defaultPrevented)return;const n=this._getDimension();this._element.style[n]=`${this._element.getBoundingClientRect()[n]}px`,sn(this._element),this._element.classList.add(vn),this._element.classList.remove(Ot,Tr);for(const i of this._triggerArray){const o=L.getElementFromSelector(i);o&&!this._isShown(o)&&this._addAriaAndCollapsedClass([i],!1)}this._isTransitioning=!0;const r=()=>{this._isTransitioning=!1,this._element.classList.remove(vn),this._element.classList.add(Ot),y.trigger(this._element,ih)};this._element.style[n]="",this._queueCallback(r,this._element,!0)}_isShown(t=this._element){return t.classList.contains(Tr)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=Fe(t.parent),t}_getDimension(){return this._element.classList.contains(ch)?lh:uh}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(Wr);for(const n of t){const r=L.getElementFromSelector(n);r&&this._addAriaAndCollapsedClass([n],this._isShown(r))}}_getFirstLevelChildren(t){const n=L.find(ah,this._config.parent);return L.find(t,this._config.parent).filter(r=>!n.includes(r))}_addAriaAndCollapsedClass(t,n){if(!!t.length)for(const r of t)r.classList.toggle(oh,!n),r.setAttribute("aria-expanded",n)}static jQueryInterface(t){const n={};return typeof t=="string"&&/show|hide/.test(t)&&(n.toggle=!1),this.each(function(){const r=kt.getOrCreateInstance(this,n);if(typeof t=="string"){if(typeof r[t]>"u")throw new TypeError(`No method named "${t}"`);r[t]()}})}}y.on(document,sh,Wr,function(e){(e.target.tagName==="A"||e.delegateTarget&&e.delegateTarget.tagName==="A")&&e.preventDefault();for(const t of L.getMultipleElementsFromSelector(this))kt.getOrCreateInstance(t,{toggle:!1}).toggle()});ge(kt);const Ms="dropdown",ph="bs.dropdown",mt=`.${ph}`,Ri=".data-api",_h="Escape",ks="Tab",mh="ArrowUp",Ps="ArrowDown",gh=2,vh=`hide${mt}`,yh=`hidden${mt}`,bh=`show${mt}`,Eh=`shown${mt}`,Ba=`click${mt}${Ri}`,Va=`keydown${mt}${Ri}`,wh=`keyup${mt}${Ri}`,St="show",Ah="dropup",Th="dropend",Sh="dropstart",Oh="dropup-center",xh="dropdown-center",nt='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',Ch=`${nt}.${St}`,Cn=".dropdown-menu",$h=".navbar",Nh=".navbar-nav",Dh=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",Lh=_e()?"top-end":"top-start",Ih=_e()?"top-start":"top-end",Mh=_e()?"bottom-end":"bottom-start",kh=_e()?"bottom-start":"bottom-end",Ph=_e()?"left-start":"right-start",Rh=_e()?"right-start":"left-start",Hh="top",Bh="bottom",Vh={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Fh={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Oe extends be{constructor(t,n){super(t,n),this._popper=null,this._parent=this._element.parentNode,this._menu=L.next(this._element,Cn)[0]||L.prev(this._element,Cn)[0]||L.findOne(Cn,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Vh}static get DefaultType(){return Fh}static get NAME(){return Ms}toggle(){return this._isShown()?this.hide():this.show()}show(){if(je(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!y.trigger(this._element,bh,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(Nh))for(const r of[].concat(...document.body.children))y.on(r,"mouseover",In);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(St),this._element.classList.add(St),y.trigger(this._element,Eh,t)}}hide(){if(je(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!y.trigger(this._element,vh,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const r of[].concat(...document.body.children))y.off(r,"mouseover",In);this._popper&&this._popper.destroy(),this._menu.classList.remove(St),this._element.classList.remove(St),this._element.setAttribute("aria-expanded","false"),Ie.removeDataAttribute(this._menu,"popper"),y.trigger(this._element,yh,t)}}_getConfig(t){if(t=super._getConfig(t),typeof t.reference=="object"&&!Le(t.reference)&&typeof t.reference.getBoundingClientRect!="function")throw new TypeError(`${Ms.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(typeof wa>"u")throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;this._config.reference==="parent"?t=this._parent:Le(this._config.reference)?t=Fe(this._config.reference):typeof this._config.reference=="object"&&(t=this._config.reference);const n=this._getPopperConfig();this._popper=Mi(t,this._menu,n)}_isShown(){return this._menu.classList.contains(St)}_getPlacement(){const t=this._parent;if(t.classList.contains(Th))return Ph;if(t.classList.contains(Sh))return Rh;if(t.classList.contains(Oh))return Hh;if(t.classList.contains(xh))return Bh;const n=getComputedStyle(this._menu).getPropertyValue("--bs-position").trim()==="end";return t.classList.contains(Ah)?n?Ih:Lh:n?kh:Mh}_detectNavbar(){return this._element.closest($h)!==null}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(n=>Number.parseInt(n,10)):typeof t=="function"?n=>t(n,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||this._config.display==="static")&&(Ie.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...ce(this._config.popperConfig,[t])}}_selectMenuItem({key:t,target:n}){const r=L.find(Dh,this._menu).filter(i=>Vt(i));!r.length||ki(r,n,t===Ps,!r.includes(n)).focus()}static jQueryInterface(t){return this.each(function(){const n=Oe.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t]()}})}static clearMenus(t){if(t.button===gh||t.type==="keyup"&&t.key!==ks)return;const n=L.find(Ch);for(const r of n){const i=Oe.getInstance(r);if(!i||i._config.autoClose===!1)continue;const o=t.composedPath(),a=o.includes(i._menu);if(o.includes(i._element)||i._config.autoClose==="inside"&&!a||i._config.autoClose==="outside"&&a||i._menu.contains(t.target)&&(t.type==="keyup"&&t.key===ks||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const u={relatedTarget:i._element};t.type==="click"&&(u.clickEvent=t),i._completeHide(u)}}static dataApiKeydownHandler(t){const n=/input|textarea/i.test(t.target.tagName),r=t.key===_h,i=[mh,Ps].includes(t.key);if(!i&&!r||n&&!r)return;t.preventDefault();const o=this.matches(nt)?this:L.prev(this,nt)[0]||L.next(this,nt)[0]||L.findOne(nt,t.delegateTarget.parentNode),a=Oe.getOrCreateInstance(o);if(i){t.stopPropagation(),a.show(),a._selectMenuItem(t);return}a._isShown()&&(t.stopPropagation(),a.hide(),o.focus())}}y.on(document,Va,nt,Oe.dataApiKeydownHandler);y.on(document,Va,Cn,Oe.dataApiKeydownHandler);y.on(document,Ba,Oe.clearMenus);y.on(document,wh,Oe.clearMenus);y.on(document,Ba,nt,function(e){e.preventDefault(),Oe.getOrCreateInstance(this).toggle()});ge(Oe);const Fa="backdrop",jh="fade",Rs="show",Hs=`mousedown.bs.${Fa}`,Wh={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},Kh={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class ja extends on{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return Wh}static get DefaultType(){return Kh}static get NAME(){return Fa}show(t){if(!this._config.isVisible){ce(t);return}this._append();const n=this._getElement();this._config.isAnimated&&sn(n),n.classList.add(Rs),this._emulateAnimation(()=>{ce(t)})}hide(t){if(!this._config.isVisible){ce(t);return}this._getElement().classList.remove(Rs),this._emulateAnimation(()=>{this.dispose(),ce(t)})}dispose(){!this._isAppended||(y.off(this._element,Hs),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add(jh),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=Fe(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),y.on(t,Hs,()=>{ce(this._config.clickCallback)}),this._isAppended=!0}_emulateAnimation(t){xa(t,this._getElement(),this._config.isAnimated)}}const Uh="focustrap",Gh="bs.focustrap",kn=`.${Gh}`,Yh=`focusin${kn}`,qh=`keydown.tab${kn}`,zh="Tab",Xh="forward",Bs="backward",Qh={autofocus:!0,trapElement:null},Jh={autofocus:"boolean",trapElement:"element"};class Wa extends on{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return Qh}static get DefaultType(){return Jh}static get NAME(){return Uh}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),y.off(document,kn),y.on(document,Yh,t=>this._handleFocusin(t)),y.on(document,qh,t=>this._handleKeydown(t)),this._isActive=!0)}deactivate(){!this._isActive||(this._isActive=!1,y.off(document,kn))}_handleFocusin(t){const{trapElement:n}=this._config;if(t.target===document||t.target===n||n.contains(t.target))return;const r=L.focusableChildren(n);r.length===0?n.focus():this._lastTabNavDirection===Bs?r[r.length-1].focus():r[0].focus()}_handleKeydown(t){t.key===zh&&(this._lastTabNavDirection=t.shiftKey?Bs:Xh)}}const Vs=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Fs=".sticky-top",yn="padding-right",js="margin-right";class Kr{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,yn,n=>n+t),this._setElementAttributes(Vs,yn,n=>n+t),this._setElementAttributes(Fs,js,n=>n-t)}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,yn),this._resetElementAttributes(Vs,yn),this._resetElementAttributes(Fs,js)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,n,r){const i=this.getWidth(),o=a=>{if(a!==this._element&&window.innerWidth>a.clientWidth+i)return;this._saveInitialAttribute(a,n);const u=window.getComputedStyle(a).getPropertyValue(n);a.style.setProperty(n,`${r(Number.parseFloat(u))}px`)};this._applyManipulationCallback(t,o)}_saveInitialAttribute(t,n){const r=t.style.getPropertyValue(n);r&&Ie.setDataAttribute(t,n,r)}_resetElementAttributes(t,n){const r=i=>{const o=Ie.getDataAttribute(i,n);if(o===null){i.style.removeProperty(n);return}Ie.removeDataAttribute(i,n),i.style.setProperty(n,o)};this._applyManipulationCallback(t,r)}_applyManipulationCallback(t,n){if(Le(t)){n(t);return}for(const r of L.find(t,this._element))n(r)}}const Zh="modal",ep="bs.modal",me=`.${ep}`,tp=".data-api",np="Escape",rp=`hide${me}`,ip=`hidePrevented${me}`,Ka=`hidden${me}`,Ua=`show${me}`,sp=`shown${me}`,op=`resize${me}`,ap=`click.dismiss${me}`,cp=`mousedown.dismiss${me}`,lp=`keydown.dismiss${me}`,up=`click${me}${tp}`,Ws="modal-open",fp="fade",Ks="show",Sr="modal-static",dp=".modal.show",hp=".modal-dialog",pp=".modal-body",_p='[data-bs-toggle="modal"]',mp={backdrop:!0,focus:!0,keyboard:!0},gp={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class ft extends be{constructor(t,n){super(t,n),this._dialog=L.findOne(hp,this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Kr,this._addEventListeners()}static get Default(){return mp}static get DefaultType(){return gp}static get NAME(){return Zh}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||y.trigger(this._element,Ua,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Ws),this._adjustDialog(),this._backdrop.show(()=>this._showElement(t)))}hide(){!this._isShown||this._isTransitioning||y.trigger(this._element,rp).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Ks),this._queueCallback(()=>this._hideModal(),this._element,this._isAnimated()))}dispose(){y.off(window,me),y.off(this._dialog,me),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new ja({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new Wa({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const n=L.findOne(pp,this._dialog);n&&(n.scrollTop=0),sn(this._element),this._element.classList.add(Ks);const r=()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,y.trigger(this._element,sp,{relatedTarget:t})};this._queueCallback(r,this._dialog,this._isAnimated())}_addEventListeners(){y.on(this._element,lp,t=>{if(t.key===np){if(this._config.keyboard){this.hide();return}this._triggerBackdropTransition()}}),y.on(window,op,()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()}),y.on(this._element,cp,t=>{y.one(this._element,ap,n=>{if(!(this._element!==t.target||this._element!==n.target)){if(this._config.backdrop==="static"){this._triggerBackdropTransition();return}this._config.backdrop&&this.hide()}})})}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide(()=>{document.body.classList.remove(Ws),this._resetAdjustments(),this._scrollBar.reset(),y.trigger(this._element,Ka)})}_isAnimated(){return this._element.classList.contains(fp)}_triggerBackdropTransition(){if(y.trigger(this._element,ip).defaultPrevented)return;const n=this._element.scrollHeight>document.documentElement.clientHeight,r=this._element.style.overflowY;r==="hidden"||this._element.classList.contains(Sr)||(n||(this._element.style.overflowY="hidden"),this._element.classList.add(Sr),this._queueCallback(()=>{this._element.classList.remove(Sr),this._queueCallback(()=>{this._element.style.overflowY=r},this._dialog)},this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,n=this._scrollBar.getWidth(),r=n>0;if(r&&!t){const i=_e()?"paddingLeft":"paddingRight";this._element.style[i]=`${n}px`}if(!r&&t){const i=_e()?"paddingRight":"paddingLeft";this._element.style[i]=`${n}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,n){return this.each(function(){const r=ft.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof r[t]>"u")throw new TypeError(`No method named "${t}"`);r[t](n)}})}}y.on(document,up,_p,function(e){const t=L.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),y.one(t,Ua,i=>{i.defaultPrevented||y.one(t,Ka,()=>{Vt(this)&&this.focus()})});const n=L.findOne(dp);n&&ft.getInstance(n).hide(),ft.getOrCreateInstance(t).toggle(this)});Qn(ft);ge(ft);const vp="offcanvas",yp="bs.offcanvas",He=`.${yp}`,Ga=".data-api",bp=`load${He}${Ga}`,Ep="Escape",Us="show",Gs="showing",Ys="hiding",wp="offcanvas-backdrop",Ya=".offcanvas.show",Ap=`show${He}`,Tp=`shown${He}`,Sp=`hide${He}`,qs=`hidePrevented${He}`,qa=`hidden${He}`,Op=`resize${He}`,xp=`click${He}${Ga}`,Cp=`keydown.dismiss${He}`,$p='[data-bs-toggle="offcanvas"]',Np={backdrop:!0,keyboard:!0,scroll:!1},Dp={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class We extends be{constructor(t,n){super(t,n),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Np}static get DefaultType(){return Dp}static get NAME(){return vp}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||y.trigger(this._element,Ap,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||new Kr().hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Gs);const r=()=>{(!this._config.scroll||this._config.backdrop)&&this._focustrap.activate(),this._element.classList.add(Us),this._element.classList.remove(Gs),y.trigger(this._element,Tp,{relatedTarget:t})};this._queueCallback(r,this._element,!0)}hide(){if(!this._isShown||y.trigger(this._element,Sp).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Ys),this._backdrop.hide();const n=()=>{this._element.classList.remove(Us,Ys),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||new Kr().reset(),y.trigger(this._element,qa)};this._queueCallback(n,this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=()=>{if(this._config.backdrop==="static"){y.trigger(this._element,qs);return}this.hide()},n=Boolean(this._config.backdrop);return new ja({className:wp,isVisible:n,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:n?t:null})}_initializeFocusTrap(){return new Wa({trapElement:this._element})}_addEventListeners(){y.on(this._element,Cp,t=>{if(t.key===Ep){if(this._config.keyboard){this.hide();return}y.trigger(this._element,qs)}})}static jQueryInterface(t){return this.each(function(){const n=We.getOrCreateInstance(this,t);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t](this)}})}}y.on(document,xp,$p,function(e){const t=L.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),je(this))return;y.one(t,qa,()=>{Vt(this)&&this.focus()});const n=L.findOne(Ya);n&&n!==t&&We.getInstance(n).hide(),We.getOrCreateInstance(t).toggle(this)});y.on(window,bp,()=>{for(const e of L.find(Ya))We.getOrCreateInstance(e).show()});y.on(window,Op,()=>{for(const e of L.find("[aria-modal][class*=show][class*=offcanvas-]"))getComputedStyle(e).position!=="fixed"&&We.getOrCreateInstance(e).hide()});Qn(We);ge(We);const Lp=/^aria-[\w-]*$/i,za={"*":["class","dir","id","lang","role",Lp],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Ip=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Mp=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,kp=(e,t)=>{const n=e.nodeName.toLowerCase();return t.includes(n)?Ip.has(n)?Boolean(Mp.test(e.nodeValue)):!0:t.filter(r=>r instanceof RegExp).some(r=>r.test(n))};function Pp(e,t,n){if(!e.length)return e;if(n&&typeof n=="function")return n(e);const i=new window.DOMParser().parseFromString(e,"text/html"),o=[].concat(...i.body.querySelectorAll("*"));for(const a of o){const u=a.nodeName.toLowerCase();if(!Object.keys(t).includes(u)){a.remove();continue}const f=[].concat(...a.attributes),d=[].concat(t["*"]||[],t[u]||[]);for(const g of f)kp(g,d)||a.removeAttribute(g.nodeName)}return i.body.innerHTML}const Rp="TemplateFactory",Hp={allowList:za,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},Bp={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},Vp={entry:"(string|element|function|null)",selector:"(string|element)"};class Fp extends on{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return Hp}static get DefaultType(){return Bp}static get NAME(){return Rp}getContent(){return Object.values(this._config.content).map(t=>this._resolvePossibleFunction(t)).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[i,o]of Object.entries(this._config.content))this._setContent(t,o,i);const n=t.children[0],r=this._resolvePossibleFunction(this._config.extraClass);return r&&n.classList.add(...r.split(" ")),n}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[n,r]of Object.entries(t))super._typeCheckConfig({selector:n,entry:r},Vp)}_setContent(t,n,r){const i=L.findOne(r,t);if(!!i){if(n=this._resolvePossibleFunction(n),!n){i.remove();return}if(Le(n)){this._putElementInTemplate(Fe(n),i);return}if(this._config.html){i.innerHTML=this._maybeSanitize(n);return}i.textContent=n}}_maybeSanitize(t){return this._config.sanitize?Pp(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return ce(t,[this])}_putElementInTemplate(t,n){if(this._config.html){n.innerHTML="",n.append(t);return}n.textContent=t.textContent}}const jp="tooltip",Wp=new Set(["sanitize","allowList","sanitizeFn"]),Or="fade",Kp="modal",bn="show",Up=".tooltip-inner",zs=`.${Kp}`,Xs="hide.bs.modal",Ut="hover",xr="focus",Gp="click",Yp="manual",qp="hide",zp="hidden",Xp="show",Qp="shown",Jp="inserted",Zp="click",e_="focusin",t_="focusout",n_="mouseenter",r_="mouseleave",i_={AUTO:"auto",TOP:"top",RIGHT:_e()?"left":"right",BOTTOM:"bottom",LEFT:_e()?"right":"left"},s_={allowList:za,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},o_={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class gt extends be{constructor(t,n){if(typeof wa>"u")throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,n),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return s_}static get DefaultType(){return o_}static get NAME(){return jp}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){if(!!this._isEnabled){if(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()){this._leave();return}this._enter()}}dispose(){clearTimeout(this._timeout),y.off(this._element.closest(zs),Xs,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if(this._element.style.display==="none")throw new Error("Please use show on visible elements");if(!(this._isWithContent()&&this._isEnabled))return;const t=y.trigger(this._element,this.constructor.eventName(Xp)),r=(Sa(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!r)return;this._disposePopper();const i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));const{container:o}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(o.append(i),y.trigger(this._element,this.constructor.eventName(Jp))),this._popper=this._createPopper(i),i.classList.add(bn),"ontouchstart"in document.documentElement)for(const u of[].concat(...document.body.children))y.on(u,"mouseover",In);const a=()=>{y.trigger(this._element,this.constructor.eventName(Qp)),this._isHovered===!1&&this._leave(),this._isHovered=!1};this._queueCallback(a,this.tip,this._isAnimated())}hide(){if(!this._isShown()||y.trigger(this._element,this.constructor.eventName(qp)).defaultPrevented)return;if(this._getTipElement().classList.remove(bn),"ontouchstart"in document.documentElement)for(const i of[].concat(...document.body.children))y.off(i,"mouseover",In);this._activeTrigger[Gp]=!1,this._activeTrigger[xr]=!1,this._activeTrigger[Ut]=!1,this._isHovered=null;const r=()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),y.trigger(this._element,this.constructor.eventName(zp)))};this._queueCallback(r,this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const n=this._getTemplateFactory(t).toHtml();if(!n)return null;n.classList.remove(Or,bn),n.classList.add(`bs-${this.constructor.NAME}-auto`);const r=Gf(this.constructor.NAME).toString();return n.setAttribute("id",r),this._isAnimated()&&n.classList.add(Or),n}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new Fp({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[Up]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(Or)}_isShown(){return this.tip&&this.tip.classList.contains(bn)}_createPopper(t){const n=ce(this._config.placement,[this,t,this._element]),r=i_[n.toUpperCase()];return Mi(this._element,t,this._getPopperConfig(r))}_getOffset(){const{offset:t}=this._config;return typeof t=="string"?t.split(",").map(n=>Number.parseInt(n,10)):typeof t=="function"?n=>t(n,this._element):t}_resolvePossibleFunction(t){return ce(t,[this._element])}_getPopperConfig(t){const n={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:r=>{this._getTipElement().setAttribute("data-popper-placement",r.state.placement)}}]};return{...n,...ce(this._config.popperConfig,[n])}}_setListeners(){const t=this._config.trigger.split(" ");for(const n of t)if(n==="click")y.on(this._element,this.constructor.eventName(Zp),this._config.selector,r=>{this._initializeOnDelegatedTarget(r).toggle()});else if(n!==Yp){const r=n===Ut?this.constructor.eventName(n_):this.constructor.eventName(e_),i=n===Ut?this.constructor.eventName(r_):this.constructor.eventName(t_);y.on(this._element,r,this._config.selector,o=>{const a=this._initializeOnDelegatedTarget(o);a._activeTrigger[o.type==="focusin"?xr:Ut]=!0,a._enter()}),y.on(this._element,i,this._config.selector,o=>{const a=this._initializeOnDelegatedTarget(o);a._activeTrigger[o.type==="focusout"?xr:Ut]=a._element.contains(o.relatedTarget),a._leave()})}this._hideModalHandler=()=>{this._element&&this.hide()},y.on(this._element.closest(zs),Xs,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");!t||(!this._element.getAttribute("aria-label")&&!this._element.textContent.trim()&&this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){if(this._isShown()||this._isHovered){this._isHovered=!0;return}this._isHovered=!0,this._setTimeout(()=>{this._isHovered&&this.show()},this._config.delay.show)}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout(()=>{this._isHovered||this.hide()},this._config.delay.hide))}_setTimeout(t,n){clearTimeout(this._timeout),this._timeout=setTimeout(t,n)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const n=Ie.getDataAttributes(this._element);for(const r of Object.keys(n))Wp.has(r)&&delete n[r];return t={...n,...typeof t=="object"&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=t.container===!1?document.body:Fe(t.container),typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),typeof t.title=="number"&&(t.title=t.title.toString()),typeof t.content=="number"&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[n,r]of Object.entries(this._config))this.constructor.Default[n]!==r&&(t[n]=r);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each(function(){const n=gt.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t]()}})}}ge(gt);const a_="popover",c_=".popover-header",l_=".popover-body",u_={...gt.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},f_={...gt.DefaultType,content:"(null|string|element|function)"};class Hi extends gt{static get Default(){return u_}static get DefaultType(){return f_}static get NAME(){return a_}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[c_]:this._getTitle(),[l_]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each(function(){const n=Hi.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t]()}})}}ge(Hi);const d_="scrollspy",h_="bs.scrollspy",Bi=`.${h_}`,p_=".data-api",__=`activate${Bi}`,Qs=`click${Bi}`,m_=`load${Bi}${p_}`,g_="dropdown-item",Et="active",v_='[data-bs-spy="scroll"]',Cr="[href]",y_=".nav, .list-group",Js=".nav-link",b_=".nav-item",E_=".list-group-item",w_=`${Js}, ${b_} > ${Js}, ${E_}`,A_=".dropdown",T_=".dropdown-toggle",S_={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},O_={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Zn extends be{constructor(t,n){super(t,n),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement=getComputedStyle(this._element).overflowY==="visible"?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return S_}static get DefaultType(){return O_}static get NAME(){return d_}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=Fe(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,typeof t.threshold=="string"&&(t.threshold=t.threshold.split(",").map(n=>Number.parseFloat(n))),t}_maybeEnableSmoothScroll(){!this._config.smoothScroll||(y.off(this._config.target,Qs),y.on(this._config.target,Qs,Cr,t=>{const n=this._observableSections.get(t.target.hash);if(n){t.preventDefault();const r=this._rootElement||window,i=n.offsetTop-this._element.offsetTop;if(r.scrollTo){r.scrollTo({top:i,behavior:"smooth"});return}r.scrollTop=i}}))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver(n=>this._observerCallback(n),t)}_observerCallback(t){const n=a=>this._targetLinks.get(`#${a.target.id}`),r=a=>{this._previousScrollData.visibleEntryTop=a.target.offsetTop,this._process(n(a))},i=(this._rootElement||document.documentElement).scrollTop,o=i>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=i;for(const a of t){if(!a.isIntersecting){this._activeTarget=null,this._clearActiveClass(n(a));continue}const u=a.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(o&&u){if(r(a),!i)return;continue}!o&&!u&&r(a)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=L.find(Cr,this._config.target);for(const n of t){if(!n.hash||je(n))continue;const r=L.findOne(decodeURI(n.hash),this._element);Vt(r)&&(this._targetLinks.set(decodeURI(n.hash),n),this._observableSections.set(n.hash,r))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(Et),this._activateParents(t),y.trigger(this._element,__,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains(g_)){L.findOne(T_,t.closest(A_)).classList.add(Et);return}for(const n of L.parents(t,y_))for(const r of L.prev(n,w_))r.classList.add(Et)}_clearActiveClass(t){t.classList.remove(Et);const n=L.find(`${Cr}.${Et}`,t);for(const r of n)r.classList.remove(Et)}static jQueryInterface(t){return this.each(function(){const n=Zn.getOrCreateInstance(this,t);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t]()}})}}y.on(window,m_,()=>{for(const e of L.find(v_))Zn.getOrCreateInstance(e)});ge(Zn);const x_="tab",C_="bs.tab",vt=`.${C_}`,$_=`hide${vt}`,N_=`hidden${vt}`,D_=`show${vt}`,L_=`shown${vt}`,I_=`click${vt}`,M_=`keydown${vt}`,k_=`load${vt}`,P_="ArrowLeft",Zs="ArrowRight",R_="ArrowUp",eo="ArrowDown",$r="Home",to="End",rt="active",no="fade",Nr="show",H_="dropdown",Xa=".dropdown-toggle",B_=".dropdown-menu",Dr=`:not(${Xa})`,V_='.list-group, .nav, [role="tablist"]',F_=".nav-item, .list-group-item",j_=`.nav-link${Dr}, .list-group-item${Dr}, [role="tab"]${Dr}`,Qa='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Lr=`${j_}, ${Qa}`,W_=`.${rt}[data-bs-toggle="tab"], .${rt}[data-bs-toggle="pill"], .${rt}[data-bs-toggle="list"]`;class Pt extends be{constructor(t){super(t),this._parent=this._element.closest(V_),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),y.on(this._element,M_,n=>this._keydown(n)))}static get NAME(){return x_}show(){const t=this._element;if(this._elemIsActive(t))return;const n=this._getActiveElem(),r=n?y.trigger(n,$_,{relatedTarget:t}):null;y.trigger(t,D_,{relatedTarget:n}).defaultPrevented||r&&r.defaultPrevented||(this._deactivate(n,t),this._activate(t,n))}_activate(t,n){if(!t)return;t.classList.add(rt),this._activate(L.getElementFromSelector(t));const r=()=>{if(t.getAttribute("role")!=="tab"){t.classList.add(Nr);return}t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),y.trigger(t,L_,{relatedTarget:n})};this._queueCallback(r,t,t.classList.contains(no))}_deactivate(t,n){if(!t)return;t.classList.remove(rt),t.blur(),this._deactivate(L.getElementFromSelector(t));const r=()=>{if(t.getAttribute("role")!=="tab"){t.classList.remove(Nr);return}t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),y.trigger(t,N_,{relatedTarget:n})};this._queueCallback(r,t,t.classList.contains(no))}_keydown(t){if(![P_,Zs,R_,eo,$r,to].includes(t.key))return;t.stopPropagation(),t.preventDefault();const n=this._getChildren().filter(i=>!je(i));let r;if([$r,to].includes(t.key))r=n[t.key===$r?0:n.length-1];else{const i=[Zs,eo].includes(t.key);r=ki(n,t.target,i,!0)}r&&(r.focus({preventScroll:!0}),Pt.getOrCreateInstance(r).show())}_getChildren(){return L.find(Lr,this._parent)}_getActiveElem(){return this._getChildren().find(t=>this._elemIsActive(t))||null}_setInitialAttributes(t,n){this._setAttributeIfNotExists(t,"role","tablist");for(const r of n)this._setInitialAttributesOnChild(r)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const n=this._elemIsActive(t),r=this._getOuterElement(t);t.setAttribute("aria-selected",n),r!==t&&this._setAttributeIfNotExists(r,"role","presentation"),n||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const n=L.getElementFromSelector(t);!n||(this._setAttributeIfNotExists(n,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(n,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,n){const r=this._getOuterElement(t);if(!r.classList.contains(H_))return;const i=(o,a)=>{const u=L.findOne(o,r);u&&u.classList.toggle(a,n)};i(Xa,rt),i(B_,Nr),r.setAttribute("aria-expanded",n)}_setAttributeIfNotExists(t,n,r){t.hasAttribute(n)||t.setAttribute(n,r)}_elemIsActive(t){return t.classList.contains(rt)}_getInnerElement(t){return t.matches(Lr)?t:L.findOne(Lr,t)}_getOuterElement(t){return t.closest(F_)||t}static jQueryInterface(t){return this.each(function(){const n=Pt.getOrCreateInstance(this);if(typeof t=="string"){if(n[t]===void 0||t.startsWith("_")||t==="constructor")throw new TypeError(`No method named "${t}"`);n[t]()}})}}y.on(document,I_,Qa,function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),!je(this)&&Pt.getOrCreateInstance(this).show()});y.on(window,k_,()=>{for(const e of L.find(W_))Pt.getOrCreateInstance(e)});ge(Pt);const K_="toast",U_="bs.toast",Qe=`.${U_}`,G_=`mouseover${Qe}`,Y_=`mouseout${Qe}`,q_=`focusin${Qe}`,z_=`focusout${Qe}`,X_=`hide${Qe}`,Q_=`hidden${Qe}`,J_=`show${Qe}`,Z_=`shown${Qe}`,em="fade",ro="hide",En="show",wn="showing",tm={animation:"boolean",autohide:"boolean",delay:"number"},nm={animation:!0,autohide:!0,delay:5e3};class un extends be{constructor(t,n){super(t,n),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return nm}static get DefaultType(){return tm}static get NAME(){return K_}show(){if(y.trigger(this._element,J_).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add(em);const n=()=>{this._element.classList.remove(wn),y.trigger(this._element,Z_),this._maybeScheduleHide()};this._element.classList.remove(ro),sn(this._element),this._element.classList.add(En,wn),this._queueCallback(n,this._element,this._config.animation)}hide(){if(!this.isShown()||y.trigger(this._element,X_).defaultPrevented)return;const n=()=>{this._element.classList.add(ro),this._element.classList.remove(wn,En),y.trigger(this._element,Q_)};this._element.classList.add(wn),this._queueCallback(n,this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(En),super.dispose()}isShown(){return this._element.classList.contains(En)}_maybeScheduleHide(){!this._config.autohide||this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout(()=>{this.hide()},this._config.delay))}_onInteraction(t,n){switch(t.type){case"mouseover":case"mouseout":{this._hasMouseInteraction=n;break}case"focusin":case"focusout":{this._hasKeyboardInteraction=n;break}}if(n){this._clearTimeout();return}const r=t.relatedTarget;this._element===r||this._element.contains(r)||this._maybeScheduleHide()}_setListeners(){y.on(this._element,G_,t=>this._onInteraction(t,!0)),y.on(this._element,Y_,t=>this._onInteraction(t,!1)),y.on(this._element,q_,t=>this._onInteraction(t,!0)),y.on(this._element,z_,t=>this._onInteraction(t,!1))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each(function(){const n=un.getOrCreateInstance(this,t);if(typeof t=="string"){if(typeof n[t]>"u")throw new TypeError(`No method named "${t}"`);n[t](this)}})}}Qn(un);ge(un);const rm=()=>{[].slice.call(document.querySelectorAll(".alert")).map(function(t){return new an(t)})},im=()=>{[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(t=>new gt(t))},sm=()=>{[].slice.call(document.querySelectorAll(".collapse")).map(t=>new kt(t,{toggle:!1}))};var Ur=!1,Gr=!1,st=[];function om(e){am(e)}function am(e){st.includes(e)||st.push(e),cm()}function Ja(e){let t=st.indexOf(e);t!==-1&&st.splice(t,1)}function cm(){!Gr&&!Ur&&(Ur=!0,queueMicrotask(lm))}function lm(){Ur=!1,Gr=!0;for(let e=0;e<st.length;e++)st[e]();st.length=0,Gr=!1}var jt,fn,er,Za,Yr=!0;function um(e){Yr=!1,e(),Yr=!0}function fm(e){jt=e.reactive,er=e.release,fn=t=>e.effect(t,{scheduler:n=>{Yr?om(n):n()}}),Za=e.raw}function io(e){fn=e}function dm(e){let t=()=>{};return[r=>{let i=fn(r);return e._x_effects||(e._x_effects=new Set,e._x_runEffects=()=>{e._x_effects.forEach(o=>o())}),e._x_effects.add(i),t=()=>{i!==void 0&&(e._x_effects.delete(i),er(i))},i},()=>{t()}]}var ec=[],tc=[],nc=[];function hm(e){nc.push(e)}function rc(e,t){typeof t=="function"?(e._x_cleanups||(e._x_cleanups=[]),e._x_cleanups.push(t)):(t=e,tc.push(t))}function pm(e){ec.push(e)}function _m(e,t,n){e._x_attributeCleanups||(e._x_attributeCleanups={}),e._x_attributeCleanups[t]||(e._x_attributeCleanups[t]=[]),e._x_attributeCleanups[t].push(n)}function ic(e,t){!e._x_attributeCleanups||Object.entries(e._x_attributeCleanups).forEach(([n,r])=>{(t===void 0||t.includes(n))&&(r.forEach(i=>i()),delete e._x_attributeCleanups[n])})}var Vi=new MutationObserver(Wi),Fi=!1;function sc(){Vi.observe(document,{subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0}),Fi=!0}function mm(){gm(),Vi.disconnect(),Fi=!1}var Qt=[],Ir=!1;function gm(){Qt=Qt.concat(Vi.takeRecords()),Qt.length&&!Ir&&(Ir=!0,queueMicrotask(()=>{vm(),Ir=!1}))}function vm(){Wi(Qt),Qt.length=0}function Q(e){if(!Fi)return e();mm();let t=e();return sc(),t}var ji=!1,Pn=[];function ym(){ji=!0}function bm(){ji=!1,Wi(Pn),Pn=[]}function Wi(e){if(ji){Pn=Pn.concat(e);return}let t=[],n=[],r=new Map,i=new Map;for(let o=0;o<e.length;o++)if(!e[o].target._x_ignoreMutationObserver&&(e[o].type==="childList"&&(e[o].addedNodes.forEach(a=>a.nodeType===1&&t.push(a)),e[o].removedNodes.forEach(a=>a.nodeType===1&&n.push(a))),e[o].type==="attributes")){let a=e[o].target,u=e[o].attributeName,f=e[o].oldValue,d=()=>{r.has(a)||r.set(a,[]),r.get(a).push({name:u,value:a.getAttribute(u)})},g=()=>{i.has(a)||i.set(a,[]),i.get(a).push(u)};a.hasAttribute(u)&&f===null?d():a.hasAttribute(u)?(g(),d()):g()}i.forEach((o,a)=>{ic(a,o)}),r.forEach((o,a)=>{ec.forEach(u=>u(a,o))});for(let o of n)if(!t.includes(o)&&(tc.forEach(a=>a(o)),o._x_cleanups))for(;o._x_cleanups.length;)o._x_cleanups.pop()();t.forEach(o=>{o._x_ignoreSelf=!0,o._x_ignore=!0});for(let o of t)n.includes(o)||!o.isConnected||(delete o._x_ignoreSelf,delete o._x_ignore,nc.forEach(a=>a(o)),o._x_ignore=!0,o._x_ignoreSelf=!0);t.forEach(o=>{delete o._x_ignoreSelf,delete o._x_ignore}),t=null,n=null,r=null,i=null}function oc(e){return hn(Rt(e))}function dn(e,t,n){return e._x_dataStack=[t,...Rt(n||e)],()=>{e._x_dataStack=e._x_dataStack.filter(r=>r!==t)}}function so(e,t){let n=e._x_dataStack[0];Object.entries(t).forEach(([r,i])=>{n[r]=i})}function Rt(e){return e._x_dataStack?e._x_dataStack:typeof ShadowRoot=="function"&&e instanceof ShadowRoot?Rt(e.host):e.parentNode?Rt(e.parentNode):[]}function hn(e){let t=new Proxy({},{ownKeys:()=>Array.from(new Set(e.flatMap(n=>Object.keys(n)))),has:(n,r)=>e.some(i=>i.hasOwnProperty(r)),get:(n,r)=>(e.find(i=>{if(i.hasOwnProperty(r)){let o=Object.getOwnPropertyDescriptor(i,r);if(o.get&&o.get._x_alreadyBound||o.set&&o.set._x_alreadyBound)return!0;if((o.get||o.set)&&o.enumerable){let a=o.get,u=o.set,f=o;a=a&&a.bind(t),u=u&&u.bind(t),a&&(a._x_alreadyBound=!0),u&&(u._x_alreadyBound=!0),Object.defineProperty(i,r,{...f,get:a,set:u})}return!0}return!1})||{})[r],set:(n,r,i)=>{let o=e.find(a=>a.hasOwnProperty(r));return o?o[r]=i:e[e.length-1][r]=i,!0}});return t}function ac(e){let t=r=>typeof r=="object"&&!Array.isArray(r)&&r!==null,n=(r,i="")=>{Object.entries(Object.getOwnPropertyDescriptors(r)).forEach(([o,{value:a,enumerable:u}])=>{if(u===!1||a===void 0)return;let f=i===""?o:`${i}.${o}`;typeof a=="object"&&a!==null&&a._x_interceptor?r[o]=a.initialize(e,f,o):t(a)&&a!==r&&!(a instanceof Element)&&n(a,f)})};return n(e)}function cc(e,t=()=>{}){let n={initialValue:void 0,_x_interceptor:!0,initialize(r,i,o){return e(this.initialValue,()=>Em(r,i),a=>qr(r,i,a),i,o)}};return t(n),r=>{if(typeof r=="object"&&r!==null&&r._x_interceptor){let i=n.initialize.bind(n);n.initialize=(o,a,u)=>{let f=r.initialize(o,a,u);return n.initialValue=f,i(o,a,u)}}else n.initialValue=r;return n}}function Em(e,t){return t.split(".").reduce((n,r)=>n[r],e)}function qr(e,t,n){if(typeof t=="string"&&(t=t.split(".")),t.length===1)e[t[0]]=n;else{if(t.length===0)throw error;return e[t[0]]||(e[t[0]]={}),qr(e[t[0]],t.slice(1),n)}}var lc={};function Ne(e,t){lc[e]=t}function zr(e,t){return Object.entries(lc).forEach(([n,r])=>{Object.defineProperty(e,`$${n}`,{get(){let[i,o]=pc(t);return i={interceptor:cc,...i},rc(t,o),r(t,i)},enumerable:!1})}),e}function wm(e,t,n,...r){try{return n(...r)}catch(i){tn(i,e,t)}}function tn(e,t,n=void 0){Object.assign(e,{el:t,expression:n}),console.warn(`Alpine Expression Error: ${e.message}

${n?'Expression: "'+n+`"

`:""}`,t),setTimeout(()=>{throw e},0)}function xt(e,t,n={}){let r;return se(e,t)(i=>r=i,n),r}function se(...e){return uc(...e)}var uc=fc;function Am(e){uc=e}function fc(e,t){let n={};zr(n,e);let r=[n,...Rt(e)];if(typeof t=="function")return Tm(r,t);let i=Om(r,t,e);return wm.bind(null,e,t,i)}function Tm(e,t){return(n=()=>{},{scope:r={},params:i=[]}={})=>{let o=t.apply(hn([r,...e]),i);Rn(n,o)}}var Mr={};function Sm(e,t){if(Mr[e])return Mr[e];let n=Object.getPrototypeOf(async function(){}).constructor,r=/^[\n\s]*if.*\(.*\)/.test(e)||/^(let|const)\s/.test(e)?`(() => { ${e} })()`:e,o=(()=>{try{return new n(["__self","scope"],`with (scope) { __self.result = ${r} }; __self.finished = true; return __self.result;`)}catch(a){return tn(a,t,e),Promise.resolve()}})();return Mr[e]=o,o}function Om(e,t,n){let r=Sm(t,n);return(i=()=>{},{scope:o={},params:a=[]}={})=>{r.result=void 0,r.finished=!1;let u=hn([o,...e]);if(typeof r=="function"){let f=r(r,u).catch(d=>tn(d,n,t));r.finished?(Rn(i,r.result,u,a,n),r.result=void 0):f.then(d=>{Rn(i,d,u,a,n)}).catch(d=>tn(d,n,t)).finally(()=>r.result=void 0)}}}function Rn(e,t,n,r,i){if(typeof t=="function"){let o=t.apply(n,r);o instanceof Promise?o.then(a=>Rn(e,a,n,r)).catch(a=>tn(a,i,t)):e(o)}else e(t)}var Ki="x-";function Wt(e=""){return Ki+e}function xm(e){Ki=e}var dc={};function q(e,t){dc[e]=t}function Ui(e,t,n){let r={};return Array.from(t).map(gc((o,a)=>r[o]=a)).filter(yc).map(Dm(r,n)).sort(Lm).map(o=>Nm(e,o))}function Cm(e){return Array.from(e).map(gc()).filter(t=>!yc(t))}var Xr=!1,qt=new Map,hc=Symbol();function $m(e){Xr=!0;let t=Symbol();hc=t,qt.set(t,[]);let n=()=>{for(;qt.get(t).length;)qt.get(t).shift()();qt.delete(t)},r=()=>{Xr=!1,n()};e(n),r()}function pc(e){let t=[],n=u=>t.push(u),[r,i]=dm(e);return t.push(i),[{Alpine:pn,effect:r,cleanup:n,evaluateLater:se.bind(se,e),evaluate:xt.bind(xt,e)},()=>t.forEach(u=>u())]}function Nm(e,t){let n=()=>{},r=dc[t.type]||n,[i,o]=pc(e);_m(e,t.original,o);let a=()=>{e._x_ignore||e._x_ignoreSelf||(r.inline&&r.inline(e,t,i),r=r.bind(r,e,t,i),Xr?qt.get(hc).push(r):r())};return a.runCleanups=o,a}var _c=(e,t)=>({name:n,value:r})=>(n.startsWith(e)&&(n=n.replace(e,t)),{name:n,value:r}),mc=e=>e;function gc(e=()=>{}){return({name:t,value:n})=>{let{name:r,value:i}=vc.reduce((o,a)=>a(o),{name:t,value:n});return r!==t&&e(r,t),{name:r,value:i}}}var vc=[];function Gi(e){vc.push(e)}function yc({name:e}){return bc().test(e)}var bc=()=>new RegExp(`^${Ki}([^:^.]+)\\b`);function Dm(e,t){return({name:n,value:r})=>{let i=n.match(bc()),o=n.match(/:([a-zA-Z0-9\-:]+)/),a=n.match(/\.[^.\]]+(?=[^\]]*$)/g)||[],u=t||e[n]||n;return{type:i?i[1]:null,value:o?o[1]:null,modifiers:a.map(f=>f.replace(".","")),expression:r,original:u}}}var Qr="DEFAULT",An=["ignore","ref","data","id","bind","init","for","model","modelable","transition","show","if",Qr,"teleport","element"];function Lm(e,t){let n=An.indexOf(e.type)===-1?Qr:e.type,r=An.indexOf(t.type)===-1?Qr:t.type;return An.indexOf(n)-An.indexOf(r)}function Jt(e,t,n={}){e.dispatchEvent(new CustomEvent(t,{detail:n,bubbles:!0,composed:!0,cancelable:!0}))}var Jr=[],Yi=!1;function Ec(e){Jr.push(e),queueMicrotask(()=>{Yi||setTimeout(()=>{Zr()})})}function Zr(){for(Yi=!1;Jr.length;)Jr.shift()()}function Im(){Yi=!0}function dt(e,t){if(typeof ShadowRoot=="function"&&e instanceof ShadowRoot){Array.from(e.children).forEach(i=>dt(i,t));return}let n=!1;if(t(e,()=>n=!0),n)return;let r=e.firstElementChild;for(;r;)dt(r,t),r=r.nextElementSibling}function Hn(e,...t){console.warn(`Alpine Warning: ${e}`,...t)}function Mm(){document.body||Hn("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?"),Jt(document,"alpine:init"),Jt(document,"alpine:initializing"),sc(),hm(t=>Ke(t,dt)),rc(t=>Pm(t)),pm((t,n)=>{Ui(t,n).forEach(r=>r())});let e=t=>!tr(t.parentElement,!0);Array.from(document.querySelectorAll(Tc())).filter(e).forEach(t=>{Ke(t)}),Jt(document,"alpine:initialized")}var qi=[],wc=[];function Ac(){return qi.map(e=>e())}function Tc(){return qi.concat(wc).map(e=>e())}function Sc(e){qi.push(e)}function Oc(e){wc.push(e)}function tr(e,t=!1){return nr(e,n=>{if((t?Tc():Ac()).some(i=>n.matches(i)))return!0})}function nr(e,t){if(!!e){if(t(e))return e;if(e._x_teleportBack&&(e=e._x_teleportBack),!!e.parentElement)return nr(e.parentElement,t)}}function km(e){return Ac().some(t=>e.matches(t))}function Ke(e,t=dt){$m(()=>{t(e,(n,r)=>{Ui(n,n.attributes).forEach(i=>i()),n._x_ignore&&r()})})}function Pm(e){dt(e,t=>ic(t))}function zi(e,t){return Array.isArray(t)?oo(e,t.join(" ")):typeof t=="object"&&t!==null?Rm(e,t):typeof t=="function"?zi(e,t()):oo(e,t)}function oo(e,t){let n=i=>i.split(" ").filter(o=>!e.classList.contains(o)).filter(Boolean),r=i=>(e.classList.add(...i),()=>{e.classList.remove(...i)});return t=t===!0?t="":t||"",r(n(t))}function Rm(e,t){let n=u=>u.split(" ").filter(Boolean),r=Object.entries(t).flatMap(([u,f])=>f?n(u):!1).filter(Boolean),i=Object.entries(t).flatMap(([u,f])=>f?!1:n(u)).filter(Boolean),o=[],a=[];return i.forEach(u=>{e.classList.contains(u)&&(e.classList.remove(u),a.push(u))}),r.forEach(u=>{e.classList.contains(u)||(e.classList.add(u),o.push(u))}),()=>{a.forEach(u=>e.classList.add(u)),o.forEach(u=>e.classList.remove(u))}}function rr(e,t){return typeof t=="object"&&t!==null?Hm(e,t):Bm(e,t)}function Hm(e,t){let n={};return Object.entries(t).forEach(([r,i])=>{n[r]=e.style[r],r.startsWith("--")||(r=Vm(r)),e.style.setProperty(r,i)}),setTimeout(()=>{e.style.length===0&&e.removeAttribute("style")}),()=>{rr(e,n)}}function Bm(e,t){let n=e.getAttribute("style",t);return e.setAttribute("style",t),()=>{e.setAttribute("style",n||"")}}function Vm(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function ei(e,t=()=>{}){let n=!1;return function(){n?t.apply(this,arguments):(n=!0,e.apply(this,arguments))}}q("transition",(e,{value:t,modifiers:n,expression:r},{evaluate:i})=>{typeof r=="function"&&(r=i(r)),r?Fm(e,r,t):jm(e,n,t)});function Fm(e,t,n){xc(e,zi,""),{enter:i=>{e._x_transition.enter.during=i},"enter-start":i=>{e._x_transition.enter.start=i},"enter-end":i=>{e._x_transition.enter.end=i},leave:i=>{e._x_transition.leave.during=i},"leave-start":i=>{e._x_transition.leave.start=i},"leave-end":i=>{e._x_transition.leave.end=i}}[n](t)}function jm(e,t,n){xc(e,rr);let r=!t.includes("in")&&!t.includes("out")&&!n,i=r||t.includes("in")||["enter"].includes(n),o=r||t.includes("out")||["leave"].includes(n);t.includes("in")&&!r&&(t=t.filter((_,v)=>v<t.indexOf("out"))),t.includes("out")&&!r&&(t=t.filter((_,v)=>v>t.indexOf("out")));let a=!t.includes("opacity")&&!t.includes("scale"),u=a||t.includes("opacity"),f=a||t.includes("scale"),d=u?0:1,g=f?Gt(t,"scale",95)/100:1,s=Gt(t,"delay",0),c=Gt(t,"origin","center"),l="opacity, transform",h=Gt(t,"duration",150)/1e3,m=Gt(t,"duration",75)/1e3,p="cubic-bezier(0.4, 0.0, 0.2, 1)";i&&(e._x_transition.enter.during={transformOrigin:c,transitionDelay:s,transitionProperty:l,transitionDuration:`${h}s`,transitionTimingFunction:p},e._x_transition.enter.start={opacity:d,transform:`scale(${g})`},e._x_transition.enter.end={opacity:1,transform:"scale(1)"}),o&&(e._x_transition.leave.during={transformOrigin:c,transitionDelay:s,transitionProperty:l,transitionDuration:`${m}s`,transitionTimingFunction:p},e._x_transition.leave.start={opacity:1,transform:"scale(1)"},e._x_transition.leave.end={opacity:d,transform:`scale(${g})`})}function xc(e,t,n={}){e._x_transition||(e._x_transition={enter:{during:n,start:n,end:n},leave:{during:n,start:n,end:n},in(r=()=>{},i=()=>{}){ti(e,t,{during:this.enter.during,start:this.enter.start,end:this.enter.end},r,i)},out(r=()=>{},i=()=>{}){ti(e,t,{during:this.leave.during,start:this.leave.start,end:this.leave.end},r,i)}})}window.Element.prototype._x_toggleAndCascadeWithTransitions=function(e,t,n,r){let i=()=>{document.visibilityState==="visible"?requestAnimationFrame(n):setTimeout(n)};if(t){e._x_transition&&(e._x_transition.enter||e._x_transition.leave)?e._x_transition.enter&&(Object.entries(e._x_transition.enter.during).length||Object.entries(e._x_transition.enter.start).length||Object.entries(e._x_transition.enter.end).length)?e._x_transition.in(n):i():e._x_transition?e._x_transition.in(n):i();return}e._x_hidePromise=e._x_transition?new Promise((o,a)=>{e._x_transition.out(()=>{},()=>o(r)),e._x_transitioning.beforeCancel(()=>a({isFromCancelledTransition:!0}))}):Promise.resolve(r),queueMicrotask(()=>{let o=Cc(e);o?(o._x_hideChildren||(o._x_hideChildren=[]),o._x_hideChildren.push(e)):queueMicrotask(()=>{let a=u=>{let f=Promise.all([u._x_hidePromise,...(u._x_hideChildren||[]).map(a)]).then(([d])=>d());return delete u._x_hidePromise,delete u._x_hideChildren,f};a(e).catch(u=>{if(!u.isFromCancelledTransition)throw u})})})};function Cc(e){let t=e.parentNode;if(!!t)return t._x_hidePromise?t:Cc(t)}function ti(e,t,{during:n,start:r,end:i}={},o=()=>{},a=()=>{}){if(e._x_transitioning&&e._x_transitioning.cancel(),Object.keys(n).length===0&&Object.keys(r).length===0&&Object.keys(i).length===0){o(),a();return}let u,f,d;Wm(e,{start(){u=t(e,r)},during(){f=t(e,n)},before:o,end(){u(),d=t(e,i)},after:a,cleanup(){f(),d()}})}function Wm(e,t){let n,r,i,o=ei(()=>{Q(()=>{n=!0,r||t.before(),i||(t.end(),Zr()),t.after(),e.isConnected&&t.cleanup(),delete e._x_transitioning})});e._x_transitioning={beforeCancels:[],beforeCancel(a){this.beforeCancels.push(a)},cancel:ei(function(){for(;this.beforeCancels.length;)this.beforeCancels.shift()();o()}),finish:o},Q(()=>{t.start(),t.during()}),Im(),requestAnimationFrame(()=>{if(n)return;let a=Number(getComputedStyle(e).transitionDuration.replace(/,.*/,"").replace("s",""))*1e3,u=Number(getComputedStyle(e).transitionDelay.replace(/,.*/,"").replace("s",""))*1e3;a===0&&(a=Number(getComputedStyle(e).animationDuration.replace("s",""))*1e3),Q(()=>{t.before()}),r=!0,requestAnimationFrame(()=>{n||(Q(()=>{t.end()}),Zr(),setTimeout(e._x_transitioning.finish,a+u),i=!0)})})}function Gt(e,t,n){if(e.indexOf(t)===-1)return n;const r=e[e.indexOf(t)+1];if(!r||t==="scale"&&isNaN(r))return n;if(t==="duration"){let i=r.match(/([0-9]+)ms/);if(i)return i[1]}return t==="origin"&&["top","right","left","center","bottom"].includes(e[e.indexOf(t)+2])?[r,e[e.indexOf(t)+2]].join(" "):r}var ni=!1;function ir(e,t=()=>{}){return(...n)=>ni?t(...n):e(...n)}function Km(e,t){t._x_dataStack||(t._x_dataStack=e._x_dataStack),ni=!0,Gm(()=>{Um(t)}),ni=!1}function Um(e){let t=!1;Ke(e,(r,i)=>{dt(r,(o,a)=>{if(t&&km(o))return a();t=!0,i(o,a)})})}function Gm(e){let t=fn;io((n,r)=>{let i=t(n);return er(i),()=>{}}),e(),io(t)}function $c(e,t,n,r=[]){switch(e._x_bindings||(e._x_bindings=jt({})),e._x_bindings[t]=n,t=r.includes("camel")?Zm(t):t,t){case"value":Ym(e,n);break;case"style":zm(e,n);break;case"class":qm(e,n);break;default:Xm(e,t,n);break}}function Ym(e,t){if(e.type==="radio")e.attributes.value===void 0&&(e.value=t),window.fromModel&&(e.checked=ao(e.value,t));else if(e.type==="checkbox")Number.isInteger(t)?e.value=t:!Number.isInteger(t)&&!Array.isArray(t)&&typeof t!="boolean"&&![null,void 0].includes(t)?e.value=String(t):Array.isArray(t)?e.checked=t.some(n=>ao(n,e.value)):e.checked=!!t;else if(e.tagName==="SELECT")Jm(e,t);else{if(e.value===t)return;e.value=t}}function qm(e,t){e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedClasses=zi(e,t)}function zm(e,t){e._x_undoAddedStyles&&e._x_undoAddedStyles(),e._x_undoAddedStyles=rr(e,t)}function Xm(e,t,n){[null,void 0,!1].includes(n)&&eg(t)?e.removeAttribute(t):(Nc(t)&&(n=t),Qm(e,t,n))}function Qm(e,t,n){e.getAttribute(t)!=n&&e.setAttribute(t,n)}function Jm(e,t){const n=[].concat(t).map(r=>r+"");Array.from(e.options).forEach(r=>{r.selected=n.includes(r.value)})}function Zm(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function ao(e,t){return e==t}function Nc(e){return["disabled","checked","required","readonly","hidden","open","selected","autofocus","itemscope","multiple","novalidate","allowfullscreen","allowpaymentrequest","formnovalidate","autoplay","controls","loop","muted","playsinline","default","ismap","reversed","async","defer","nomodule"].includes(e)}function eg(e){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(e)}function tg(e,t,n){if(e._x_bindings&&e._x_bindings[t]!==void 0)return e._x_bindings[t];let r=e.getAttribute(t);return r===null?typeof n=="function"?n():n:Nc(t)?!![t,"true"].includes(r):r===""?!0:r}function Dc(e,t){var n;return function(){var r=this,i=arguments,o=function(){n=null,e.apply(r,i)};clearTimeout(n),n=setTimeout(o,t)}}function Lc(e,t){let n;return function(){let r=this,i=arguments;n||(e.apply(r,i),n=!0,setTimeout(()=>n=!1,t))}}function ng(e){e(pn)}var et={},co=!1;function rg(e,t){if(co||(et=jt(et),co=!0),t===void 0)return et[e];et[e]=t,typeof t=="object"&&t!==null&&t.hasOwnProperty("init")&&typeof t.init=="function"&&et[e].init(),ac(et[e])}function ig(){return et}var Ic={};function sg(e,t){Ic[e]=typeof t!="function"?()=>t:t}function og(e){return Object.entries(Ic).forEach(([t,n])=>{Object.defineProperty(e,t,{get(){return(...r)=>n(...r)}})}),e}var Mc={};function ag(e,t){Mc[e]=t}function cg(e,t){return Object.entries(Mc).forEach(([n,r])=>{Object.defineProperty(e,n,{get(){return(...i)=>r.bind(t)(...i)},enumerable:!1})}),e}var lg={get reactive(){return jt},get release(){return er},get effect(){return fn},get raw(){return Za},version:"3.9.5",flushAndStopDeferringMutations:bm,disableEffectScheduling:um,setReactivityEngine:fm,closestDataStack:Rt,skipDuringClone:ir,addRootSelector:Sc,addInitSelector:Oc,addScopeToNode:dn,deferMutations:ym,mapAttributes:Gi,evaluateLater:se,setEvaluator:Am,mergeProxies:hn,findClosest:nr,closestRoot:tr,interceptor:cc,transition:ti,setStyles:rr,mutateDom:Q,directive:q,throttle:Lc,debounce:Dc,evaluate:xt,initTree:Ke,nextTick:Ec,prefixed:Wt,prefix:xm,plugin:ng,magic:Ne,store:rg,start:Mm,clone:Km,bound:tg,$data:oc,data:ag,bind:sg},pn=lg;function ug(e,t){const n=Object.create(null),r=e.split(",");for(let i=0;i<r.length;i++)n[r[i]]=!0;return t?i=>!!n[i.toLowerCase()]:i=>!!n[i]}var fg=Object.freeze({});Object.freeze([]);var kc=Object.assign,dg=Object.prototype.hasOwnProperty,sr=(e,t)=>dg.call(e,t),ot=Array.isArray,Zt=e=>Pc(e)==="[object Map]",hg=e=>typeof e=="string",Xi=e=>typeof e=="symbol",or=e=>e!==null&&typeof e=="object",pg=Object.prototype.toString,Pc=e=>pg.call(e),Rc=e=>Pc(e).slice(8,-1),Qi=e=>hg(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,_g=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},mg=_g(e=>e.charAt(0).toUpperCase()+e.slice(1)),Hc=(e,t)=>e!==t&&(e===e||t===t),ri=new WeakMap,Yt=[],Ae,at=Symbol("iterate"),ii=Symbol("Map key iterate");function gg(e){return e&&e._isEffect===!0}function vg(e,t=fg){gg(e)&&(e=e.raw);const n=Eg(e,t);return t.lazy||n(),n}function yg(e){e.active&&(Bc(e),e.options.onStop&&e.options.onStop(),e.active=!1)}var bg=0;function Eg(e,t){const n=function(){if(!n.active)return e();if(!Yt.includes(n)){Bc(n);try{return Ag(),Yt.push(n),Ae=n,e()}finally{Yt.pop(),Vc(),Ae=Yt[Yt.length-1]}}};return n.id=bg++,n.allowRecurse=!!t.allowRecurse,n._isEffect=!0,n.active=!0,n.raw=e,n.deps=[],n.options=t,n}function Bc(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}var Ht=!0,Ji=[];function wg(){Ji.push(Ht),Ht=!1}function Ag(){Ji.push(Ht),Ht=!0}function Vc(){const e=Ji.pop();Ht=e===void 0?!0:e}function ye(e,t,n){if(!Ht||Ae===void 0)return;let r=ri.get(e);r||ri.set(e,r=new Map);let i=r.get(n);i||r.set(n,i=new Set),i.has(Ae)||(i.add(Ae),Ae.deps.push(i),Ae.options.onTrack&&Ae.options.onTrack({effect:Ae,target:e,type:t,key:n}))}function Ue(e,t,n,r,i,o){const a=ri.get(e);if(!a)return;const u=new Set,f=g=>{g&&g.forEach(s=>{(s!==Ae||s.allowRecurse)&&u.add(s)})};if(t==="clear")a.forEach(f);else if(n==="length"&&ot(e))a.forEach((g,s)=>{(s==="length"||s>=r)&&f(g)});else switch(n!==void 0&&f(a.get(n)),t){case"add":ot(e)?Qi(n)&&f(a.get("length")):(f(a.get(at)),Zt(e)&&f(a.get(ii)));break;case"delete":ot(e)||(f(a.get(at)),Zt(e)&&f(a.get(ii)));break;case"set":Zt(e)&&f(a.get(at));break}const d=g=>{g.options.onTrigger&&g.options.onTrigger({effect:g,target:e,key:n,type:t,newValue:r,oldValue:i,oldTarget:o}),g.options.scheduler?g.options.scheduler(g):g()};u.forEach(d)}var Tg=ug("__proto__,__v_isRef,__isVue"),Fc=new Set(Object.getOwnPropertyNames(Symbol).map(e=>Symbol[e]).filter(Xi)),Sg=ar(),Og=ar(!1,!0),xg=ar(!0),Cg=ar(!0,!0),Bn={};["includes","indexOf","lastIndexOf"].forEach(e=>{const t=Array.prototype[e];Bn[e]=function(...n){const r=W(this);for(let o=0,a=this.length;o<a;o++)ye(r,"get",o+"");const i=t.apply(r,n);return i===-1||i===!1?t.apply(r,n.map(W)):i}});["push","pop","shift","unshift","splice"].forEach(e=>{const t=Array.prototype[e];Bn[e]=function(...n){wg();const r=t.apply(this,n);return Vc(),r}});function ar(e=!1,t=!1){return function(r,i,o){if(i==="__v_isReactive")return!e;if(i==="__v_isReadonly")return e;if(i==="__v_raw"&&o===(e?t?Hg:nl:t?Rg:tl).get(r))return r;const a=ot(r);if(!e&&a&&sr(Bn,i))return Reflect.get(Bn,i,o);const u=Reflect.get(r,i,o);return(Xi(i)?Fc.has(i):Tg(i))||(e||ye(r,"get",i),t)?u:si(u)?!a||!Qi(i)?u.value:u:or(u)?e?rl(u):ns(u):u}}var $g=jc(),Ng=jc(!0);function jc(e=!1){return function(n,r,i,o){let a=n[r];if(!e&&(i=W(i),a=W(a),!ot(n)&&si(a)&&!si(i)))return a.value=i,!0;const u=ot(n)&&Qi(r)?Number(r)<n.length:sr(n,r),f=Reflect.set(n,r,i,o);return n===W(o)&&(u?Hc(i,a)&&Ue(n,"set",r,i,a):Ue(n,"add",r,i)),f}}function Dg(e,t){const n=sr(e,t),r=e[t],i=Reflect.deleteProperty(e,t);return i&&n&&Ue(e,"delete",t,void 0,r),i}function Lg(e,t){const n=Reflect.has(e,t);return(!Xi(t)||!Fc.has(t))&&ye(e,"has",t),n}function Ig(e){return ye(e,"iterate",ot(e)?"length":at),Reflect.ownKeys(e)}var Wc={get:Sg,set:$g,deleteProperty:Dg,has:Lg,ownKeys:Ig},Kc={get:xg,set(e,t){return console.warn(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0},deleteProperty(e,t){return console.warn(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}};kc({},Wc,{get:Og,set:Ng});kc({},Kc,{get:Cg});var Zi=e=>or(e)?ns(e):e,es=e=>or(e)?rl(e):e,ts=e=>e,cr=e=>Reflect.getPrototypeOf(e);function lr(e,t,n=!1,r=!1){e=e.__v_raw;const i=W(e),o=W(t);t!==o&&!n&&ye(i,"get",t),!n&&ye(i,"get",o);const{has:a}=cr(i),u=r?ts:n?es:Zi;if(a.call(i,t))return u(e.get(t));if(a.call(i,o))return u(e.get(o));e!==i&&e.get(t)}function ur(e,t=!1){const n=this.__v_raw,r=W(n),i=W(e);return e!==i&&!t&&ye(r,"has",e),!t&&ye(r,"has",i),e===i?n.has(e):n.has(e)||n.has(i)}function fr(e,t=!1){return e=e.__v_raw,!t&&ye(W(e),"iterate",at),Reflect.get(e,"size",e)}function Uc(e){e=W(e);const t=W(this);return cr(t).has.call(t,e)||(t.add(e),Ue(t,"add",e,e)),this}function Gc(e,t){t=W(t);const n=W(this),{has:r,get:i}=cr(n);let o=r.call(n,e);o?el(n,r,e):(e=W(e),o=r.call(n,e));const a=i.call(n,e);return n.set(e,t),o?Hc(t,a)&&Ue(n,"set",e,t,a):Ue(n,"add",e,t),this}function Yc(e){const t=W(this),{has:n,get:r}=cr(t);let i=n.call(t,e);i?el(t,n,e):(e=W(e),i=n.call(t,e));const o=r?r.call(t,e):void 0,a=t.delete(e);return i&&Ue(t,"delete",e,void 0,o),a}function qc(){const e=W(this),t=e.size!==0,n=Zt(e)?new Map(e):new Set(e),r=e.clear();return t&&Ue(e,"clear",void 0,void 0,n),r}function dr(e,t){return function(r,i){const o=this,a=o.__v_raw,u=W(a),f=t?ts:e?es:Zi;return!e&&ye(u,"iterate",at),a.forEach((d,g)=>r.call(i,f(d),f(g),o))}}function Tn(e,t,n){return function(...r){const i=this.__v_raw,o=W(i),a=Zt(o),u=e==="entries"||e===Symbol.iterator&&a,f=e==="keys"&&a,d=i[e](...r),g=n?ts:t?es:Zi;return!t&&ye(o,"iterate",f?ii:at),{next(){const{value:s,done:c}=d.next();return c?{value:s,done:c}:{value:u?[g(s[0]),g(s[1])]:g(s),done:c}},[Symbol.iterator](){return this}}}}function Ve(e){return function(...t){{const n=t[0]?`on key "${t[0]}" `:"";console.warn(`${mg(e)} operation ${n}failed: target is readonly.`,W(this))}return e==="delete"?!1:this}}var zc={get(e){return lr(this,e)},get size(){return fr(this)},has:ur,add:Uc,set:Gc,delete:Yc,clear:qc,forEach:dr(!1,!1)},Xc={get(e){return lr(this,e,!1,!0)},get size(){return fr(this)},has:ur,add:Uc,set:Gc,delete:Yc,clear:qc,forEach:dr(!1,!0)},Qc={get(e){return lr(this,e,!0)},get size(){return fr(this,!0)},has(e){return ur.call(this,e,!0)},add:Ve("add"),set:Ve("set"),delete:Ve("delete"),clear:Ve("clear"),forEach:dr(!0,!1)},Jc={get(e){return lr(this,e,!0,!0)},get size(){return fr(this,!0)},has(e){return ur.call(this,e,!0)},add:Ve("add"),set:Ve("set"),delete:Ve("delete"),clear:Ve("clear"),forEach:dr(!0,!0)},Mg=["keys","values","entries",Symbol.iterator];Mg.forEach(e=>{zc[e]=Tn(e,!1,!1),Qc[e]=Tn(e,!0,!1),Xc[e]=Tn(e,!1,!0),Jc[e]=Tn(e,!0,!0)});function Zc(e,t){const n=t?e?Jc:Xc:e?Qc:zc;return(r,i,o)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?r:Reflect.get(sr(n,i)&&i in r?n:r,i,o)}var kg={get:Zc(!1,!1)},Pg={get:Zc(!0,!1)};function el(e,t,n){const r=W(n);if(r!==n&&t.call(e,r)){const i=Rc(e);console.warn(`Reactive ${i} contains both the raw and reactive versions of the same object${i==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}var tl=new WeakMap,Rg=new WeakMap,nl=new WeakMap,Hg=new WeakMap;function Bg(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Vg(e){return e.__v_skip||!Object.isExtensible(e)?0:Bg(Rc(e))}function ns(e){return e&&e.__v_isReadonly?e:il(e,!1,Wc,kg,tl)}function rl(e){return il(e,!0,Kc,Pg,nl)}function il(e,t,n,r,i){if(!or(e))return console.warn(`value cannot be made reactive: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=i.get(e);if(o)return o;const a=Vg(e);if(a===0)return e;const u=new Proxy(e,a===2?r:n);return i.set(e,u),u}function W(e){return e&&W(e.__v_raw)||e}function si(e){return Boolean(e&&e.__v_isRef===!0)}Ne("nextTick",()=>Ec);Ne("dispatch",e=>Jt.bind(Jt,e));Ne("watch",(e,{evaluateLater:t,effect:n})=>(r,i)=>{let o=t(r),a=!0,u,f=n(()=>o(d=>{JSON.stringify(d),a?u=d:queueMicrotask(()=>{i(d,u),u=d}),a=!1}));e._x_effects.delete(f)});Ne("store",ig);Ne("data",e=>oc(e));Ne("root",e=>tr(e));Ne("refs",e=>(e._x_refs_proxy||(e._x_refs_proxy=hn(Fg(e))),e._x_refs_proxy));function Fg(e){let t=[],n=e;for(;n;)n._x_refs&&t.push(n._x_refs),n=n.parentNode;return t}var kr={};function sl(e){return kr[e]||(kr[e]=0),++kr[e]}function jg(e,t){return nr(e,n=>{if(n._x_ids&&n._x_ids[t])return!0})}function Wg(e,t){e._x_ids||(e._x_ids={}),e._x_ids[t]||(e._x_ids[t]=sl(t))}Ne("id",e=>(t,n=null)=>{let r=jg(e,t),i=r?r._x_ids[t]:sl(t);return n?`${t}-${i}-${n}`:`${t}-${i}`});Ne("el",e=>e);q("modelable",(e,{expression:t},{effect:n,evaluateLater:r})=>{let i=r(t),o=()=>{let d;return i(g=>d=g),d},a=r(`${t} = __placeholder`),u=d=>a(()=>{},{scope:{__placeholder:d}}),f=o();u(f),queueMicrotask(()=>{if(!e._x_model)return;e._x_removeModelListeners.default();let d=e._x_model.get,g=e._x_model.set;n(()=>u(d())),n(()=>g(o()))})});q("teleport",(e,{expression:t},{cleanup:n})=>{e.tagName.toLowerCase()!=="template"&&Hn("x-teleport can only be used on a <template> tag",e);let r=document.querySelector(t);r||Hn(`Cannot find x-teleport element for selector: "${t}"`);let i=e.content.cloneNode(!0).firstElementChild;e._x_teleport=i,i._x_teleportBack=e,e._x_forwardEvents&&e._x_forwardEvents.forEach(o=>{i.addEventListener(o,a=>{a.stopPropagation(),e.dispatchEvent(new a.constructor(a.type,a))})}),dn(i,{},e),Q(()=>{r.appendChild(i),Ke(i),i._x_ignore=!0}),n(()=>i.remove())});var ol=()=>{};ol.inline=(e,{modifiers:t},{cleanup:n})=>{t.includes("self")?e._x_ignoreSelf=!0:e._x_ignore=!0,n(()=>{t.includes("self")?delete e._x_ignoreSelf:delete e._x_ignore})};q("ignore",ol);q("effect",(e,{expression:t},{effect:n})=>n(se(e,t)));function al(e,t,n,r){let i=e,o=f=>r(f),a={},u=(f,d)=>g=>d(f,g);if(n.includes("dot")&&(t=Kg(t)),n.includes("camel")&&(t=Ug(t)),n.includes("passive")&&(a.passive=!0),n.includes("capture")&&(a.capture=!0),n.includes("window")&&(i=window),n.includes("document")&&(i=document),n.includes("prevent")&&(o=u(o,(f,d)=>{d.preventDefault(),f(d)})),n.includes("stop")&&(o=u(o,(f,d)=>{d.stopPropagation(),f(d)})),n.includes("self")&&(o=u(o,(f,d)=>{d.target===e&&f(d)})),(n.includes("away")||n.includes("outside"))&&(i=document,o=u(o,(f,d)=>{e.contains(d.target)||d.target.isConnected!==!1&&(e.offsetWidth<1&&e.offsetHeight<1||e._x_isShown!==!1&&f(d))})),n.includes("once")&&(o=u(o,(f,d)=>{f(d),i.removeEventListener(t,o,a)})),o=u(o,(f,d)=>{Yg(t)&&qg(d,n)||f(d)}),n.includes("debounce")){let f=n[n.indexOf("debounce")+1]||"invalid-wait",d=oi(f.split("ms")[0])?Number(f.split("ms")[0]):250;o=Dc(o,d)}if(n.includes("throttle")){let f=n[n.indexOf("throttle")+1]||"invalid-wait",d=oi(f.split("ms")[0])?Number(f.split("ms")[0]):250;o=Lc(o,d)}return i.addEventListener(t,o,a),()=>{i.removeEventListener(t,o,a)}}function Kg(e){return e.replace(/-/g,".")}function Ug(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function oi(e){return!Array.isArray(e)&&!isNaN(e)}function Gg(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function Yg(e){return["keydown","keyup"].includes(e)}function qg(e,t){let n=t.filter(o=>!["window","document","prevent","stop","once"].includes(o));if(n.includes("debounce")){let o=n.indexOf("debounce");n.splice(o,oi((n[o+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.length===0||n.length===1&&lo(e.key).includes(n[0]))return!1;const i=["ctrl","shift","alt","meta","cmd","super"].filter(o=>n.includes(o));return n=n.filter(o=>!i.includes(o)),!(i.length>0&&i.filter(a=>((a==="cmd"||a==="super")&&(a="meta"),e[`${a}Key`])).length===i.length&&lo(e.key).includes(n[0]))}function lo(e){if(!e)return[];e=Gg(e);let t={ctrl:"control",slash:"/",space:"-",spacebar:"-",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",equal:"="};return t[e]=e,Object.keys(t).map(n=>{if(t[n]===e)return n}).filter(n=>n)}q("model",(e,{modifiers:t,expression:n},{effect:r,cleanup:i})=>{let o=se(e,n),a=`${n} = rightSideOfExpression($event, ${n})`,u=se(e,a);var f=e.tagName.toLowerCase()==="select"||["checkbox","radio"].includes(e.type)||t.includes("lazy")?"change":"input";let d=zg(e,t,n),g=al(e,f,t,c=>{u(()=>{},{scope:{$event:c,rightSideOfExpression:d}})});e._x_removeModelListeners||(e._x_removeModelListeners={}),e._x_removeModelListeners.default=g,i(()=>e._x_removeModelListeners.default());let s=se(e,`${n} = __placeholder`);e._x_model={get(){let c;return o(l=>c=l),c},set(c){s(()=>{},{scope:{__placeholder:c}})}},e._x_forceModelUpdate=()=>{o(c=>{c===void 0&&n.match(/\./)&&(c=""),window.fromModel=!0,Q(()=>$c(e,"value",c)),delete window.fromModel})},r(()=>{t.includes("unintrusive")&&document.activeElement.isSameNode(e)||e._x_forceModelUpdate()})});function zg(e,t,n){return e.type==="radio"&&Q(()=>{e.hasAttribute("name")||e.setAttribute("name",n)}),(r,i)=>Q(()=>{if(r instanceof CustomEvent&&r.detail!==void 0)return r.detail||r.target.value;if(e.type==="checkbox")if(Array.isArray(i)){let o=t.includes("number")?Pr(r.target.value):r.target.value;return r.target.checked?i.concat([o]):i.filter(a=>!Xg(a,o))}else return r.target.checked;else{if(e.tagName.toLowerCase()==="select"&&e.multiple)return t.includes("number")?Array.from(r.target.selectedOptions).map(o=>{let a=o.value||o.text;return Pr(a)}):Array.from(r.target.selectedOptions).map(o=>o.value||o.text);{let o=r.target.value;return t.includes("number")?Pr(o):t.includes("trim")?o.trim():o}}})}function Pr(e){let t=e?parseFloat(e):null;return Qg(t)?t:e}function Xg(e,t){return e==t}function Qg(e){return!Array.isArray(e)&&!isNaN(e)}q("cloak",e=>queueMicrotask(()=>Q(()=>e.removeAttribute(Wt("cloak")))));Oc(()=>`[${Wt("init")}]`);q("init",ir((e,{expression:t},{evaluate:n})=>typeof t=="string"?!!t.trim()&&n(t,{},!1):n(t,{},!1)));q("text",(e,{expression:t},{effect:n,evaluateLater:r})=>{let i=r(t);n(()=>{i(o=>{Q(()=>{e.textContent=o})})})});q("html",(e,{expression:t},{effect:n,evaluateLater:r})=>{let i=r(t);n(()=>{i(o=>{Q(()=>{e.innerHTML=o,e._x_ignoreSelf=!0,Ke(e),delete e._x_ignoreSelf})})})});Gi(_c(":",mc(Wt("bind:"))));q("bind",(e,{value:t,modifiers:n,expression:r,original:i},{effect:o})=>{if(!t)return Jg(e,r,i);if(t==="key")return Zg(e,r);let a=se(e,r);o(()=>a(u=>{u===void 0&&r.match(/\./)&&(u=""),Q(()=>$c(e,t,u,n))}))});function Jg(e,t,n,r){let i={};og(i);let o=se(e,t),a=[];for(;a.length;)a.pop()();o(u=>{let f=Object.entries(u).map(([g,s])=>({name:g,value:s})),d=Cm(f);f=f.map(g=>d.find(s=>s.name===g.name)?{name:`x-bind:${g.name}`,value:`"${g.value}"`}:g),Ui(e,f,n).map(g=>{a.push(g.runCleanups),g()})},{scope:i})}function Zg(e,t){e._x_keyExpression=t}Sc(()=>`[${Wt("data")}]`);q("data",ir((e,{expression:t},{cleanup:n})=>{t=t===""?"{}":t;let r={};zr(r,e);let i={};cg(i,r);let o=xt(e,t,{scope:i});o===void 0&&(o={}),zr(o,e);let a=jt(o);ac(a);let u=dn(e,a);a.init&&xt(e,a.init),n(()=>{a.destroy&&xt(e,a.destroy),u()})}));q("show",(e,{modifiers:t,expression:n},{effect:r})=>{let i=se(e,n),o=()=>Q(()=>{e.style.display="none",e._x_isShown=!1}),a=()=>Q(()=>{e.style.length===1&&e.style.display==="none"?e.removeAttribute("style"):e.style.removeProperty("display"),e._x_isShown=!0}),u=()=>setTimeout(a),f=ei(s=>s?a():o(),s=>{typeof e._x_toggleAndCascadeWithTransitions=="function"?e._x_toggleAndCascadeWithTransitions(e,s,a,o):s?u():o()}),d,g=!0;r(()=>i(s=>{!g&&s===d||(t.includes("immediate")&&(s?u():o()),f(s),d=s,g=!1)}))});q("for",(e,{expression:t},{effect:n,cleanup:r})=>{let i=tv(t),o=se(e,i.items),a=se(e,e._x_keyExpression||"index");e._x_prevKeys=[],e._x_lookup={},n(()=>ev(e,i,o,a)),r(()=>{Object.values(e._x_lookup).forEach(u=>u.remove()),delete e._x_prevKeys,delete e._x_lookup})});function ev(e,t,n,r){let i=a=>typeof a=="object"&&!Array.isArray(a),o=e;n(a=>{nv(a)&&a>=0&&(a=Array.from(Array(a).keys(),p=>p+1)),a===void 0&&(a=[]);let u=e._x_lookup,f=e._x_prevKeys,d=[],g=[];if(i(a))a=Object.entries(a).map(([p,_])=>{let v=uo(t,_,p,a);r(b=>g.push(b),{scope:{index:p,...v}}),d.push(v)});else for(let p=0;p<a.length;p++){let _=uo(t,a[p],p,a);r(v=>g.push(v),{scope:{index:p,..._}}),d.push(_)}let s=[],c=[],l=[],h=[];for(let p=0;p<f.length;p++){let _=f[p];g.indexOf(_)===-1&&l.push(_)}f=f.filter(p=>!l.includes(p));let m="template";for(let p=0;p<g.length;p++){let _=g[p],v=f.indexOf(_);if(v===-1)f.splice(p,0,_),s.push([m,p]);else if(v!==p){let b=f.splice(p,1)[0],T=f.splice(v-1,1)[0];f.splice(p,0,T),f.splice(v,0,b),c.push([b,T])}else h.push(_);m=_}for(let p=0;p<l.length;p++){let _=l[p];u[_]._x_effects&&u[_]._x_effects.forEach(Ja),u[_].remove(),u[_]=null,delete u[_]}for(let p=0;p<c.length;p++){let[_,v]=c[p],b=u[_],T=u[v],C=document.createElement("div");Q(()=>{T.after(C),b.after(T),T._x_currentIfEl&&T.after(T._x_currentIfEl),C.before(b),b._x_currentIfEl&&b.after(b._x_currentIfEl),C.remove()}),so(T,d[g.indexOf(v)])}for(let p=0;p<s.length;p++){let[_,v]=s[p],b=_==="template"?o:u[_];b._x_currentIfEl&&(b=b._x_currentIfEl);let T=d[v],C=g[v],D=document.importNode(o.content,!0).firstElementChild;dn(D,jt(T),o),Q(()=>{b.after(D),Ke(D)}),typeof C=="object"&&Hn("x-for key cannot be an object, it must be a string or an integer",o),u[C]=D}for(let p=0;p<h.length;p++)so(u[h[p]],d[g.indexOf(h[p])]);o._x_prevKeys=g})}function tv(e){let t=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,n=/^\s*\(|\)\s*$/g,r=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,i=e.match(r);if(!i)return;let o={};o.items=i[2].trim();let a=i[1].replace(n,"").trim(),u=a.match(t);return u?(o.item=a.replace(t,"").trim(),o.index=u[1].trim(),u[2]&&(o.collection=u[2].trim())):o.item=a,o}function uo(e,t,n,r){let i={};return/^\[.*\]$/.test(e.item)&&Array.isArray(t)?e.item.replace("[","").replace("]","").split(",").map(a=>a.trim()).forEach((a,u)=>{i[a]=t[u]}):/^\{.*\}$/.test(e.item)&&!Array.isArray(t)&&typeof t=="object"?e.item.replace("{","").replace("}","").split(",").map(a=>a.trim()).forEach(a=>{i[a]=t[a]}):i[e.item]=t,e.index&&(i[e.index]=n),e.collection&&(i[e.collection]=r),i}function nv(e){return!Array.isArray(e)&&!isNaN(e)}function cl(){}cl.inline=(e,{expression:t},{cleanup:n})=>{let r=tr(e);r._x_refs||(r._x_refs={}),r._x_refs[t]=e,n(()=>delete r._x_refs[t])};q("ref",cl);q("if",(e,{expression:t},{effect:n,cleanup:r})=>{let i=se(e,t),o=()=>{if(e._x_currentIfEl)return e._x_currentIfEl;let u=e.content.cloneNode(!0).firstElementChild;return dn(u,{},e),Q(()=>{e.after(u),Ke(u)}),e._x_currentIfEl=u,e._x_undoIf=()=>{dt(u,f=>{f._x_effects&&f._x_effects.forEach(Ja)}),u.remove(),delete e._x_currentIfEl},u},a=()=>{!e._x_undoIf||(e._x_undoIf(),delete e._x_undoIf)};n(()=>i(u=>{u?o():a()})),r(()=>e._x_undoIf&&e._x_undoIf())});q("id",(e,{expression:t},{evaluate:n})=>{n(t).forEach(i=>Wg(e,i))});Gi(_c("@",mc(Wt("on:"))));q("on",ir((e,{value:t,modifiers:n,expression:r},{cleanup:i})=>{let o=r?se(e,r):()=>{};e.tagName.toLowerCase()==="template"&&(e._x_forwardEvents||(e._x_forwardEvents=[]),e._x_forwardEvents.includes(t)||e._x_forwardEvents.push(t));let a=al(e,t,n,u=>{o(()=>{},{scope:{$event:u},params:[u]})});i(()=>a())}));pn.setEvaluator(fc);pn.setReactivityEngine({reactive:ns,effect:vg,release:yg,raw:W});var rv=pn,ht=rv;const iv=()=>{ht.store("modal",{title:"",html:""}),O._functions.events.eventAlert=e=>{ht.store("modal",e);let t=new ft(document.querySelector("[x-ref='modal']"));t._element.addEventListener("hidden.bs.modal",n=>{O._functions.events.eventRead(e.id)},{once:!0}),t.show()}},sv=()=>{ht.store("toast",{title:"",html:""}),O._functions.events.eventToast=e=>{ht.store("toast",e);let t=new un(document.querySelector("[x-ref='toast']")),n=t._element.querySelector("[data-bs-dismiss='toast']"),r=i=>{O._functions.events.eventRead(e.id)};n.addEventListener("click",r,{once:!0}),t._element.addEventListener("hidden.bs.toast",i=>{n.removeEventListener("click",r)},{once:!0}),t.show()}},ov=()=>{O._functions.events.eventCount=e=>{ht.store("unread_count",e)},O._functions.events.eventRead=e=>{O.events.counter.read.add(e);let t=O.events.counter.unread.getAll().length;O.events.controller.broadcast("counter",{count:t}),ht.store("unread_count",t)},document.addEventListener("alpine:init",()=>{O._functions.events.eventCount(O.events.counter.unread.getAll().length)})};ht.data("LanguageForm",()=>({async set(e){let t=e.target.getAttribute("value");document.cookie=`language=${t};SameSite=Lax`,O.user.id&&await O.fetch("/api/v1/users/me",{method:"PATCH",body:JSON.stringify({language:t})}),window.location.reload()}}));Ge.extend(Vn);O.init(window.init);Zu(),Ju(),tf(),rm(),im(),sm(),ov(),iv(),sv();export{O as C,ft as M,Pt as T,gt as a,mu as c,Ge as d,tf as h,ht as m};
