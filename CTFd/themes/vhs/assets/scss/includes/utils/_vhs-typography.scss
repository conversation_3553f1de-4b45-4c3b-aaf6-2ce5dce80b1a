// VHS Neo-Minimalist Typography System
// Professional, elegant typography with excellent readability and modern aesthetics

@use "vhs-variables" as *;

// Import Professional Fonts
@import url('@fontsource/inter/400.css');
@import url('@fontsource/inter/500.css');
@import url('@fontsource/inter/600.css');
@import url('@fontsource/jetbrains-mono/400.css');
@import url('@fontsource/jetbrains-mono/500.css');
@import url('@fontsource/jetbrains-mono/600.css');
@import url('@fontsource/space-grotesk/400.css');
@import url('@fontsource/space-grotesk/500.css');
@import url('@fontsource/space-grotesk/600.css');

// Professional Font Stacks
$vhs-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
$vhs-font-display: 'Space Grotesk', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
$vhs-font-mono: 'JetBrains Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', monospace;

// Professional Typography Classes
.vhs-font-primary {
  font-family: $vhs-font-primary;
  font-feature-settings: 'kern' 1, 'liga' 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.vhs-font-display {
  font-family: $vhs-font-display;
  font-weight: 600;
  letter-spacing: -0.025em;
  font-feature-settings: 'kern' 1, 'liga' 1;
}

.vhs-font-mono {
  font-family: $vhs-font-mono;
  font-feature-settings: 'kern' 1, 'liga' 0;
}

// Refined Text Effects
.vhs-text-accent {
  color: $vhs-red;
  font-weight: 500;
}

.vhs-text-subtle {
  color: $vhs-white-dark;
  font-weight: 400;
}

.vhs-text-code {
  font-family: $vhs-font-mono;
  font-size: 0.875em;
  color: $vhs-green;
  background: rgba($vhs-black-soft, 0.6);
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  border: 1px solid rgba($vhs-green, 0.2);
}

.vhs-text-error {
  font-family: $vhs-font-mono;
  font-size: 0.875em;
  color: $vhs-red;
  background: rgba($vhs-red, 0.05);
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  border-left: 3px solid $vhs-red;
}

.vhs-text-warning {
  font-family: $vhs-font-mono;
  font-size: 0.875em;
  color: $vhs-yellow;
  background: rgba($vhs-yellow, 0.05);
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  border-left: 3px solid $vhs-yellow;
}

// Professional Heading Styles
.vhs-heading-primary {
  font-family: $vhs-font-display;
  font-weight: 600;
  font-size: clamp(1.75rem, 4vw, 2.5rem);
  line-height: 1.2;
  letter-spacing: -0.02em;
  color: $vhs-white;
  margin-bottom: 1.5rem;
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: -0.75rem;
    left: 0;
    width: 3rem;
    height: 2px;
    background: linear-gradient(90deg, $vhs-red, transparent);
    border-radius: 1px;
  }

  &.vhs-accent {
    color: $vhs-red;
  }
}

.vhs-heading-secondary {
  font-family: $vhs-font-display;
  font-weight: 500;
  font-size: clamp(1.25rem, 3vw, 1.75rem);
  line-height: 1.3;
  letter-spacing: -0.01em;
  color: $vhs-white-medium;
  margin-bottom: 1rem;
}

.vhs-heading-tertiary {
  font-family: $vhs-font-primary;
  font-weight: 500;
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  line-height: 1.4;
  color: $vhs-white;
  margin-bottom: 0.75rem;
}

// Professional Code and Preformatted Text
code {
  font-family: $vhs-font-mono;
  background: rgba($vhs-red, 0.1);
  color: $vhs-red;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

pre {
  font-family: $vhs-font-mono;
  background: $vhs-black-soft;
  color: $vhs-white;
  border: 1px solid $vhs-black-medium;
  border-radius: 0.5rem;
  padding: 1rem;

  code {
    background: transparent;
    color: inherit;
    padding: 0;
  }
}

// Professional Blockquotes
blockquote {
  font-family: $vhs-font-primary;
  border-left: 4px solid $vhs-red;
  background: rgba($vhs-red, 0.05);
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  color: $vhs-white-dark;
  font-style: italic;
  border-radius: 0.5rem;

  &::before {
    content: '"';
    color: $vhs-red;
    font-weight: bold;
    margin-right: 0.5rem;
  }
}

// Professional Labels and Badges
.badge {
  font-family: $vhs-font-mono;
  font-weight: 500;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  border-radius: 0.375rem;
  
  &.badge-primary {
    background: $vhs-red;
    color: $vhs-white;
  }
  
  &.badge-secondary {
    background: $vhs-black-medium;
    color: $vhs-white;
  }
  
  &.badge-success {
    background: $vhs-green;
    color: $vhs-white;
  }
  
  &.badge-warning {
    background: $vhs-yellow;
    color: $vhs-black;
  }
}

// Professional Table Typography
.table {
  font-family: $vhs-font-primary;

  th {
    font-family: $vhs-font-display;
    font-weight: 600;
    font-size: 0.875rem;
    letter-spacing: -0.01em;
    color: $vhs-white;
    border-bottom: 2px solid $vhs-red;
  }

  td {
    color: $vhs-white-dark;
    vertical-align: middle;
    font-size: 0.875rem;
  }
}

// Professional Form Typography
.form-label {
  font-family: $vhs-font-primary;
  font-weight: 500;
  font-size: 0.875rem;
  color: $vhs-white;
  margin-bottom: 0.5rem;
}

.form-control, .form-select {
  font-family: $vhs-font-primary;
  color: $vhs-white;
  background: $vhs-black-soft;
  border: 1px solid $vhs-black-medium;
  border-radius: 0.5rem;
  font-size: 0.875rem;

  &:focus {
    color: $vhs-white;
    background: $vhs-black-soft;
    border-color: $vhs-red;
    box-shadow: 0 0 0 2px rgba($vhs-red, 0.2);
  }

  &::placeholder {
    color: $vhs-white-dark;
    opacity: 0.6;
  }
}

// Professional Navigation Typography
.navbar {
  font-family: $vhs-font-display;

  .navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
    letter-spacing: -0.02em;
    color: $vhs-white !important;
  }

  .nav-link {
    font-weight: 500;
    font-size: 0.875rem;
    color: $vhs-white-dark !important;
    transition: all 150ms cubic-bezier(0.25, 1, 0.5, 1);
    
    &:hover {
      color: $vhs-red !important;
      text-shadow: 0 0 3px rgba($vhs-red, 0.5);
    }
  }
}

// VHS Responsive Typography
@media (max-width: 768px) {
  .vhs-heading-primary {
    font-size: 1.5rem;
    letter-spacing: 2px;
  }
  
  .vhs-heading-secondary {
    font-size: 1.25rem;
    letter-spacing: 1px;
  }
  
  body {
    font-size: 0.9rem;
  }
}
