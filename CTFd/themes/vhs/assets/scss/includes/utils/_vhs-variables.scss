// VHS Neo-Minimalist Theme Color Palette
// Professional, elegant, and refined red, black, and white theme

// Primary VHS Colors - Refined and Professional
$vhs-red: #e11d48;           // Sophisticated red for primary actions
$vhs-red-dark: #be123c;      // Deeper red for hover states
$vhs-red-light: #fecdd3;     // Subtle red for backgrounds
$vhs-red-glow: #f43f5e;      // Refined glow effect

// Sophisticated Grayscale Palette
$vhs-black: #0f0f0f;         // Rich black for backgrounds
$vhs-black-soft: #1c1c1c;    // Elevated surfaces
$vhs-black-medium: #2d2d2d;  // Borders and dividers
$vhs-black-light: #404040;   // Interactive states

$vhs-white: #ffffff;         // Pure white for primary text
$vhs-white-soft: #fafafa;    // Subtle backgrounds
$vhs-white-medium: #e4e4e7;  // Secondary borders
$vhs-white-dark: #a1a1aa;    // Muted text and labels

// Professional Accent Colors
$vhs-green: #10b981;         // Modern success green
$vhs-yellow: #f59e0b;        // Refined warning amber
$vhs-blue: #3b82f6;          // Clean info blue

// Neo-Minimalist Gradients
$vhs-gradient-primary: linear-gradient(135deg, $vhs-red 0%, $vhs-red-dark 100%);
$vhs-gradient-surface: linear-gradient(135deg, $vhs-black-soft 0%, $vhs-black-medium 100%);
$vhs-gradient-subtle: linear-gradient(180deg, transparent 0%, rgba($vhs-red, 0.02) 100%);

// Refined Visual Effects
$vhs-shadow-subtle: 0 1px 3px rgba($vhs-black, 0.12), 0 1px 2px rgba($vhs-black, 0.24);
$vhs-shadow-medium: 0 4px 6px rgba($vhs-black, 0.07), 0 2px 4px rgba($vhs-black, 0.06);
$vhs-shadow-large: 0 10px 15px rgba($vhs-black, 0.1), 0 4px 6px rgba($vhs-black, 0.05);
$vhs-glow-subtle: 0 0 0 1px rgba($vhs-red, 0.1), 0 0 8px rgba($vhs-red, 0.15);
$vhs-glow-focus: 0 0 0 2px rgba($vhs-red, 0.2), 0 0 12px rgba($vhs-red, 0.25);

// Bootstrap Variable Overrides for VHS Theme
$primary: $vhs-red;
$secondary: $vhs-white-dark;
$success: $vhs-green;
$info: $vhs-blue;
$warning: $vhs-yellow;
$danger: $vhs-red;
$light: $vhs-white-soft;
$dark: $vhs-black;

// Background Colors
$body-bg: $vhs-black;
$body-color: $vhs-white;

// Component Colors
$card-bg: $vhs-black-soft;
$card-border-color: $vhs-black-medium;
$navbar-dark-bg: $vhs-black;
$navbar-dark-color: $vhs-white;
$navbar-dark-hover-color: $vhs-red;

// Form Colors
$input-bg: $vhs-black-soft;
$input-border-color: $vhs-black-medium;
$input-color: $vhs-white;
$input-focus-border-color: $vhs-red;
$input-focus-box-shadow: 0 0 0 0.2rem rgba($vhs-red, 0.25);

// Button Colors
$btn-primary-bg: $vhs-red;
$btn-primary-border: $vhs-red;
$btn-primary-hover-bg: $vhs-red-dark;
$btn-primary-hover-border: $vhs-red-dark;

// Table Colors
$table-bg: transparent;
$table-accent-bg: rgba($vhs-red, 0.05);
$table-hover-bg: rgba($vhs-red, 0.075);
$table-border-color: $vhs-black-medium;
$table-color: $vhs-white;

// Link Colors
$link-color: $vhs-red;
$link-hover-color: $vhs-red-glow;

// Border Colors
$border-color: $vhs-black-medium;
$border-color-translucent: rgba($vhs-white, 0.175);

// Text Colors
$text-muted: $vhs-white-dark;
$headings-color: $vhs-white;

// Modal Colors
$modal-content-bg: $vhs-black-soft;
$modal-content-border-color: $vhs-black-medium;
$modal-header-border-color: $vhs-black-medium;
$modal-footer-border-color: $vhs-black-medium;

// Dropdown Colors
$dropdown-bg: $vhs-black-soft;
$dropdown-border-color: $vhs-black-medium;
$dropdown-color: $vhs-white;
$dropdown-link-color: $vhs-white;
$dropdown-link-hover-color: $vhs-white;
$dropdown-link-hover-bg: rgba($vhs-red, 0.1);

// Alert Colors
$alert-bg-scale: -80%;
$alert-border-scale: -70%;
$alert-color-scale: 40%;

// Progress Colors
$progress-bg: $vhs-black-medium;
$progress-bar-bg: $vhs-red;

// Pagination Colors
$pagination-bg: $vhs-black-soft;
$pagination-border-color: $vhs-black-medium;
$pagination-color: $vhs-white;
$pagination-hover-color: $vhs-white;
$pagination-hover-bg: rgba($vhs-red, 0.1);
$pagination-hover-border-color: $vhs-red;
$pagination-active-color: $vhs-white;
$pagination-active-bg: $vhs-red;
$pagination-active-border-color: $vhs-red;
