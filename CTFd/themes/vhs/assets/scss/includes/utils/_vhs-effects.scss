// VHS Visual Effects
// Subtle retro effects that maintain professionalism

@use "vhs-variables" as *;

// VHS Scanline Effect
@keyframes vhs-scanlines {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100vh); }
}

.vhs-scanlines {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      rgba($vhs-red, 0.03) 2px,
      rgba($vhs-red, 0.03) 4px
    );
    pointer-events: none;
    z-index: 1;
  }
}

// VHS Glow Effects
.vhs-glow-subtle {
  box-shadow: 0 0 5px rgba($vhs-red, 0.2);
  transition: box-shadow 0.3s ease;
  
  &:hover {
    box-shadow: 0 0 10px rgba($vhs-red, 0.4);
  }
}

.vhs-glow-medium {
  box-shadow: 0 0 10px rgba($vhs-red, 0.3);
  transition: box-shadow 0.3s ease;
  
  &:hover {
    box-shadow: 0 0 15px rgba($vhs-red, 0.5);
  }
}

.vhs-glow-strong {
  box-shadow: 0 0 15px rgba($vhs-red, 0.4);
  transition: box-shadow 0.3s ease;
  
  &:hover {
    box-shadow: 0 0 20px rgba($vhs-red, 0.6);
  }
}

// VHS Border Effects
.vhs-border-glow {
  border: 1px solid rgba($vhs-red, 0.3);
  box-shadow: 
    0 0 5px rgba($vhs-red, 0.2),
    inset 0 0 5px rgba($vhs-red, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: rgba($vhs-red, 0.6);
    box-shadow: 
      0 0 10px rgba($vhs-red, 0.4),
      inset 0 0 10px rgba($vhs-red, 0.2);
  }
}

.vhs-border-solid {
  border: 2px solid $vhs-red;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, $vhs-red, transparent, $vhs-red);
    z-index: -1;
    opacity: 0.3;
  }
}

// VHS Flicker Effect (subtle)
@keyframes vhs-flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.98; }
}

.vhs-flicker {
  animation: vhs-flicker 3s ease-in-out infinite;
}

// VHS Static Effect (very subtle)
@keyframes vhs-static {
  0% { transform: translate(0); }
  10% { transform: translate(-1px, -1px); }
  20% { transform: translate(1px, -1px); }
  30% { transform: translate(-1px, 1px); }
  40% { transform: translate(1px, 1px); }
  50% { transform: translate(-1px, -1px); }
  60% { transform: translate(1px, -1px); }
  70% { transform: translate(-1px, 1px); }
  80% { transform: translate(1px, 1px); }
  90% { transform: translate(-1px, -1px); }
  100% { transform: translate(0); }
}

.vhs-static {
  animation: vhs-static 0.5s ease-in-out infinite;
  animation-play-state: paused;
  
  &:hover {
    animation-play-state: running;
  }
}

// VHS Terminal Cursor
@keyframes vhs-cursor {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.vhs-cursor::after {
  content: '_';
  color: $vhs-red;
  animation: vhs-cursor 1s infinite;
  font-weight: bold;
}

// VHS Loading Effect
@keyframes vhs-loading {
  0% { width: 0%; }
  100% { width: 100%; }
}

.vhs-loading {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0%;
    background: linear-gradient(90deg, transparent, rgba($vhs-red, 0.3), transparent);
    animation: vhs-loading 2s ease-in-out infinite;
  }
}

// VHS Noise Overlay (very subtle)
.vhs-noise {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(circle, rgba($vhs-white, 0.01) 1px, transparent 1px);
    background-size: 4px 4px;
    pointer-events: none;
    opacity: 0.1;
  }
}

// VHS Retro Grid
.vhs-grid {
  background-image: 
    linear-gradient(rgba($vhs-red, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba($vhs-red, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

// VHS Hologram Effect
.vhs-hologram {
  background: linear-gradient(
    45deg,
    rgba($vhs-red, 0.1),
    rgba($vhs-blue, 0.1),
    rgba($vhs-red, 0.1)
  );
  background-size: 400% 400%;
  animation: vhs-hologram 4s ease infinite;
}

@keyframes vhs-hologram {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

// VHS Glitch Effect (very subtle)
@keyframes vhs-glitch {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-1px, 1px); }
  40% { transform: translate(-1px, -1px); }
  60% { transform: translate(1px, 1px); }
  80% { transform: translate(1px, -1px); }
}

.vhs-glitch {
  animation: vhs-glitch 0.3s ease-in-out;
  animation-play-state: paused;
  
  &:hover {
    animation-play-state: running;
  }
}

// VHS Pulse Effect
@keyframes vhs-pulse {
  0%, 100% { 
    box-shadow: 0 0 5px rgba($vhs-red, 0.3);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 15px rgba($vhs-red, 0.6);
    transform: scale(1.02);
  }
}

.vhs-pulse {
  animation: vhs-pulse 2s ease-in-out infinite;
}

// VHS Focus Ring
.vhs-focus {
  outline: none;
  
  &:focus {
    box-shadow: 
      0 0 0 2px $vhs-black,
      0 0 0 4px $vhs-red,
      0 0 10px rgba($vhs-red, 0.5);
  }
}

// VHS Transition Effects
.vhs-transition {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.vhs-transition-fast {
  transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.vhs-transition-slow {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

// VHS Hover Effects
.vhs-hover-lift {
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba($vhs-red, 0.3);
  }
}

.vhs-hover-glow {
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 0 20px rgba($vhs-red, 0.4);
    border-color: $vhs-red;
  }
}

// VHS Active States
.vhs-active {
  background: rgba($vhs-red, 0.1);
  border-color: $vhs-red;
  box-shadow: inset 0 0 10px rgba($vhs-red, 0.2);
}

// VHS Disabled States
.vhs-disabled {
  opacity: 0.5;
  filter: grayscale(50%);
  cursor: not-allowed;
  
  &:hover {
    opacity: 0.5;
    transform: none;
    box-shadow: none;
  }
}
