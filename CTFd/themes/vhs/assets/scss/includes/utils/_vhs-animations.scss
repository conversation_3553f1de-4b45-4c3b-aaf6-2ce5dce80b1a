// VHS Neo-Minimalist Animations
// Smooth, professional animations with refined easing

@use "vhs-variables" as *;

// Professional Easing Functions
$ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
$ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
$ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
$ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);

// Animation Durations
$duration-fast: 150ms;
$duration-normal: 250ms;
$duration-slow: 350ms;
$duration-slower: 500ms;

// Fade Animations
@keyframes vhs-fade-in {
  from {
    opacity: 0;
    transform: translateY(0.5rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes vhs-fade-in-up {
  from {
    opacity: 0;
    transform: translateY(1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes vhs-fade-in-scale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// Slide Animations
@keyframes vhs-slide-in-right {
  from {
    opacity: 0;
    transform: translateX(1rem);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes vhs-slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-1rem);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Glow Pulse Animation
@keyframes vhs-glow-pulse {
  0%, 100% {
    box-shadow: $vhs-glow-subtle;
  }
  50% {
    box-shadow: $vhs-glow-focus;
  }
}

// Subtle Breathing Animation
@keyframes vhs-breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.95;
  }
}

// Loading Shimmer
@keyframes vhs-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// Smooth Hover Lift
@keyframes vhs-hover-lift {
  from {
    transform: translateY(0);
    box-shadow: $vhs-shadow-subtle;
  }
  to {
    transform: translateY(-2px);
    box-shadow: $vhs-shadow-medium;
  }
}

// Animation Classes
.vhs-animate-fade-in {
  animation: vhs-fade-in $duration-normal $ease-out-quart;
}

.vhs-animate-fade-in-up {
  animation: vhs-fade-in-up $duration-slow $ease-out-expo;
}

.vhs-animate-fade-in-scale {
  animation: vhs-fade-in-scale $duration-normal $ease-out-quart;
}

.vhs-animate-slide-in-right {
  animation: vhs-slide-in-right $duration-normal $ease-out-quart;
}

.vhs-animate-slide-in-left {
  animation: vhs-slide-in-left $duration-normal $ease-out-quart;
}

// Hover Animations
.vhs-hover-lift {
  transition: all $duration-normal $ease-out-quart;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $vhs-shadow-medium;
  }
}

.vhs-hover-scale {
  transition: transform $duration-fast $ease-out-quart;
  
  &:hover {
    transform: scale(1.02);
  }
}

.vhs-hover-glow {
  transition: box-shadow $duration-normal $ease-out-quart;
  
  &:hover {
    box-shadow: $vhs-glow-subtle;
  }
}

.vhs-hover-accent {
  transition: color $duration-fast $ease-out-quart;
  
  &:hover {
    color: $vhs-red;
  }
}

// Focus Animations
.vhs-focus-ring {
  transition: box-shadow $duration-fast $ease-out-quart;
  
  &:focus {
    outline: none;
    box-shadow: $vhs-glow-focus;
  }
}

// Loading States
.vhs-loading {
  position: relative;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(
      90deg,
      transparent,
      rgba($vhs-white, 0.1),
      transparent
    );
    animation: vhs-shimmer 2s infinite;
  }
}

// Stagger Animation Delays
.vhs-stagger-1 { animation-delay: 0ms; }
.vhs-stagger-2 { animation-delay: 100ms; }
.vhs-stagger-3 { animation-delay: 200ms; }
.vhs-stagger-4 { animation-delay: 300ms; }
.vhs-stagger-5 { animation-delay: 400ms; }

// Transition Utilities
.vhs-transition-all {
  transition: all $duration-normal $ease-out-quart;
}

.vhs-transition-colors {
  transition: color $duration-fast $ease-out-quart,
              background-color $duration-fast $ease-out-quart,
              border-color $duration-fast $ease-out-quart;
}

.vhs-transition-transform {
  transition: transform $duration-normal $ease-out-quart;
}

.vhs-transition-opacity {
  transition: opacity $duration-fast $ease-out-quart;
}

// Reduced Motion Support
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .vhs-hover-lift:hover {
    transform: none;
  }
  
  .vhs-hover-scale:hover {
    transform: none;
  }
}

// Page Load Animation
.vhs-page-enter {
  animation: vhs-fade-in-up $duration-slower $ease-out-expo;
}

// Modal Animations
.vhs-modal-enter {
  animation: vhs-fade-in-scale $duration-normal $ease-out-quart;
}

// Button Press Animation
.vhs-button-press {
  transition: transform $duration-fast $ease-out-quart;
  
  &:active {
    transform: scale(0.98);
  }
}
