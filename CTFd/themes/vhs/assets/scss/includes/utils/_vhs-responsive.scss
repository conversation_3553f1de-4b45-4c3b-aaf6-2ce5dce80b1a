// VHS Responsive Design System
// Professional breakpoints and responsive utilities

@use "vhs-variables" as *;

// Breakpoints
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

// Container Max Widths
$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px
);

// Responsive Mixins
@mixin media-up($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    $min-width: map-get($breakpoints, $breakpoint);
    @if $min-width != 0 {
      @media (min-width: $min-width) {
        @content;
      }
    } @else {
      @content;
    }
  }
}

@mixin media-down($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    $max-width: map-get($breakpoints, $breakpoint) - 0.02px;
    @media (max-width: $max-width) {
      @content;
    }
  }
}

@mixin media-between($lower, $upper) {
  @if map-has-key($breakpoints, $lower) and map-has-key($breakpoints, $upper) {
    $min-width: map-get($breakpoints, $lower);
    $max-width: map-get($breakpoints, $upper) - 0.02px;
    
    @if $min-width != 0 {
      @media (min-width: $min-width) and (max-width: $max-width) {
        @content;
      }
    } @else {
      @media (max-width: $max-width) {
        @content;
      }
    }
  }
}

// Responsive Typography Scale
.vhs-text-xs { font-size: clamp(0.75rem, 1.5vw, 0.875rem); }
.vhs-text-sm { font-size: clamp(0.875rem, 2vw, 1rem); }
.vhs-text-base { font-size: clamp(1rem, 2.5vw, 1.125rem); }
.vhs-text-lg { font-size: clamp(1.125rem, 3vw, 1.25rem); }
.vhs-text-xl { font-size: clamp(1.25rem, 3.5vw, 1.5rem); }
.vhs-text-2xl { font-size: clamp(1.5rem, 4vw, 2rem); }
.vhs-text-3xl { font-size: clamp(2rem, 5vw, 2.5rem); }

// Responsive Spacing
.vhs-space-xs { margin: clamp(0.25rem, 1vw, 0.5rem); }
.vhs-space-sm { margin: clamp(0.5rem, 2vw, 1rem); }
.vhs-space-md { margin: clamp(1rem, 3vw, 1.5rem); }
.vhs-space-lg { margin: clamp(1.5rem, 4vw, 2rem); }
.vhs-space-xl { margin: clamp(2rem, 5vw, 3rem); }

// Responsive Padding
.vhs-p-xs { padding: clamp(0.25rem, 1vw, 0.5rem); }
.vhs-p-sm { padding: clamp(0.5rem, 2vw, 1rem); }
.vhs-p-md { padding: clamp(1rem, 3vw, 1.5rem); }
.vhs-p-lg { padding: clamp(1.5rem, 4vw, 2rem); }
.vhs-p-xl { padding: clamp(2rem, 5vw, 3rem); }

// Responsive Grid
.vhs-grid {
  display: grid;
  gap: clamp(1rem, 3vw, 2rem);
  
  &.vhs-grid-1 { grid-template-columns: 1fr; }
  &.vhs-grid-2 { 
    grid-template-columns: 1fr;
    @include media-up(md) {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  &.vhs-grid-3 { 
    grid-template-columns: 1fr;
    @include media-up(md) {
      grid-template-columns: repeat(2, 1fr);
    }
    @include media-up(lg) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  &.vhs-grid-4 { 
    grid-template-columns: 1fr;
    @include media-up(sm) {
      grid-template-columns: repeat(2, 1fr);
    }
    @include media-up(lg) {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}

// Responsive Flexbox
.vhs-flex {
  display: flex;
  gap: clamp(0.5rem, 2vw, 1rem);
  
  &.vhs-flex-col {
    flex-direction: column;
  }
  
  &.vhs-flex-row-reverse {
    flex-direction: row-reverse;
  }
  
  &.vhs-flex-col-reverse {
    flex-direction: column-reverse;
  }
  
  &.vhs-flex-wrap {
    flex-wrap: wrap;
  }
  
  &.vhs-flex-center {
    align-items: center;
    justify-content: center;
  }
  
  &.vhs-flex-between {
    justify-content: space-between;
    align-items: center;
  }
  
  &.vhs-flex-around {
    justify-content: space-around;
    align-items: center;
  }
}

// Responsive Visibility
.vhs-hidden-xs {
  @include media-down(sm) {
    display: none !important;
  }
}

.vhs-hidden-sm {
  @include media-between(sm, md) {
    display: none !important;
  }
}

.vhs-hidden-md {
  @include media-between(md, lg) {
    display: none !important;
  }
}

.vhs-hidden-lg {
  @include media-between(lg, xl) {
    display: none !important;
  }
}

.vhs-visible-xs {
  @include media-up(sm) {
    display: none !important;
  }
}

.vhs-visible-sm {
  display: none !important;
  @include media-between(sm, md) {
    display: block !important;
  }
}

.vhs-visible-md {
  display: none !important;
  @include media-between(md, lg) {
    display: block !important;
  }
}

.vhs-visible-lg {
  display: none !important;
  @include media-between(lg, xl) {
    display: block !important;
  }
}

// Responsive Containers
.vhs-container {
  width: 100%;
  margin: 0 auto;
  padding: 0 clamp(1rem, 4vw, 2rem);
  
  @include media-up(sm) {
    max-width: map-get($container-max-widths, sm);
  }
  
  @include media-up(md) {
    max-width: map-get($container-max-widths, md);
  }
  
  @include media-up(lg) {
    max-width: map-get($container-max-widths, lg);
  }
  
  @include media-up(xl) {
    max-width: map-get($container-max-widths, xl);
  }
  
  @include media-up(xxl) {
    max-width: map-get($container-max-widths, xxl);
  }
}

.vhs-container-fluid {
  width: 100%;
  padding: 0 clamp(1rem, 4vw, 2rem);
}

// Responsive Aspect Ratios
.vhs-aspect-square {
  aspect-ratio: 1 / 1;
}

.vhs-aspect-video {
  aspect-ratio: 16 / 9;
}

.vhs-aspect-photo {
  aspect-ratio: 4 / 3;
}

// Mobile-First Responsive Utilities
@include media-down(md) {
  .vhs-mobile-stack {
    flex-direction: column !important;
    
    > * {
      width: 100% !important;
      margin-bottom: 1rem;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .vhs-mobile-center {
    text-align: center !important;
  }
  
  .vhs-mobile-full-width {
    width: 100% !important;
  }
}

// Touch Device Optimizations
@media (hover: none) and (pointer: coarse) {
  .vhs-touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  .vhs-hover-lift:hover {
    transform: none;
  }
  
  .vhs-hover-scale:hover {
    transform: none;
  }
}

// High DPI Display Support
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .vhs-crisp-edges {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}
