// VHS Challenge Component Styles
// Professional styling for challenge cards and related elements

@use "../utils/vhs-variables" as *;

.challenge-button {
  background: $vhs-black-soft !important;
  border: 1px solid $vhs-black-medium !important;
  color: $vhs-white !important;
  font-family: 'Source Code Pro', monospace;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  min-height: 120px;
  
  // VHS glow effect on hover
  &:hover {
    border-color: rgba($vhs-red, 0.6) !important;
    box-shadow: 
      0 0 10px rgba($vhs-red, 0.3),
      inset 0 0 10px rgba($vhs-red, 0.1) !important;
    color: $vhs-white !important;
    
    &::before {
      opacity: 1;
    }
  }
  
  // Subtle scanline effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      rgba($vhs-red, 0.05) 2px,
      rgba($vhs-red, 0.05) 4px
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }
  
  // Challenge status indicators
  &.challenge-solved {
    background: rgba($vhs-green, 0.1) !important;
    border-color: $vhs-green !important;
    color: $vhs-green !important;
    
    &:hover {
      border-color: $vhs-green !important;
      box-shadow: 
        0 0 10px rgba($vhs-green, 0.4),
        inset 0 0 10px rgba($vhs-green, 0.1) !important;
    }
    
    &::after {
      content: '✓';
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      color: $vhs-green;
      font-size: 1.2rem;
      font-weight: bold;
      text-shadow: 0 0 3px rgba($vhs-green, 0.6);
    }
  }
  
  &.challenge-attempted {
    background: rgba($vhs-yellow, 0.1) !important;
    border-color: $vhs-yellow !important;
    
    &:hover {
      border-color: $vhs-yellow !important;
      box-shadow: 
        0 0 10px rgba($vhs-yellow, 0.4),
        inset 0 0 10px rgba($vhs-yellow, 0.1) !important;
    }
    
    &::after {
      content: '!';
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      color: $vhs-yellow;
      font-size: 1.2rem;
      font-weight: bold;
      text-shadow: 0 0 3px rgba($vhs-yellow, 0.6);
    }
  }
  
  // Challenge title styling
  .challenge-name {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
  
  // Challenge value styling
  .challenge-value {
    font-size: 0.9rem;
    color: $vhs-red;
    font-weight: 600;
    text-shadow: 0 0 3px rgba($vhs-red, 0.4);
    
    &::before {
      content: '[';
      color: $vhs-white-dark;
    }
    
    &::after {
      content: ' pts]';
      color: $vhs-white-dark;
    }
  }
  
  // Challenge category styling
  .challenge-category {
    font-size: 0.8rem;
    color: $vhs-white-dark;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.25rem;
  }
}

// Challenge modal styling
.challenge-modal {
  .modal-content {
    background: $vhs-black-soft;
    border: 2px solid $vhs-red;
    box-shadow: 
      0 0 20px rgba($vhs-red, 0.3),
      inset 0 0 20px rgba($vhs-red, 0.05);
  }
  
  .modal-header {
    background: $vhs-black;
    border-bottom: 1px solid $vhs-red;
    
    .modal-title {
      color: $vhs-white;
      font-family: 'Source Code Pro', monospace;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 2px;
      text-shadow: 0 0 5px rgba($vhs-red, 0.4);
    }
  }
  
  .modal-body {
    color: $vhs-white;
    font-family: 'Source Code Pro', monospace;
    line-height: 1.6;
    
    .challenge-description {
      background: rgba($vhs-black, 0.3);
      padding: 1rem;
      border-left: 3px solid $vhs-red;
      margin-bottom: 1rem;
    }
    
    .challenge-files {
      .file-button {
        background: rgba($vhs-blue, 0.1);
        border: 1px solid $vhs-blue;
        color: $vhs-blue;
        font-family: 'Source Code Pro', monospace;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        
        &:hover {
          background: rgba($vhs-blue, 0.2);
          box-shadow: 0 0 5px rgba($vhs-blue, 0.4);
        }
      }
    }
    
    .challenge-hints {
      .hint-button {
        background: rgba($vhs-yellow, 0.1);
        border: 1px solid $vhs-yellow;
        color: $vhs-yellow;
        font-family: 'Source Code Pro', monospace;
        font-weight: 600;
        
        &:hover {
          background: rgba($vhs-yellow, 0.2);
          box-shadow: 0 0 5px rgba($vhs-yellow, 0.4);
        }
      }
    }
  }
  
  .modal-footer {
    background: $vhs-black;
    border-top: 1px solid $vhs-red;
    
    .flag-input {
      background: $vhs-black-soft;
      border: 1px solid $vhs-black-medium;
      color: $vhs-white;
      font-family: 'Source Code Pro', monospace;
      
      &:focus {
        border-color: $vhs-red;
        box-shadow: 0 0 0 0.2rem rgba($vhs-red, 0.25);
      }
    }
    
    .submit-flag {
      background: $vhs-red;
      border-color: $vhs-red;
      color: $vhs-white;
      font-family: 'Source Code Pro', monospace;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      
      &:hover {
        background: $vhs-red-dark;
        border-color: $vhs-red-dark;
        box-shadow: 0 0 10px rgba($vhs-red, 0.5);
      }
    }
  }
}

// Challenge category headers
.category-header {
  position: relative;
  margin-bottom: 2rem;
  
  h3 {
    color: $vhs-white;
    font-family: 'Source Code Pro', monospace;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 3px;
    margin-bottom: 0.5rem;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -0.5rem;
      left: 0;
      width: 100%;
      height: 1px;
      background: linear-gradient(90deg, $vhs-red, transparent);
      box-shadow: 0 0 3px rgba($vhs-red, 0.5);
    }
  }
}

// Challenge loading states
.challenge-loading {
  .spinner {
    color: $vhs-red;
    text-shadow: 0 0 5px rgba($vhs-red, 0.5);
  }
}

// Challenge statistics
.challenge-stats {
  font-family: 'Source Code Pro', monospace;
  color: $vhs-white-medium;
  font-size: 0.9rem;
  
  .stat-item {
    display: inline-block;
    margin-right: 1rem;
    
    .stat-label {
      color: $vhs-white-dark;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
    
    .stat-value {
      color: $vhs-red;
      font-weight: 600;
      text-shadow: 0 0 3px rgba($vhs-red, 0.3);
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .challenge-button {
    min-height: 100px;
    
    .challenge-name {
      font-size: 0.9rem;
    }
    
    .challenge-value {
      font-size: 0.8rem;
    }
  }
  
  .category-header h3 {
    font-size: 1.25rem;
    letter-spacing: 2px;
  }
}
