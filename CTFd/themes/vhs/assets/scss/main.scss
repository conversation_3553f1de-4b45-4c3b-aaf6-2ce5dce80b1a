// Import VHS variables first
@use "includes/utils/vhs-variables" as vhs;

// Import Bootstrap with VHS variable overrides
@use "bootstrap/scss/bootstrap" as * with (
  $primary: vhs.$vhs-red,
  $secondary: vhs.$vhs-white-dark,
  $success: vhs.$vhs-green,
  $info: vhs.$vhs-blue,
  $warning: vhs.$vhs-yellow,
  $danger: vhs.$vhs-red,
  $light: vhs.$vhs-white-soft,
  $dark: vhs.$vhs-black,
  $body-bg: vhs.$vhs-black,
  $body-color: vhs.$vhs-white
);

@use "includes/components/table";
@use "includes/components/jumbotron";
@use "includes/components/challenge";
@use "includes/components/vhs-challenge";
@use "includes/components/sticky-footer";
@use "includes/components/graphs";

@use "includes/utils/fonts";
@use "includes/utils/vhs-typography";
@use "includes/utils/vhs-effects";
@use "includes/utils/vhs-animations";
@use "includes/utils/vhs-responsive";
@use "includes/utils/opacity";
@use "includes/utils/min-height";
@use "includes/utils/cursors";
@use "includes/utils/lolight";

@use "includes/icons/award-icons";
@use "includes/icons/flag-icons";

h1,
h2 {
  font-weight: 500;
  letter-spacing: 2px;
}

a {
  text-decoration: none !important;
}

blockquote {
  border-left: 4px solid vhs.$vhs-white-dark;
  padding-left: 15px;
}

input,
select {
  padding: 0.6rem !important;
  height: auto !important;
}

.fa-spin.spinner {
  text-align: center;
  opacity: 0.5;
}

.badge-notification {
  vertical-align: top;
  margin-left: -1.5em;
  font-size: 50%;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23ffffff' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e");
  background-size: 8px 10px;
}

// VHS Theme Specific Styles
// =========================

// VHS Neo-Minimalist Body and Background
body {
  background: vhs.$vhs-black;
  color: vhs.$vhs-white;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;

  // Subtle gradient overlay
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: vhs.$vhs-gradient-subtle;
    pointer-events: none;
    z-index: -1;
  }
}

// Professional Typography
h1, h2, h3, h4, h5, h6 {
  color: vhs.$vhs-white;
  font-family: 'Space Grotesk', 'Inter', sans-serif;
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin-bottom: 1rem;

  &.vhs-accent {
    color: vhs.$vhs-red;
  }
}

h1 { font-size: clamp(1.75rem, 4vw, 2.5rem); }
h2 { font-size: clamp(1.5rem, 3.5vw, 2rem); }
h3 { font-size: clamp(1.25rem, 3vw, 1.75rem); }
h4 { font-size: clamp(1.125rem, 2.5vw, 1.5rem); }
h5 { font-size: clamp(1rem, 2vw, 1.25rem); }
h6 { font-size: clamp(0.875rem, 1.5vw, 1rem); }

// Professional Links
a {
  color: vhs.$vhs-red;
  text-decoration: none;
  transition: color 150ms cubic-bezier(0.25, 1, 0.5, 1);

  &:hover {
    color: vhs.$vhs-red-glow;
  }

  &:focus {
    outline: 2px solid vhs.$vhs-red;
    outline-offset: 2px;
  }
}

// Professional Cards
.card {
  background: vhs.$vhs-black-soft;
  border: 1px solid vhs.$vhs-black-medium;
  border-radius: 0.75rem;
  box-shadow: vhs.$vhs-shadow-subtle;
  transition: all 250ms cubic-bezier(0.25, 1, 0.5, 1);
  overflow: hidden;

  &:hover {
    border-color: rgba(vhs.$vhs-red, 0.2);
    box-shadow: vhs.$vhs-shadow-medium;
    transform: translateY(-2px);
  }

  .card-header {
    background: linear-gradient(135deg, vhs.$vhs-black-soft, vhs.$vhs-black-medium);
    border-bottom: 1px solid vhs.$vhs-black-medium;
    color: vhs.$vhs-white;
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 500;
    font-size: 1.125rem;
    padding: 1.25rem 1.5rem;
  }

  .card-body {
    padding: 1.5rem;
  }
}

// Professional Buttons
.btn {
  border-radius: 0.5rem;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.75rem 1.5rem;
  transition: all 150ms cubic-bezier(0.25, 1, 0.5, 1);
  position: relative;
  overflow: hidden;

  &:active {
    transform: scale(0.98);
  }

  &.btn-primary {
    background: linear-gradient(135deg, vhs.$vhs-red, vhs.$vhs-red-dark);
    border: none;
    color: vhs.$vhs-white;
    box-shadow: vhs.$vhs-shadow-subtle;

    &:hover {
      box-shadow: vhs.$vhs-shadow-medium;
      transform: translateY(-1px);
    }

    &:focus {
      box-shadow: vhs.$vhs-glow-focus;
    }
  }

  &.btn-outline-primary {
    background: transparent;
    border: 1px solid vhs.$vhs-red;
    color: vhs.$vhs-red;

    &:hover {
      background: vhs.$vhs-red;
      color: vhs.$vhs-white;
      transform: translateY(-1px);
    }
  }

  &.btn-secondary {
    background: vhs.$vhs-black-medium;
    border: 1px solid vhs.$vhs-black-light;
    color: vhs.$vhs-white;

    &:hover {
      background: vhs.$vhs-black-light;
      transform: translateY(-1px);
    }
  }
}

// Professional Forms
.form-control, .form-select {
  background: vhs.$vhs-black-soft;
  border: 1px solid vhs.$vhs-black-medium;
  color: vhs.$vhs-white;
  border-radius: 0.5rem;
  font-family: 'Inter', sans-serif;
  font-size: 0.875rem;
  padding: 0.75rem 1rem;
  transition: all 150ms cubic-bezier(0.25, 1, 0.5, 1);

  &:focus {
    background: vhs.$vhs-black-soft;
    border-color: vhs.$vhs-red;
    color: vhs.$vhs-white;
    box-shadow: vhs.$vhs-glow-focus;
    outline: none;
  }

  &::placeholder {
    color: vhs.$vhs-white-dark;
    opacity: 0.6;
  }

  &:hover {
    border-color: vhs.$vhs-black-light;
  }
}

.form-label {
  color: vhs.$vhs-white;
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

// VHS Tables
.table {
  color: vhs.$vhs-white;

  th {
    border-bottom: 2px solid vhs.$vhs-red;
    color: vhs.$vhs-white;
    font-family: 'Source Code Pro', monospace;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  td {
    border-top: 1px solid vhs.$vhs-black-medium;
    color: vhs.$vhs-white-medium;
  }

  &.table-hover tbody tr:hover {
    background: rgba(vhs.$vhs-red, 0.1);
    color: vhs.$vhs-white;
  }
}

// Professional Navbar
.navbar {
  background: rgba(vhs.$vhs-black, 0.95) !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid vhs.$vhs-black-medium;
  box-shadow: vhs.$vhs-shadow-medium;

  .navbar-brand {
    color: vhs.$vhs-white !important;
    font-family: 'Space Grotesk', sans-serif;
    font-weight: 600;
    font-size: 1.25rem;
    letter-spacing: -0.02em;
    transition: color 150ms cubic-bezier(0.25, 1, 0.5, 1);

    &:hover {
      color: vhs.$vhs-red !important;
    }
  }

  .nav-link {
    color: vhs.$vhs-white-dark !important;
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    font-size: 0.875rem;
    padding: 0.5rem 1rem !important;
    border-radius: 0.375rem;
    transition: all 150ms cubic-bezier(0.25, 1, 0.5, 1);

    &:hover {
      color: vhs.$vhs-white !important;
      background: rgba(vhs.$vhs-red, 0.1) !important;
    }

    &.active {
      color: vhs.$vhs-red !important;
      background: rgba(vhs.$vhs-red, 0.1) !important;
    }
  }

  .navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;

    &:focus {
      box-shadow: none;
    }
  }
}

// VHS Modals
.modal-content {
  background: vhs.$vhs-black-soft;
  border: 1px solid vhs.$vhs-black-medium;
  border-radius: 0;
  box-shadow: 0 10px 30px rgba(vhs.$vhs-black, 0.8);

  .modal-header {
    border-bottom: 1px solid vhs.$vhs-black-medium;
    background: vhs.$vhs-black;

    .modal-title {
      color: vhs.$vhs-white;
      font-family: 'Source Code Pro', monospace;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .btn-close {
      filter: invert(1);
      opacity: 0.7;

      &:hover {
        opacity: 1;
      }
    }
  }

  .modal-body {
    color: vhs.$vhs-white;
  }

  .modal-footer {
    border-top: 1px solid vhs.$vhs-black-medium;
    background: vhs.$vhs-black;
  }
}

// VHS Alerts
.alert {
  border-radius: 0;
  border-left: 4px solid;
  font-family: 'Source Code Pro', monospace;

  &.alert-primary {
    background: rgba(vhs.$vhs-red, 0.1);
    border-left-color: vhs.$vhs-red;
    color: vhs.$vhs-red-light;
  }

  &.alert-success {
    background: rgba(vhs.$vhs-green, 0.1);
    border-left-color: vhs.$vhs-green;
    color: vhs.$vhs-green;
  }

  &.alert-warning {
    background: rgba(vhs.$vhs-yellow, 0.1);
    border-left-color: vhs.$vhs-yellow;
    color: vhs.$vhs-yellow;
  }

  &.alert-danger {
    background: rgba(vhs.$vhs-red, 0.1);
    border-left-color: vhs.$vhs-red;
    color: vhs.$vhs-red-light;
  }
}
