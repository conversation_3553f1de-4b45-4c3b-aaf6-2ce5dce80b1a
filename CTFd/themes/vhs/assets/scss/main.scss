// Import VHS variables first
@use "includes/utils/vhs-variables" as vhs;

// Import Bootstrap with VHS variable overrides
@use "bootstrap/scss/bootstrap" as * with (
  $primary: vhs.$vhs-red,
  $secondary: vhs.$vhs-white-dark,
  $success: vhs.$vhs-green,
  $info: vhs.$vhs-blue,
  $warning: vhs.$vhs-yellow,
  $danger: vhs.$vhs-red,
  $light: vhs.$vhs-white-soft,
  $dark: vhs.$vhs-black,
  $body-bg: vhs.$vhs-black,
  $body-color: vhs.$vhs-white
);

@use "includes/components/table";
@use "includes/components/jumbotron";
@use "includes/components/challenge";
@use "includes/components/vhs-challenge";
@use "includes/components/sticky-footer";
@use "includes/components/graphs";

@use "includes/utils/fonts";
@use "includes/utils/vhs-typography";
@use "includes/utils/vhs-effects";
@use "includes/utils/opacity";
@use "includes/utils/min-height";
@use "includes/utils/cursors";
@use "includes/utils/lolight";

@use "includes/icons/award-icons";
@use "includes/icons/flag-icons";

h1,
h2 {
  font-weight: 500;
  letter-spacing: 2px;
}

a {
  text-decoration: none !important;
}

blockquote {
  border-left: 4px solid vhs.$vhs-white-dark;
  padding-left: 15px;
}

input,
select {
  padding: 0.6rem !important;
  height: auto !important;
}

.fa-spin.spinner {
  text-align: center;
  opacity: 0.5;
}

.badge-notification {
  vertical-align: top;
  margin-left: -1.5em;
  font-size: 50%;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23ffffff' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e");
  background-size: 8px 10px;
}

// VHS Theme Specific Styles
// =========================

// VHS Body and Background
body {
  background: vhs.$vhs-black;
  color: vhs.$vhs-white;
  font-family: 'Source Code Pro', 'Fira Mono', 'Courier New', monospace;
  position: relative;

  // Subtle scanline effect
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: vhs.$vhs-gradient-scanline;
    background-size: 4px 4px;
    pointer-events: none;
    z-index: -1;
    opacity: 0.1;
  }
}

// VHS Typography
h1, h2, h3, h4, h5, h6 {
  color: vhs.$vhs-white;
  font-family: 'Source Code Pro', 'Fira Mono', monospace;
  font-weight: 600;
  letter-spacing: 1px;
  text-transform: uppercase;

  &.vhs-glow {
    text-shadow: vhs.$vhs-text-shadow;
    color: vhs.$vhs-red;
  }
}

// VHS Links
a {
  color: vhs.$vhs-red;
  transition: all 0.3s ease;

  &:hover {
    color: vhs.$vhs-red-glow;
    text-shadow: 0 0 3px rgba(vhs.$vhs-red, 0.6);
  }
}

// VHS Cards
.card {
  background: vhs.$vhs-black-soft;
  border: 1px solid vhs.$vhs-black-medium;
  border-radius: 0;
  box-shadow: 0 2px 8px rgba(vhs.$vhs-black, 0.3);

  &:hover {
    border-color: rgba(vhs.$vhs-red, 0.3);
    box-shadow: vhs.$vhs-border-glow;
  }

  .card-header {
    background: vhs.$vhs-black;
    border-bottom: 1px solid vhs.$vhs-black-medium;
    color: vhs.$vhs-white;
    font-family: 'Source Code Pro', monospace;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
  }
}

// VHS Buttons
.btn {
  border-radius: 0;
  font-family: 'Source Code Pro', monospace;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;

  &.btn-primary {
    background: vhs.$vhs-red;
    border-color: vhs.$vhs-red;

    &:hover {
      background: vhs.$vhs-red-dark;
      border-color: vhs.$vhs-red-dark;
      box-shadow: vhs.$vhs-glow-shadow;
    }
  }

  &.btn-outline-primary {
    color: vhs.$vhs-red;
    border-color: vhs.$vhs-red;

    &:hover {
      background: vhs.$vhs-red;
      border-color: vhs.$vhs-red;
      box-shadow: vhs.$vhs-glow-shadow;
    }
  }
}

// VHS Forms
.form-control, .form-select {
  background: vhs.$vhs-black-soft;
  border: 1px solid vhs.$vhs-black-medium;
  color: vhs.$vhs-white;
  border-radius: 0;
  font-family: 'Source Code Pro', monospace;

  &:focus {
    background: vhs.$vhs-black-soft;
    border-color: vhs.$vhs-red;
    color: vhs.$vhs-white;
    box-shadow: 0 0 0 0.2rem rgba(vhs.$vhs-red, 0.25);
  }

  &::placeholder {
    color: vhs.$vhs-white-dark;
    opacity: 0.7;
  }
}

// VHS Tables
.table {
  color: vhs.$vhs-white;

  th {
    border-bottom: 2px solid vhs.$vhs-red;
    color: vhs.$vhs-white;
    font-family: 'Source Code Pro', monospace;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  td {
    border-top: 1px solid vhs.$vhs-black-medium;
    color: vhs.$vhs-white-medium;
  }

  &.table-hover tbody tr:hover {
    background: rgba(vhs.$vhs-red, 0.1);
    color: vhs.$vhs-white;
  }
}

// VHS Navbar
.navbar {
  background: vhs.$vhs-black !important;
  border-bottom: 2px solid vhs.$vhs-red;
  box-shadow: 0 2px 10px rgba(vhs.$vhs-black, 0.5);

  .navbar-brand {
    color: vhs.$vhs-white !important;
    font-family: 'Source Code Pro', monospace;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 0 5px rgba(vhs.$vhs-red, 0.3);
  }

  .nav-link {
    color: vhs.$vhs-white-medium !important;
    font-family: 'Source Code Pro', monospace;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;

    &:hover {
      color: vhs.$vhs-red !important;
      text-shadow: 0 0 3px rgba(vhs.$vhs-red, 0.5);
    }

    &.active {
      color: vhs.$vhs-red !important;
      text-shadow: 0 0 5px rgba(vhs.$vhs-red, 0.6);
    }
  }
}

// VHS Modals
.modal-content {
  background: vhs.$vhs-black-soft;
  border: 1px solid vhs.$vhs-black-medium;
  border-radius: 0;
  box-shadow: 0 10px 30px rgba(vhs.$vhs-black, 0.8);

  .modal-header {
    border-bottom: 1px solid vhs.$vhs-black-medium;
    background: vhs.$vhs-black;

    .modal-title {
      color: vhs.$vhs-white;
      font-family: 'Source Code Pro', monospace;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .btn-close {
      filter: invert(1);
      opacity: 0.7;

      &:hover {
        opacity: 1;
      }
    }
  }

  .modal-body {
    color: vhs.$vhs-white;
  }

  .modal-footer {
    border-top: 1px solid vhs.$vhs-black-medium;
    background: vhs.$vhs-black;
  }
}

// VHS Alerts
.alert {
  border-radius: 0;
  border-left: 4px solid;
  font-family: 'Source Code Pro', monospace;

  &.alert-primary {
    background: rgba(vhs.$vhs-red, 0.1);
    border-left-color: vhs.$vhs-red;
    color: vhs.$vhs-red-light;
  }

  &.alert-success {
    background: rgba(vhs.$vhs-green, 0.1);
    border-left-color: vhs.$vhs-green;
    color: vhs.$vhs-green;
  }

  &.alert-warning {
    background: rgba(vhs.$vhs-yellow, 0.1);
    border-left-color: vhs.$vhs-yellow;
    color: vhs.$vhs-yellow;
  }

  &.alert-danger {
    background: rgba(vhs.$vhs-red, 0.1);
    border-left-color: vhs.$vhs-red;
    color: vhs.$vhs-red-light;
  }
}
