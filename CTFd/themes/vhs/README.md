# VHS Theme for CTFd

A professional, elegant, and minimalist VHS-style theme for CTFd with a red, black, and white color scheme.

## Features

- **Professional VHS Aesthetic**: Inspired by retro computing and VHS aesthetics while maintaining modern usability
- **Monospace Typography**: Uses Source Code Pro and Fira Mono fonts for that authentic terminal feel
- **Red, Black & White Color Scheme**: High contrast colors that are both stylish and accessible
- **Subtle Visual Effects**:
  - Scanline overlays
  - Glow effects on interactive elements
  - Smooth transitions and hover states
  - Terminal-style typography
- **Fully Responsive**: Works perfectly on desktop, tablet, and mobile devices
- **Accessibility Focused**: High contrast ratios and clear visual hierarchy

## Color Palette

- **Primary Red**: `#dc2626` - Used for primary actions, highlights, and accents
- **Deep Black**: `#0a0a0a` - Main background color
- **Pure White**: `#ffffff` - Primary text color
- **Success Green**: `#16a34a` - Terminal-style green for success states
- **Warning Yellow**: `#eab308` - Retro amber for warnings
- **Info Blue**: `#2563eb` - Cyan-inspired blue for information

## Installation

1. Copy the `vhs` theme folder to your CTFd `themes` directory
2. Install dependencies: `npm install`
3. Build the theme: `npm run build`
4. In CTFd admin panel, go to Admin Panel > Config > Appearance
5. Select "vhs" from the theme dropdown
6. Save changes

## Development

```bash
# Install dependencies
npm install

# Development build with watch mode
npm run dev

# Production build
npm run build
```

## License

This theme is released under the same license as CTFd.

## Credits

Built for CTFd with retro computing and VHS aesthetics in mind.