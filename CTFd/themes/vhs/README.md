# VHS Neo-Minimalist Theme for CTFd

A professional, elegant, and refined VHS-inspired theme for CTFd with sophisticated red, black, and white aesthetics.

## Features

- **Neo-Minimalist Design**: Clean, sophisticated interface with purposeful visual elements
- **Professional Typography**: Uses Inter, Space Grotesk, and JetBrains Mono for optimal readability and modern appeal
- **Refined Color Palette**: Sophisticated red, black, and white scheme with carefully balanced contrast
- **Smooth Animations**: Professional easing functions and micro-interactions that enhance user experience
- **Advanced Responsiveness**: Mobile-first design with fluid typography and adaptive layouts
- **Modern Visual Effects**:
  - Subtle gradients and shadows
  - Smooth hover transitions
  - Focus states for accessibility
  - Professional button interactions
- **Accessibility First**: WCAG compliant with high contrast ratios and keyboard navigation support

## Color Palette

- **Primary Red**: `#dc2626` - Used for primary actions, highlights, and accents
- **Deep Black**: `#0a0a0a` - Main background color
- **Pure White**: `#ffffff` - Primary text color
- **Success Green**: `#16a34a` - Terminal-style green for success states
- **Warning Yellow**: `#eab308` - Retro amber for warnings
- **Info Blue**: `#2563eb` - Cyan-inspired blue for information

## Typography

The theme uses a professional, modern font system:
- **Primary**: Inter - Clean, readable sans-serif for body text and UI elements
- **Display**: Space Grotesk - Modern, distinctive font for headings and branding
- **Monospace**: JetBrains Mono - Professional monospace font for code and technical content
- **Features**: Font smoothing, ligatures, and optimized letter spacing for enhanced readability

## Animations & Interactions

- **Professional Easing**: Custom cubic-bezier curves for smooth, natural motion
- **Micro-interactions**: Subtle hover effects, button presses, and focus states
- **Responsive Animations**: Optimized for different screen sizes and reduced motion preferences
- **Performance**: Hardware-accelerated transforms and optimized animation timing

## Installation

1. Copy the `vhs` theme folder to your CTFd `themes` directory
2. Install dependencies: `npm install`
3. Build the theme: `npm run build`
4. In CTFd admin panel, go to Admin Panel > Config > Appearance
5. Select "vhs" from the theme dropdown
6. Save changes

## Development

```bash
# Install dependencies
npm install

# Development build with watch mode
npm run dev

# Production build
npm run build
```

## License

This theme is released under the same license as CTFd.

## Credits

Built for CTFd with retro computing and VHS aesthetics in mind.