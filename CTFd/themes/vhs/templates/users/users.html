{% extends "base.html" %}

{% block content %}
  <div class="jumbotron">
    <div class="container">
      <h1>{% trans %}Users{% endtrans %}</h1>
    </div>
  </div>
  <div class="container">
    <div class="row">
      <div class="col-md-12">
        {% if q and field %}
          <h5 class="text-muted text-center">
            {% trans %}Searching for users with <strong>{{ field }}</strong> matching <strong>{{ q }}</strong>{% endtrans %}
          </h5>
          <h6 class="text-muted text-center pb-3">
            {% trans page=users.page, total=users.total %}Page {{ page }} of {{ total }} results{% endtrans %}
          </h6>
        {% endif %}

        {% with form = Forms.users.PublicUserSearchForm(field=field, q=q) %}
          <form method="GET" class="row justify-content-around align-items-center">
            <div class="mb-3 col-md-2">
              {{ form.field(class="form-select w-100") }}
            </div>
            <div class="mb-3 col-md-8">
              {{ form.q(class="form-control w-100", placeholder=form.q.description) }}
            </div>
            <div class="mb-3 col-md-2">
              <button type="submit" class="btn btn-primary w-100">
                <i class="fas fa-search" aria-hidden="true"></i>
              </button>
            </div>
          </form>
        {% endwith %}
      </div>
    </div>

    <hr>

    <div class="row">
      <div class="col-md-12">
        <table class="table table-striped align-middle">
          <thead>
          <tr>
            <th>{% trans %}User{% endtrans %}</th>
            <th>{% trans %}Website{% endtrans %}</th>
            <th class="d-none d-md-table-cell">{% trans %}Affiliation{% endtrans %}</th>
            <th class="d-none d-md-table-cell">{% trans %}Country{% endtrans %}</th>
          </tr>
          </thead>
          <tbody>
          {% for user in users.items %}
            <tr>
              <td>
                {% if scores_visible() %}
                  <a href="{{ url_for('users.public', user_id=user.id) }}">
                    {{ user.name | truncate(50) }}
                  </a>
                {% else %}
                  <span>{{ user.name | truncate(50) }}</span>
                {% endif %}

                {% if user.bracket_id %}
                  <span class="badge bg-secondary ms-2">{{ user.bracket.name }}</span>
                {% endif %}

                {% if user.oauth_id %}
                  <a href="https://majorleaguecyber.org/u/{{ user.name }}">
                    <span class="badge bg-primary ms-2">{% trans %}Official{% endtrans %}</span>
                  </a>
                {% endif %}
              </td>

              <td class="text-center" style="width: 10px;">
                {% if user.website and (user.website.startswith('http://') or user.website.startswith('https://')) %}
                  <a href="{{ user.website }}" target="_blank" rel="noopener">
                    <i
                        class="fas fa-external-link-alt" data-toggle="tooltip" data-placement="top"
                        title="{{ user.website }}"
                    ></i>
                  </a>
                {% endif %}
              </td>

              <td class="d-none d-md-table-cell d-lg-table-cell">
                {% if user.affiliation %}
                  {% if user.affiliation | length > 50 %}
                    <span data-toggle="tooltip" data-placement="top" title="{{ user.affiliation }}">
									{% if user.affiliation %}{{ user.affiliation | truncate(50) }}{% endif %}
								</span>
                  {% else %}
                    <span>
									{% if user.affiliation %}{{ user.affiliation | truncate(50) }}{% endif %}
								</span>
                  {% endif %}
                {% endif %}
              </td>
              <td class="d-none d-md-table-cell d-lg-table-cell">
								<span>
									{% if user.country %}
                    <i class="flag-{{ user.country.lower() }}"></i>
                    {{ lookup_country_code(user.country) }}
                  {% endif %}
								</span>
              </td>
            </tr>
          {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    {% if users.pages > 1 %}
      <div class="row">
        <div class="col-md-12">
          <div class="text-center">
            {% trans %}Page{% endtrans %} <br>

            {% if users.page != 1 %}
              <a href="{{ prev_page }}">&lt;&lt;&lt;</a>
            {% endif %}

            <select class="page-select">
              {% for page in range(1, users.pages + 1) %}
                <option {% if users.page == page %}selected{% endif %}>{{ page }}</option>
              {% endfor %}
            </select>

            {% if users.next_num %}
              <a href="{{ next_page }}">&gt;&gt;&gt;</a>
            {% endif %}
          </div>
        </div>
      </div>
    {% endif %}
  </div>
{% endblock %}

{% block scripts %}
  {{ Assets.js("assets/js/users/list.js") }}
{% endblock %}
