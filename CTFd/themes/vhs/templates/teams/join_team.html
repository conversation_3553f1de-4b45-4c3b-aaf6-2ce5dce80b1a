{% extends "base.html" %}

{% block content %}
  <div class="jumbotron">
    <div class="container">
      <h1>{% trans %}Join Team{% endtrans %}</h1>
    </div>
  </div>
  <div class="container">
    <div class="row">
      <div class="col-md-8 col-lg-6 offset-md-2 offset-lg-3">
        {% include "components/errors.html" %}

        {% with form = Forms.teams.TeamJoinForm() %}
          <form method="POST">
            <div class="mb-3">
              {{ form.name.label(class="form-label") }}
              {{ form.name(class="form-control") }}
            </div>

            <div class="mb-3">
              {{ form.password.label(class="form-label") }}
              {{ form.password(class="form-control") }}
            </div>

            <div class="row pt-3">
              <div class="col-md-12">
                {{ form.submit(class="btn btn-success float-end px-4") }}
              </div>
            </div>
            {{ form.nonce() }}
          </form>
        {% endwith %}
      </div>
    </div>
  </div>
{% endblock %}
