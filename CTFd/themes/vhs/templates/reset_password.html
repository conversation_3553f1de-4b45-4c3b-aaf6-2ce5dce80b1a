{% extends "base.html" %}

{% block content %}
  <div class="jumbotron">
    <div class="container">
      <h1>
        {% trans %}Reset Password{% endtrans %}
      </h1>
    </div>
  </div>

  <div class="container">
    <div class="row">
      <div class="col-md-6 offset-md-3">
        {% include "components/errors.html" %}

        {% if mode == "set" %}
          {% with form = Forms.auth.ResetPasswordForm() %}
            <form method="post" accept-charset="utf-8" role="form" class="form-horizontal">
              <p>
                {% trans %}You can now reset the password for your account and log in. Please enter in a new password below.{% endtrans %}
              </p>

              <div class="mb-3">
                {{ form.password.label(class="form-label") }}
                {{ form.password(class="form-control", autocomplete="new-password") }}
              </div>

              <div class="row pt-3">
                <div class="col-6 col-md-4 offset-6 offset-md-8">
                  {{ form.submit(class="btn btn-block btn-primary w-100") }}
                </div>
              </div>

              {{ form.nonce() }}
            </form>

          {% endwith %}
        {% else %}
          {% with form = Forms.auth.ResetPasswordRequestForm() %}

            <form method="post" accept-charset="utf-8" autocomplete="off" role="form" class="form-horizontal">
              <p>
                {% trans %}Please provide the email address associated with your account below.{% endtrans %}
              </p>

              <div class="mb-3">
                {{ form.email.label(class="form-label") }}
                {{ form.email(class="form-control ") }}
              </div>

              <div class="row pt-3">
                <div class="col-6 col-md-4 offset-6 offset-md-8">
                  {{ form.submit(class="btn btn-block btn-primary w-100") }}
                </div>
              </div>

              {{ form.nonce() }}
            </form>

          {% endwith %}
        {% endif %}
      </div>
    </div>
  </div>
{% endblock %}
