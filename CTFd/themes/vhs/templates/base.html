<!DOCTYPE html>
<html>
<head>
  <title>{{ title or Configs.ctf_name }}</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="shortcut icon" href="{{ Configs.ctf_small_icon }}" type="image/x-icon">

  {{ meta | safe }}

  {% block stylesheets %}
    {{ Assets.css("assets/scss/main.scss") }}
  {% endblock %}

  {{ Plugins.styles }}

  {{ Assets.js("assets/js/color_mode_switcher.js", type=None) }}

  <script type="text/javascript">
      window.init = {
          'urlRoot': "{{ request.script_root }}",
          'csrfNonce': "{{ Session.nonce }}",
          'userMode': "{{ Configs.user_mode }}",
          'userId': {{ Session.id }},
          'userName': {{ User.name | tojson }},
          'userEmail': {{ User.email | tojson }},
          'userVerified': {{ User.verified | tojson }},
          'teamId': {{ Team.id | tojson }},
          'teamName': {{ Team.name | tojson }},
          'start': {{ Configs.start | tojson }},
          'end': {{ Configs.end | tojson }},
          'themeSettings': {{ Configs.theme_settings | tojson }},
          'eventSounds': [
            "/themes/core/static/sounds/notification.webm",
            "/themes/core/static/sounds/notification.mp3",
          ],
      }
  </script>

  {{ Configs.theme_header }}
</head>
<body class="vhs-font-primary vhs-animate-fade-in">
{% include "components/navbar.html" %}

<main role="main" class="vhs-transition-all">
  {% block content %}
  {% endblock %}
</main>

{% include "components/snackbar.html" %}

<!-- Footer removed for clean, minimalist design -->

{% include "components/notifications.html" %}

{% block scripts %}
  {{ Assets.js("assets/js/page.js") }}
{% endblock %}

{{ Plugins.scripts }}

{{ Configs.theme_footer }}
</body>
</html>
