.CodeMirror.cm-s-easymde {
  box-sizing: border-box;
  height: auto;
  border: 1px solid lightgray;
  padding: 10px;
  font: inherit;
  z-index: 0;
  word-wrap: break-word;
}

.CodeMirror-scroll {
  overflow-y: hidden;
  overflow-x: auto;
}

.editor-toolbar {
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
  padding: 0 10px;
  border-top: 1px solid #bbb;
  border-left: 1px solid #bbb;
  border-right: 1px solid #bbb;
}

.editor-toolbar:after,
.editor-toolbar:before {
  display: block;
  content: " ";
  height: 1px;
}

.editor-toolbar:before {
  margin-bottom: 8px;
}

.editor-toolbar:after {
  margin-top: 8px;
}

.editor-toolbar.fullscreen {
  width: 100%;
  height: 50px;
  padding-top: 10px;
  padding-bottom: 10px;
  box-sizing: border-box;
  background: #fff;
  border: 0;
  position: fixed;
  top: 0;
  left: 0;
  opacity: 1;
  z-index: 9;
}

.editor-toolbar.fullscreen::before {
  width: 20px;
  height: 50px;
  background: -moz-linear-gradient(
    left,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    color-stop(0%, rgba(255, 255, 255, 1)),
    color-stop(100%, rgba(255, 255, 255, 0))
  );
  background: -webkit-linear-gradient(
    left,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  background: -o-linear-gradient(
    left,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  background: -ms-linear-gradient(
    left,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0) 100%
  );
  position: fixed;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
}

.editor-toolbar.fullscreen::after {
  width: 20px;
  height: 50px;
  background: -moz-linear-gradient(
    left,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 1) 100%
  );
  background: -webkit-gradient(
    linear,
    left top,
    right top,
    color-stop(0%, rgba(255, 255, 255, 0)),
    color-stop(100%, rgba(255, 255, 255, 1))
  );
  background: -webkit-linear-gradient(
    left,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 1) 100%
  );
  background: -o-linear-gradient(
    left,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 1) 100%
  );
  background: -ms-linear-gradient(
    left,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 1) 100%
  );
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 1) 100%
  );
  position: fixed;
  top: 0;
  right: 0;
  margin: 0;
  padding: 0;
}

.editor-toolbar button,
.editor-toolbar .easymde-dropdown {
  background: transparent;
  display: inline-block;
  text-align: center;
  text-decoration: none !important;
  height: 30px;
  margin: 0;
  padding: 0;
  border: 1px solid transparent;
  border-radius: 3px;
  cursor: pointer;
}

.editor-toolbar button {
  width: 30px;
}

.editor-toolbar button.active,
.editor-toolbar button:hover {
  background: #fcfcfc;
  border-color: #95a5a6;
}

.editor-toolbar i.separator {
  display: inline-block;
  width: 0;
  border-left: 1px solid #d9d9d9;
  border-right: 1px solid #fff;
  color: transparent;
  text-indent: -10px;
  margin: 0 6px;
}

.editor-toolbar button:after {
  font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
  font-size: 65%;
  vertical-align: text-bottom;
  position: relative;
  top: 2px;
}

.editor-toolbar button.heading-1:after {
  content: "1";
}

.editor-toolbar button.heading-2:after {
  content: "2";
}

.editor-toolbar button.heading-3:after {
  content: "3";
}

.editor-toolbar button.heading-bigger:after {
  content: "▲";
}

.editor-toolbar button.heading-smaller:after {
  content: "▼";
}

.editor-toolbar.disabled-for-preview button:not(.no-disable) {
  opacity: 0.6;
  pointer-events: none;
}

@media only screen and (max-width: 700px) {
  .editor-toolbar i.no-mobile {
    display: none;
  }
}

.editor-statusbar {
  padding: 8px 10px;
  font-size: 12px;
  color: #959694;
  text-align: right;
}

.editor-statusbar span {
  display: inline-block;
  min-width: 4em;
  margin-left: 1em;
}

.editor-statusbar .lines:before {
  content: "lines: ";
}

.editor-statusbar .words:before {
  content: "words: ";
}

.editor-statusbar .characters:before {
  content: "characters: ";
}

.editor-preview-full {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 7;
  overflow: auto;
  display: none;
  box-sizing: border-box;
}

.editor-preview-side {
  position: fixed;
  bottom: 0;
  width: 50%;
  top: 50px;
  right: 0;
  z-index: 9;
  overflow: auto;
  display: none;
  box-sizing: border-box;
  border: 1px solid #ddd;
  word-wrap: break-word;
}

.editor-preview-active-side {
  display: block;
}

.editor-preview-active {
  display: block;
}

.editor-preview {
  padding: 10px;
  background: #fafafa;
}

.editor-preview > p {
  margin-top: 0;
}

.editor-preview pre {
  background: #eee;
  margin-bottom: 10px;
}

.editor-preview table td,
.editor-preview table th {
  border: 1px solid #ddd;
  padding: 5px;
}

.cm-s-easymde .cm-tag {
  color: #63a35c;
}

.cm-s-easymde .cm-attribute {
  color: #795da3;
}

.cm-s-easymde .cm-string {
  color: #183691;
}

.cm-s-easymde .cm-header-1 {
  font-size: 200%;
  line-height: 200%;
}

.cm-s-easymde .cm-header-2 {
  font-size: 160%;
  line-height: 160%;
}

.cm-s-easymde .cm-header-3 {
  font-size: 125%;
  line-height: 125%;
}

.cm-s-easymde .cm-header-4 {
  font-size: 110%;
  line-height: 110%;
}

.cm-s-easymde .cm-comment {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
}

.cm-s-easymde .cm-link {
  color: #7f8c8d;
}

.cm-s-easymde .cm-url {
  color: #aab2b3;
}

.cm-s-easymde .cm-quote {
  color: #7f8c8d;
  font-style: italic;
}

.editor-toolbar .easymde-dropdown {
  position: relative;
  background: linear-gradient(
    to bottom right,
    #fff 0%,
    #fff 84%,
    #333 50%,
    #333 100%
  );
  border-radius: 0;
  border: 1px solid #fff;
}

.editor-toolbar .easymde-dropdown:hover {
  background: linear-gradient(
    to bottom right,
    #fff 0%,
    #fff 84%,
    #333 50%,
    #333 100%
  );
}

.easymde-dropdown-content {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2);
  padding: 8px;
  z-index: 2;
  top: 30px;
}

.easymde-dropdown:active .easymde-dropdown-content,
.easymde-dropdown:focus .easymde-dropdown-content {
  display: block;
}
