@import "includes/sticky-footer.css";

.section-title {
  border-top: 1px solid lightgray;
  margin: 15px 0 5px 0;
  font-size: 14px;
  font-weight: bold;
  padding: 10px 0 0 20px;
  color: #636c76;
}

// Intended for the Bootstrap navbar icons
.action-icon {
  margin-right: 8px;
  display: inline-block;
  width: 1.25em;
  text-align: center;
}

// Custom color picker on Theme page
input[type="color"].custom-color-picker {
  padding: 5px;
  margin-right: 8px;
  border: none;
  height: 50px;
  width: 50px;
  vertical-align: middle;
}

#score-graph {
  min-height: 400px;
  display: block;
  clear: both;
}

#solves-graph {
  display: block;
  height: 350px;
}

#keys-pie-graph {
  min-height: 400px;
  display: block;
}

#categories-pie-graph {
  min-height: 400px;
  display: block;
}

#points-pie-graph {
  min-height: 400px;
  display: block;
}

#solve-percentages-graph {
  min-height: 400px;
  display: block;
}

#score-distribution-graph {
  min-height: 400px;
  display: block;
}

.no-decoration {
  color: inherit !important;
  text-decoration: none !important;
}

.no-decoration:hover {
  color: inherit !important;
  text-decoration: none !important;
}

.table td,
.table th {
  vertical-align: inherit;
}

pre {
  white-space: pre-wrap;
  margin: 0;
  padding: 0;
}

.form-control {
  position: relative;
  display: block;
  /*padding: 0.8em;*/
  border-radius: 0;
  /*background: #f0f0f0;*/
  /*color: #aaa;*/
  font-weight: 400;
  font-family: "Avenir Next", "Helvetica Neue", Helvetica, Arial, sans-serif;
  -webkit-appearance: none;
}

tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.1) !important;
}

[data-href] {
  cursor: pointer;
}

.sort-col {
  cursor: pointer;
}

input[type="checkbox"] {
  cursor: pointer;
}

.card-radio:checked + .card {
  background-color: transparent !important;
  border-color: #a3d39c;
  box-shadow: 0 0 0 0.1rem #a3d39c;
  transition:
    background-color 0.3s,
    border-color 0.3s;
}
.card-radio:checked + .card .card-radio-clone {
  visibility: visible !important;
}
.card:hover {
  cursor: pointer;
}
