{% extends "admin/base.html" %}

{% block content %}
<div class="jumbotron">
	<div class="container">
		<h1>Scoreboard</h1>
	</div>
</div>
<div class="container">
	<div class="row">
		<div class="col-md-12">
			<div class="float-right pb-3">
				<div class="btn-group" role="group">
					<button type="button" class="btn btn-outline-secondary" id="scoreboard-edit-button" data-toggle="tooltip" title="Hide/Unhide Accounts">
						<i class="btn-fa fas fa-eye"></i>
					</button>
				</div>
			</div>
		</div>
	</div>

	{% if Configs.user_mode == UserModeTypes.TEAMS %}
	<div class="row pb-4">
		<div class="col-md-12">
			<ul class="nav nav-tabs nav-fill" role="tablist">
				<li class="nav-item">
					<a class="nav-link active" data-toggle="tab" href="#standings" role="tab">
						Teams
					</a>
				</li>
				<li class="nav-item">
					<a class="nav-link" data-toggle="tab" href="#user-standings" role="tab">
						Users
					</a>
				</li>
			</ul>
		</div>
	</div>
	{% endif %}

	<div class="row">
		<div class="col-md-12 table-responsive">
			<div class="tab-content">
				<div class="tab-pane fade show active" id="standings" role="tabpanel">
					{% include "admin/scoreboard/standings.html" %}
				</div>
				{% if Configs.user_mode == UserModeTypes.TEAMS %}
				<div class="tab-pane fade" id="user-standings" role="tabpanel">
					{% include "admin/scoreboard/users.html" %}
				</div>
				{% endif %}
			</div>
		</div>
	</div>
</div>
{% endblock %}

{% block scripts %}
{% endblock %}

{% block entrypoint %}
	{{ Assets.js("assets/js/pages/scoreboard.js", theme="admin") }}
{% endblock %}
