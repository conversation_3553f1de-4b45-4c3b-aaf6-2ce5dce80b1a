<table id="scoreboard" class="table table-striped border">
	<thead>
		<tr>
			<th class="border-right" data-checkbox>
				<div class="form-check text-center">
					<input type="checkbox" class="form-check-input" id="scoreboard-bulk-select" autocomplete="off" data-checkbox-all>&nbsp;
				</div>
			</th>
			<th class="sort-col text-center"><b>Place</b></th>
			<th class="sort-col"><b>{{ get_mode_as_word(capitalize=True) }}</b></th>
			<th class="sort-col"><b>Bracket</b></th>
			<th class="sort-col"><b>Score</b></th>
			<th class="sort-col"><b>Visibility</b></th>
		</tr>
	</thead>
	<tbody>
	{% for standing in standings %}
		<tr data-href="{{ generate_account_url(standing.account_id, admin=True) }}">
			<td class="border-right text-center" data-checkbox>
				<div class="form-check">
					<input type="checkbox" class="form-check-input" value="{{ standing.account_id }}" data-account-id="{{ standing.account_id }}" autocomplete="off">&nbsp;
				</div>
			</td>
			<td class="text-center" width="10%">{{ loop.index }}</td>
			<td>
				<a href="{{ generate_account_url(standing.account_id, admin=True) }}">
					{{ standing.name }}
					{% if standing.oauth_id %}
						{% if get_config('user_mode') == 'teams' %}
							<a href="https://majorleaguecyber.org/t/{{ standing.name }}">
								<span class="badge badge-primary">Official</span>
							</a>
						{% elif get_config('user_mode') == 'users' %}
							<a href="https://majorleaguecyber.org/u/{{ standing.name }}">
								<span class="badge badge-primary">Official</span>
							</a>
						{% endif %}
					{% endif %}
				</a>
			</td>
			<td>
				<span class="badge badge-secondary">{{ standing.bracket_name }}</span>
			</td>
			<td>{{ standing.score }}</td>
			<td>
				{% if standing.hidden %}
					<span class="badge badge-danger">hidden</span>
				{% else %}
					<span class="badge badge-success">visible</span>
				{% endif %}
			</td>
		</tr>
	{% endfor %}
	</tbody>
</table>