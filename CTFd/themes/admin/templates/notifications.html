{% extends "admin/base.html" %}

{% block content %}
	<div class="jumbotron">
		<div class="container">
			<h1>Notifications</h1>
		</div>
	</div>
	<div class="container">
		<div class="row">
			<div class="col-md-12">
				{% with form = Forms.notifications.NotificationForm() %}
				<form method="POST" id="notifications_form" autocomplete="off">
					<div class="form-group">
						<b>{{ form.title.label }}</b>
						{{ form.title(class="form-control") }}
						<small class="form-text text-muted">
							{{ form.title.description }}
						</small>
					</div>
					<div class="form-group">
						<b>{{ form.content.label }}</b>
						{{ form.content(class="form-control", rows="3") }}
						<small class="form-text text-muted">
							{{ form.content.description }}
						</small>
					</div>
					<div class="form-row">
						<div class="col">
							<div class="form-group">
								<b>{{ form.type.label }}</b>
							</div>
							{% for radio in form.type %}
								<div class="form-check form-check-inline pr-1">
									{{ radio(class="form-check-input") }}
									{{ radio.label(class="form-check-label") }}
								</div>
							{% endfor %}
							<small class="form-text text-muted">
								{{ form.type.description }}
							</small>
						</div>
						<div class="col">
							<div class="form-group">
								<b>{{ form.sound.label }}</b>
							</div>
							<div class="form-check">
								{{ form.sound(class="form-check-input") }}
								{{ form.sound.label(class="form-check-label") }}
							</div>
							<small class="form-text text-muted">
								{{ form.sound.description }}
							</small>
						</div>
					</div>
					<div class="float-right">
						{{ form.submit(class="btn btn-success text-center") }}
					</div>
				</form>
				{% endwith %}
			</div>
		</div>
	</div>

	<div class="container">
		<div class="row">
			<div class="col-md-12">
				<hr>
				<div id="notifications-list">
				{% for notification in notifications %}
					<div class="card bg-light mb-4">
						<button type="button" data-notif-id="{{ notification.id }}" class="delete-notification close position-absolute p-3" style="right:0;" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
						<div class="card-body">
							<h3 class="card-title">{{ notification.title }}</h3>
							<blockquote class="blockquote mb-0">
								<p>{{ notification.html }}</p>
								<small class="text-muted"><span data-time="{{ notification.date | isoformat }}"></span></small>
							</blockquote>
						</div>
					</div>
				{% endfor %}
				</div>
			</div>
		</div>
	</div>
{% endblock %}

{% block scripts %}
{% endblock %}

{% block entrypoint %}
	{{ Assets.js("assets/js/pages/notifications.js", theme="admin") }}
{% endblock %}
