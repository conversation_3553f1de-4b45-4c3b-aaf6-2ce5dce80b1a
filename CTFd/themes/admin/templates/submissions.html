{% extends "admin/base.html" %}

{% block stylesheets %}
{% endblock %}

{% block content %}

<div class="jumbotron">
	<div class="container">
		<h1>{% if type %}{{ type|title }} {% endif %}Submissions</h1>
	</div>
</div>

<div class="container">

	<div class="row">
		<div class="col-md-12">
			{% if q and field %}
			<h5 class="text-muted text-center">
				Searching for submissions with <strong>{{ field }}</strong> matching <strong>{{ q }}</strong>
			</h5>
			<h6 class="text-muted text-center pb-3">
				Page {{ submissions.page }} of {{ submissions.total }} results
			</h6>
			{% endif %}

			{% with form = Forms.submissions.SubmissionSearchForm(field=field, q=q) %}
			<form method="GET" class="form-inline">
				<div class="form-group col-md-2 pr-0">
					{{ form.field(class="form-control custom-select w-100") }}
				</div>
				<div class="form-group col-md-8">
					{{ form.q(class="form-control w-100", placeholder="Search for matching submission") }}
				</div>
				<div class="form-group col-md-2">
					<button type="submit" class="btn btn-primary w-100">
						<i class="fas fa-search" aria-hidden="true"></i>
					</button>
				</div>
			</form>
			{% endwith %}
		</div>
	</div>

	<hr>

	<div class="row">
		<div class="col-md-12">
			<div class="float-right pb-3">
				<div class="btn-group" role="group">
					{% if type == "incorrect" %}
					<button type="button" class="btn btn-outline-success" data-toggle="tooltip" title="Mark submissions correct" id="correct-flags-button">
						<i class="btn-fa fas fa-check"></i>
					</button>
					{% endif %}
					{% if request.args.get("full") %}
					<button type="button" class="btn btn-outline-primary" data-toggle="tooltip" title="Show truncated flags" id="show-short-flags-button">
						<i class="btn-fa far fa-flag"></i>
					</button>
					{% else %}
					<button type="button" class="btn btn-outline-primary" data-toggle="tooltip" title="Show full flags" id="show-full-flags-button">
						<i class="btn-fa fas fa-flag"></i>
					</button>
					{% endif %}
					<button type="button" class="btn btn-outline-danger" data-toggle="tooltip" title="Delete selected submissions" id="submission-delete-button">
						<i class="btn-fa fas fa-trash-alt"></i>
					</button>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12 table-responsive">
			{% set mode = Configs.user_mode %}
			<table id="teamsboard" class="table table-striped border">
				<thead>
					<tr>
						<th class="border-right" data-checkbox>
							<div class="form-check text-center">
								<input type="checkbox" class="form-check-input" autocomplete="off" data-checkbox-all>&nbsp;
							</div>
						</th>
						<th class="text-center sort-col"><b>ID</b></th>
						{% if mode == UserModeTypes.TEAMS %}
						<th class="sort-col"><b>User</b></th>
						{% endif %}
						<th class="sort-col"><b>{{ get_mode_as_word(capitalize=True) }}</b></th>
						<th class="sort-col"><b>Challenge</b></th>
						<th class="sort-col"><b>Type</b></th>
						<th class="sort-col"><b>Provided</b></th>
						<th class="text-center sort-col"><b>Date</b></th>
					</tr>
				</thead>
				<tbody>
					{% for sub in submissions.items %}
					<tr>
						<td class="border-right" data-checkbox>
							<div class="form-check text-center">
								<input type="checkbox" class="form-check-input" value="{{ sub.id }}" data-submission-id="{{ sub.id }}" autocomplete="off">&nbsp;
							</div>
						</td>
						<td class="text-center" id="{{ sub.id }}">
							{{ sub.id }}
						</td>
						{% if mode == UserModeTypes.TEAMS %}
						<td>
							<a href="{{ url_for('admin.users_detail', user_id=sub.user_id) }}">
								{{ sub.user.name }}
							</a>
						</td>
						{% endif %}
						<td class="team" id="{{ sub.account_id }}">
							<a href="{{ generate_account_url(sub.account_id, admin=True) }}">
								{{ sub.account.name }}
							</a>
						</td>
						<td class="chal" id="{{ sub.challenge_id }}">
							<a href="{{ url_for('admin.challenges_detail', challenge_id=sub.challenge_id) }}">
								{{ sub.challenge.name }}
							</a>
						</td>
						<td>
							{{ sub.type }}
						</td>
						<td class="flag" id="{{ sub.id }}">
							<button class="btn btn-link p-0 float-left copy-flag" type="button">
								<i class="fas fa-clipboard"></i>
							</button>
							{% if request.args.get('full') %}
								<pre class="mb-0 pl-2" title="{{ sub.provided }}">{{ sub.provided }}</pre>
							{% else %}
								<pre class="mb-0 pl-2 float-left" title="{{ sub.provided }}">{{ sub.provided | truncate(45, True) }}</pre>
								{% if sub.provided | length > 50 %}
									<button class="btn btn-link p-0 pl-1 float-left show-flag">
										<i class="fas fa-eye"></i>
									</button>
								{% endif %}
							{% endif %}
						</td>
						<td class="text-center solve-time">
							<span data-time="{{ sub.date | isoformat }}"></span>
						</td>
					</tr>
					{% endfor %}
				</tbody>
			</table>
			{% if submissions.pages > 1 %}
			<div class="text-center">Page
				<br>
				{% if submissions.page != 1 %}
					<a href="{{ prev_page }}">&lt;&lt;&lt;</a>
				{% endif %}
				<select class="page-select">
					{% for page in range(1, submissions.pages + 1) %}
					<option {% if submissions.page == page %}selected{% endif %}>{{ page }}</option>
					{% endfor %}
				</select>
				{% if submissions.next_num %}
					<a href="{{ next_page }}">&gt;&gt;&gt;</a>
				{% endif %}
			</div>
			{% endif %}
		</div>
	</div>
</div>
{% endblock %}

{% block scripts %}
{% endblock %}

{% block entrypoint %}
	{{ Assets.js("assets/js/pages/submissions.js", theme="admin") }}
{% endblock %}
