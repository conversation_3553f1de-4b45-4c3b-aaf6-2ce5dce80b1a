<div role="tabpanel" class="tab-pane config-section" id="backup">
	<ul class="nav nav-tabs mb-3" role="tablist">
		<li class="nav-item">
			<a class="nav-link active" href="#export-ctf" aria-controls="export-ctf" role="tab" data-toggle="tab">Export</a>
		</li>
		<li class="nav-item">
			<a class="nav-link" href="#import-ctf" aria-controls="import-ctf" role="tab" data-toggle="tab">Import</a>
		</li>
		<li class="nav-item">
			<a class="nav-link" href="#save-csv" aria-controls="save-csv" role="tab" data-toggle="tab">Download CSV</a>
		</li>
		<li class="nav-item">
			<a class="nav-link" href="#import-csv" aria-controls="import-csv" role="tab" data-toggle="tab">Import CSV</a>
		</li>
	</ul>
	<div class="tab-content">
		<div role="tabpanel" class="tab-pane active" id="export-ctf">
			<div class="form-group">
				<p>Exports are an archive of your CTF in its current state. They can be re-imported into other CTFd
					instances or used by scripts and third parties to calculate statistics.</p>
				<p>To download an export click the button below.</p>
			</div>
			<div class="form-group">
				<a href="{{ url_for('admin.export_ctf') }}" id="export-button" class="btn btn-warning">Export</a>
			</div>
		</div>
		<div role="tabpanel" class="tab-pane" id="import-ctf">
			<div class="form-group">
				<p>You can import saved CTFd exports by uploading them below. This will completely wipe your existing
					CTFd instance and all your data will be replaced by the imported data.
					You should only import data that you trust!</p>

				<div class="alert alert-warning" role="alert">
					<small class="text-muted text-right">
						<i class="fas fa-exclamation pr-2"></i>
						Importing a CTFd export will completely wipe your existing data
					</small>
				</div>
			</div>
			<div class="form-group">
				<label for="container-files">Import File</label>
				<input class="form-control-file" type="file" name="backup" id="import-file" accept=".zip">
			</div>
			<input id="import-button" type="submit" class="btn btn-warning" value="Import">
		</div>
		<div role="tabpanel" class="tab-pane" id="save-csv">
			<div class="alert alert-warning" role="alert">
				<small class="text-muted text-right">
					<i class="fas fa-exclamation pr-2"></i>
					CSVs exported from CTFd are not guaranteed to import back in via the Import CSV functionality. See <a href="https://docs.ctfd.io/docs/imports/csv/" target="_blank">CSV Importing instructions</a> for details.
				</small>
			</div>
			{% with form = Forms.config.ExportCSVForm() %}
			<form method="GET" action="{{ url_for('admin.export_csv') }}">
				<div class="form-group">
					{{ form.table.label }}
					{{ form.table(class="form-control custom-select") }}
				</div>
				<input type="submit" class="btn btn-warning" value="Download CSV">
			</form>
			{% endwith %}
		</div>
		<div role="tabpanel" class="tab-pane" id="import-csv">
			{% with form = Forms.config.ImportCSVForm() %}
			<form method="POST" action="{{ url_for('admin.import_csv') }}" enctype="multipart/form-data" id="import-csv-form">
				<div class="form-group">
					<div class="alert alert-info" role="alert">
						<small class="text-muted text-right">
							<a href="https://docs.ctfd.io/docs/imports/csv/" target="_blank">
								<i class="far fa-question-circle pr-2"></i> Instructions and CSV templates
							</a>
						</small>
					</div>
				</div>
				<div class="form-group">
					<b>{{ form.csv_type.label }}</b>
					{{ form.csv_type(class="form-control custom-select", id="import-csv-type") }}
					<small class="form-text text-muted">
						{{ form.csv_type.description }}
					</small>
				</div>
				<div class="form-group">
					<b>{{ form.csv_file.label }}</b>
					{{ form.csv_file(class="form-control-file", id="import-csv-file", accept=".csv") }}
					<small class="form-text text-muted">
						{{ form.csv_file.description }}
					</small>
				</div>
				{{ form.nonce() }}
				<input type="submit" class="btn btn-warning" value="Import CSV">
			</form>
			{% endwith %}
		</div>
	</div>
</div>
