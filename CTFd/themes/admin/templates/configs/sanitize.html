<div role="tabpanel" class="tab-pane config-section" id="sanitize">
	{% set html_sanitization = "true" if html_sanitization == True else "false" %}
	<form method="POST" autocomplete="off" class="w-100">
		<div class="form-group">
			<label for="html_sanitization">
				HTML Sanitization
				<small class="form-text text-muted">
					Whether CTFd will attempt to sanitize HTML content from content.
				</small>
			</label>
			<select class="form-control custom-select" name="html_sanitization">
				{% if force_html_sanitization %}
				<option>
					Required (Disable in config.ini)
				</option>
				{% else %}
				<option value="true" {% if html_sanitization=='true' %}selected{% endif %}>
					Enabled
				</option>
				<option value="false" {% if html_sanitization=='false' %}selected{% endif %}>
					Disabled
				</option>
				{% endif %}
			</select>
		</div>
		<button type="submit" class="btn btn-md btn-primary float-right">Update</button>
	</form>
</div>
