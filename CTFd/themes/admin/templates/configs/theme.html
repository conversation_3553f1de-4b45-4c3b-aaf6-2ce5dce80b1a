<div role="tabpanel" class="tab-pane config-section" id="theme">
	<div class="modal fade" role="document" id="theme-settings-modal">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title">Theme Settings</h5>
				</div>
				<div class="modal-body">
				{% include ctf_theme + "/config.html" ignore missing %}
				</div>
			</div>
		</div>
	</div>

	<form method="POST" autocomplete="off" class="w-100">
		<div class="form-group">
			<label for="ctf_theme">
				Theme
				<small class="form-text text-muted">
					Switch themes to change CTFd's aesthetics
				</small>
			</label>
			<select class="form-control custom-select" id="ctf_theme" name="ctf_theme">
				<option>{{ ctf_theme }}</option>
				{% for theme in themes %}
					<option>{{ theme }}</option>
				{% endfor %}
			</select>
		</div>

		<div class="form-group">
			<div style="display: flex; justify-content: space-between; align-items: center;">
				<label>
					Primary Color
					<small class="form-text text-muted">
						Base color used for theme features. Requires theme support.
					</small>
				</label>
				<div>
					<input type="color" id="config-color-picker" class="custom-color-picker">
					<button type="button" class="btn-sm btn-primary" id="config-color-update">
						<i class="fas fa-pencil action-icon"></i> Build CSS
					</button>
				</div>
			</div>
		</div>
		<div class="form-group">
			<label>
				Theme Header
				<small class="form-text text-muted">
					Theme headers are inserted just before the <code>&lt;/head&gt;</code> tag on all public facing pages.
					Requires theme support.
				</small>
			</label>
			<!-- TODO: Smaller (expanding) textarea -->
			<textarea class="form-control" id="theme-header" name="theme_header" rows="7">{{ theme_header or "" }}</textarea>
		</div>

		<div class="form-group">
			<label>
				Theme Footer
				<small class="form-text text-muted">
					Theme footers are inserted just before the <code>&lt;/body&gt;</code> tag on all public facing pages.
					Requires theme support.
				</small>
			</label>
			<!-- TODO: Smaller (expanding) textarea -->
			<textarea class="form-control" id="theme-footer" name="theme_footer" rows="7">{{ theme_footer or "" }}</textarea>
		</div>

		<div class="form-group">
			<div style="display: flex; justify-content: space-between;">
				<label for="ctf_theme">
					Theme Settings
				</label>
				<div class="d-block pb-2">
					<button type="button" class="btn-sm btn-primary" id="theme-settings-button">
						<i class="fas fa-pencil-alt action-icon"></i> Build
					</button>
				</div>
			</div>
			<!-- TODO: Smaller (expanding) textarea -->
			<textarea class="form-control" id="theme-settings" name="theme_settings" rows="7">{{ theme_settings or "" }}</textarea>
			<small class="form-text text-muted">
				Settings specific to the theme
			</small>
		</div>

		<button type="submit" class="btn btn-md btn-primary float-right">Update</button>
	</form>
</div>
