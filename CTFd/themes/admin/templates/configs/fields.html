<div role="tabpanel" class="tab-pane config-section" id="fields">
	<form method="POST" autocomplete="off" class="w-100">
		<h5>Custom Fields</h5>

		<small class="form-text text-muted">
			Add custom fields to get additional data from your participants
		</small>

		<ul class="nav nav-tabs mt-3" role="tablist">
			<li class="nav-item active">
				<a class="nav-link active" href="#user-fields" role="tab" data-toggle="tab">
					Users
				</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#team-fields" role="tab" data-toggle="tab">
					Teams
				</a>
			</li>
		</ul>

		<div class="tab-content">
			<div role="tabpanel" class="tab-pane active" id="user-fields">
				<div class="col-md-12 py-3">
					<small>Custom user fields are shown during registration. Users can optionally edit these fields in their profile.</small>
				</div>

				<div id="user-field-list" class="pt-3">
				</div>
			</div>
			<div role="tabpanel" class="tab-pane" id="team-fields">
				<div class="col-md-12 py-3">
					<small>Custom team fields are shown during team creation. Team captains can optionally edit these fields in the team profile.</small>
				</div>

				<div id="team-field-list" class="pt-3">
				</div>
			</div>
		</div>
	</form>
</div>