<div role="tabpanel" class="tab-pane config-section" id="email">
	<form method="POST" autocomplete="off" class="w-100">
		<h5>Email notifications</h5>
		<small class="form-text text-muted">
			Customize the emails that CTFd sends your users with custom content using <a href="https://docs.ctfd.io/docs/settings/emails/#email-content" target="_blank">predefined variables</a>, mail servers, and addresses.
		</small>
		<ul class="nav nav-tabs mt-3" role="tablist">
			<li class="nav-item active">
				<a class="nav-link active" href="#email-server-tab" role="tab" data-toggle="tab">
					Mail Server
				</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#registration-email-tab" role="tab" data-toggle="tab">
					Registration
				</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#verification-email-tab" role="tab" data-toggle="tab">
					Verification
				</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#account-details-email-tab" role="tab" data-toggle="tab">Account Details</a>
			</li>
			<li class="nav-item">
				<a class="nav-link" href="#password-reset-email-tab" role="tab" data-toggle="tab">Password Reset</a>
			</li>
		</ul>

		<div class="tab-content">
			<div role="tabpanel" class="tab-pane active" id="email-server-tab">
				<div class="form-group">
					<label class="pt-3">
						Mail From Address<br>
						<small class="form-text text-muted">
							Email address used to send email
						</small>
					</label>
					<input class="form-control" id='mailfrom_addr' name='mailfrom_addr' type='text' {% if mailfrom_addr is defined and mailfrom_addr != None %}value="{{ mailfrom_addr }}"{% endif %}>
				</div>
				<div class="form-group">
					<label>
						Mail Server Address
						<br>
						<small class="form-text text-muted">
							Change the email server used by CTFd to send email
						</small>
					</label>
					<a class="float-right" href="https://docs.ctfd.io/docs/settings/emails#email-server" target="_blank">
						<i class="far fa-question-circle pr-2"></i>
					</a>
					<input class="form-control" id='mail_server' name='mail_server' type='text'
						{% if mail_server is defined and mail_server != None %}value="{{ mail_server }}"{% endif %}>
				</div>
				<div class="form-group">
					<label>
						Mail Server Port<br>
						<small class="form-text text-muted">
							Mail Server Port
						</small>
					</label>
					<input class="form-control" id='mail_port' name='mail_port' type='text'
						{% if mail_port is defined and mail_port != None %}value="{{ mail_port }}"{% endif %}>
				</div>
				<div class="form-check">
					<label>
						<input id="mail_useauth" name="mail_useauth" type="checkbox"
							{% if mail_useauth %}checked{% endif %}>
						Use Mail Server Username and Password
					</label>
				</div>
				<div id="mail_username_password">
					<div class="form-group">
						<label>
							Username<br>
							<small class="form-text text-muted">
								Mail Server Account Username
							</small>
						</label>
						{% if mail_username is defined and mail_username != None %}
							<label>
								<sup class="form-text text-muted">A mail server username is currently set</sup>
							</label>
						{% endif %}
						<input class="form-control" id='mail_username' name='mail_username' autocomplete='off' type='text'>
					</div>
					<div class="form-group">
						<label for="mail_password">
							Password<br>
							<small class="form-text text-muted">
								Mail Server Account Password
							</small>
						</label>
						{% if mail_password is defined and mail_password != None %}
							<label>
								<sup class="form-text text-muted">A mail server password is currently set</sup>
							</label>
						{% endif %}
						<input class="form-control" id='mail_password' name='mail_password' autocomplete='off' type='password'>
					</div>
					<sup>Uncheck setting and update to remove username and password</sup>
					<br>
				</div>
				<div class="form-check">
					<label>
						<input id="mail_ssl" name="mail_ssl" type="checkbox" {% if mail_ssl %}checked{% endif %}>
						TLS/SSL
					</label>
				</div>
				<div class="form-check">
					<label>
						<input id="mail_tls" name="mail_tls" type="checkbox" {% if mail_tls %}checked{% endif %}>
						STARTTLS
					</label>
				</div>
				<br>
				<div class="alert alert-warning" role="alert">
					Mailgun integration is deprecated! Please see your Mailgun account for SMTP credentials.
				</div>
				<div class="form-group">
					<label>
						Mailgun API Base URL<br>
						<small class="form-text text-muted">
							Mailgun API Base URL
						</small>
					</label>
					<input class="form-control" id='mailgun_base_url' name='mailgun_base_url' type='text'
						{% if mailgun_base_url is defined and mailgun_base_url != None %}value="{{ mailgun_base_url }}"{% endif %}>
				</div>
				<div class="form-group">
					<label>
						Mailgun API Key<br>
						<small class="form-text text-muted">
							Mailgun API Key
						</small>
					</label>
					<input class="form-control" id='mailgun_api_key' name='mailgun_api_key' type='text'
						{% if mailgun_api_key is defined and mailgun_api_key != None %}value="{{ mailgun_api_key }}"{% endif %}>
				</div>
			</div>
			<div role="tabpanel" class="tab-pane" id="registration-email-tab">
				<div class="form-group">
					<label class="pt-3">
						Account Registration<br>
						<small class="form-text text-muted">
							Email sent to users after they've finished registering
						</small>
					</label>
					<div>
						<label>
							Subject
						</label>
						<input class="form-control" id='successful_registration_email_subject' name='successful_registration_email_subject' type='text'
							value="{{ successful_registration_email_subject or ''}}">
						<label>
							Body
						</label>
						<textarea class="form-control" type="text" id="successful_registration_email_body" name="successful_registration_email_body"
							rows="5">{{ successful_registration_email_body or '' }}</textarea>
					</div>
				</div>
			</div>

			<div role="tabpanel" class="tab-pane" id="verification-email-tab">
				<div class="form-group">
					<label class="pt-3">
						Account Verification<br>
						<small class="form-text text-muted">
							Email sent to users to confirm their account at the address they provided during registration. You can require email verification in the <a href="#accounts">Accounts settings</a>.
						</small>
					</label>
					<div>
						<label>
							Subject
						</label>
						<input class="form-control" id='verification_email_subject' name='verification_email_subject' type='text' value="{{ verification_email_subject or ''}}">
						<label>
							Body
						</label>
						<textarea class="form-control" type="text" id="verification_email_body" name="verification_email_body"
							rows="5">{{ verification_email_body or '' }}</textarea>
					</div>
				</div>
			</div>

			<div role="tabpanel" class="tab-pane" id="account-details-email-tab">
				<div class="form-group">
					<label class="pt-3">
						New Account Details<br>
						<small class="form-text text-muted">
							Email sent to new users <i>(manually created by an admin)</i> with their initial account details
						</small>
					</label>
					<div>
						<label>
							Subject
						</label>
						<input class="form-control" id='user_creation_email_subject' name='user_creation_email_subject' type='text'
							value="{{ user_creation_email_subject or ''}}">
						<label>
							Body
						</label>
						<textarea class="form-control" type="text" id="user_creation_email_body" name="user_creation_email_body"
							rows="5">{{ user_creation_email_body or '' }}</textarea>
					</div>
				</div>
			</div>

			<div role="tabpanel" class="tab-pane" id="password-reset-email-tab">
				<div class="form-group">
					<label class="pt-3">
						Password Reset Request<br>
						<small class="form-text text-muted">
							Email sent whent a user requests a password reset
						</small>
					</label>
					<div>
						<label>
							Subject
						</label>
						<input class="form-control" id='password_reset_subject' name='password_reset_subject' type='text'
							value="{{ password_reset_subject or ''}}">
						<label>
							Body
						</label>
						<textarea class="form-control" type="text" id="password_reset_body" name="password_reset_body"
							rows="5">{{ password_reset_body or '' }}</textarea>
					</div>
				</div>

				<div class="form-group">
					<label class="pt-3">
						Password Reset Confirmation<br>
						<small class="form-text text-muted">
							Email sent whent a user successfully resets their password
						</small>
					</label>
					<div>
						<label>
							Subject
						</label>
						<input class="form-control" id='password_change_alert_subject' name='password_change_alert_subject' type='text'
							value="{{ password_change_alert_subject or ''}}">
						<label>
							Body
						</label>
						<textarea class="form-control" type="text" id="password_change_alert_body" name="password_change_alert_body"
							rows="5">{{ password_change_alert_body or '' }}</textarea>
					</div>
				</div>
			</div>
		</div>

		
		<button type="submit" class="btn btn-md btn-primary float-right">Update</button>
	</form>
</div>
