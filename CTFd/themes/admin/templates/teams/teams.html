{% extends "admin/base.html" %}

{% block stylesheets %}
{% endblock %}

{% block content %}
<div class="jumbotron">
	<div class="container">
		<h1>Teams
			<span class="create-team" role="button" data-toggle="tooltip" title="Create Team">
				<a href="{{ url_for('admin.teams_new') }}" style="color: inherit;">
					<i class="btn-fa fas fa-plus-circle"></i>
				</a>
			</span>
		</h1>
	</div>
</div>

<div class="container">
	<div class="row">
		<div class="col-md-12">
			{% if q and field %}
			<h5 class="text-muted text-center">
				Searching for teams with <strong>{{ field }}</strong> matching <strong>{{ q }}</strong>
			</h5>
			<h6 class="text-muted text-center pb-3">
				Page {{ teams.page }} of {{ teams.total }} results
			</h6>
			{% endif %}

			{% with form = Forms.teams.TeamSearchForm(field=field, q=q) %}
			<form method="GET" class="form-inline">
				<div class="form-group col-md-2">
					{{ form.field(class="form-control custom-select w-100") }}
				</div>
				<div class="form-group col-md-8">
					{{ form.q(class="form-control w-100", placeholder="Search for matching teams") }}
				</div>
				<div class="form-group col-md-2">
					<button type="submit" class="btn btn-primary w-100">
						<i class="fas fa-search" aria-hidden="true"></i>
					</button>
				</div>
			</form>
			{% endwith %}
		</div>
	</div>

	<hr>

	<div class="row">
		<div class="col-md-12">
			<div class="float-right pb-3">
				<div class="btn-group" role="group">
					<button type="button" class="btn btn-outline-secondary" data-toggle="tooltip" title="Edit Teams" id="teams-edit-button">
						<i class="btn-fa fas fa-pencil-alt"></i>
					</button>
					<button type="button" class="btn btn-outline-danger" data-toggle="tooltip" title="Delete Teams" id="teams-delete-button">
						<i class="btn-fa fas fa-trash-alt"></i>
					</button>
				</div>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 table-responsive">
			<table id="teamsboard" class="table table-striped border">
				<thead>
					<tr>
						<th class="border-right" data-checkbox>
							<div class="form-check text-center">
								<input type="checkbox" class="form-check-input" autocomplete="off" data-checkbox-all>&nbsp;
							</div>
						</th>
						<th class="sort-col text-center"><b>ID</b></th>
						<th class="sort-col text-left"><b>Team</b></th>
						<th class="sort-col text-center"><b>Country</b></th>
						<th class="sort-col text-center px-0"><b>Hidden</b></th>
						<th class="sort-col text-center px-0"><b>Banned</b></th>
					</tr>
				</thead>
				<tbody>
					{% for team in teams.items %}
					<tr name="{{ team.id }}" data-href="{{ url_for('admin.teams_detail', team_id=team.id) }}">
						<td class="border-right" data-checkbox>
							<div class="form-check text-center">
								<input type="checkbox" class="form-check-input" value="{{ team.id }}" autocomplete="off" data-team-id="{{ team.id }}">&nbsp;
							</div>
						</td>
						<td class="team-id text-center" value="{{ team.id }}">{{ team.id }}</td>
						<td class="team-name" value="{{ team.name }}">
							<a href="{{ url_for('admin.teams_detail', team_id=team.id) }}">
								{{ team.name | truncate(32) }}
							</a>
							{% if team.bracket_id %}
								<span class="badge badge-secondary">{{ team.bracket.name }}</span>
							{% endif %}
							{% if team.oauth_id %}
								<a href="https://majorleaguecyber.org/t/{{ team.name }}">
									<span class="badge badge-primary">Official</span>
								</a>
							{% endif %}
							{% if team.website %}
								<a href="{{ team.website }}" target="_blank" class="badge badge-info" rel="noopener">
									<i class="btn-fa fas fa-external-link-alt" data-toggle="tooltip" data-placement="top"
									   title="{{ team.website }}" aria-hidden="true"></i>
								</a>
							{% endif %}
							<span class="d-block text-muted">
								<small>
									{% if team.affiliation %}
										{{ team.affiliation | truncate(20) }}
									{% endif %}
								</small>
							</span>
						</td>
						<td class="team-country text-center" value="{{ team.country if team.country is not none }}">
							<span>
								{% if team.country %}
									<i class="flag-{{ team.country.lower() }}"></i>
									<small>{{ lookup_country_code(team.country) }}</small>
								{% endif %}
							</span>
						</td>

						<td class="team-hidden d-md-table-cell d-lg-table-cell text-center" value="{{ team.hidden }}">
							{% if team.hidden %}
								<span class="badge badge-danger">hidden</span>
							{% endif %}
						</td>

						<td class="team-banned d-md-table-cell d-lg-table-cell text-center" value="{{ team.banned }}">
							{% if team.banned %}
								<span class="badge badge-danger">banned</span>
							{% endif %}
						</td>
					</tr>
					{% endfor %}
				</tbody>
			</table>
			{% if teams.pages > 1 %}
			<div class="text-center">Page
				<br>
				{% if teams.page != 1 %}
				<a href="{{ prev_page }}">&lt;&lt;&lt;</a>
				{% endif %}
				<select class="page-select">
					{% for page in range(1, teams.pages + 1) %}
					<option {% if teams.page == page %}selected{% endif %}>{{ page }}</option>
					{% endfor %}
				</select>
				{% if teams.next_num %}
				<a href="{{ next_page }}">&gt;&gt;&gt;</a>
				{% endif %}
			</div>
			{% endif %}
		</div>
	</div>
</div>
{% endblock %}

{% block scripts %}
{% endblock %}

{% block entrypoint %}
	{{ Assets.js("assets/js/pages/teams.js", theme="admin") }}
{% endblock %}
