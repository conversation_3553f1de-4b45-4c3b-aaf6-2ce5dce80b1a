{% extends "admin/base.html" %}

{% block stylesheets %}
{% endblock %}

{% block content %}
<div class="jumbotron">
	<div class="container">
		<h1>Create Challenge</h1>
	</div>
</div>

<div class="modal fade bd-example-modal-lg" role="dialog" id="challenge-create-options">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title">Options</h5>
				<button type="button" class="close" data-dismiss="modal">
					<span>&times;</span>
				</button>
			</div>
			<div class="modal-body">
				<form method="POST" enctype="multipart/form-data">
					<div class="form-group">
						<div class="row">
							<div class="col-md-8">
								<label>
									Flag:<br>
									<small class="form-text text-muted">Static flag for your challenge</small>
								</label>
								<input type="text" class="form-control" name="flag">
							</div>
							<div class="col-md-4">
								<label>
									&nbsp;
									<small class="form-text text-muted">&nbsp;</small>
								</label>
								<select class="form-control custom-select w-100" name="flag_data">
									<option value="">Case Sensitive</option>
									<option value="case_insensitive">Case Insensitive</option>
								</select>
							</div>
						</div>
						<input type="hidden" name="flag_type" value="static">
					</div>
					<div class="form-group">
						<div class="form-group">
							<label>
								Files:
								<small class="form-text text-muted">Files distributed along with your challenge</small>
							</label>
							<input class="form-control-file" type="file" name="file" multiple="multiple">
							<sub class="text-muted">Attach multiple files using Control+Click or Cmd+Click</sub>
						</div>
					</div>
					<div class="form-group">
						<label>
							State<br>
							<small class="form-text text-muted">Should the challenge be visible to users</small>
						</label>
						<select class="form-control custom-select" name="state">
							<option value="visible">Visible</option>
							<option value="hidden" selected>Hidden</option>
						</select>
					</div>
					<input id="challenge_id" type="hidden" name="challenge_id">
					<div class="form-group">
						<button class="btn btn-primary float-right create-challenge-submit" type="submit">Finish</button>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>

<div class="container-fluid">
	<div class="row">
		<div class="col-md-3 offset-md-1">
			<h5 class="text-center pb-3">
				Challenge Types
			</h5>
			<div id="create-chals-select">
				{% for type in types %}
				<label class="w-100">
					<input type="radio" name="type" class="card-radio d-none" value="{{ type }}" {% if type == "standard" %}checked{% endif %}/>
					<div class="card rounded-0">
						<div class="card-body">
							<span class="card-title">
								<div class="form-check">
									<input class="form-check-input card-radio-clone" type="radio" style="visibility: hidden;" checked>
									<span class="form-check-label">{{ type }}</span>
								</div>
							</span>
						</div>
					</div>
				</label>
				{% endfor %}
			</div>
		</div>
		<div class="col-md-7">
			<div id="create-chal-entry-div">
			</div>
		</div>
	</div>
</div>

{% endblock %}

{% block scripts %}
{% endblock %}

{% block entrypoint %}
	{{ Assets.js("assets/js/pages/challenge.js", theme="admin") }}
{% endblock %}
