{% extends "admin/base.html" %}

{% block stylesheets %}
	{{ Assets.css("assets/css/challenge-board.scss", theme="admin") }}
{% endblock %}


{% block content %}
<div class="jumbotron">
	<div class="container">
		<h1>Challenges
			<a class="no-decoration" href="{{ url_for('admin.challenges_new') }}">
				<span role="button" data-toggle="tooltip" title="Create Challenge">
					<i class="btn-fa fas fa-plus-circle"></i>
				</span>
			</a>
		</h1>
	</div>
</div>
<div class="container">
	<div class="row">
		<div class="col-md-12">
			{% if q and field %}
			<h5 class="text-muted text-center">
				Searching for challenges with <strong>{{ field }}</strong> matching <strong>{{ q }}</strong>
			</h5>
			<h6 class="text-muted text-center pb-3">
				{{ total }} results
			</h6>
			{% endif %}

			{% with form = Forms.challenges.ChallengeSearchForm(field=field, q=q) %}
			<form method="GET" class="form-inline">
				<div class="form-group col-md-2">
					{{ form.field(class="form-control custom-select w-100") }}
				</div>
				<div class="form-group col-md-8">
					{{ form.q(class="form-control w-100", placeholder="Search for matching challenge") }}
				</div>
				<div class="form-group col-md-2">
					<button type="submit" class="btn btn-primary w-100">
						<i class="fas fa-search" aria-hidden="true"></i>
					</button>
				</div>
			</form>
			{% endwith %}
		</div>
	</div>

	<hr>

	<div class="row">
		<div class="col-md-12">
			<div class="float-right pb-3">
				<div class="btn-group" role="group">
					<button type="button" class="btn btn-outline-secondary" data-toggle="tooltip" title="Edit Challenges" id="challenges-edit-button">
						<i class="btn-fa fas fa-pencil-alt"></i>
					</button>
					<button type="button" class="btn btn-outline-danger" data-toggle="tooltip" title="Delete Challenges" id="challenges-delete-button">
						<i class="btn-fa fas fa-trash-alt"></i>
					</button>
				</div>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 table-responsive">
			<div>
				<table id="challenges" class="table table-striped border">
					<thead>
					<tr>
						<td class="d-block border-right border-bottom text-center" data-checkbox>
							<div class="form-check">
								<input type="checkbox" class="form-check-input" autocomplete="off" data-checkbox-all>&nbsp;
							</div>
						</td>
						<th class="sort-col text-center"><b>ID</b></th>
						<th class="sort-col"><b>Name</b></th>
						<th class="sort-col"><b>Category</b></th>
						<th class="sort-col text-center"><b>Value</b></th>
						<th class="sort-col text-center"><b>Type</b></th>
						<th class="sort-col text-center"><b>State</b></th>
					</tr>
					</thead>
					<tbody>
					{% for challenge in challenges %}
						<tr data-href="{{ url_for('admin.challenges_detail', challenge_id=challenge.id) }}">
							<td class="d-block border-right text-center" data-checkbox>
								<div class="form-check">
									<input type="checkbox" class="form-check-input" value="{{ challenge.id }}" autocomplete="off" data-challenge-id="{{ challenge.id }}">&nbsp;
								</div>
							</td>
							<td class="text-center">{{ challenge.id }}</td>
							<td><a href="{{ url_for('admin.challenges_detail', challenge_id=challenge.id) }}">{{ challenge.name }}</a></td>
							<td>{{ challenge.category }}</td>
							<td class="text-center">{{ challenge.value }}</td>
							<td class="text-center">{{ challenge.type }}</td>
							<td class="text-center">
								{% set badge_state = 'badge-danger' if challenge.state == 'hidden' else 'badge-success' %}
								<span class="badge {{ badge_state }}">{{ challenge.state }}</span>
							</td>
						</tr>
					{% endfor %}
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
{% endblock %}

{% block scripts %}
{% endblock %}

{% block entrypoint %}
	{{ Assets.js("assets/js/pages/challenges.js", theme="admin") }}
{% endblock %}
