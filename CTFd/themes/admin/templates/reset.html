{% extends "admin/base.html" %}

{% block stylesheets %}
{% endblock %}

{% block content %}
	<div class="jumbotron">
		<div class="container">
			<h1>Reset</h1>
		</div>
	</div>
	<div class="container">
		<div class="row">
			<div class="col-md-6 offset-md-3">
				{% with form = Forms.config.ResetInstanceForm() %}
				<form method="POST" id="reset-ctf-form">
					<div class="alert alert-danger" role="alert">
						<p>
							Resetting your CTFd instance allows you to bulk delete data to prepare it for other events,
							other classes, or to otherwise get it to a clean state.
						</p>

						<p>
							Resetting your CTFd instance will delete the selected data <strong>PERMANENTLY</strong>.
						</p>

						<p>
							Think carefully before resetting because no automated backups are made and all selected data will be lost.
						</p>

						<span>
							<strong>
								Create backups of all data you need by <a href="{{ url_for('admin.config', _anchor='backup') }}">creating a CTFd Export</a>
							</strong>
						</span>
					</div>

					<hr>

					<div class="form-group pb-2">
						<div class="form-check">
							{{ form.accounts(class="form-check-input", autocomplete="off") }}
							{{ form.accounts.label(class="form-check-label") }}
						</div>
						<span class="text-muted">
							Deletes all user and team accounts and their associated information<br>
							<small>(Users, Teams, Submissions, Tracking)</small>
						</span>
					</div>

					<div class="form-group pb-2">
						<div class="form-check">
							{{ form.submissions(class="form-check-input", autocomplete="off") }}
							{{ form.submissions.label(class="form-check-label") }}
						</div>
						<span class="text-muted">
							Deletes all records that accounts gained points or took an action<br>
							<small>(Submissions, Awards, Unlocks, Tracking)</small>
						</span>
					</div>

					<div class="form-group pb-2">
						<div class="form-check">
							{{ form.challenges(class="form-check-input", autocomplete="off") }}
							{{ form.challenges.label(class="form-check-label") }}
						</div>
						<span class="text-muted">
							Deletes all challenges and associated data<br>
							<small>(Challenges, Flags, Hints, Tags, Challenge Files)</small>
						</span>
					</div>

					<div class="form-group pb-2">
						<div class="form-check">
							{{ form.pages(class="form-check-input", autocomplete="off") }}
							{{ form.pages.label(class="form-check-label") }}
						</div>
						<span class="text-muted">
							Deletes all pages and their associated files<br>
							<small>(Pages, Page Files)</small>
						</span>
					</div>

					<div class="form-group pb-2">
						<div class="form-check">
							{{ form.notifications(class="form-check-input", autocomplete="off") }}
							{{ form.notifications.label(class="form-check-label") }}
						</div>
						<span class="text-muted">
							Deletes all notifications<br>
							<small>(Notifications)</small>
						</span>
					</div>

					<br>

					{{ form.nonce() }}

					{{ form.submit(class="btn btn-warning btn-lg btn-block") }}
				</form>
				{% endwith %}
			</div>
		</div>
	</div>
{% endblock %}

{% block scripts %}
{% endblock %}

{% block entrypoint %}
	{{ Assets.js("assets/js/pages/reset.js", theme="admin") }}
{% endblock %}
