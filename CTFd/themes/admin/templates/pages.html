{% extends "admin/base.html" %}

{% block stylesheets %}
{% endblock %}

{% block content %}
<div class="jumbotron">
	<div class="container">
		<h1>Pages
			<a class="no-decoration" href="{{ url_for('admin.pages_new') }}">
				<span class="create-page" role="button" data-toggle="tooltip" title="Create Page">
					<i class="btn-fa fas fa-plus-circle"></i>
				</span>
			</a>
		</h1>
	</div>
</div>

<div class="container">
	<div class="row">
		<div class="col-md-12">
			<div class="float-right pb-3">
				<div class="btn-group" role="group">
					<button type="button" class="btn btn-outline-danger" data-toggle="tooltip" title="Delete Pages" id="pages-delete-button">
						<i class="btn-fa fas fa-trash-alt"></i>
					</button>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12 table-responsive">
			<table id="pages" class="table table-striped border">
				<thead>
				<tr>
					<th class="border-right" data-checkbox>
						<div class="form-check text-center">
							<input type="checkbox" class="form-check-input" id="pages-bulk-select" autocomplete="off" data-checkbox-all>&nbsp;
						</div>
					</th>
					<th class="sort-col text-center"><b>Title</b></th>
					<th class="sort-col text-center"><b>Route</b></th>
					<th class="sort-col text-center"><b>Authentication</b></th>
					<th class="sort-col text-center"><b>Hidden</b></th>
					<th class="sort-col text-center"><b>Published</b></th>
				</tr>
				</thead>
				<tbody>
				{% for page in pages %}
					<tr data-href="{{ url_for('admin.pages_detail', page_id=page.id) }}">
						<td class="border-right" data-checkbox>
							<div class="form-check text-center">
								<input type="checkbox" class="form-check-input" value="{{ page.id }}" data-page-id="{{ page.id }}" autocomplete="off">&nbsp;
							</div>
						</td>
						<td class="page-title">
							{{ page.title }}
						</td>
						<td class="page-route" page-id="{{ page.id }}" page-route="{{ page.route }}">
							<a href="{{ url_for('admin.pages_detail', page_id=page.id) }}">
								{{ page.route }}
							</a>
						</td>
						<td class="text-center">
							{% if page.auth_required %}
							Required
							{% else %}
							{% endif %}
						</td>
						<td class="text-center">
							{% if page.hidden %}
								Hidden
							{% else %}
							{% endif %}
						</td>
						<td class="text-center">
							{% if page.draft %}
								Draft
							{% else %}
								Published
							{% endif %}
						</td>
					</tr>
				{% endfor %}
				</tbody>
			</table>
		</div>
	</div>
</div>
{% endblock %}

{% block scripts %}
{% endblock %}

{% block entrypoint %}
	{{ Assets.js("assets/js/pages/pages.js", theme="admin") }}
{% endblock %}
