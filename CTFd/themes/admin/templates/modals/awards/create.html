{% with form = Forms.awards.AwardCreationForm() %}
<form id="user-award-form" method="POST">
	<div class="form-group">
		<b>{{ form.name.label }}</b>
		{{ form.name(class="form-control", id="award-name-input") }}
	</div>
	<div class="form-group">
		<b>{{ form.value.label }}</b>
		{{ form.value(class="form-control", id="award-value-input") }}
	</div>
	<div class="form-group">
		<b>{{ form.category.label }}</b>
		{{ form.category(class="form-control", id="award-category-input") }}
	</div>
	<div class="form-group">
		<b>{{ form.description.label }}</b>
		{{ form.description(id="award-description-input", class="form-control", rows="5") }}
	</div>
	<div class="form-group">
		<b>{{ form.icon.label }}</b>
		<div class="pt-1">
			<table class="table table-sm w-100">
				<tr>
					<td>
						<div class="form-check">
							<input class="form-check-input" id="icon-input-none" type="radio" name="icon" value="">
							<label class="form-check-label" for="icon-input-none">
								None
							</label>
						</div>
					</td>
					<td>
						<div class="form-check">
							<input class="form-check-input" id="icon-input-shield" type="radio" name="icon" value="shield">
							<label class="form-check-label" for="icon-input-shield">
								<i class="fas fa-shield-alt pr-1"></i>Shield
							</label>
						</div>
					</td>
					<td>
						<div class="form-check">
							<input class="form-check-input" id="icon-input-bug" type="radio" name="icon" value="bug">
							<label class="form-check-label" for="icon-input-bug">
								<i class="fas fa-bug pr-1"></i>Bug
							</label>
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<div class="form-check">
							<input class="form-check-input" id="icon-input-crown" type="radio" name="icon" value="crown">
							<label class="form-check-label" for="icon-input-crown">
								<i class="fas fa-crown pr-1"></i>Crown
							</label>
						</div>
					</td>
					<td>
						<div class="form-check">
							<input class="form-check-input" id="icon-input-crosshairs" type="radio" name="icon" value="crosshairs">
							<label class="form-check-label" for="icon-input-crosshairs">
								<i class="fas fa-crosshairs pr-1"></i>Crosshairs
							</label>
						</div>
					</td>
					<td>
						<div class="form-check">
							<input class="form-check-input" id="icon-input-ban" type="radio" name="icon" value="ban">
							<label class="form-check-label" for="icon-input-ban">
								<i class="fas fa-ban pr-1"></i>Ban
							</label>
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<div class="form-check">
							<input class="form-check-input" id="icon-input-lightning" type="radio" name="icon" value="lightning">
							<label class="form-check-label" for="icon-input-lightning">
								<i class="fas fa-bolt pr-1"></i>Lightning
							</label>
						</div>
					</td>
					<td>
						<div class="form-check">
							<input class="form-check-input" id="icon-input-skull" type="radio" name="icon" value="skull">
							<label class="form-check-label" for="icon-input-skull">
								<i class="fas fa-skull pr-1"></i>Skull
							</label>
						</div>
					</td>
					<td>
						<div class="form-check">
							<input class="form-check-input" id="icon-input-brain" type="radio" name="icon" value="brain">
							<label class="form-check-label" for="icon-input-brain">
								<i class="fas fa-brain pr-1"></i>Brain
							</label>
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<div class="form-check">
							<input class="form-check-input" id="icon-input-code" type="radio" name="icon" value="code">
							<label class="form-check-label" for="icon-input-code">
								<i class="fas fa-code pr-1"></i>Code
							</label>
						</div>
					</td>
					<td>
						<div class="form-check">
							<input class="form-check-input" id="icon-input-cowboy" type="radio" name="icon" value="cowboy">
							<label class="form-check-label" for="icon-input-cowboy">
								<i class="fas fa-hat-cowboy pr-1"></i>Cowboy
							</label>
						</div>
					</td>
					<td>
						<div class="form-check">
							<input class="form-check-input" id="icon-input-angry" type="radio" name="icon" value="angry">
							<label class="form-check-label" for="icon-input-angry">
								<i class="far fa-angry"></i>Angry
							</label>
						</div>
					</td>
				</tr>
			</table>
		</div>
	</div>
	<div id="results">
	</div>
	<div class="modal-footer">
		{{ form.submit(id="award-create-button", class="btn btn-primary") }}
	</div>
</form>
{% endwith %}