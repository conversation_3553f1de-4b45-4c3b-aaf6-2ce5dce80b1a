<div class="row">
	<div class="col-md-12 table-responsive">
		<table class="table table-striped">
			<thead>
				<tr>
					<td class="text-center"><b>User</b></td>
					<td class="text-center"><b>IP Address</b></td>
					<td class="text-center"><b>Last Seen</b></td>
					<td class="text-center"><b>City</b></td>
					<td class="text-center"><b>Country</b></td>
				</tr>
			</thead>
			<tbody>
			{% for addr in addrs %}
				<tr>
					<td class="text-center">
						<a href="{{ url_for("admin.users_detail", user_id=addr.user_id) }}">
							{{ addr.user.name }}
						</a>
					</td>
					<td class="text-center">
						<a href="{{ url_for('admin.users_listing', field='ip', q=addr.ip) }}">
							{{ addr.ip }}
						</a>
					</td>
					<td class="text-center solve-time">
						<span data-time="{{ addr.date | isoformat }}"></span>
					</td>
					{% set city = lookup_ip_address_city(addr.ip) %}
					<td class="text-center">
						{% if city %}{{ city }}{% endif %}
					</td>
					{% set country = lookup_ip_address(addr.ip) %}
					<td class="text-center">
						{% if country %}
						<i class="flag-{{ country.lower() }}"></i>
						&nbsp;
						{{ lookup_country_code(country) }}
						{% endif %}
					</td>
				</tr>
			{% endfor %}
			</tbody>
		</table>
		<small class="float-right">
			<a class="text-muted" href='https://db-ip.com'>IP Geolocation by DB-IP</a>
		</small>
	</div>
</div>
