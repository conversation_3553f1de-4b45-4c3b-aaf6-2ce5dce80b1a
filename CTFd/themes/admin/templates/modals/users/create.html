{% with form = Forms.users.UserCreateForm() %}
{% from "admin/macros/forms.html" import render_extra_fields %}
<form id="user-info-create-form" method="POST">
	<div class="form-group">
		{{ form.name.label }}
		{{ form.name(class="form-control", autocomplete="off") }}
	</div>
	<div class="form-group">
		{{ form.email.label }}
		{{ form.email(class="form-control", autocomplete="off") }}
	</div>
	<div class="form-group">
		{{ form.password.label }}
		{{ form.password(class="form-control", autocomplete="off") }}
	</div>
	<div class="form-group">
		{{ form.website.label }}
		<small class="float-right text-muted align-text-bottom">Optional</small>
		{{ form.website(class="form-control", autocomplete="off") }}
	</div>
	<div class="form-group">
		{{ form.affiliation.label }}
		<small class="float-right text-muted align-text-bottom">Optional</small>
		{{ form.affiliation(class="form-control", autocomplete="off") }}
	</div>
	<div class="form-group">
		{{ form.country.label }}
		<small class="float-right text-muted align-text-bottom">Optional</small>
		{{ form.country(class="form-control custom-select", autocomplete="off") }}
	</div>

	{{ render_extra_fields(form.extra) }}

	<div class="form-group">
		<div class="form-check form-check-inline">
			{{ form.type(class="form-control form-inline custom-select", id="type-select") }}
		</div>
		<div class="form-check form-check-inline">
			{{ form.verified(class="form-check-input", autocomplete="off") }}
			{{ form.verified.label(class="form-check-label") }}
		</div>
		<div class="form-check form-check-inline">
			{{ form.hidden(class="form-check-input", autocomplete="off") }}
			{{ form.hidden.label(class="form-check-label") }}
		</div>
		<div class="form-check form-check-inline">
			{{ form.banned(class="form-check-input", autocomplete="off") }}
			{{ form.banned.label(class="form-check-label") }}
		</div>
	</div>

	{% if can_send_mail() %}
	<div class="form-group">
		<div class="form-check form-check-inline">
			{{ form.notify(class="form-check-input", id="notify", autocomplete="off") }}
			{{ form.notify.label(class="form-check-label") }}
		</div>
	</div>
	{% endif %}

	<div id="results">
	</div>

	{{ form.submit(id="update-user", class="btn btn-primary btn-outlined float-right modal-action") }}
</form>
{% endwith %}