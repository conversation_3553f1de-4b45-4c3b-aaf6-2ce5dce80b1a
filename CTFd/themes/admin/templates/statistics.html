{% extends "admin/base.html" %}

{% block content %}
<div class="jumbotron">
	<div class="container">
		<h1>Statistics</h1>
	</div>
</div>
<div class="container">
	<div class="row d-flex align-items-center">
		<div class="col-md-4 text-right">
			<h5><b>{{ user_count }}</b> users registered</h5>
			{% if get_config('user_mode') == 'teams' %}
			<h5><b>{{ team_count }}</b> teams registered</h5>
			{% endif %}
			<h5><b>{{ ip_count }}</b> IP addresses</h5>
			<hr>
			<h5><b>{{ total_points }}</b> total possible points</h5>
			<h5><b>{{ challenge_count }}</b> challenges</h5>
			{% if most_solved %}
				<h5><b>{{ most_solved }}</b> has the most solves with <br>{{ solve_data[most_solved] }} solves</h5>
			{% endif %}
			{% if least_solved %}
			<h5><b>{{ least_solved }}</b> has the least solves with <br>{{ solve_data[least_solved] }} solves</h5>
			{% endif %}
		</div>

		<div class="col-md-8">
			<div id="solves-graph" class="d-flex align-items-center">
				<div class="text-center w-100">
					<i class="fas fa-circle-notch fa-spin fa-3x fa-fw spinner"></i>
				</div>
			</div>
		</div>
	</div>

	<hr>

	<div class="row">
		<div class="col-md-12">
			<div id="score-distribution-graph" class="d-flex align-items-center">
				<div class="text-center w-100">
					<i class="fas fa-circle-notch fa-spin fa-3x fa-fw spinner"></i>
				</div>
			</div>
		</div>
	</div>

	<hr>

	<div class="row">
		<div class="col-md-12">
			<div id="solve-percentages-graph" class="d-flex align-items-center">
				<div class="text-center w-100">
					<i class="fas fa-circle-notch fa-spin fa-3x fa-fw spinner"></i>
				</div>
			</div>
		</div>
	</div>

	<hr>

</div>
<div class="container-fluid">
	<div class="row">
		<div class="col-md-4">
			<div id="keys-pie-graph" class="d-flex align-items-center">
				<div class="text-center w-100">
					<i class="fas fa-circle-notch fa-spin fa-3x fa-fw spinner"></i>
				</div>
			</div>
			<div class="text-center">
				<h5><b>{{ solve_count }}</b> right submissions</h5>
				<h5><b>{{ wrong_count }}</b> wrong submissions</h5>
			</div>
		</div>
		<div class="col-md-4">
			<div id="categories-pie-graph" class="d-flex align-items-center">
				<div class="text-center w-100">
					<i class="fas fa-circle-notch fa-spin fa-3x fa-fw spinner"></i>
				</div>
			</div>
		</div>
		<div class="col-md-4">
			<div id="points-pie-graph" class="d-flex align-items-center">
				<div class="text-center w-100">
					<i class="fas fa-circle-notch fa-spin fa-3x fa-fw spinner"></i>
				</div>
			</div>
		</div>
	</div>

</div>
{% endblock %}

{% block scripts %}
{% endblock %}

{% block entrypoint %}
	{{ Assets.js("assets/js/pages/statistics.js", theme="admin") }}
{% endblock %}
