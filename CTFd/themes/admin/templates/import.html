{% extends "admin/base.html" %}

{% block content %}
<div class="jumbotron">
	<div class="container">
		<h1>Import Status</h1>
	</div>
</div>
<div class="container">
	<div class="row">
		<div class="col-md-6 offset-md-3">
			<p>
				<b>Start Time:</b> <span id="start-time">{{ start_time }}</span>
			</p>
			{% if end_time %}
			<p>
				<b>End Time:</b> <span id="end-time">{{ end_time }}</span>
			</p>
			{% endif %}
			{% if import_error %}
			<p>
				<b>Import Error:</b> {{ import_error }}
			</p>
			<div class="alert alert-danger" role="alert">
				An error occurred during the import. Please try again. 
			</div>
			{% else %}
			<p>
				<b>Current Status:</b> {{ import_status }}
			</p>
			<div class="alert alert-secondary" role="alert">
				Page will redirect upon completion. Refresh page to get latest status.<br>
				Page will automatically refresh every 5 seconds.
			</div>
			{% endif %}
		</div>
	</div>
</div>
{% endblock %}

{% block scripts %}
<script>
	// Reload every 5 seconds to poll import status
	setTimeout(function(){
		window.location.reload();
	}, 5000);
	
	let start_time = "{{ start_time | tojson }}";
	let end_time = "{{ end_time | tojson }}";
	let start = document.getElementById("start-time");
	start.innerText = new Date(parseInt(start_time) * 1000);
	
	if (end_time !== "null") {
		let end = document.getElementById("end-time");
		end.innerText = new Date(parseInt(end_time) * 1000);
	}
</script>
{% endblock %}