{"name": "admin", "version": "0.0.1", "main": "index.js", "license": "Apache-2.0", "scripts": {"dev": "vite build --watch --mode=development", "build": "vite build", "format": "prettier --write assets/", "lint": "eslint assets/ && prettier --check assets/", "verify": "vite build; git diff --quiet --exit-code"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "@vue/compiler-sfc": "^3.3.13", "eslint": "^8.56.0", "prettier": "3.2.5", "rollup-plugin-copy": "^3.5.0", "sass": "^1.70.0", "vite": "^5.1.2"}, "dependencies": {"@ctfdio/ctfd-js": "^0.0.13", "@fontsource/lato": "^5.0.18", "@fontsource/raleway": "^5.0.16", "@fortawesome/fontawesome-free": "^6.5.1", "@vue/compat": "^3.3.8", "alpinejs": "^3.13.3", "bootstrap": "~4.3.1", "codemirror": "~5.58.2", "easymde": "^2.18.0", "echarts": "^5.4.3", "highlight.js": "^11.9.0", "jquery": "^3.7.1", "markdown-it": "^14.0.0", "nunjucks": "^3.2.4", "q": "^1.5.1", "vue": "^3.2.25"}}