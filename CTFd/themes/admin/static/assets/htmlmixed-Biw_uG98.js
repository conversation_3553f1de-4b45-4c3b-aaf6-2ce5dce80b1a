import{D as pu,E as vu}from"./pages/main-Dzav4vK7.js";var lo={exports:{}},sa;function An(){return sa||(sa=1,function(Zr,Dn){(function(F,We){Zr.exports=We()})(pu,function(){var F=navigator.userAgent,We=navigator.platform,pe=/gecko\/\d/i.test(F),Je=/MSIE \d/.test(F),dt=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(F),Pe=/Edge\/(\d+)/.exec(F),P=Je||dt||Pe,I=P&&(Je?document.documentMode||6:+(Pe||dt)[1]),se=!Pe&&/WebKit\//.test(F),re=se&&/Qt\/\d+\.\d+/.test(F),X=!Pe&&/Chrome\//.test(F),J=/Opera\//.test(F),fe=/Apple Computer/.test(navigator.vendor),Le=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(F),ze=/PhantomJS/.test(F),$=!Pe&&/AppleWebKit/.test(F)&&/Mobile\/\w+/.test(F),_e=/Android/.test(F),Q=$||_e||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(F),Y=$||/Mac/.test(We),Te=/\bCrOS\b/.test(F),bt=/win/i.test(We),Ae=J&&F.match(/Version\/(\d*\.\d*)/);Ae&&(Ae=Number(Ae[1])),Ae&&Ae>=15&&(J=!1,se=!0);var rt=Y&&(re||J&&(Ae==null||Ae<12.11)),Ie=pe||P&&I>=9;function _(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var j=function(e,t){var n=e.className,r=_(t).exec(n);if(r){var i=n.slice(r.index+r[0].length);e.className=n.slice(0,r.index)+(i?r[1]+i:"")}};function k(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function z(e,t){return k(e).appendChild(t)}function c(e,t,n,r){var i=document.createElement(e);if(n&&(i.className=n),r&&(i.style.cssText=r),typeof t=="string")i.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)i.appendChild(t[o]);return i}function D(e,t,n,r){var i=c(e,t,n,r);return i.setAttribute("role","presentation"),i}var L;document.createRange?L=function(e,t,n,r){var i=document.createRange();return i.setEnd(r||e,n),i.setStart(e,t),i}:L=function(e,t,n){var r=document.body.createTextRange();try{r.moveToElementText(e.parentNode)}catch{return r}return r.collapse(!0),r.moveEnd("character",n),r.moveStart("character",t),r};function U(e,t){if(t.nodeType==3&&(t=t.parentNode),e.contains)return e.contains(t);do if(t.nodeType==11&&(t=t.host),t==e)return!0;while(t=t.parentNode)}function ve(){var e;try{e=document.activeElement}catch{e=document.body||null}for(;e&&e.shadowRoot&&e.shadowRoot.activeElement;)e=e.shadowRoot.activeElement;return e}function De(e,t){var n=e.className;_(t).test(n)||(e.className+=(n?" ":"")+t)}function xt(e,t){for(var n=e.split(" "),r=0;r<n.length;r++)n[r]&&!_(n[r]).test(t)&&(t+=" "+n[r]);return t}var Kt=function(e){e.select()};$?Kt=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:P&&(Kt=function(e){try{e.select()}catch{}});function wt(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function vt(e,t,n){t||(t={});for(var r in e)e.hasOwnProperty(r)&&(n!==!1||!t.hasOwnProperty(r))&&(t[r]=e[r]);return t}function be(e,t,n,r,i){t==null&&(t=e.search(/[^\s\u00a0]/),t==-1&&(t=e.length));for(var o=r||0,l=i||0;;){var a=e.indexOf("	",o);if(a<0||a>=t)return l+(t-o);l+=a-o,l+=n-l%n,o=a+1}}var K=function(){this.id=null,this.f=null,this.time=0,this.handler=wt(this.onTimeout,this)};K.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)},K.prototype.set=function(e,t){this.f=t;var n=+new Date+e;(!this.id||n<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=n)};function E(e,t){for(var n=0;n<e.length;++n)if(e[n]==t)return n;return-1}var ne=50,ye={toString:function(){return"CodeMirror.Pass"}},Qe={scroll:!1},H={origin:"*mouse"},ie={origin:"+move"};function _t(e,t,n){for(var r=0,i=0;;){var o=e.indexOf("	",r);o==-1&&(o=e.length);var l=o-r;if(o==e.length||i+l>=t)return r+Math.min(l,t-i);if(i+=o-r,i+=n-i%n,r=o+1,i>=t)return r}}var Dt=[""];function Ct(e){for(;Dt.length<=e;)Dt.push(ee(Dt)+" ");return Dt[e]}function ee(e){return e[e.length-1]}function ce(e,t){for(var n=[],r=0;r<e.length;r++)n[r]=t(e[r],r);return n}function Ve(e,t,n){for(var r=0,i=n(t);r<e.length&&n(e[r])<=i;)r++;e.splice(r,0,t)}function Ot(){}function gt(e,t){var n;return Object.create?n=Object.create(e):(Ot.prototype=e,n=new Ot),t&&vt(t,n),n}var fr=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function me(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||fr.test(e))}function y(e,t){return t?t.source.indexOf("\\w")>-1&&me(e)?!0:t.test(e):me(e)}function T(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var x=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function oe(e){return e.charCodeAt(0)>=768&&x.test(e)}function Ue(e,t,n){for(;(n<0?t>0:t<e.length)&&oe(e.charAt(t));)t+=n;return t}function cr(e,t,n){for(var r=t>n?-1:1;;){if(t==n)return t;var i=(t+n)/2,o=r<0?Math.ceil(i):Math.floor(i);if(o==t)return e(o)?t:n;e(o)?n=o:t=o+r}}function Ut(e,t,n,r){if(!e)return r(t,n,"ltr",0);for(var i=!1,o=0;o<e.length;++o){var l=e[o];(l.from<n&&l.to>t||t==n&&l.to==t)&&(r(Math.max(l.from,t),Math.min(l.to,n),l.level==1?"rtl":"ltr",o),i=!0)}i||r(t,n,"ltr")}var Se=null;function Lt(e,t,n){var r;Se=null;for(var i=0;i<e.length;++i){var o=e[i];if(o.from<t&&o.to>t)return i;o.to==t&&(o.from!=o.to&&n=="before"?r=i:Se=i),o.from==t&&(o.from!=o.to&&n!="before"?r=i:Se=i)}return r??Se}var Sr=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",t="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function n(u){return u<=247?e.charAt(u):1424<=u&&u<=1524?"R":1536<=u&&u<=1785?t.charAt(u-1536):1774<=u&&u<=2220?"r":8192<=u&&u<=8203?"w":u==8204?"b":"L"}var r=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,i=/[stwN]/,o=/[LRr]/,l=/[Lb1n]/,a=/[1n]/;function s(u,d,h){this.level=u,this.from=d,this.to=h}return function(u,d){var h=d=="ltr"?"L":"R";if(u.length==0||d=="ltr"&&!r.test(u))return!1;for(var m=u.length,g=[],b=0;b<m;++b)g.push(n(u.charCodeAt(b)));for(var S=0,C=h;S<m;++S){var N=g[S];N=="m"?g[S]=C:C=N}for(var O=0,A=h;O<m;++O){var W=g[O];W=="1"&&A=="r"?g[O]="n":o.test(W)&&(A=W,W=="r"&&(g[O]="R"))}for(var q=1,R=g[0];q<m-1;++q){var V=g[q];V=="+"&&R=="1"&&g[q+1]=="1"?g[q]="1":V==","&&R==g[q+1]&&(R=="1"||R=="n")&&(g[q]=R),R=V}for(var ge=0;ge<m;++ge){var Xe=g[ge];if(Xe==",")g[ge]="N";else if(Xe=="%"){var Ce=void 0;for(Ce=ge+1;Ce<m&&g[Ce]=="%";++Ce);for(var pt=ge&&g[ge-1]=="!"||Ce<m&&g[Ce]=="1"?"1":"N",ut=ge;ut<Ce;++ut)g[ut]=pt;ge=Ce-1}}for(var Ee=0,ft=h;Ee<m;++Ee){var Ze=g[Ee];ft=="L"&&Ze=="1"?g[Ee]="L":o.test(Ze)&&(ft=Ze)}for(var Ke=0;Ke<m;++Ke)if(i.test(g[Ke])){var Fe=void 0;for(Fe=Ke+1;Fe<m&&i.test(g[Fe]);++Fe);for(var Ne=(Ke?g[Ke-1]:h)=="L",ct=(Fe<m?g[Fe]:h)=="L",Yr=Ne==ct?Ne?"L":"R":h,ur=Ke;ur<Fe;++ur)g[ur]=Yr;Ke=Fe-1}for(var tt=[],Rt,Ye=0;Ye<m;)if(l.test(g[Ye])){var io=Ye;for(++Ye;Ye<m&&l.test(g[Ye]);++Ye);tt.push(new s(0,io,Ye))}else{var Vt=Ye,wr=tt.length,kr=d=="rtl"?1:0;for(++Ye;Ye<m&&g[Ye]!="L";++Ye);for(var ot=Vt;ot<Ye;)if(a.test(g[ot])){Vt<ot&&(tt.splice(wr,0,new s(1,Vt,ot)),wr+=kr);var jr=ot;for(++ot;ot<Ye&&a.test(g[ot]);++ot);tt.splice(wr,0,new s(2,jr,ot)),wr+=kr,Vt=ot}else++ot;Vt<Ye&&tt.splice(wr,0,new s(1,Vt,Ye))}return d=="ltr"&&(tt[0].level==1&&(Rt=u.match(/^\s+/))&&(tt[0].from=Rt[0].length,tt.unshift(new s(0,0,Rt[0].length))),ee(tt).level==1&&(Rt=u.match(/\s+$/))&&(ee(tt).to-=Rt[0].length,tt.push(new s(0,m-Rt[0].length,m)))),d=="rtl"?tt.reverse():tt}}();function nt(e,t){var n=e.order;return n==null&&(n=e.order=Sr(e.text,t)),n}var On=[],G=function(e,t,n){if(e.addEventListener)e.addEventListener(t,n,!1);else if(e.attachEvent)e.attachEvent("on"+t,n);else{var r=e._handlers||(e._handlers={});r[t]=(r[t]||On).concat(n)}};function Jr(e,t){return e._handlers&&e._handlers[t]||On}function te(e,t,n){if(e.removeEventListener)e.removeEventListener(t,n,!1);else if(e.detachEvent)e.detachEvent("on"+t,n);else{var r=e._handlers,i=r&&r[t];if(i){var o=E(i,n);o>-1&&(r[t]=i.slice(0,o).concat(i.slice(o+1)))}}}function Me(e,t){var n=Jr(e,t);if(n.length)for(var r=Array.prototype.slice.call(arguments,2),i=0;i<n.length;++i)n[i].apply(null,r)}function xe(e,t,n){return typeof t=="string"&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),Me(e,n||t.type,e,t),dr(t)||t.codemirrorIgnore}function Cr(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var n=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),r=0;r<t.length;++r)E(n,t[r])==-1&&n.push(t[r])}function He(e,t){return Jr(e,t).length>0}function $t(e){e.prototype.on=function(t,n){G(this,t,n)},e.prototype.off=function(t,n){te(this,t,n)}}function Be(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function Wn(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function dr(e){return e.defaultPrevented!=null?e.defaultPrevented:e.returnValue==!1}function $e(e){Be(e),Wn(e)}function Lr(e){return e.target||e.srcElement}function Pn(e){var t=e.which;return t==null&&(e.button&1?t=1:e.button&2?t=3:e.button&4&&(t=2)),Y&&e.ctrlKey&&t==1&&(t=3),t}var Wt=function(){if(P&&I<9)return!1;var e=c("div");return"draggable"in e||"dragDrop"in e}(),Qr;function zn(e){if(Qr==null){var t=c("span","​");z(e,c("span",[t,document.createTextNode("x")])),e.firstChild.offsetHeight!=0&&(Qr=t.offsetWidth<=1&&t.offsetHeight>2&&!(P&&I<8))}var n=Qr?c("span","​"):c("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return n.setAttribute("cm-text",""),n}var Tr;function di(e){if(Tr!=null)return Tr;var t=z(e,document.createTextNode("AخA")),n=L(t,0,1).getBoundingClientRect(),r=L(t,1,2).getBoundingClientRect();return k(e),!n||n.left==n.right?!1:Tr=r.right-n.right<3}var Pt=`

b`.split(/\n/).length!=3?function(e){for(var t=0,n=[],r=e.length;t<=r;){var i=e.indexOf(`
`,t);i==-1&&(i=e.length);var o=e.slice(t,e.charAt(i-1)=="\r"?i-1:i),l=o.indexOf("\r");l!=-1?(n.push(o.slice(0,l)),t+=l+1):(n.push(o),t=i+1)}return n}:function(e){return e.split(/\r\n?|\n/)},zt=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch{return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch{}return!t||t.parentElement()!=e?!1:t.compareEndPoints("StartToEnd",t)!=0},er=function(){var e=c("div");return"oncopy"in e?!0:(e.setAttribute("oncopy","return;"),typeof e.oncopy=="function")}(),Mr=null;function Gt(e){if(Mr!=null)return Mr;var t=z(e,c("span","x")),n=t.getBoundingClientRect(),r=L(t,0,1).getBoundingClientRect();return Mr=Math.abs(n.left-r.left)>1}var Vr={},qt={};function Nr(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Vr[e]=t}function Tt(e,t){qt[e]=t}function Xt(e){if(typeof e=="string"&&qt.hasOwnProperty(e))e=qt[e];else if(e&&typeof e.name=="string"&&qt.hasOwnProperty(e.name)){var t=qt[e.name];typeof t=="string"&&(t={name:t}),e=gt(t,e),e.name=t.name}else{if(typeof e=="string"&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Xt("application/xml");if(typeof e=="string"&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Xt("application/json")}return typeof e=="string"?{name:e}:e||{name:"null"}}function $r(e,t){t=Xt(t);var n=Vr[t.name];if(!n)return $r(e,"text/plain");var r=n(e,t);if(Yt.hasOwnProperty(t.name)){var i=Yt[t.name];for(var o in i)i.hasOwnProperty(o)&&(r.hasOwnProperty(o)&&(r["_"+o]=r[o]),r[o]=i[o])}if(r.name=t.name,t.helperType&&(r.helperType=t.helperType),t.modeProps)for(var l in t.modeProps)r[l]=t.modeProps[l];return r}var Yt={};function hi(e,t){var n=Yt.hasOwnProperty(e)?Yt[e]:Yt[e]={};vt(t,n)}function kt(e,t){if(t===!0)return t;if(e.copyState)return e.copyState(t);var n={};for(var r in t){var i=t[r];i instanceof Array&&(i=i.concat([])),n[r]=i}return n}function Ar(e,t){for(var n;e.innerMode&&(n=e.innerMode(t),!(!n||n.mode==e));)t=n.state,e=n.mode;return n||{mode:e,state:t}}function Hn(e,t,n){return e.startState?e.startState(t,n):!0}var we=function(e,t,n){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=n};we.prototype.eol=function(){return this.pos>=this.string.length},we.prototype.sol=function(){return this.pos==this.lineStart},we.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},we.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},we.prototype.eat=function(e){var t=this.string.charAt(this.pos),n;if(typeof e=="string"?n=t==e:n=t&&(e.test?e.test(t):e(t)),n)return++this.pos,t},we.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},we.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},we.prototype.skipToEnd=function(){this.pos=this.string.length},we.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},we.prototype.backUp=function(e){this.pos-=e},we.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=be(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?be(this.string,this.lineStart,this.tabSize):0)},we.prototype.indentation=function(){return be(this.string,null,this.tabSize)-(this.lineStart?be(this.string,this.lineStart,this.tabSize):0)},we.prototype.match=function(e,t,n){if(typeof e=="string"){var r=function(l){return n?l.toLowerCase():l},i=this.string.substr(this.pos,e.length);if(r(i)==r(e))return t!==!1&&(this.pos+=e.length),!0}else{var o=this.string.slice(this.pos).match(e);return o&&o.index>0?null:(o&&t!==!1&&(this.pos+=o[0].length),o)}},we.prototype.current=function(){return this.string.slice(this.start,this.pos)},we.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},we.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},we.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};function B(e,t){if(t-=e.first,t<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var n=e;!n.lines;)for(var r=0;;++r){var i=n.children[r],o=i.chunkSize();if(t<o){n=i;break}t-=o}return n.lines[t]}function Ht(e,t,n){var r=[],i=t.line;return e.iter(t.line,n.line+1,function(o){var l=o.text;i==n.line&&(l=l.slice(0,n.ch)),i==t.line&&(l=l.slice(t.ch)),r.push(l),++i}),r}function en(e,t,n){var r=[];return e.iter(t,n,function(i){r.push(i.text)}),r}function St(e,t){var n=t-e.height;if(n)for(var r=e;r;r=r.parent)r.height+=n}function ae(e){if(e.parent==null)return null;for(var t=e.parent,n=E(t.lines,e),r=t.parent;r;t=r,r=r.parent)for(var i=0;r.children[i]!=t;++i)n+=r.children[i].chunkSize();return n+t.first}function f(e,t){var n=e.first;e:do{for(var r=0;r<e.children.length;++r){var i=e.children[r],o=i.height;if(t<o){e=i;continue e}t-=o,n+=i.chunkSize()}return n}while(!e.lines);for(var l=0;l<e.lines.length;++l){var a=e.lines[l],s=a.height;if(t<s)break;t-=s}return n+l}function p(e,t){return t>=e.first&&t<e.first+e.size}function w(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function v(e,t,n){if(n===void 0&&(n=null),!(this instanceof v))return new v(e,t,n);this.line=e,this.ch=t,this.sticky=n}function M(e,t){return e.line-t.line||e.ch-t.ch}function le(e,t){return e.sticky==t.sticky&&M(e,t)==0}function de(e){return v(e.line,e.ch)}function Re(e,t){return M(e,t)<0?t:e}function lt(e,t){return M(e,t)<0?e:t}function En(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function Z(e,t){if(t.line<e.first)return v(e.first,0);var n=e.first+e.size-1;return t.line>n?v(n,B(e,n).text.length):va(t,B(e,t.line).text.length)}function va(e,t){var n=e.ch;return n==null||n>t?v(e.line,t):n<0?v(e.line,0):e}function ao(e,t){for(var n=[],r=0;r<t.length;r++)n[r]=Z(e,t[r]);return n}var Fn=function(e,t){this.state=e,this.lookAhead=t},Et=function(e,t,n,r){this.state=t,this.doc=e,this.line=n,this.maxLookAhead=r||0,this.baseTokens=null,this.baseTokenPos=1};Et.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return t!=null&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},Et.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},Et.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},Et.fromSaved=function(e,t,n){return t instanceof Fn?new Et(e,kt(e.mode,t.state),n,t.lookAhead):new Et(e,kt(e.mode,t),n)},Et.prototype.save=function(e){var t=e!==!1?kt(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new Fn(t,this.maxLookAhead):t};function so(e,t,n,r){var i=[e.state.modeGen],o={};vo(e,t.text,e.doc.mode,n,function(u,d){return i.push(u,d)},o,r);for(var l=n.state,a=function(u){n.baseTokens=i;var d=e.state.overlays[u],h=1,m=0;n.state=!0,vo(e,t.text,d.mode,n,function(g,b){for(var S=h;m<g;){var C=i[h];C>g&&i.splice(h,1,g,i[h+1],C),h+=2,m=Math.min(g,C)}if(b)if(d.opaque)i.splice(S,h-S,g,"overlay "+b),h=S+2;else for(;S<h;S+=2){var N=i[S+1];i[S+1]=(N?N+" ":"")+"overlay "+b}},o),n.state=l,n.baseTokens=null,n.baseTokenPos=1},s=0;s<e.state.overlays.length;++s)a(s);return{styles:i,classes:o.bgClass||o.textClass?o:null}}function uo(e,t,n){if(!t.styles||t.styles[0]!=e.state.modeGen){var r=tn(e,ae(t)),i=t.text.length>e.options.maxHighlightLength&&kt(e.doc.mode,r.state),o=so(e,t,r);i&&(r.state=i),t.stateAfter=r.save(!i),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),n===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function tn(e,t,n){var r=e.doc,i=e.display;if(!r.mode.startState)return new Et(r,!0,t);var o=ga(e,t,n),l=o>r.first&&B(r,o-1).stateAfter,a=l?Et.fromSaved(r,l,o):new Et(r,Hn(r.mode),o);return r.iter(o,t,function(s){pi(e,s.text,a);var u=a.line;s.stateAfter=u==t-1||u%5==0||u>=i.viewFrom&&u<i.viewTo?a.save():null,a.nextLine()}),n&&(r.modeFrontier=a.line),a}function pi(e,t,n,r){var i=e.doc.mode,o=new we(t,e.options.tabSize,n);for(o.start=o.pos=r||0,t==""&&fo(i,n.state);!o.eol();)vi(i,o,n.state),o.start=o.pos}function fo(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var n=Ar(e,t);if(n.mode.blankLine)return n.mode.blankLine(n.state)}}function vi(e,t,n,r){for(var i=0;i<10;i++){r&&(r[0]=Ar(e,n).mode);var o=e.token(t,n);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}var co=function(e,t,n){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=n};function ho(e,t,n,r){var i=e.doc,o=i.mode,l;t=Z(i,t);var a=B(i,t.line),s=tn(e,t.line,n),u=new we(a.text,e.options.tabSize,s),d;for(r&&(d=[]);(r||u.pos<t.ch)&&!u.eol();)u.start=u.pos,l=vi(o,u,s.state),r&&d.push(new co(u,l,kt(i.mode,s.state)));return r?d:new co(u,l,s.state)}function po(e,t){if(e)for(;;){var n=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!n)break;e=e.slice(0,n.index)+e.slice(n.index+n[0].length);var r=n[1]?"bgClass":"textClass";t[r]==null?t[r]=n[2]:new RegExp("(?:^|\\s)"+n[2]+"(?:$|\\s)").test(t[r])||(t[r]+=" "+n[2])}return e}function vo(e,t,n,r,i,o,l){var a=n.flattenSpans;a==null&&(a=e.options.flattenSpans);var s=0,u=null,d=new we(t,e.options.tabSize,r),h,m=e.options.addModeClass&&[null];for(t==""&&po(fo(n,r.state),o);!d.eol();){if(d.pos>e.options.maxHighlightLength?(a=!1,l&&pi(e,t,r,d.pos),d.pos=t.length,h=null):h=po(vi(n,d,r.state,m),o),m){var g=m[0].name;g&&(h="m-"+(h?g+" "+h:g))}if(!a||u!=h){for(;s<d.start;)s=Math.min(d.start,s+5e3),i(s,u);u=h}d.start=d.pos}for(;s<d.pos;){var b=Math.min(d.pos,s+5e3);i(b,u),s=b}}function ga(e,t,n){for(var r,i,o=e.doc,l=n?-1:t-(e.doc.mode.innerMode?1e3:100),a=t;a>l;--a){if(a<=o.first)return o.first;var s=B(o,a-1),u=s.stateAfter;if(u&&(!n||a+(u instanceof Fn?u.lookAhead:0)<=o.modeFrontier))return a;var d=be(s.text,null,e.options.tabSize);(i==null||r>d)&&(i=a-1,r=d)}return i}function ma(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var n=e.first,r=t-1;r>n;r--){var i=B(e,r).stateAfter;if(i&&(!(i instanceof Fn)||r+i.lookAhead<t)){n=r+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,n)}}var go=!1,jt=!1;function ya(){go=!0}function ba(){jt=!0}function In(e,t,n){this.marker=e,this.from=t,this.to=n}function rn(e,t){if(e)for(var n=0;n<e.length;++n){var r=e[n];if(r.marker==t)return r}}function xa(e,t){for(var n,r=0;r<e.length;++r)e[r]!=t&&(n||(n=[])).push(e[r]);return n}function wa(e,t){e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],t.marker.attachLine(e)}function ka(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker,a=o.from==null||(l.inclusiveLeft?o.from<=t:o.from<t);if(a||o.from==t&&l.type=="bookmark"&&(!n||!o.marker.insertLeft)){var s=o.to==null||(l.inclusiveRight?o.to>=t:o.to>t);(r||(r=[])).push(new In(l,o.from,s?null:o.to))}}return r}function Sa(e,t,n){var r;if(e)for(var i=0;i<e.length;++i){var o=e[i],l=o.marker,a=o.to==null||(l.inclusiveRight?o.to>=t:o.to>t);if(a||o.from==t&&l.type=="bookmark"&&(!n||o.marker.insertLeft)){var s=o.from==null||(l.inclusiveLeft?o.from<=t:o.from<t);(r||(r=[])).push(new In(l,s?null:o.from-t,o.to==null?null:o.to-t))}}return r}function gi(e,t){if(t.full)return null;var n=p(e,t.from.line)&&B(e,t.from.line).markedSpans,r=p(e,t.to.line)&&B(e,t.to.line).markedSpans;if(!n&&!r)return null;var i=t.from.ch,o=t.to.ch,l=M(t.from,t.to)==0,a=ka(n,i,l),s=Sa(r,o,l),u=t.text.length==1,d=ee(t.text).length+(u?i:0);if(a)for(var h=0;h<a.length;++h){var m=a[h];if(m.to==null){var g=rn(s,m.marker);g?u&&(m.to=g.to==null?null:g.to+d):m.to=i}}if(s)for(var b=0;b<s.length;++b){var S=s[b];if(S.to!=null&&(S.to+=d),S.from==null){var C=rn(a,S.marker);C||(S.from=d,u&&(a||(a=[])).push(S))}else S.from+=d,u&&(a||(a=[])).push(S)}a&&(a=mo(a)),s&&s!=a&&(s=mo(s));var N=[a];if(!u){var O=t.text.length-2,A;if(O>0&&a)for(var W=0;W<a.length;++W)a[W].to==null&&(A||(A=[])).push(new In(a[W].marker,null,null));for(var q=0;q<O;++q)N.push(A);N.push(s)}return N}function mo(e){for(var t=0;t<e.length;++t){var n=e[t];n.from!=null&&n.from==n.to&&n.marker.clearWhenEmpty!==!1&&e.splice(t--,1)}return e.length?e:null}function Ca(e,t,n){var r=null;if(e.iter(t.line,n.line+1,function(g){if(g.markedSpans)for(var b=0;b<g.markedSpans.length;++b){var S=g.markedSpans[b].marker;S.readOnly&&(!r||E(r,S)==-1)&&(r||(r=[])).push(S)}}),!r)return null;for(var i=[{from:t,to:n}],o=0;o<r.length;++o)for(var l=r[o],a=l.find(0),s=0;s<i.length;++s){var u=i[s];if(!(M(u.to,a.from)<0||M(u.from,a.to)>0)){var d=[s,1],h=M(u.from,a.from),m=M(u.to,a.to);(h<0||!l.inclusiveLeft&&!h)&&d.push({from:u.from,to:a.from}),(m>0||!l.inclusiveRight&&!m)&&d.push({from:a.to,to:u.to}),i.splice.apply(i,d),s+=d.length-3}}return i}function yo(e){var t=e.markedSpans;if(t){for(var n=0;n<t.length;++n)t[n].marker.detachLine(e);e.markedSpans=null}}function bo(e,t){if(t){for(var n=0;n<t.length;++n)t[n].marker.attachLine(e);e.markedSpans=t}}function Bn(e){return e.inclusiveLeft?-1:0}function Rn(e){return e.inclusiveRight?1:0}function mi(e,t){var n=e.lines.length-t.lines.length;if(n!=0)return n;var r=e.find(),i=t.find(),o=M(r.from,i.from)||Bn(e)-Bn(t);if(o)return-o;var l=M(r.to,i.to)||Rn(e)-Rn(t);return l||t.id-e.id}function xo(e,t){var n=jt&&e.markedSpans,r;if(n)for(var i=void 0,o=0;o<n.length;++o)i=n[o],i.marker.collapsed&&(t?i.from:i.to)==null&&(!r||mi(r,i.marker)<0)&&(r=i.marker);return r}function wo(e){return xo(e,!0)}function Kn(e){return xo(e,!1)}function La(e,t){var n=jt&&e.markedSpans,r;if(n)for(var i=0;i<n.length;++i){var o=n[i];o.marker.collapsed&&(o.from==null||o.from<t)&&(o.to==null||o.to>t)&&(!r||mi(r,o.marker)<0)&&(r=o.marker)}return r}function ko(e,t,n,r,i){var o=B(e,t),l=jt&&o.markedSpans;if(l)for(var a=0;a<l.length;++a){var s=l[a];if(s.marker.collapsed){var u=s.marker.find(0),d=M(u.from,n)||Bn(s.marker)-Bn(i),h=M(u.to,r)||Rn(s.marker)-Rn(i);if(!(d>=0&&h<=0||d<=0&&h>=0)&&(d<=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?M(u.to,n)>=0:M(u.to,n)>0)||d>=0&&(s.marker.inclusiveRight&&i.inclusiveLeft?M(u.from,r)<=0:M(u.from,r)<0)))return!0}}}function Ft(e){for(var t;t=wo(e);)e=t.find(-1,!0).line;return e}function Ta(e){for(var t;t=Kn(e);)e=t.find(1,!0).line;return e}function Ma(e){for(var t,n;t=Kn(e);)e=t.find(1,!0).line,(n||(n=[])).push(e);return n}function yi(e,t){var n=B(e,t),r=Ft(n);return n==r?t:ae(r)}function So(e,t){if(t>e.lastLine())return t;var n=B(e,t),r;if(!tr(e,n))return t;for(;r=Kn(n);)n=r.find(1,!0).line;return ae(n)+1}function tr(e,t){var n=jt&&t.markedSpans;if(n){for(var r=void 0,i=0;i<n.length;++i)if(r=n[i],!!r.marker.collapsed){if(r.from==null)return!0;if(!r.marker.widgetNode&&r.from==0&&r.marker.inclusiveLeft&&bi(e,t,r))return!0}}}function bi(e,t,n){if(n.to==null){var r=n.marker.find(1,!0);return bi(e,r.line,rn(r.line.markedSpans,n.marker))}if(n.marker.inclusiveRight&&n.to==t.text.length)return!0;for(var i=void 0,o=0;o<t.markedSpans.length;++o)if(i=t.markedSpans[o],i.marker.collapsed&&!i.marker.widgetNode&&i.from==n.to&&(i.to==null||i.to!=n.from)&&(i.marker.inclusiveLeft||n.marker.inclusiveRight)&&bi(e,t,i))return!0}function Zt(e){e=Ft(e);for(var t=0,n=e.parent,r=0;r<n.lines.length;++r){var i=n.lines[r];if(i==e)break;t+=i.height}for(var o=n.parent;o;n=o,o=n.parent)for(var l=0;l<o.children.length;++l){var a=o.children[l];if(a==n)break;t+=a.height}return t}function _n(e){if(e.height==0)return 0;for(var t=e.text.length,n,r=e;n=wo(r);){var i=n.find(0,!0);r=i.from.line,t+=i.from.ch-i.to.ch}for(r=e;n=Kn(r);){var o=n.find(0,!0);t-=r.text.length-o.from.ch,r=o.to.line,t+=r.text.length-o.to.ch}return t}function xi(e){var t=e.display,n=e.doc;t.maxLine=B(n,n.first),t.maxLineLength=_n(t.maxLine),t.maxLineChanged=!0,n.iter(function(r){var i=_n(r);i>t.maxLineLength&&(t.maxLineLength=i,t.maxLine=r)})}var Dr=function(e,t,n){this.text=e,bo(this,t),this.height=n?n(this):1};Dr.prototype.lineNo=function(){return ae(this)},$t(Dr);function Na(e,t,n,r){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),e.order!=null&&(e.order=null),yo(e),bo(e,n);var i=r?r(e):1;i!=e.height&&St(e,i)}function Aa(e){e.parent=null,yo(e)}var Da={},Oa={};function Co(e,t){if(!e||/^\s*$/.test(e))return null;var n=t.addModeClass?Oa:Da;return n[e]||(n[e]=e.replace(/\S+/g,"cm-$&"))}function Lo(e,t){var n=D("span",null,null,se?"padding-right: .1px":null),r={pre:D("pre",[n],"CodeMirror-line"),content:n,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var i=0;i<=(t.rest?t.rest.length:0);i++){var o=i?t.rest[i-1]:t.line,l=void 0;r.pos=0,r.addToken=Pa,di(e.display.measure)&&(l=nt(o,e.doc.direction))&&(r.addToken=Ha(r.addToken,l)),r.map=[];var a=t!=e.display.externalMeasured&&ae(o);Ea(o,r,uo(e,o,a)),o.styleClasses&&(o.styleClasses.bgClass&&(r.bgClass=xt(o.styleClasses.bgClass,r.bgClass||"")),o.styleClasses.textClass&&(r.textClass=xt(o.styleClasses.textClass,r.textClass||""))),r.map.length==0&&r.map.push(0,0,r.content.appendChild(zn(e.display.measure))),i==0?(t.measure.map=r.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(r.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(se){var s=r.content.lastChild;(/\bcm-tab\b/.test(s.className)||s.querySelector&&s.querySelector(".cm-tab"))&&(r.content.className="cm-tab-wrap-hack")}return Me(e,"renderLine",e,t.line,r.pre),r.pre.className&&(r.textClass=xt(r.pre.className,r.textClass||"")),r}function Wa(e){var t=c("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function Pa(e,t,n,r,i,o,l){if(t){var a=e.splitSpaces?za(t,e.trailingSpace):t,s=e.cm.state.specialChars,u=!1,d;if(!s.test(t))e.col+=t.length,d=document.createTextNode(a),e.map.push(e.pos,e.pos+t.length,d),P&&I<9&&(u=!0),e.pos+=t.length;else{d=document.createDocumentFragment();for(var h=0;;){s.lastIndex=h;var m=s.exec(t),g=m?m.index-h:t.length-h;if(g){var b=document.createTextNode(a.slice(h,h+g));P&&I<9?d.appendChild(c("span",[b])):d.appendChild(b),e.map.push(e.pos,e.pos+g,b),e.col+=g,e.pos+=g}if(!m)break;h+=g+1;var S=void 0;if(m[0]=="	"){var C=e.cm.options.tabSize,N=C-e.col%C;S=d.appendChild(c("span",Ct(N),"cm-tab")),S.setAttribute("role","presentation"),S.setAttribute("cm-text","	"),e.col+=N}else m[0]=="\r"||m[0]==`
`?(S=d.appendChild(c("span",m[0]=="\r"?"␍":"␤","cm-invalidchar")),S.setAttribute("cm-text",m[0]),e.col+=1):(S=e.cm.options.specialCharPlaceholder(m[0]),S.setAttribute("cm-text",m[0]),P&&I<9?d.appendChild(c("span",[S])):d.appendChild(S),e.col+=1);e.map.push(e.pos,e.pos+1,S),e.pos++}}if(e.trailingSpace=a.charCodeAt(t.length-1)==32,n||r||i||u||o||l){var O=n||"";r&&(O+=r),i&&(O+=i);var A=c("span",[d],O,o);if(l)for(var W in l)l.hasOwnProperty(W)&&W!="style"&&W!="class"&&A.setAttribute(W,l[W]);return e.content.appendChild(A)}e.content.appendChild(d)}}function za(e,t){if(e.length>1&&!/  /.test(e))return e;for(var n=t,r="",i=0;i<e.length;i++){var o=e.charAt(i);o==" "&&n&&(i==e.length-1||e.charCodeAt(i+1)==32)&&(o=" "),r+=o,n=o==" "}return r}function Ha(e,t){return function(n,r,i,o,l,a,s){i=i?i+" cm-force-border":"cm-force-border";for(var u=n.pos,d=u+r.length;;){for(var h=void 0,m=0;m<t.length&&(h=t[m],!(h.to>u&&h.from<=u));m++);if(h.to>=d)return e(n,r,i,o,l,a,s);e(n,r.slice(0,h.to-u),i,o,null,a,s),o=null,r=r.slice(h.to-u),u=h.to}}}function To(e,t,n,r){var i=!r&&n.widgetNode;i&&e.map.push(e.pos,e.pos+t,i),!r&&e.cm.display.input.needsContentAttribute&&(i||(i=e.content.appendChild(document.createElement("span"))),i.setAttribute("cm-marker",n.id)),i&&(e.cm.display.input.setUneditable(i),e.content.appendChild(i)),e.pos+=t,e.trailingSpace=!1}function Ea(e,t,n){var r=e.markedSpans,i=e.text,o=0;if(!r){for(var l=1;l<n.length;l+=2)t.addToken(t,i.slice(o,o=n[l]),Co(n[l+1],t.cm.options));return}for(var a=i.length,s=0,u=1,d="",h,m,g=0,b,S,C,N,O;;){if(g==s){b=S=C=m="",O=null,N=null,g=1/0;for(var A=[],W=void 0,q=0;q<r.length;++q){var R=r[q],V=R.marker;if(V.type=="bookmark"&&R.from==s&&V.widgetNode)A.push(V);else if(R.from<=s&&(R.to==null||R.to>s||V.collapsed&&R.to==s&&R.from==s)){if(R.to!=null&&R.to!=s&&g>R.to&&(g=R.to,S=""),V.className&&(b+=" "+V.className),V.css&&(m=(m?m+";":"")+V.css),V.startStyle&&R.from==s&&(C+=" "+V.startStyle),V.endStyle&&R.to==g&&(W||(W=[])).push(V.endStyle,R.to),V.title&&((O||(O={})).title=V.title),V.attributes)for(var ge in V.attributes)(O||(O={}))[ge]=V.attributes[ge];V.collapsed&&(!N||mi(N.marker,V)<0)&&(N=R)}else R.from>s&&g>R.from&&(g=R.from)}if(W)for(var Xe=0;Xe<W.length;Xe+=2)W[Xe+1]==g&&(S+=" "+W[Xe]);if(!N||N.from==s)for(var Ce=0;Ce<A.length;++Ce)To(t,0,A[Ce]);if(N&&(N.from||0)==s){if(To(t,(N.to==null?a+1:N.to)-s,N.marker,N.from==null),N.to==null)return;N.to==s&&(N=!1)}}if(s>=a)break;for(var pt=Math.min(a,g);;){if(d){var ut=s+d.length;if(!N){var Ee=ut>pt?d.slice(0,pt-s):d;t.addToken(t,Ee,h?h+b:b,C,s+Ee.length==g?S:"",m,O)}if(ut>=pt){d=d.slice(pt-s),s=pt;break}s=ut,C=""}d=i.slice(o,o=n[u++]),h=Co(n[u++],t.cm.options)}}}function Mo(e,t,n){this.line=t,this.rest=Ma(t),this.size=this.rest?ae(ee(this.rest))-n+1:1,this.node=this.text=null,this.hidden=tr(e,t)}function Un(e,t,n){for(var r=[],i,o=t;o<n;o=i){var l=new Mo(e.doc,B(e.doc,o),o);i=o+l.size,r.push(l)}return r}var Or=null;function Fa(e){Or?Or.ops.push(e):e.ownsGroup=Or={ops:[e],delayedCallbacks:[]}}function Ia(e){var t=e.delayedCallbacks,n=0;do{for(;n<t.length;n++)t[n].call(null);for(var r=0;r<e.ops.length;r++){var i=e.ops[r];if(i.cursorActivityHandlers)for(;i.cursorActivityCalled<i.cursorActivityHandlers.length;)i.cursorActivityHandlers[i.cursorActivityCalled++].call(null,i.cm)}}while(n<t.length)}function Ba(e,t){var n=e.ownsGroup;if(n)try{Ia(n)}finally{Or=null,t(n)}}var nn=null;function je(e,t){var n=Jr(e,t);if(n.length){var r=Array.prototype.slice.call(arguments,2),i;Or?i=Or.delayedCallbacks:nn?i=nn:(i=nn=[],setTimeout(Ra,0));for(var o=function(a){i.push(function(){return n[a].apply(null,r)})},l=0;l<n.length;++l)o(l)}}function Ra(){var e=nn;nn=null;for(var t=0;t<e.length;++t)e[t]()}function No(e,t,n,r){for(var i=0;i<t.changes.length;i++){var o=t.changes[i];o=="text"?_a(e,t):o=="gutter"?Do(e,t,n,r):o=="class"?wi(e,t):o=="widget"&&Ua(e,t,r)}t.changes=null}function on(e){return e.node==e.text&&(e.node=c("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),P&&I<8&&(e.node.style.zIndex=2)),e.node}function Ka(e,t){var n=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(n&&(n+=" CodeMirror-linebackground"),t.background)n?t.background.className=n:(t.background.parentNode.removeChild(t.background),t.background=null);else if(n){var r=on(t);t.background=r.insertBefore(c("div",null,n),r.firstChild),e.display.input.setUneditable(t.background)}}function Ao(e,t){var n=e.display.externalMeasured;return n&&n.line==t.line?(e.display.externalMeasured=null,t.measure=n.measure,n.built):Lo(e,t)}function _a(e,t){var n=t.text.className,r=Ao(e,t);t.text==t.node&&(t.node=r.pre),t.text.parentNode.replaceChild(r.pre,t.text),t.text=r.pre,r.bgClass!=t.bgClass||r.textClass!=t.textClass?(t.bgClass=r.bgClass,t.textClass=r.textClass,wi(e,t)):n&&(t.text.className=n)}function wi(e,t){Ka(e,t),t.line.wrapClass?on(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var n=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=n||""}function Do(e,t,n,r){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var i=on(t);t.gutterBackground=c("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px; width: "+r.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),i.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var l=on(t),a=t.gutter=c("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?r.fixedPos:-r.gutterTotalWidth)+"px");if(e.display.input.setUneditable(a),l.insertBefore(a,t.text),t.line.gutterClass&&(a.className+=" "+t.line.gutterClass),e.options.lineNumbers&&(!o||!o["CodeMirror-linenumbers"])&&(t.lineNumber=a.appendChild(c("div",w(e.options,n),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+r.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var s=0;s<e.display.gutterSpecs.length;++s){var u=e.display.gutterSpecs[s].className,d=o.hasOwnProperty(u)&&o[u];d&&a.appendChild(c("div",[d],"CodeMirror-gutter-elt","left: "+r.gutterLeft[u]+"px; width: "+r.gutterWidth[u]+"px"))}}}function Ua(e,t,n){t.alignable&&(t.alignable=null);for(var r=_("CodeMirror-linewidget"),i=t.node.firstChild,o=void 0;i;i=o)o=i.nextSibling,r.test(i.className)&&t.node.removeChild(i);Oo(e,t,n)}function Ga(e,t,n,r){var i=Ao(e,t);return t.text=t.node=i.pre,i.bgClass&&(t.bgClass=i.bgClass),i.textClass&&(t.textClass=i.textClass),wi(e,t),Do(e,t,n,r),Oo(e,t,r),t.node}function Oo(e,t,n){if(Wo(e,t.line,t,n,!0),t.rest)for(var r=0;r<t.rest.length;r++)Wo(e,t.rest[r],t,n,!1)}function Wo(e,t,n,r,i){if(t.widgets)for(var o=on(n),l=0,a=t.widgets;l<a.length;++l){var s=a[l],u=c("div",[s.node],"CodeMirror-linewidget"+(s.className?" "+s.className:""));s.handleMouseEvents||u.setAttribute("cm-ignore-events","true"),qa(s,u,n,r),e.display.input.setUneditable(u),i&&s.above?o.insertBefore(u,n.gutter||n.text):o.appendChild(u),je(s,"redraw")}}function qa(e,t,n,r){if(e.noHScroll){(n.alignable||(n.alignable=[])).push(t);var i=r.wrapperWidth;t.style.left=r.fixedPos+"px",e.coverGutter||(i-=r.gutterTotalWidth,t.style.paddingLeft=r.gutterTotalWidth+"px"),t.style.width=i+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-r.gutterTotalWidth+"px"))}function ln(e){if(e.height!=null)return e.height;var t=e.doc.cm;if(!t)return 0;if(!U(document.body,e.node)){var n="position: relative;";e.coverGutter&&(n+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(n+="width: "+t.display.wrapper.clientWidth+"px;"),z(t.display.measure,c("div",[e.node],null,n))}return e.height=e.node.parentNode.offsetHeight}function Jt(e,t){for(var n=Lr(t);n!=e.wrapper;n=n.parentNode)if(!n||n.nodeType==1&&n.getAttribute("cm-ignore-events")=="true"||n.parentNode==e.sizer&&n!=e.mover)return!0}function Gn(e){return e.lineSpace.offsetTop}function ki(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Po(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=z(e.measure,c("pre","x","CodeMirror-line-like")),n=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,r={left:parseInt(n.paddingLeft),right:parseInt(n.paddingRight)};return!isNaN(r.left)&&!isNaN(r.right)&&(e.cachedPaddingH=r),r}function It(e){return ne-e.display.nativeBarWidth}function hr(e){return e.display.scroller.clientWidth-It(e)-e.display.barWidth}function Si(e){return e.display.scroller.clientHeight-It(e)-e.display.barHeight}function Xa(e,t,n){var r=e.options.lineWrapping,i=r&&hr(e);if(!t.measure.heights||r&&t.measure.width!=i){var o=t.measure.heights=[];if(r){t.measure.width=i;for(var l=t.text.firstChild.getClientRects(),a=0;a<l.length-1;a++){var s=l[a],u=l[a+1];Math.abs(s.bottom-u.bottom)>2&&o.push((s.bottom+u.top)/2-n.top)}}o.push(n.bottom-n.top)}}function zo(e,t,n){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};for(var r=0;r<e.rest.length;r++)if(e.rest[r]==t)return{map:e.measure.maps[r],cache:e.measure.caches[r]};for(var i=0;i<e.rest.length;i++)if(ae(e.rest[i])>n)return{map:e.measure.maps[i],cache:e.measure.caches[i],before:!0}}function Ya(e,t){t=Ft(t);var n=ae(t),r=e.display.externalMeasured=new Mo(e.doc,t,n);r.lineN=n;var i=r.built=Lo(e,r);return r.text=i.pre,z(e.display.lineMeasure,i.pre),r}function Ho(e,t,n,r){return Bt(e,Wr(e,t),n,r)}function Ci(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[gr(e,t)];var n=e.display.externalMeasured;if(n&&t>=n.lineN&&t<n.lineN+n.size)return n}function Wr(e,t){var n=ae(t),r=Ci(e,n);r&&!r.text?r=null:r&&r.changes&&(No(e,r,n,Di(e)),e.curOp.forceUpdate=!0),r||(r=Ya(e,t));var i=zo(r,t,n);return{line:t,view:r,rect:null,map:i.map,cache:i.cache,before:i.before,hasHeights:!1}}function Bt(e,t,n,r,i){t.before&&(n=-1);var o=n+(r||""),l;return t.cache.hasOwnProperty(o)?l=t.cache[o]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(Xa(e,t.view,t.rect),t.hasHeights=!0),l=Za(e,t,n,r),l.bogus||(t.cache[o]=l)),{left:l.left,right:l.right,top:i?l.rtop:l.top,bottom:i?l.rbottom:l.bottom}}var Eo={left:0,right:0,top:0,bottom:0};function Fo(e,t,n){for(var r,i,o,l,a,s,u=0;u<e.length;u+=3)if(a=e[u],s=e[u+1],t<a?(i=0,o=1,l="left"):t<s?(i=t-a,o=i+1):(u==e.length-3||t==s&&e[u+3]>t)&&(o=s-a,i=o-1,t>=s&&(l="right")),i!=null){if(r=e[u+2],a==s&&n==(r.insertLeft?"left":"right")&&(l=n),n=="left"&&i==0)for(;u&&e[u-2]==e[u-3]&&e[u-1].insertLeft;)r=e[(u-=3)+2],l="left";if(n=="right"&&i==s-a)for(;u<e.length-3&&e[u+3]==e[u+4]&&!e[u+5].insertLeft;)r=e[(u+=3)+2],l="right";break}return{node:r,start:i,end:o,collapse:l,coverStart:a,coverEnd:s}}function ja(e,t){var n=Eo;if(t=="left")for(var r=0;r<e.length&&(n=e[r]).left==n.right;r++);else for(var i=e.length-1;i>=0&&(n=e[i]).left==n.right;i--);return n}function Za(e,t,n,r){var i=Fo(t.map,n,r),o=i.node,l=i.start,a=i.end,s=i.collapse,u;if(o.nodeType==3){for(var d=0;d<4;d++){for(;l&&oe(t.line.text.charAt(i.coverStart+l));)--l;for(;i.coverStart+a<i.coverEnd&&oe(t.line.text.charAt(i.coverStart+a));)++a;if(P&&I<9&&l==0&&a==i.coverEnd-i.coverStart?u=o.parentNode.getBoundingClientRect():u=ja(L(o,l,a).getClientRects(),r),u.left||u.right||l==0)break;a=l,l=l-1,s="right"}P&&I<11&&(u=Ja(e.display.measure,u))}else{l>0&&(s=r="right");var h;e.options.lineWrapping&&(h=o.getClientRects()).length>1?u=h[r=="right"?h.length-1:0]:u=o.getBoundingClientRect()}if(P&&I<9&&!l&&(!u||!u.left&&!u.right)){var m=o.parentNode.getClientRects()[0];m?u={left:m.left,right:m.left+zr(e.display),top:m.top,bottom:m.bottom}:u=Eo}for(var g=u.top-t.rect.top,b=u.bottom-t.rect.top,S=(g+b)/2,C=t.view.measure.heights,N=0;N<C.length-1&&!(S<C[N]);N++);var O=N?C[N-1]:0,A=C[N],W={left:(s=="right"?u.right:u.left)-t.rect.left,right:(s=="left"?u.left:u.right)-t.rect.left,top:O,bottom:A};return!u.left&&!u.right&&(W.bogus=!0),e.options.singleCursorHeightPerLine||(W.rtop=g,W.rbottom=b),W}function Ja(e,t){if(!window.screen||screen.logicalXDPI==null||screen.logicalXDPI==screen.deviceXDPI||!Gt(e))return t;var n=screen.logicalXDPI/screen.deviceXDPI,r=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*n,right:t.right*n,top:t.top*r,bottom:t.bottom*r}}function Io(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function Bo(e){e.display.externalMeasure=null,k(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)Io(e.display.view[t])}function an(e){Bo(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function Ro(){return X&&_e?-(document.body.getBoundingClientRect().left-parseInt(getComputedStyle(document.body).marginLeft)):window.pageXOffset||(document.documentElement||document.body).scrollLeft}function Ko(){return X&&_e?-(document.body.getBoundingClientRect().top-parseInt(getComputedStyle(document.body).marginTop)):window.pageYOffset||(document.documentElement||document.body).scrollTop}function Li(e){var t=0;if(e.widgets)for(var n=0;n<e.widgets.length;++n)e.widgets[n].above&&(t+=ln(e.widgets[n]));return t}function qn(e,t,n,r,i){if(!i){var o=Li(t);n.top+=o,n.bottom+=o}if(r=="line")return n;r||(r="local");var l=Zt(t);if(r=="local"?l+=Gn(e.display):l-=e.display.viewOffset,r=="page"||r=="window"){var a=e.display.lineSpace.getBoundingClientRect();l+=a.top+(r=="window"?0:Ko());var s=a.left+(r=="window"?0:Ro());n.left+=s,n.right+=s}return n.top+=l,n.bottom+=l,n}function _o(e,t,n){if(n=="div")return t;var r=t.left,i=t.top;if(n=="page")r-=Ro(),i-=Ko();else if(n=="local"||!n){var o=e.display.sizer.getBoundingClientRect();r+=o.left,i+=o.top}var l=e.display.lineSpace.getBoundingClientRect();return{left:r-l.left,top:i-l.top}}function Ti(e,t,n,r,i){return r||(r=B(e.doc,t.line)),qn(e,r,Ho(e,r,t.ch,i),n)}function Mt(e,t,n,r,i,o){r=r||B(e.doc,t.line),i||(i=Wr(e,r));function l(b,S){var C=Bt(e,i,b,S?"right":"left",o);return S?C.left=C.right:C.right=C.left,qn(e,r,C,n)}var a=nt(r,e.doc.direction),s=t.ch,u=t.sticky;if(s>=r.text.length?(s=r.text.length,u="before"):s<=0&&(s=0,u="after"),!a)return l(u=="before"?s-1:s,u=="before");function d(b,S,C){var N=a[S],O=N.level==1;return l(C?b-1:b,O!=C)}var h=Lt(a,s,u),m=Se,g=d(s,h,u=="before");return m!=null&&(g.other=d(s,m,u!="before")),g}function Uo(e,t){var n=0;t=Z(e.doc,t),e.options.lineWrapping||(n=zr(e.display)*t.ch);var r=B(e.doc,t.line),i=Zt(r)+Gn(e.display);return{left:n,right:n,top:i,bottom:i+r.height}}function Mi(e,t,n,r,i){var o=v(e,t,n);return o.xRel=i,r&&(o.outside=r),o}function Ni(e,t,n){var r=e.doc;if(n+=e.display.viewOffset,n<0)return Mi(r.first,0,null,-1,-1);var i=f(r,n),o=r.first+r.size-1;if(i>o)return Mi(r.first+r.size-1,B(r,o).text.length,null,1,1);t<0&&(t=0);for(var l=B(r,i);;){var a=Qa(e,l,i,t,n),s=La(l,a.ch+(a.xRel>0||a.outside>0?1:0));if(!s)return a;var u=s.find(1);if(u.line==i)return u;l=B(r,i=u.line)}}function Go(e,t,n,r){r-=Li(t);var i=t.text.length,o=cr(function(l){return Bt(e,n,l-1).bottom<=r},i,0);return i=cr(function(l){return Bt(e,n,l).top>r},o,i),{begin:o,end:i}}function qo(e,t,n,r){n||(n=Wr(e,t));var i=qn(e,t,Bt(e,n,r),"line").top;return Go(e,t,n,i)}function Ai(e,t,n,r){return e.bottom<=n?!1:e.top>n?!0:(r?e.left:e.right)>t}function Qa(e,t,n,r,i){i-=Zt(t);var o=Wr(e,t),l=Li(t),a=0,s=t.text.length,u=!0,d=nt(t,e.doc.direction);if(d){var h=(e.options.lineWrapping?$a:Va)(e,t,n,o,d,r,i);u=h.level!=1,a=u?h.from:h.to-1,s=u?h.to:h.from-1}var m=null,g=null,b=cr(function(q){var R=Bt(e,o,q);return R.top+=l,R.bottom+=l,Ai(R,r,i,!1)?(R.top<=i&&R.left<=r&&(m=q,g=R),!0):!1},a,s),S,C,N=!1;if(g){var O=r-g.left<g.right-r,A=O==u;b=m+(A?0:1),C=A?"after":"before",S=O?g.left:g.right}else{!u&&(b==s||b==a)&&b++,C=b==0?"after":b==t.text.length?"before":Bt(e,o,b-(u?1:0)).bottom+l<=i==u?"after":"before";var W=Mt(e,v(n,b,C),"line",t,o);S=W.left,N=i<W.top?-1:i>=W.bottom?1:0}return b=Ue(t.text,b,1),Mi(n,b,C,N,r-S)}function Va(e,t,n,r,i,o,l){var a=cr(function(h){var m=i[h],g=m.level!=1;return Ai(Mt(e,v(n,g?m.to:m.from,g?"before":"after"),"line",t,r),o,l,!0)},0,i.length-1),s=i[a];if(a>0){var u=s.level!=1,d=Mt(e,v(n,u?s.from:s.to,u?"after":"before"),"line",t,r);Ai(d,o,l,!0)&&d.top>l&&(s=i[a-1])}return s}function $a(e,t,n,r,i,o,l){var a=Go(e,t,r,l),s=a.begin,u=a.end;/\s/.test(t.text.charAt(u-1))&&u--;for(var d=null,h=null,m=0;m<i.length;m++){var g=i[m];if(!(g.from>=u||g.to<=s)){var b=g.level!=1,S=Bt(e,r,b?Math.min(u,g.to)-1:Math.max(s,g.from)).right,C=S<o?o-S+1e9:S-o;(!d||h>C)&&(d=g,h=C)}}return d||(d=i[i.length-1]),d.from<s&&(d={from:s,to:d.to,level:d.level}),d.to>u&&(d={from:d.from,to:u,level:d.level}),d}var pr;function Pr(e){if(e.cachedTextHeight!=null)return e.cachedTextHeight;if(pr==null){pr=c("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)pr.appendChild(document.createTextNode("x")),pr.appendChild(c("br"));pr.appendChild(document.createTextNode("x"))}z(e.measure,pr);var n=pr.offsetHeight/50;return n>3&&(e.cachedTextHeight=n),k(e.measure),n||1}function zr(e){if(e.cachedCharWidth!=null)return e.cachedCharWidth;var t=c("span","xxxxxxxxxx"),n=c("pre",[t],"CodeMirror-line-like");z(e.measure,n);var r=t.getBoundingClientRect(),i=(r.right-r.left)/10;return i>2&&(e.cachedCharWidth=i),i||10}function Di(e){for(var t=e.display,n={},r={},i=t.gutters.clientLeft,o=t.gutters.firstChild,l=0;o;o=o.nextSibling,++l){var a=e.display.gutterSpecs[l].className;n[a]=o.offsetLeft+o.clientLeft+i,r[a]=o.clientWidth}return{fixedPos:Oi(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:n,gutterWidth:r,wrapperWidth:t.wrapper.clientWidth}}function Oi(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function Xo(e){var t=Pr(e.display),n=e.options.lineWrapping,r=n&&Math.max(5,e.display.scroller.clientWidth/zr(e.display)-3);return function(i){if(tr(e.doc,i))return 0;var o=0;if(i.widgets)for(var l=0;l<i.widgets.length;l++)i.widgets[l].height&&(o+=i.widgets[l].height);return n?o+(Math.ceil(i.text.length/r)||1)*t:o+t}}function Wi(e){var t=e.doc,n=Xo(e);t.iter(function(r){var i=n(r);i!=r.height&&St(r,i)})}function vr(e,t,n,r){var i=e.display;if(!n&&Lr(t).getAttribute("cm-not-content")=="true")return null;var o,l,a=i.lineSpace.getBoundingClientRect();try{o=t.clientX-a.left,l=t.clientY-a.top}catch{return null}var s=Ni(e,o,l),u;if(r&&s.xRel>0&&(u=B(e.doc,s.line).text).length==s.ch){var d=be(u,u.length,e.options.tabSize)-u.length;s=v(s.line,Math.max(0,Math.round((o-Po(e.display).left)/zr(e.display))-d))}return s}function gr(e,t){if(t>=e.display.viewTo||(t-=e.display.viewFrom,t<0))return null;for(var n=e.display.view,r=0;r<n.length;r++)if(t-=n[r].size,t<0)return r}function at(e,t,n,r){t==null&&(t=e.doc.first),n==null&&(n=e.doc.first+e.doc.size),r||(r=0);var i=e.display;if(r&&n<i.viewTo&&(i.updateLineNumbers==null||i.updateLineNumbers>t)&&(i.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=i.viewTo)jt&&yi(e.doc,t)<i.viewTo&&nr(e);else if(n<=i.viewFrom)jt&&So(e.doc,n+r)>i.viewFrom?nr(e):(i.viewFrom+=r,i.viewTo+=r);else if(t<=i.viewFrom&&n>=i.viewTo)nr(e);else if(t<=i.viewFrom){var o=Xn(e,n,n+r,1);o?(i.view=i.view.slice(o.index),i.viewFrom=o.lineN,i.viewTo+=r):nr(e)}else if(n>=i.viewTo){var l=Xn(e,t,t,-1);l?(i.view=i.view.slice(0,l.index),i.viewTo=l.lineN):nr(e)}else{var a=Xn(e,t,t,-1),s=Xn(e,n,n+r,1);a&&s?(i.view=i.view.slice(0,a.index).concat(Un(e,a.lineN,s.lineN)).concat(i.view.slice(s.index)),i.viewTo+=r):nr(e)}var u=i.externalMeasured;u&&(n<u.lineN?u.lineN+=r:t<u.lineN+u.size&&(i.externalMeasured=null))}function rr(e,t,n){e.curOp.viewChanged=!0;var r=e.display,i=e.display.externalMeasured;if(i&&t>=i.lineN&&t<i.lineN+i.size&&(r.externalMeasured=null),!(t<r.viewFrom||t>=r.viewTo)){var o=r.view[gr(e,t)];if(o.node!=null){var l=o.changes||(o.changes=[]);E(l,n)==-1&&l.push(n)}}}function nr(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function Xn(e,t,n,r){var i=gr(e,t),o,l=e.display.view;if(!jt||n==e.doc.first+e.doc.size)return{index:i,lineN:n};for(var a=e.display.viewFrom,s=0;s<i;s++)a+=l[s].size;if(a!=t){if(r>0){if(i==l.length-1)return null;o=a+l[i].size-t,i++}else o=a-t;t+=o,n+=o}for(;yi(e.doc,n)!=n;){if(i==(r<0?0:l.length-1))return null;n+=r*l[i-(r<0?1:0)].size,i+=r}return{index:i,lineN:n}}function es(e,t,n){var r=e.display,i=r.view;i.length==0||t>=r.viewTo||n<=r.viewFrom?(r.view=Un(e,t,n),r.viewFrom=t):(r.viewFrom>t?r.view=Un(e,t,r.viewFrom).concat(r.view):r.viewFrom<t&&(r.view=r.view.slice(gr(e,t))),r.viewFrom=t,r.viewTo<n?r.view=r.view.concat(Un(e,r.viewTo,n)):r.viewTo>n&&(r.view=r.view.slice(0,gr(e,n)))),r.viewTo=n}function Yo(e){for(var t=e.display.view,n=0,r=0;r<t.length;r++){var i=t[r];!i.hidden&&(!i.node||i.changes)&&++n}return n}function sn(e){e.display.input.showSelection(e.display.input.prepareSelection())}function jo(e,t){t===void 0&&(t=!0);for(var n=e.doc,r={},i=r.cursors=document.createDocumentFragment(),o=r.selection=document.createDocumentFragment(),l=0;l<n.sel.ranges.length;l++)if(!(!t&&l==n.sel.primIndex)){var a=n.sel.ranges[l];if(!(a.from().line>=e.display.viewTo||a.to().line<e.display.viewFrom)){var s=a.empty();(s||e.options.showCursorWhenSelecting)&&Zo(e,a.head,i),s||ts(e,a,o)}}return r}function Zo(e,t,n){var r=Mt(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),i=n.appendChild(c("div"," ","CodeMirror-cursor"));if(i.style.left=r.left+"px",i.style.top=r.top+"px",i.style.height=Math.max(0,r.bottom-r.top)*e.options.cursorHeight+"px",r.other){var o=n.appendChild(c("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));o.style.display="",o.style.left=r.other.left+"px",o.style.top=r.other.top+"px",o.style.height=(r.other.bottom-r.other.top)*.85+"px"}}function Yn(e,t){return e.top-t.top||e.left-t.left}function ts(e,t,n){var r=e.display,i=e.doc,o=document.createDocumentFragment(),l=Po(e.display),a=l.left,s=Math.max(r.sizerWidth,hr(e)-r.sizer.offsetLeft)-l.right,u=i.direction=="ltr";function d(A,W,q,R){W<0&&(W=0),W=Math.round(W),R=Math.round(R),o.appendChild(c("div",null,"CodeMirror-selected","position: absolute; left: "+A+`px;
                             top: `+W+"px; width: "+(q??s-A)+`px;
                             height: `+(R-W)+"px"))}function h(A,W,q){var R=B(i,A),V=R.text.length,ge,Xe;function Ce(Ee,ft){return Ti(e,v(A,Ee),"div",R,ft)}function pt(Ee,ft,Ze){var Ke=qo(e,R,null,Ee),Fe=ft=="ltr"==(Ze=="after")?"left":"right",Ne=Ze=="after"?Ke.begin:Ke.end-(/\s/.test(R.text.charAt(Ke.end-1))?2:1);return Ce(Ne,Fe)[Fe]}var ut=nt(R,i.direction);return Ut(ut,W||0,q??V,function(Ee,ft,Ze,Ke){var Fe=Ze=="ltr",Ne=Ce(Ee,Fe?"left":"right"),ct=Ce(ft-1,Fe?"right":"left"),Yr=W==null&&Ee==0,ur=q==null&&ft==V,tt=Ke==0,Rt=!ut||Ke==ut.length-1;if(ct.top-Ne.top<=3){var Ye=(u?Yr:ur)&&tt,io=(u?ur:Yr)&&Rt,Vt=Ye?a:(Fe?Ne:ct).left,wr=io?s:(Fe?ct:Ne).right;d(Vt,Ne.top,wr-Vt,Ne.bottom)}else{var kr,ot,jr,oo;Fe?(kr=u&&Yr&&tt?a:Ne.left,ot=u?s:pt(Ee,Ze,"before"),jr=u?a:pt(ft,Ze,"after"),oo=u&&ur&&Rt?s:ct.right):(kr=u?pt(Ee,Ze,"before"):a,ot=!u&&Yr&&tt?s:Ne.right,jr=!u&&ur&&Rt?a:ct.left,oo=u?pt(ft,Ze,"after"):s),d(kr,Ne.top,ot-kr,Ne.bottom),Ne.bottom<ct.top&&d(a,Ne.bottom,null,ct.top),d(jr,ct.top,oo-jr,ct.bottom)}(!ge||Yn(Ne,ge)<0)&&(ge=Ne),Yn(ct,ge)<0&&(ge=ct),(!Xe||Yn(Ne,Xe)<0)&&(Xe=Ne),Yn(ct,Xe)<0&&(Xe=ct)}),{start:ge,end:Xe}}var m=t.from(),g=t.to();if(m.line==g.line)h(m.line,m.ch,g.ch);else{var b=B(i,m.line),S=B(i,g.line),C=Ft(b)==Ft(S),N=h(m.line,m.ch,C?b.text.length+1:null).end,O=h(g.line,C?0:null,g.ch).start;C&&(N.top<O.top-2?(d(N.right,N.top,null,N.bottom),d(a,O.top,O.left,O.bottom)):d(N.right,N.top,O.left-N.right,N.bottom)),N.bottom<O.top&&d(a,N.bottom,null,O.top)}n.appendChild(o)}function Pi(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var n=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval(function(){e.hasFocus()||Hr(e),t.cursorDiv.style.visibility=(n=!n)?"":"hidden"},e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function Jo(e){e.hasFocus()||(e.display.input.focus(),e.state.focused||Hi(e))}function zi(e){e.state.delayingBlurEvent=!0,setTimeout(function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,e.state.focused&&Hr(e))},100)}function Hi(e,t){e.state.delayingBlurEvent&&!e.state.draggingText&&(e.state.delayingBlurEvent=!1),e.options.readOnly!="nocursor"&&(e.state.focused||(Me(e,"focus",e,t),e.state.focused=!0,De(e.display.wrapper,"CodeMirror-focused"),!e.curOp&&e.display.selForContextMenu!=e.doc.sel&&(e.display.input.reset(),se&&setTimeout(function(){return e.display.input.reset(!0)},20)),e.display.input.receivedFocus()),Pi(e))}function Hr(e,t){e.state.delayingBlurEvent||(e.state.focused&&(Me(e,"blur",e,t),e.state.focused=!1,j(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout(function(){e.state.focused||(e.display.shift=!1)},150))}function jn(e){for(var t=e.display,n=t.lineDiv.offsetTop,r=0;r<t.view.length;r++){var i=t.view[r],o=e.options.lineWrapping,l=void 0,a=0;if(!i.hidden){if(P&&I<8){var s=i.node.offsetTop+i.node.offsetHeight;l=s-n,n=s}else{var u=i.node.getBoundingClientRect();l=u.bottom-u.top,!o&&i.text.firstChild&&(a=i.text.firstChild.getBoundingClientRect().right-u.left-1)}var d=i.line.height-l;if((d>.005||d<-.005)&&(St(i.line,l),Qo(i.line),i.rest))for(var h=0;h<i.rest.length;h++)Qo(i.rest[h]);if(a>e.display.sizerWidth){var m=Math.ceil(a/zr(e.display));m>e.display.maxLineLength&&(e.display.maxLineLength=m,e.display.maxLine=i.line,e.display.maxLineChanged=!0)}}}}function Qo(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var n=e.widgets[t],r=n.node.parentNode;r&&(n.height=r.offsetHeight)}}function Zn(e,t,n){var r=n&&n.top!=null?Math.max(0,n.top):e.scroller.scrollTop;r=Math.floor(r-Gn(e));var i=n&&n.bottom!=null?n.bottom:r+e.wrapper.clientHeight,o=f(t,r),l=f(t,i);if(n&&n.ensure){var a=n.ensure.from.line,s=n.ensure.to.line;a<o?(o=a,l=f(t,Zt(B(t,a))+e.wrapper.clientHeight)):Math.min(s,t.lastLine())>=l&&(o=f(t,Zt(B(t,s))-e.wrapper.clientHeight),l=s)}return{from:o,to:Math.max(l,o+1)}}function rs(e,t){if(!xe(e,"scrollCursorIntoView")){var n=e.display,r=n.sizer.getBoundingClientRect(),i=null;if(t.top+r.top<0?i=!0:t.bottom+r.top>(window.innerHeight||document.documentElement.clientHeight)&&(i=!1),i!=null&&!ze){var o=c("div","​",null,`position: absolute;
                         top: `+(t.top-n.viewOffset-Gn(e.display))+`px;
                         height: `+(t.bottom-t.top+It(e)+n.barHeight)+`px;
                         left: `+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(o),o.scrollIntoView(i),e.display.lineSpace.removeChild(o)}}}function ns(e,t,n,r){r==null&&(r=0);var i;!e.options.lineWrapping&&t==n&&(t=t.ch?v(t.line,t.sticky=="before"?t.ch-1:t.ch,"after"):t,n=t.sticky=="before"?v(t.line,t.ch+1,"before"):t);for(var o=0;o<5;o++){var l=!1,a=Mt(e,t),s=!n||n==t?a:Mt(e,n);i={left:Math.min(a.left,s.left),top:Math.min(a.top,s.top)-r,right:Math.max(a.left,s.left),bottom:Math.max(a.bottom,s.bottom)+r};var u=Ei(e,i),d=e.doc.scrollTop,h=e.doc.scrollLeft;if(u.scrollTop!=null&&(fn(e,u.scrollTop),Math.abs(e.doc.scrollTop-d)>1&&(l=!0)),u.scrollLeft!=null&&(mr(e,u.scrollLeft),Math.abs(e.doc.scrollLeft-h)>1&&(l=!0)),!l)break}return i}function is(e,t){var n=Ei(e,t);n.scrollTop!=null&&fn(e,n.scrollTop),n.scrollLeft!=null&&mr(e,n.scrollLeft)}function Ei(e,t){var n=e.display,r=Pr(e.display);t.top<0&&(t.top=0);var i=e.curOp&&e.curOp.scrollTop!=null?e.curOp.scrollTop:n.scroller.scrollTop,o=Si(e),l={};t.bottom-t.top>o&&(t.bottom=t.top+o);var a=e.doc.height+ki(n),s=t.top<r,u=t.bottom>a-r;if(t.top<i)l.scrollTop=s?0:t.top;else if(t.bottom>i+o){var d=Math.min(t.top,(u?a:t.bottom)-o);d!=i&&(l.scrollTop=d)}var h=e.options.fixedGutter?0:n.gutters.offsetWidth,m=e.curOp&&e.curOp.scrollLeft!=null?e.curOp.scrollLeft:n.scroller.scrollLeft-h,g=hr(e)-n.gutters.offsetWidth,b=t.right-t.left>g;return b&&(t.right=t.left+g),t.left<10?l.scrollLeft=0:t.left<m?l.scrollLeft=Math.max(0,t.left+h-(b?0:10)):t.right>g+m-3&&(l.scrollLeft=t.right+(b?0:10)-g),l}function Fi(e,t){t!=null&&(Jn(e),e.curOp.scrollTop=(e.curOp.scrollTop==null?e.doc.scrollTop:e.curOp.scrollTop)+t)}function Er(e){Jn(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function un(e,t,n){(t!=null||n!=null)&&Jn(e),t!=null&&(e.curOp.scrollLeft=t),n!=null&&(e.curOp.scrollTop=n)}function os(e,t){Jn(e),e.curOp.scrollToPos=t}function Jn(e){var t=e.curOp.scrollToPos;if(t){e.curOp.scrollToPos=null;var n=Uo(e,t.from),r=Uo(e,t.to);Vo(e,n,r,t.margin)}}function Vo(e,t,n,r){var i=Ei(e,{left:Math.min(t.left,n.left),top:Math.min(t.top,n.top)-r,right:Math.max(t.right,n.right),bottom:Math.max(t.bottom,n.bottom)+r});un(e,i.scrollLeft,i.scrollTop)}function fn(e,t){Math.abs(e.doc.scrollTop-t)<2||(pe||Bi(e,{top:t}),$o(e,t,!0),pe&&Bi(e),hn(e,100))}function $o(e,t,n){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),!(e.display.scroller.scrollTop==t&&!n)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function mr(e,t,n,r){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),!((n?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!r)&&(e.doc.scrollLeft=t,il(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function cn(e){var t=e.display,n=t.gutters.offsetWidth,r=Math.round(e.doc.height+ki(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?n:0,docHeight:r,scrollHeight:r+It(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:n}}var yr=function(e,t,n){this.cm=n;var r=this.vert=c("div",[c("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),i=this.horiz=c("div",[c("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");r.tabIndex=i.tabIndex=-1,e(r),e(i),G(r,"scroll",function(){r.clientHeight&&t(r.scrollTop,"vertical")}),G(i,"scroll",function(){i.clientWidth&&t(i.scrollLeft,"horizontal")}),this.checkedZeroWidth=!1,P&&I<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};yr.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,n=e.scrollHeight>e.clientHeight+1,r=e.nativeBarWidth;if(n){this.vert.style.display="block",this.vert.style.bottom=t?r+"px":"0";var i=e.viewHeight-(t?r:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+i)+"px"}else this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=n?r+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(n?r:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(r==0&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:n?r:0,bottom:t?r:0}},yr.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},yr.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},yr.prototype.zeroWidthHack=function(){var e=Y&&!Le?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.pointerEvents=this.vert.style.pointerEvents="none",this.disableHoriz=new K,this.disableVert=new K},yr.prototype.enableZeroWidthBar=function(e,t,n){e.style.pointerEvents="auto";function r(){var i=e.getBoundingClientRect(),o=n=="vert"?document.elementFromPoint(i.right-1,(i.top+i.bottom)/2):document.elementFromPoint((i.right+i.left)/2,i.bottom-1);o!=e?e.style.pointerEvents="none":t.set(1e3,r)}t.set(1e3,r)},yr.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var dn=function(){};dn.prototype.update=function(){return{bottom:0,right:0}},dn.prototype.setScrollLeft=function(){},dn.prototype.setScrollTop=function(){},dn.prototype.clear=function(){};function Fr(e,t){t||(t=cn(e));var n=e.display.barWidth,r=e.display.barHeight;el(e,t);for(var i=0;i<4&&n!=e.display.barWidth||r!=e.display.barHeight;i++)n!=e.display.barWidth&&e.options.lineWrapping&&jn(e),el(e,cn(e)),n=e.display.barWidth,r=e.display.barHeight}function el(e,t){var n=e.display,r=n.scrollbars.update(t);n.sizer.style.paddingRight=(n.barWidth=r.right)+"px",n.sizer.style.paddingBottom=(n.barHeight=r.bottom)+"px",n.heightForcer.style.borderBottom=r.bottom+"px solid transparent",r.right&&r.bottom?(n.scrollbarFiller.style.display="block",n.scrollbarFiller.style.height=r.bottom+"px",n.scrollbarFiller.style.width=r.right+"px"):n.scrollbarFiller.style.display="",r.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(n.gutterFiller.style.display="block",n.gutterFiller.style.height=r.bottom+"px",n.gutterFiller.style.width=t.gutterWidth+"px"):n.gutterFiller.style.display=""}var tl={native:yr,null:dn};function rl(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&j(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new tl[e.options.scrollbarStyle](function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),G(t,"mousedown",function(){e.state.focused&&setTimeout(function(){return e.display.input.focus()},0)}),t.setAttribute("cm-not-content","true")},function(t,n){n=="horizontal"?mr(e,t):fn(e,t)},e),e.display.scrollbars.addClass&&De(e.display.wrapper,e.display.scrollbars.addClass)}var ls=0;function br(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++ls},Fa(e.curOp)}function xr(e){var t=e.curOp;t&&Ba(t,function(n){for(var r=0;r<n.ops.length;r++)n.ops[r].cm.curOp=null;as(n)})}function as(e){for(var t=e.ops,n=0;n<t.length;n++)ss(t[n]);for(var r=0;r<t.length;r++)us(t[r]);for(var i=0;i<t.length;i++)fs(t[i]);for(var o=0;o<t.length;o++)cs(t[o]);for(var l=0;l<t.length;l++)ds(t[l])}function ss(e){var t=e.cm,n=t.display;ps(t),e.updateMaxLine&&xi(t),e.mustUpdate=e.viewChanged||e.forceUpdate||e.scrollTop!=null||e.scrollToPos&&(e.scrollToPos.from.line<n.viewFrom||e.scrollToPos.to.line>=n.viewTo)||n.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new Qn(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function us(e){e.updatedDisplay=e.mustUpdate&&Ii(e.cm,e.update)}function fs(e){var t=e.cm,n=t.display;e.updatedDisplay&&jn(t),e.barMeasure=cn(t),n.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=Ho(t,n.maxLine,n.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(n.scroller.clientWidth,n.sizer.offsetLeft+e.adjustWidthTo+It(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,n.sizer.offsetLeft+e.adjustWidthTo-hr(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=n.input.prepareSelection())}function cs(e){var t=e.cm;e.adjustWidthTo!=null&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&mr(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var n=e.focus&&e.focus==ve();e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,n),(e.updatedDisplay||e.startHeight!=t.doc.height)&&Fr(t,e.barMeasure),e.updatedDisplay&&Ki(t,e.barMeasure),e.selectionChanged&&Pi(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),n&&Jo(e.cm)}function ds(e){var t=e.cm,n=t.display,r=t.doc;if(e.updatedDisplay&&nl(t,e.update),n.wheelStartX!=null&&(e.scrollTop!=null||e.scrollLeft!=null||e.scrollToPos)&&(n.wheelStartX=n.wheelStartY=null),e.scrollTop!=null&&$o(t,e.scrollTop,e.forceScroll),e.scrollLeft!=null&&mr(t,e.scrollLeft,!0,!0),e.scrollToPos){var i=ns(t,Z(r,e.scrollToPos.from),Z(r,e.scrollToPos.to),e.scrollToPos.margin);rs(t,i)}var o=e.maybeHiddenMarkers,l=e.maybeUnhiddenMarkers;if(o)for(var a=0;a<o.length;++a)o[a].lines.length||Me(o[a],"hide");if(l)for(var s=0;s<l.length;++s)l[s].lines.length&&Me(l[s],"unhide");n.wrapper.offsetHeight&&(r.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&Me(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function ht(e,t){if(e.curOp)return t();br(e);try{return t()}finally{xr(e)}}function Ge(e,t){return function(){if(e.curOp)return t.apply(e,arguments);br(e);try{return t.apply(e,arguments)}finally{xr(e)}}}function it(e){return function(){if(this.curOp)return e.apply(this,arguments);br(this);try{return e.apply(this,arguments)}finally{xr(this)}}}function qe(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);br(t);try{return e.apply(this,arguments)}finally{xr(t)}}}function hn(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,wt(hs,e))}function hs(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var n=+new Date+e.options.workTime,r=tn(e,t.highlightFrontier),i=[];t.iter(r.line,Math.min(t.first+t.size,e.display.viewTo+500),function(o){if(r.line>=e.display.viewFrom){var l=o.styles,a=o.text.length>e.options.maxHighlightLength?kt(t.mode,r.state):null,s=so(e,o,r,!0);a&&(r.state=a),o.styles=s.styles;var u=o.styleClasses,d=s.classes;d?o.styleClasses=d:u&&(o.styleClasses=null);for(var h=!l||l.length!=o.styles.length||u!=d&&(!u||!d||u.bgClass!=d.bgClass||u.textClass!=d.textClass),m=0;!h&&m<l.length;++m)h=l[m]!=o.styles[m];h&&i.push(r.line),o.stateAfter=r.save(),r.nextLine()}else o.text.length<=e.options.maxHighlightLength&&pi(e,o.text,r),o.stateAfter=r.line%5==0?r.save():null,r.nextLine();if(+new Date>n)return hn(e,e.options.workDelay),!0}),t.highlightFrontier=r.line,t.modeFrontier=Math.max(t.modeFrontier,r.line),i.length&&ht(e,function(){for(var o=0;o<i.length;o++)rr(e,i[o],"text")})}}var Qn=function(e,t,n){var r=e.display;this.viewport=t,this.visible=Zn(r,e.doc,t),this.editorIsHidden=!r.wrapper.offsetWidth,this.wrapperHeight=r.wrapper.clientHeight,this.wrapperWidth=r.wrapper.clientWidth,this.oldDisplayWidth=hr(e),this.force=n,this.dims=Di(e),this.events=[]};Qn.prototype.signal=function(e,t){He(e,t)&&this.events.push(arguments)},Qn.prototype.finish=function(){for(var e=0;e<this.events.length;e++)Me.apply(null,this.events[e])};function ps(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=It(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=It(e)+"px",t.scrollbarsClipped=!0)}function vs(e){if(e.hasFocus())return null;var t=ve();if(!t||!U(e.display.lineDiv,t))return null;var n={activeElt:t};if(window.getSelection){var r=window.getSelection();r.anchorNode&&r.extend&&U(e.display.lineDiv,r.anchorNode)&&(n.anchorNode=r.anchorNode,n.anchorOffset=r.anchorOffset,n.focusNode=r.focusNode,n.focusOffset=r.focusOffset)}return n}function gs(e){if(!(!e||!e.activeElt||e.activeElt==ve())&&(e.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(e.activeElt.nodeName)&&e.anchorNode&&U(document.body,e.anchorNode)&&U(document.body,e.focusNode))){var t=window.getSelection(),n=document.createRange();n.setEnd(e.anchorNode,e.anchorOffset),n.collapse(!1),t.removeAllRanges(),t.addRange(n),t.extend(e.focusNode,e.focusOffset)}}function Ii(e,t){var n=e.display,r=e.doc;if(t.editorIsHidden)return nr(e),!1;if(!t.force&&t.visible.from>=n.viewFrom&&t.visible.to<=n.viewTo&&(n.updateLineNumbers==null||n.updateLineNumbers>=n.viewTo)&&n.renderedView==n.view&&Yo(e)==0)return!1;ol(e)&&(nr(e),t.dims=Di(e));var i=r.first+r.size,o=Math.max(t.visible.from-e.options.viewportMargin,r.first),l=Math.min(i,t.visible.to+e.options.viewportMargin);n.viewFrom<o&&o-n.viewFrom<20&&(o=Math.max(r.first,n.viewFrom)),n.viewTo>l&&n.viewTo-l<20&&(l=Math.min(i,n.viewTo)),jt&&(o=yi(e.doc,o),l=So(e.doc,l));var a=o!=n.viewFrom||l!=n.viewTo||n.lastWrapHeight!=t.wrapperHeight||n.lastWrapWidth!=t.wrapperWidth;es(e,o,l),n.viewOffset=Zt(B(e.doc,n.viewFrom)),e.display.mover.style.top=n.viewOffset+"px";var s=Yo(e);if(!a&&s==0&&!t.force&&n.renderedView==n.view&&(n.updateLineNumbers==null||n.updateLineNumbers>=n.viewTo))return!1;var u=vs(e);return s>4&&(n.lineDiv.style.display="none"),ms(e,n.updateLineNumbers,t.dims),s>4&&(n.lineDiv.style.display=""),n.renderedView=n.view,gs(u),k(n.cursorDiv),k(n.selectionDiv),n.gutters.style.height=n.sizer.style.minHeight=0,a&&(n.lastWrapHeight=t.wrapperHeight,n.lastWrapWidth=t.wrapperWidth,hn(e,400)),n.updateLineNumbers=null,!0}function nl(e,t){for(var n=t.viewport,r=!0;;r=!1){if(!r||!e.options.lineWrapping||t.oldDisplayWidth==hr(e)){if(n&&n.top!=null&&(n={top:Math.min(e.doc.height+ki(e.display)-Si(e),n.top)}),t.visible=Zn(e.display,e.doc,n),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break}else r&&(t.visible=Zn(e.display,e.doc,n));if(!Ii(e,t))break;jn(e);var i=cn(e);sn(e),Fr(e,i),Ki(e,i),t.force=!1}t.signal(e,"update",e),(e.display.viewFrom!=e.display.reportedViewFrom||e.display.viewTo!=e.display.reportedViewTo)&&(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function Bi(e,t){var n=new Qn(e,t);if(Ii(e,n)){jn(e),nl(e,n);var r=cn(e);sn(e),Fr(e,r),Ki(e,r),n.finish()}}function ms(e,t,n){var r=e.display,i=e.options.lineNumbers,o=r.lineDiv,l=o.firstChild;function a(b){var S=b.nextSibling;return se&&Y&&e.display.currentWheelTarget==b?b.style.display="none":b.parentNode.removeChild(b),S}for(var s=r.view,u=r.viewFrom,d=0;d<s.length;d++){var h=s[d];if(!h.hidden)if(!h.node||h.node.parentNode!=o){var m=Ga(e,h,u,n);o.insertBefore(m,l)}else{for(;l!=h.node;)l=a(l);var g=i&&t!=null&&t<=u&&h.lineNumber;h.changes&&(E(h.changes,"gutter")>-1&&(g=!1),No(e,h,u,n)),g&&(k(h.lineNumber),h.lineNumber.appendChild(document.createTextNode(w(e.options,u)))),l=h.node.nextSibling}u+=h.size}for(;l;)l=a(l)}function Ri(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px"}function Ki(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+It(e)+"px"}function il(e){var t=e.display,n=t.view;if(!(!t.alignWidgets&&(!t.gutters.firstChild||!e.options.fixedGutter))){for(var r=Oi(t)-t.scroller.scrollLeft+e.doc.scrollLeft,i=t.gutters.offsetWidth,o=r+"px",l=0;l<n.length;l++)if(!n[l].hidden){e.options.fixedGutter&&(n[l].gutter&&(n[l].gutter.style.left=o),n[l].gutterBackground&&(n[l].gutterBackground.style.left=o));var a=n[l].alignable;if(a)for(var s=0;s<a.length;s++)a[s].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=r+i+"px")}}function ol(e){if(!e.options.lineNumbers)return!1;var t=e.doc,n=w(e.options,t.first+t.size-1),r=e.display;if(n.length!=r.lineNumChars){var i=r.measure.appendChild(c("div",[c("div",n)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=i.firstChild.offsetWidth,l=i.offsetWidth-o;return r.lineGutter.style.width="",r.lineNumInnerWidth=Math.max(o,r.lineGutter.offsetWidth-l)+1,r.lineNumWidth=r.lineNumInnerWidth+l,r.lineNumChars=r.lineNumInnerWidth?n.length:-1,r.lineGutter.style.width=r.lineNumWidth+"px",Ri(e.display),!0}return!1}function _i(e,t){for(var n=[],r=!1,i=0;i<e.length;i++){var o=e[i],l=null;if(typeof o!="string"&&(l=o.style,o=o.className),o=="CodeMirror-linenumbers")if(t)r=!0;else continue;n.push({className:o,style:l})}return t&&!r&&n.push({className:"CodeMirror-linenumbers",style:null}),n}function ll(e){var t=e.gutters,n=e.gutterSpecs;k(t),e.lineGutter=null;for(var r=0;r<n.length;++r){var i=n[r],o=i.className,l=i.style,a=t.appendChild(c("div",null,"CodeMirror-gutter "+o));l&&(a.style.cssText=l),o=="CodeMirror-linenumbers"&&(e.lineGutter=a,a.style.width=(e.lineNumWidth||1)+"px")}t.style.display=n.length?"":"none",Ri(e)}function pn(e){ll(e.display),at(e),il(e)}function ys(e,t,n,r){var i=this;this.input=n,i.scrollbarFiller=c("div",null,"CodeMirror-scrollbar-filler"),i.scrollbarFiller.setAttribute("cm-not-content","true"),i.gutterFiller=c("div",null,"CodeMirror-gutter-filler"),i.gutterFiller.setAttribute("cm-not-content","true"),i.lineDiv=D("div",null,"CodeMirror-code"),i.selectionDiv=c("div",null,null,"position: relative; z-index: 1"),i.cursorDiv=c("div",null,"CodeMirror-cursors"),i.measure=c("div",null,"CodeMirror-measure"),i.lineMeasure=c("div",null,"CodeMirror-measure"),i.lineSpace=D("div",[i.measure,i.lineMeasure,i.selectionDiv,i.cursorDiv,i.lineDiv],null,"position: relative; outline: none");var o=D("div",[i.lineSpace],"CodeMirror-lines");i.mover=c("div",[o],null,"position: relative"),i.sizer=c("div",[i.mover],"CodeMirror-sizer"),i.sizerWidth=null,i.heightForcer=c("div",null,null,"position: absolute; height: "+ne+"px; width: 1px;"),i.gutters=c("div",null,"CodeMirror-gutters"),i.lineGutter=null,i.scroller=c("div",[i.sizer,i.heightForcer,i.gutters],"CodeMirror-scroll"),i.scroller.setAttribute("tabIndex","-1"),i.wrapper=c("div",[i.scrollbarFiller,i.gutterFiller,i.scroller],"CodeMirror"),P&&I<8&&(i.gutters.style.zIndex=-1,i.scroller.style.paddingRight=0),!se&&!(pe&&Q)&&(i.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(i.wrapper):e(i.wrapper)),i.viewFrom=i.viewTo=t.first,i.reportedViewFrom=i.reportedViewTo=t.first,i.view=[],i.renderedView=null,i.externalMeasured=null,i.viewOffset=0,i.lastWrapHeight=i.lastWrapWidth=0,i.updateLineNumbers=null,i.nativeBarWidth=i.barHeight=i.barWidth=0,i.scrollbarsClipped=!1,i.lineNumWidth=i.lineNumInnerWidth=i.lineNumChars=null,i.alignWidgets=!1,i.cachedCharWidth=i.cachedTextHeight=i.cachedPaddingH=null,i.maxLine=null,i.maxLineLength=0,i.maxLineChanged=!1,i.wheelDX=i.wheelDY=i.wheelStartX=i.wheelStartY=null,i.shift=!1,i.selForContextMenu=null,i.activeTouch=null,i.gutterSpecs=_i(r.gutters,r.lineNumbers),ll(i),n.init(i)}var Vn=0,mt=null;P?mt=-.53:pe?mt=15:X?mt=-.7:fe&&(mt=-1/3);function al(e){var t=e.wheelDeltaX,n=e.wheelDeltaY;return t==null&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),n==null&&e.detail&&e.axis==e.VERTICAL_AXIS?n=e.detail:n==null&&(n=e.wheelDelta),{x:t,y:n}}function bs(e){var t=al(e);return t.x*=mt,t.y*=mt,t}function sl(e,t){var n=al(t),r=n.x,i=n.y,o=e.display,l=o.scroller,a=l.scrollWidth>l.clientWidth,s=l.scrollHeight>l.clientHeight;if(r&&a||i&&s){if(i&&Y&&se){e:for(var u=t.target,d=o.view;u!=l;u=u.parentNode)for(var h=0;h<d.length;h++)if(d[h].node==u){e.display.currentWheelTarget=u;break e}}if(r&&!pe&&!J&&mt!=null){i&&s&&fn(e,Math.max(0,l.scrollTop+i*mt)),mr(e,Math.max(0,l.scrollLeft+r*mt)),(!i||i&&s)&&Be(t),o.wheelStartX=null;return}if(i&&mt!=null){var m=i*mt,g=e.doc.scrollTop,b=g+o.wrapper.clientHeight;m<0?g=Math.max(0,g+m-50):b=Math.min(e.doc.height,b+m+50),Bi(e,{top:g,bottom:b})}Vn<20&&(o.wheelStartX==null?(o.wheelStartX=l.scrollLeft,o.wheelStartY=l.scrollTop,o.wheelDX=r,o.wheelDY=i,setTimeout(function(){if(o.wheelStartX!=null){var S=l.scrollLeft-o.wheelStartX,C=l.scrollTop-o.wheelStartY,N=C&&o.wheelDY&&C/o.wheelDY||S&&o.wheelDX&&S/o.wheelDX;o.wheelStartX=o.wheelStartY=null,N&&(mt=(mt*Vn+N)/(Vn+1),++Vn)}},200)):(o.wheelDX+=r,o.wheelDY+=i))}}var yt=function(e,t){this.ranges=e,this.primIndex=t};yt.prototype.primary=function(){return this.ranges[this.primIndex]},yt.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var n=this.ranges[t],r=e.ranges[t];if(!le(n.anchor,r.anchor)||!le(n.head,r.head))return!1}return!0},yt.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new ue(de(this.ranges[t].anchor),de(this.ranges[t].head));return new yt(e,this.primIndex)},yt.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},yt.prototype.contains=function(e,t){t||(t=e);for(var n=0;n<this.ranges.length;n++){var r=this.ranges[n];if(M(t,r.from())>=0&&M(e,r.to())<=0)return n}return-1};var ue=function(e,t){this.anchor=e,this.head=t};ue.prototype.from=function(){return lt(this.anchor,this.head)},ue.prototype.to=function(){return Re(this.anchor,this.head)},ue.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch};function Nt(e,t,n){var r=e&&e.options.selectionsMayTouch,i=t[n];t.sort(function(m,g){return M(m.from(),g.from())}),n=E(t,i);for(var o=1;o<t.length;o++){var l=t[o],a=t[o-1],s=M(a.to(),l.from());if(r&&!l.empty()?s>0:s>=0){var u=lt(a.from(),l.from()),d=Re(a.to(),l.to()),h=a.empty()?l.from()==l.head:a.from()==a.head;o<=n&&--n,t.splice(--o,2,new ue(h?d:u,h?u:d))}}return new yt(t,n)}function ir(e,t){return new yt([new ue(e,t||e)],0)}function or(e){return e.text?v(e.from.line+e.text.length-1,ee(e.text).length+(e.text.length==1?e.from.ch:0)):e.to}function ul(e,t){if(M(e,t.from)<0)return e;if(M(e,t.to)<=0)return or(t);var n=e.line+t.text.length-(t.to.line-t.from.line)-1,r=e.ch;return e.line==t.to.line&&(r+=or(t).ch-t.to.ch),v(n,r)}function Ui(e,t){for(var n=[],r=0;r<e.sel.ranges.length;r++){var i=e.sel.ranges[r];n.push(new ue(ul(i.anchor,t),ul(i.head,t)))}return Nt(e.cm,n,e.sel.primIndex)}function fl(e,t,n){return e.line==t.line?v(n.line,e.ch-t.ch+n.ch):v(n.line+(e.line-t.line),e.ch)}function xs(e,t,n){for(var r=[],i=v(e.first,0),o=i,l=0;l<t.length;l++){var a=t[l],s=fl(a.from,i,o),u=fl(or(a),i,o);if(i=a.to,o=u,n=="around"){var d=e.sel.ranges[l],h=M(d.head,d.anchor)<0;r[l]=new ue(h?u:s,h?s:u)}else r[l]=new ue(s,s)}return new yt(r,e.sel.primIndex)}function Gi(e){e.doc.mode=$r(e.options,e.doc.modeOption),vn(e)}function vn(e){e.doc.iter(function(t){t.stateAfter&&(t.stateAfter=null),t.styles&&(t.styles=null)}),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,hn(e,100),e.state.modeGen++,e.curOp&&at(e)}function cl(e,t){return t.from.ch==0&&t.to.ch==0&&ee(t.text)==""&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function qi(e,t,n,r){function i(O){return n?n[O]:null}function o(O,A,W){Na(O,A,W,r),je(O,"change",O,t)}function l(O,A){for(var W=[],q=O;q<A;++q)W.push(new Dr(u[q],i(q),r));return W}var a=t.from,s=t.to,u=t.text,d=B(e,a.line),h=B(e,s.line),m=ee(u),g=i(u.length-1),b=s.line-a.line;if(t.full)e.insert(0,l(0,u.length)),e.remove(u.length,e.size-u.length);else if(cl(e,t)){var S=l(0,u.length-1);o(h,h.text,g),b&&e.remove(a.line,b),S.length&&e.insert(a.line,S)}else if(d==h)if(u.length==1)o(d,d.text.slice(0,a.ch)+m+d.text.slice(s.ch),g);else{var C=l(1,u.length-1);C.push(new Dr(m+d.text.slice(s.ch),g,r)),o(d,d.text.slice(0,a.ch)+u[0],i(0)),e.insert(a.line+1,C)}else if(u.length==1)o(d,d.text.slice(0,a.ch)+u[0]+h.text.slice(s.ch),i(0)),e.remove(a.line+1,b);else{o(d,d.text.slice(0,a.ch)+u[0],i(0)),o(h,m+h.text.slice(s.ch),g);var N=l(1,u.length-1);b>1&&e.remove(a.line+1,b-1),e.insert(a.line+1,N)}je(e,"change",e,t)}function lr(e,t,n){function r(i,o,l){if(i.linked)for(var a=0;a<i.linked.length;++a){var s=i.linked[a];if(s.doc!=o){var u=l&&s.sharedHist;n&&!u||(t(s.doc,u),r(s.doc,i,u))}}}r(e,null,!0)}function dl(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,Wi(e),Gi(e),hl(e),e.options.lineWrapping||xi(e),e.options.mode=t.modeOption,at(e)}function hl(e){(e.doc.direction=="rtl"?De:j)(e.display.lineDiv,"CodeMirror-rtl")}function ws(e){ht(e,function(){hl(e),at(e)})}function $n(e){this.done=[],this.undone=[],this.undoDepth=1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e||1}function Xi(e,t){var n={from:de(t.from),to:or(t),text:Ht(e,t.from,t.to)};return gl(e,n,t.from.line,t.to.line+1),lr(e,function(r){return gl(r,n,t.from.line,t.to.line+1)},!0),n}function pl(e){for(;e.length;){var t=ee(e);if(t.ranges)e.pop();else break}}function ks(e,t){if(t)return pl(e.done),ee(e.done);if(e.done.length&&!ee(e.done).ranges)return ee(e.done);if(e.done.length>1&&!e.done[e.done.length-2].ranges)return e.done.pop(),ee(e.done)}function vl(e,t,n,r){var i=e.history;i.undone.length=0;var o=+new Date,l,a;if((i.lastOp==r||i.lastOrigin==t.origin&&t.origin&&(t.origin.charAt(0)=="+"&&i.lastModTime>o-(e.cm?e.cm.options.historyEventDelay:500)||t.origin.charAt(0)=="*"))&&(l=ks(i,i.lastOp==r)))a=ee(l.changes),M(t.from,t.to)==0&&M(t.from,a.to)==0?a.to=or(t):l.changes.push(Xi(e,t));else{var s=ee(i.done);for((!s||!s.ranges)&&ei(e.sel,i.done),l={changes:[Xi(e,t)],generation:i.generation},i.done.push(l);i.done.length>i.undoDepth;)i.done.shift(),i.done[0].ranges||i.done.shift()}i.done.push(n),i.generation=++i.maxGeneration,i.lastModTime=i.lastSelTime=o,i.lastOp=i.lastSelOp=r,i.lastOrigin=i.lastSelOrigin=t.origin,a||Me(e,"historyAdded")}function Ss(e,t,n,r){var i=t.charAt(0);return i=="*"||i=="+"&&n.ranges.length==r.ranges.length&&n.somethingSelected()==r.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}function Cs(e,t,n,r){var i=e.history,o=r&&r.origin;n==i.lastSelOp||o&&i.lastSelOrigin==o&&(i.lastModTime==i.lastSelTime&&i.lastOrigin==o||Ss(e,o,ee(i.done),t))?i.done[i.done.length-1]=t:ei(t,i.done),i.lastSelTime=+new Date,i.lastSelOrigin=o,i.lastSelOp=n,r&&r.clearRedo!==!1&&pl(i.undone)}function ei(e,t){var n=ee(t);n&&n.ranges&&n.equals(e)||t.push(e)}function gl(e,t,n,r){var i=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,n),Math.min(e.first+e.size,r),function(l){l.markedSpans&&((i||(i=t["spans_"+e.id]={}))[o]=l.markedSpans),++o})}function Ls(e){if(!e)return null;for(var t,n=0;n<e.length;++n)e[n].marker.explicitlyCleared?t||(t=e.slice(0,n)):t&&t.push(e[n]);return t?t.length?t:null:e}function Ts(e,t){var n=t["spans_"+e.id];if(!n)return null;for(var r=[],i=0;i<t.text.length;++i)r.push(Ls(n[i]));return r}function ml(e,t){var n=Ts(e,t),r=gi(e,t);if(!n)return r;if(!r)return n;for(var i=0;i<n.length;++i){var o=n[i],l=r[i];if(o&&l)e:for(var a=0;a<l.length;++a){for(var s=l[a],u=0;u<o.length;++u)if(o[u].marker==s.marker)continue e;o.push(s)}else l&&(n[i]=l)}return n}function Ir(e,t,n){for(var r=[],i=0;i<e.length;++i){var o=e[i];if(o.ranges){r.push(n?yt.prototype.deepCopy.call(o):o);continue}var l=o.changes,a=[];r.push({changes:a});for(var s=0;s<l.length;++s){var u=l[s],d=void 0;if(a.push({from:u.from,to:u.to,text:u.text}),t)for(var h in u)(d=h.match(/^spans_(\d+)$/))&&E(t,Number(d[1]))>-1&&(ee(a)[h]=u[h],delete u[h])}}return r}function Yi(e,t,n,r){if(r){var i=e.anchor;if(n){var o=M(t,i)<0;o!=M(n,i)<0?(i=t,t=n):o!=M(t,n)<0&&(t=n)}return new ue(i,t)}else return new ue(n||t,t)}function ti(e,t,n,r,i){i==null&&(i=e.cm&&(e.cm.display.shift||e.extend)),et(e,new yt([Yi(e.sel.primary(),t,n,i)],0),r)}function yl(e,t,n){for(var r=[],i=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)r[o]=Yi(e.sel.ranges[o],t[o],null,i);var l=Nt(e.cm,r,e.sel.primIndex);et(e,l,n)}function ji(e,t,n,r){var i=e.sel.ranges.slice(0);i[t]=n,et(e,Nt(e.cm,i,e.sel.primIndex),r)}function bl(e,t,n,r){et(e,ir(t,n),r)}function Ms(e,t,n){var r={ranges:t.ranges,update:function(i){this.ranges=[];for(var o=0;o<i.length;o++)this.ranges[o]=new ue(Z(e,i[o].anchor),Z(e,i[o].head))},origin:n&&n.origin};return Me(e,"beforeSelectionChange",e,r),e.cm&&Me(e.cm,"beforeSelectionChange",e.cm,r),r.ranges!=t.ranges?Nt(e.cm,r.ranges,r.ranges.length-1):t}function xl(e,t,n){var r=e.history.done,i=ee(r);i&&i.ranges?(r[r.length-1]=t,ri(e,t,n)):et(e,t,n)}function et(e,t,n){ri(e,t,n),Cs(e,e.sel,e.cm?e.cm.curOp.id:NaN,n)}function ri(e,t,n){(He(e,"beforeSelectionChange")||e.cm&&He(e.cm,"beforeSelectionChange"))&&(t=Ms(e,t,n));var r=n&&n.bias||(M(t.primary().head,e.sel.primary().head)<0?-1:1);wl(e,Sl(e,t,r,!0)),!(n&&n.scroll===!1)&&e.cm&&Er(e.cm)}function wl(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,Cr(e.cm)),je(e,"cursorActivity",e))}function kl(e){wl(e,Sl(e,e.sel,null,!1))}function Sl(e,t,n,r){for(var i,o=0;o<t.ranges.length;o++){var l=t.ranges[o],a=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],s=ni(e,l.anchor,a&&a.anchor,n,r),u=ni(e,l.head,a&&a.head,n,r);(i||s!=l.anchor||u!=l.head)&&(i||(i=t.ranges.slice(0,o)),i[o]=new ue(s,u))}return i?Nt(e.cm,i,t.primIndex):t}function Br(e,t,n,r,i){var o=B(e,t.line);if(o.markedSpans)for(var l=0;l<o.markedSpans.length;++l){var a=o.markedSpans[l],s=a.marker,u="selectLeft"in s?!s.selectLeft:s.inclusiveLeft,d="selectRight"in s?!s.selectRight:s.inclusiveRight;if((a.from==null||(u?a.from<=t.ch:a.from<t.ch))&&(a.to==null||(d?a.to>=t.ch:a.to>t.ch))){if(i&&(Me(s,"beforeCursorEnter"),s.explicitlyCleared))if(o.markedSpans){--l;continue}else break;if(!s.atomic)continue;if(n){var h=s.find(r<0?1:-1),m=void 0;if((r<0?d:u)&&(h=Cl(e,h,-r,h&&h.line==t.line?o:null)),h&&h.line==t.line&&(m=M(h,n))&&(r<0?m<0:m>0))return Br(e,h,t,r,i)}var g=s.find(r<0?-1:1);return(r<0?u:d)&&(g=Cl(e,g,r,g.line==t.line?o:null)),g?Br(e,g,t,r,i):null}}return t}function ni(e,t,n,r,i){var o=r||1,l=Br(e,t,n,o,i)||!i&&Br(e,t,n,o,!0)||Br(e,t,n,-o,i)||!i&&Br(e,t,n,-o,!0);return l||(e.cantEdit=!0,v(e.first,0))}function Cl(e,t,n,r){return n<0&&t.ch==0?t.line>e.first?Z(e,v(t.line-1)):null:n>0&&t.ch==(r||B(e,t.line)).text.length?t.line<e.first+e.size-1?v(t.line+1,0):null:new v(t.line,t.ch+n)}function Ll(e){e.setSelection(v(e.firstLine(),0),v(e.lastLine()),Qe)}function Tl(e,t,n){var r={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return r.canceled=!0}};return n&&(r.update=function(i,o,l,a){i&&(r.from=Z(e,i)),o&&(r.to=Z(e,o)),l&&(r.text=l),a!==void 0&&(r.origin=a)}),Me(e,"beforeChange",e,r),e.cm&&Me(e.cm,"beforeChange",e.cm,r),r.canceled?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:r.from,to:r.to,text:r.text,origin:r.origin}}function Rr(e,t,n){if(e.cm){if(!e.cm.curOp)return Ge(e.cm,Rr)(e,t,n);if(e.cm.state.suppressEdits)return}if(!((He(e,"beforeChange")||e.cm&&He(e.cm,"beforeChange"))&&(t=Tl(e,t,!0),!t))){var r=go&&!n&&Ca(e,t.from,t.to);if(r)for(var i=r.length-1;i>=0;--i)Ml(e,{from:r[i].from,to:r[i].to,text:i?[""]:t.text,origin:t.origin});else Ml(e,t)}}function Ml(e,t){if(!(t.text.length==1&&t.text[0]==""&&M(t.from,t.to)==0)){var n=Ui(e,t);vl(e,t,n,e.cm?e.cm.curOp.id:NaN),gn(e,t,n,gi(e,t));var r=[];lr(e,function(i,o){!o&&E(r,i.history)==-1&&(Ol(i.history,t),r.push(i.history)),gn(i,t,null,gi(i,t))})}}function ii(e,t,n){var r=e.cm&&e.cm.state.suppressEdits;if(!(r&&!n)){for(var i=e.history,o,l=e.sel,a=t=="undo"?i.done:i.undone,s=t=="undo"?i.undone:i.done,u=0;u<a.length&&(o=a[u],!(n?o.ranges&&!o.equals(e.sel):!o.ranges));u++);if(u!=a.length){for(i.lastOrigin=i.lastSelOrigin=null;;)if(o=a.pop(),o.ranges){if(ei(o,s),n&&!o.equals(e.sel)){et(e,o,{clearRedo:!1});return}l=o}else if(r){a.push(o);return}else break;var d=[];ei(l,s),s.push({changes:d,generation:i.generation}),i.generation=o.generation||++i.maxGeneration;for(var h=He(e,"beforeChange")||e.cm&&He(e.cm,"beforeChange"),m=function(S){var C=o.changes[S];if(C.origin=t,h&&!Tl(e,C,!1))return a.length=0,{};d.push(Xi(e,C));var N=S?Ui(e,C):ee(a);gn(e,C,N,ml(e,C)),!S&&e.cm&&e.cm.scrollIntoView({from:C.from,to:or(C)});var O=[];lr(e,function(A,W){!W&&E(O,A.history)==-1&&(Ol(A.history,C),O.push(A.history)),gn(A,C,null,ml(A,C))})},g=o.changes.length-1;g>=0;--g){var b=m(g);if(b)return b.v}}}}function Nl(e,t){if(t!=0&&(e.first+=t,e.sel=new yt(ce(e.sel.ranges,function(i){return new ue(v(i.anchor.line+t,i.anchor.ch),v(i.head.line+t,i.head.ch))}),e.sel.primIndex),e.cm)){at(e.cm,e.first,e.first-t,t);for(var n=e.cm.display,r=n.viewFrom;r<n.viewTo;r++)rr(e.cm,r,"gutter")}}function gn(e,t,n,r){if(e.cm&&!e.cm.curOp)return Ge(e.cm,gn)(e,t,n,r);if(t.to.line<e.first){Nl(e,t.text.length-1-(t.to.line-t.from.line));return}if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var i=t.text.length-1-(e.first-t.from.line);Nl(e,i),t={from:v(e.first,0),to:v(t.to.line+i,t.to.ch),text:[ee(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:v(o,B(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=Ht(e,t.from,t.to),n||(n=Ui(e,t)),e.cm?Ns(e.cm,t,r):qi(e,t,r),ri(e,n,Qe),e.cantEdit&&ni(e,v(e.firstLine(),0))&&(e.cantEdit=!1)}}function Ns(e,t,n){var r=e.doc,i=e.display,o=t.from,l=t.to,a=!1,s=o.line;e.options.lineWrapping||(s=ae(Ft(B(r,o.line))),r.iter(s,l.line+1,function(g){if(g==i.maxLine)return a=!0,!0})),r.sel.contains(t.from,t.to)>-1&&Cr(e),qi(r,t,n,Xo(e)),e.options.lineWrapping||(r.iter(s,o.line+t.text.length,function(g){var b=_n(g);b>i.maxLineLength&&(i.maxLine=g,i.maxLineLength=b,i.maxLineChanged=!0,a=!1)}),a&&(e.curOp.updateMaxLine=!0)),ma(r,o.line),hn(e,400);var u=t.text.length-(l.line-o.line)-1;t.full?at(e):o.line==l.line&&t.text.length==1&&!cl(e.doc,t)?rr(e,o.line,"text"):at(e,o.line,l.line+1,u);var d=He(e,"changes"),h=He(e,"change");if(h||d){var m={from:o,to:l,text:t.text,removed:t.removed,origin:t.origin};h&&je(e,"change",e,m),d&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(m)}e.display.selForContextMenu=null}function Kr(e,t,n,r,i){var o;r||(r=n),M(r,n)<0&&(o=[r,n],n=o[0],r=o[1]),typeof t=="string"&&(t=e.splitLines(t)),Rr(e,{from:n,to:r,text:t,origin:i})}function Al(e,t,n,r){n<e.line?e.line+=r:t<e.line&&(e.line=t,e.ch=0)}function Dl(e,t,n,r){for(var i=0;i<e.length;++i){var o=e[i],l=!0;if(o.ranges){o.copied||(o=e[i]=o.deepCopy(),o.copied=!0);for(var a=0;a<o.ranges.length;a++)Al(o.ranges[a].anchor,t,n,r),Al(o.ranges[a].head,t,n,r);continue}for(var s=0;s<o.changes.length;++s){var u=o.changes[s];if(n<u.from.line)u.from=v(u.from.line+r,u.from.ch),u.to=v(u.to.line+r,u.to.ch);else if(t<=u.to.line){l=!1;break}}l||(e.splice(0,i+1),i=0)}}function Ol(e,t){var n=t.from.line,r=t.to.line,i=t.text.length-(r-n)-1;Dl(e.done,n,r,i),Dl(e.undone,n,r,i)}function mn(e,t,n,r){var i=t,o=t;return typeof t=="number"?o=B(e,En(e,t)):i=ae(t),i==null?null:(r(o,i)&&e.cm&&rr(e.cm,i,n),o)}function yn(e){this.lines=e,this.parent=null;for(var t=0,n=0;n<e.length;++n)e[n].parent=this,t+=e[n].height;this.height=t}yn.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var n=e,r=e+t;n<r;++n){var i=this.lines[n];this.height-=i.height,Aa(i),je(i,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,n){this.height+=n,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var r=0;r<t.length;++r)t[r].parent=this},iterN:function(e,t,n){for(var r=e+t;e<r;++e)if(n(this.lines[e]))return!0}};function bn(e){this.children=e;for(var t=0,n=0,r=0;r<e.length;++r){var i=e[r];t+=i.chunkSize(),n+=i.height,i.parent=this}this.size=t,this.height=n,this.parent=null}bn.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var n=0;n<this.children.length;++n){var r=this.children[n],i=r.chunkSize();if(e<i){var o=Math.min(t,i-e),l=r.height;if(r.removeInner(e,o),this.height-=l-r.height,i==o&&(this.children.splice(n--,1),r.parent=null),(t-=o)==0)break;e=0}else e-=i}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof yn))){var a=[];this.collapse(a),this.children=[new yn(a)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,n){this.size+=t.length,this.height+=n;for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<=o){if(i.insertInner(e,t,n),i.lines&&i.lines.length>50){for(var l=i.lines.length%25+25,a=l;a<i.lines.length;){var s=new yn(i.lines.slice(a,a+=25));i.height-=s.height,this.children.splice(++r,0,s),s.parent=this}i.lines=i.lines.slice(0,l),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=e.children.splice(e.children.length-5,5),n=new bn(t);if(e.parent){e.size-=n.size,e.height-=n.height;var i=E(e.parent.children,e);e.parent.children.splice(i+1,0,n)}else{var r=new bn(e.children);r.parent=e,e.children=[r,n],e=r}n.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,n){for(var r=0;r<this.children.length;++r){var i=this.children[r],o=i.chunkSize();if(e<o){var l=Math.min(t,o-e);if(i.iterN(e,l,n))return!0;if((t-=l)==0)break;e=0}else e-=o}}};var xn=function(e,t,n){if(n)for(var r in n)n.hasOwnProperty(r)&&(this[r]=n[r]);this.doc=e,this.node=t};xn.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,n=this.line,r=ae(n);if(!(r==null||!t)){for(var i=0;i<t.length;++i)t[i]==this&&t.splice(i--,1);t.length||(n.widgets=null);var o=ln(this);St(n,Math.max(0,n.height-o)),e&&(ht(e,function(){Wl(e,n,-o),rr(e,r,"widget")}),je(e,"lineWidgetCleared",e,this,r))}},xn.prototype.changed=function(){var e=this,t=this.height,n=this.doc.cm,r=this.line;this.height=null;var i=ln(this)-t;i&&(tr(this.doc,r)||St(r,r.height+i),n&&ht(n,function(){n.curOp.forceUpdate=!0,Wl(n,r,i),je(n,"lineWidgetChanged",n,e,ae(r))}))},$t(xn);function Wl(e,t,n){Zt(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&Fi(e,n)}function As(e,t,n,r){var i=new xn(e,n,r),o=e.cm;return o&&i.noHScroll&&(o.display.alignWidgets=!0),mn(e,t,"widget",function(l){var a=l.widgets||(l.widgets=[]);if(i.insertAt==null?a.push(i):a.splice(Math.min(a.length,Math.max(0,i.insertAt)),0,i),i.line=l,o&&!tr(e,l)){var s=Zt(l)<e.scrollTop;St(l,l.height+ln(i)),s&&Fi(o,i.height),o.curOp.forceUpdate=!0}return!0}),o&&je(o,"lineWidgetAdded",o,i,typeof t=="number"?t:ae(t)),i}var Pl=0,ar=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++Pl};ar.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&br(e),He(this,"clear")){var n=this.find();n&&je(this,"clear",n.from,n.to)}for(var r=null,i=null,o=0;o<this.lines.length;++o){var l=this.lines[o],a=rn(l.markedSpans,this);e&&!this.collapsed?rr(e,ae(l),"text"):e&&(a.to!=null&&(i=ae(l)),a.from!=null&&(r=ae(l))),l.markedSpans=xa(l.markedSpans,a),a.from==null&&this.collapsed&&!tr(this.doc,l)&&e&&St(l,Pr(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var s=0;s<this.lines.length;++s){var u=Ft(this.lines[s]),d=_n(u);d>e.display.maxLineLength&&(e.display.maxLine=u,e.display.maxLineLength=d,e.display.maxLineChanged=!0)}r!=null&&e&&this.collapsed&&at(e,r,i+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&kl(e.doc)),e&&je(e,"markerCleared",e,this,r,i),t&&xr(e),this.parent&&this.parent.clear()}},ar.prototype.find=function(e,t){e==null&&this.type=="bookmark"&&(e=1);for(var n,r,i=0;i<this.lines.length;++i){var o=this.lines[i],l=rn(o.markedSpans,this);if(l.from!=null&&(n=v(t?o:ae(o),l.from),e==-1))return n;if(l.to!=null&&(r=v(t?o:ae(o),l.to),e==1))return r}return n&&{from:n,to:r}},ar.prototype.changed=function(){var e=this,t=this.find(-1,!0),n=this,r=this.doc.cm;!t||!r||ht(r,function(){var i=t.line,o=ae(t.line),l=Ci(r,o);if(l&&(Io(l),r.curOp.selectionChanged=r.curOp.forceUpdate=!0),r.curOp.updateMaxLine=!0,!tr(n.doc,i)&&n.height!=null){var a=n.height;n.height=null;var s=ln(n)-a;s&&St(i,i.height+s)}je(r,"markerChanged",r,e)})},ar.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(!t.maybeHiddenMarkers||E(t.maybeHiddenMarkers,this)==-1)&&(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},ar.prototype.detachLine=function(e){if(this.lines.splice(E(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},$t(ar);function _r(e,t,n,r,i){if(r&&r.shared)return Ds(e,t,n,r,i);if(e.cm&&!e.cm.curOp)return Ge(e.cm,_r)(e,t,n,r,i);var o=new ar(e,i),l=M(t,n);if(r&&vt(r,o,!1),l>0||l==0&&o.clearWhenEmpty!==!1)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=D("span",[o.replacedWith],"CodeMirror-widget"),r.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),r.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(ko(e,t.line,t,n,o)||t.line!=n.line&&ko(e,n.line,t,n,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");ba()}o.addToHistory&&vl(e,{from:t,to:n,origin:"markText"},e.sel,NaN);var a=t.line,s=e.cm,u;if(e.iter(a,n.line+1,function(h){s&&o.collapsed&&!s.options.lineWrapping&&Ft(h)==s.display.maxLine&&(u=!0),o.collapsed&&a!=t.line&&St(h,0),wa(h,new In(o,a==t.line?t.ch:null,a==n.line?n.ch:null)),++a}),o.collapsed&&e.iter(t.line,n.line+1,function(h){tr(e,h)&&St(h,0)}),o.clearOnEnter&&G(o,"beforeCursorEnter",function(){return o.clear()}),o.readOnly&&(ya(),(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++Pl,o.atomic=!0),s){if(u&&(s.curOp.updateMaxLine=!0),o.collapsed)at(s,t.line,n.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var d=t.line;d<=n.line;d++)rr(s,d,"text");o.atomic&&kl(s.doc),je(s,"markerAdded",s,o)}return o}var wn=function(e,t){this.markers=e,this.primary=t;for(var n=0;n<e.length;++n)e[n].parent=this};wn.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();je(this,"clear")}},wn.prototype.find=function(e,t){return this.primary.find(e,t)},$t(wn);function Ds(e,t,n,r,i){r=vt(r),r.shared=!1;var o=[_r(e,t,n,r,i)],l=o[0],a=r.widgetNode;return lr(e,function(s){a&&(r.widgetNode=a.cloneNode(!0)),o.push(_r(s,Z(s,t),Z(s,n),r,i));for(var u=0;u<s.linked.length;++u)if(s.linked[u].isParent)return;l=ee(o)}),new wn(o,l)}function zl(e){return e.findMarks(v(e.first,0),e.clipPos(v(e.lastLine())),function(t){return t.parent})}function Os(e,t){for(var n=0;n<t.length;n++){var r=t[n],i=r.find(),o=e.clipPos(i.from),l=e.clipPos(i.to);if(M(o,l)){var a=_r(e,o,l,r.primary,r.primary.type);r.markers.push(a),a.parent=r}}}function Ws(e){for(var t=function(r){var i=e[r],o=[i.primary.doc];lr(i.primary.doc,function(s){return o.push(s)});for(var l=0;l<i.markers.length;l++){var a=i.markers[l];E(o,a.doc)==-1&&(a.parent=null,i.markers.splice(l--,1))}},n=0;n<e.length;n++)t(n)}var Ps=0,st=function(e,t,n,r,i){if(!(this instanceof st))return new st(e,t,n,r,i);n==null&&(n=0),bn.call(this,[new yn([new Dr("",null)])]),this.first=n,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=n;var o=v(n,0);this.sel=ir(o),this.history=new $n(null),this.id=++Ps,this.modeOption=t,this.lineSep=r,this.direction=i=="rtl"?"rtl":"ltr",this.extend=!1,typeof e=="string"&&(e=this.splitLines(e)),qi(this,{from:o,to:o,text:e}),et(this,ir(o),Qe)};st.prototype=gt(bn.prototype,{constructor:st,iter:function(e,t,n){n?this.iterN(e-this.first,t-e,n):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var n=0,r=0;r<t.length;++r)n+=t[r].height;this.insertInner(e-this.first,t,n)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=en(this,this.first,this.first+this.size);return e===!1?t:t.join(e||this.lineSeparator())},setValue:qe(function(e){var t=v(this.first,0),n=this.first+this.size-1;Rr(this,{from:t,to:v(n,B(this,n).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&un(this.cm,0,0),et(this,ir(t),Qe)}),replaceRange:function(e,t,n,r){t=Z(this,t),n=n?Z(this,n):t,Kr(this,e,t,n,r)},getRange:function(e,t,n){var r=Ht(this,Z(this,e),Z(this,t));return n===!1?r:r.join(n||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(p(this,e))return B(this,e)},getLineNumber:function(e){return ae(e)},getLineHandleVisualStart:function(e){return typeof e=="number"&&(e=B(this,e)),Ft(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return Z(this,e)},getCursor:function(e){var t=this.sel.primary(),n;return e==null||e=="head"?n=t.head:e=="anchor"?n=t.anchor:e=="end"||e=="to"||e===!1?n=t.to():n=t.from(),n},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:qe(function(e,t,n){bl(this,Z(this,typeof e=="number"?v(e,t||0):e),null,n)}),setSelection:qe(function(e,t,n){bl(this,Z(this,e),Z(this,t||e),n)}),extendSelection:qe(function(e,t,n){ti(this,Z(this,e),t&&Z(this,t),n)}),extendSelections:qe(function(e,t){yl(this,ao(this,e),t)}),extendSelectionsBy:qe(function(e,t){var n=ce(this.sel.ranges,e);yl(this,ao(this,n),t)}),setSelections:qe(function(e,t,n){if(e.length){for(var r=[],i=0;i<e.length;i++)r[i]=new ue(Z(this,e[i].anchor),Z(this,e[i].head));t==null&&(t=Math.min(e.length-1,this.sel.primIndex)),et(this,Nt(this.cm,r,t),n)}}),addSelection:qe(function(e,t,n){var r=this.sel.ranges.slice(0);r.push(new ue(Z(this,e),Z(this,t||e))),et(this,Nt(this.cm,r,r.length-1),n)}),getSelection:function(e){for(var t=this.sel.ranges,n,r=0;r<t.length;r++){var i=Ht(this,t[r].from(),t[r].to());n=n?n.concat(i):i}return e===!1?n:n.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],n=this.sel.ranges,r=0;r<n.length;r++){var i=Ht(this,n[r].from(),n[r].to());e!==!1&&(i=i.join(e||this.lineSeparator())),t[r]=i}return t},replaceSelection:function(e,t,n){for(var r=[],i=0;i<this.sel.ranges.length;i++)r[i]=e;this.replaceSelections(r,t,n||"+input")},replaceSelections:qe(function(e,t,n){for(var r=[],i=this.sel,o=0;o<i.ranges.length;o++){var l=i.ranges[o];r[o]={from:l.from(),to:l.to(),text:this.splitLines(e[o]),origin:n}}for(var a=t&&t!="end"&&xs(this,r,t),s=r.length-1;s>=0;s--)Rr(this,r[s]);a?xl(this,a):this.cm&&Er(this.cm)}),undo:qe(function(){ii(this,"undo")}),redo:qe(function(){ii(this,"redo")}),undoSelection:qe(function(){ii(this,"undo",!0)}),redoSelection:qe(function(){ii(this,"redo",!0)}),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,n=0,r=0;r<e.done.length;r++)e.done[r].ranges||++t;for(var i=0;i<e.undone.length;i++)e.undone[i].ranges||++n;return{undo:t,redo:n}},clearHistory:function(){var e=this;this.history=new $n(this.history.maxGeneration),lr(this,function(t){return t.history=e.history},!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Ir(this.history.done),undone:Ir(this.history.undone)}},setHistory:function(e){var t=this.history=new $n(this.history.maxGeneration);t.done=Ir(e.done.slice(0),null,!0),t.undone=Ir(e.undone.slice(0),null,!0)},setGutterMarker:qe(function(e,t,n){return mn(this,e,"gutter",function(r){var i=r.gutterMarkers||(r.gutterMarkers={});return i[t]=n,!n&&T(i)&&(r.gutterMarkers=null),!0})}),clearGutter:qe(function(e){var t=this;this.iter(function(n){n.gutterMarkers&&n.gutterMarkers[e]&&mn(t,n,"gutter",function(){return n.gutterMarkers[e]=null,T(n.gutterMarkers)&&(n.gutterMarkers=null),!0})})}),lineInfo:function(e){var t;if(typeof e=="number"){if(!p(this,e)||(t=e,e=B(this,e),!e))return null}else if(t=ae(e),t==null)return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:qe(function(e,t,n){return mn(this,e,t=="gutter"?"gutter":"class",function(r){var i=t=="text"?"textClass":t=="background"?"bgClass":t=="gutter"?"gutterClass":"wrapClass";if(!r[i])r[i]=n;else{if(_(n).test(r[i]))return!1;r[i]+=" "+n}return!0})}),removeLineClass:qe(function(e,t,n){return mn(this,e,t=="gutter"?"gutter":"class",function(r){var i=t=="text"?"textClass":t=="background"?"bgClass":t=="gutter"?"gutterClass":"wrapClass",o=r[i];if(o)if(n==null)r[i]=null;else{var l=o.match(_(n));if(!l)return!1;var a=l.index+l[0].length;r[i]=o.slice(0,l.index)+(!l.index||a==o.length?"":" ")+o.slice(a)||null}else return!1;return!0})}),addLineWidget:qe(function(e,t,n){return As(this,e,t,n)}),removeLineWidget:function(e){e.clear()},markText:function(e,t,n){return _r(this,Z(this,e),Z(this,t),n,n&&n.type||"range")},setBookmark:function(e,t){var n={replacedWith:t&&(t.nodeType==null?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return e=Z(this,e),_r(this,e,e,n,"bookmark")},findMarksAt:function(e){e=Z(this,e);var t=[],n=B(this,e.line).markedSpans;if(n)for(var r=0;r<n.length;++r){var i=n[r];(i.from==null||i.from<=e.ch)&&(i.to==null||i.to>=e.ch)&&t.push(i.marker.parent||i.marker)}return t},findMarks:function(e,t,n){e=Z(this,e),t=Z(this,t);var r=[],i=e.line;return this.iter(e.line,t.line+1,function(o){var l=o.markedSpans;if(l)for(var a=0;a<l.length;a++){var s=l[a];!(s.to!=null&&i==e.line&&e.ch>=s.to||s.from==null&&i!=e.line||s.from!=null&&i==t.line&&s.from>=t.ch)&&(!n||n(s.marker))&&r.push(s.marker.parent||s.marker)}++i}),r},getAllMarks:function(){var e=[];return this.iter(function(t){var n=t.markedSpans;if(n)for(var r=0;r<n.length;++r)n[r].from!=null&&e.push(n[r].marker)}),e},posFromIndex:function(e){var t,n=this.first,r=this.lineSeparator().length;return this.iter(function(i){var o=i.text.length+r;if(o>e)return t=e,!0;e-=o,++n}),Z(this,v(n,t))},indexFromPos:function(e){e=Z(this,e);var t=e.ch;if(e.line<this.first||e.ch<0)return 0;var n=this.lineSeparator().length;return this.iter(this.first,e.line,function(r){t+=r.text.length+n}),t},copy:function(e){var t=new st(en(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,n=this.first+this.size;e.from!=null&&e.from>t&&(t=e.from),e.to!=null&&e.to<n&&(n=e.to);var r=new st(en(this,t,n),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(r.history=this.history),(this.linked||(this.linked=[])).push({doc:r,sharedHist:e.sharedHist}),r.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],Os(r,zl(this)),r},unlinkDoc:function(e){if(e instanceof ke&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t){var n=this.linked[t];if(n.doc==e){this.linked.splice(t,1),e.unlinkDoc(this),Ws(zl(this));break}}if(e.history==this.history){var r=[e.id];lr(e,function(i){return r.push(i.id)},!0),e.history=new $n(null),e.history.done=Ir(this.history.done,r),e.history.undone=Ir(this.history.undone,r)}},iterLinkedDocs:function(e){lr(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):Pt(e)},lineSeparator:function(){return this.lineSep||`
`},setDirection:qe(function(e){e!="rtl"&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter(function(t){return t.order=null}),this.cm&&ws(this.cm))})}),st.prototype.eachLine=st.prototype.iter;var Hl=0;function zs(e){var t=this;if(El(t),!(xe(t,e)||Jt(t.display,e))){Be(e),P&&(Hl=+new Date);var n=vr(t,e,!0),r=e.dataTransfer.files;if(!(!n||t.isReadOnly()))if(r&&r.length&&window.FileReader&&window.File)for(var i=r.length,o=Array(i),l=0,a=function(){++l==i&&Ge(t,function(){n=Z(t.doc,n);var g={from:n,to:n,text:t.doc.splitLines(o.filter(function(b){return b!=null}).join(t.doc.lineSeparator())),origin:"paste"};Rr(t.doc,g),xl(t.doc,ir(Z(t.doc,n),Z(t.doc,or(g))))})()},s=function(g,b){if(t.options.allowDropFileTypes&&E(t.options.allowDropFileTypes,g.type)==-1){a();return}var S=new FileReader;S.onerror=function(){return a()},S.onload=function(){var C=S.result;if(/[\x00-\x08\x0e-\x1f]{2}/.test(C)){a();return}o[b]=C,a()},S.readAsText(g)},u=0;u<r.length;u++)s(r[u],u);else{if(t.state.draggingText&&t.doc.sel.contains(n)>-1){t.state.draggingText(e),setTimeout(function(){return t.display.input.focus()},20);return}try{var d=e.dataTransfer.getData("Text");if(d){var h;if(t.state.draggingText&&!t.state.draggingText.copy&&(h=t.listSelections()),ri(t.doc,ir(n,n)),h)for(var m=0;m<h.length;++m)Kr(t.doc,"",h[m].anchor,h[m].head,"drag");t.replaceSelection(d,"around","paste"),t.display.input.focus()}}catch{}}}}function Hs(e,t){if(P&&(!e.state.draggingText||+new Date-Hl<100)){$e(t);return}if(!(xe(e,t)||Jt(e.display,t))&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!fe)){var n=c("img",null,null,"position: fixed; left: 0; top: 0;");n.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",J&&(n.width=n.height=1,e.display.wrapper.appendChild(n),n._top=n.offsetTop),t.dataTransfer.setDragImage(n,0,0),J&&n.parentNode.removeChild(n)}}function Es(e,t){var n=vr(e,t);if(n){var r=document.createDocumentFragment();Zo(e,n,r),e.display.dragCursor||(e.display.dragCursor=c("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),z(e.display.dragCursor,r)}}function El(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Fl(e){if(document.getElementsByClassName){for(var t=document.getElementsByClassName("CodeMirror"),n=[],r=0;r<t.length;r++){var i=t[r].CodeMirror;i&&n.push(i)}n.length&&n[0].operation(function(){for(var o=0;o<n.length;o++)e(n[o])})}}var Il=!1;function Fs(){Il||(Is(),Il=!0)}function Is(){var e;G(window,"resize",function(){e==null&&(e=setTimeout(function(){e=null,Fl(Bs)},100))}),G(window,"blur",function(){return Fl(Hr)})}function Bs(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var sr={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},kn=0;kn<10;kn++)sr[kn+48]=sr[kn+96]=String(kn);for(var oi=65;oi<=90;oi++)sr[oi]=String.fromCharCode(oi);for(var Sn=1;Sn<=12;Sn++)sr[Sn+111]=sr[Sn+63235]="F"+Sn;var Qt={};Qt.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},Qt.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},Qt.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Alt-F":"goWordRight","Alt-B":"goWordLeft","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-D":"delWordAfter","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},Qt.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},Qt.default=Y?Qt.macDefault:Qt.pcDefault;function Rs(e){var t=e.split(/-(?!$)/);e=t[t.length-1];for(var n,r,i,o,l=0;l<t.length-1;l++){var a=t[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))n=!0;else if(/^(c|ctrl|control)$/i.test(a))r=!0;else if(/^s(hift)?$/i.test(a))i=!0;else throw new Error("Unrecognized modifier name: "+a)}return n&&(e="Alt-"+e),r&&(e="Ctrl-"+e),o&&(e="Cmd-"+e),i&&(e="Shift-"+e),e}function Ks(e){var t={};for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];if(/^(name|fallthrough|(de|at)tach)$/.test(n))continue;if(r=="..."){delete e[n];continue}for(var i=ce(n.split(" "),Rs),o=0;o<i.length;o++){var l=void 0,a=void 0;o==i.length-1?(a=i.join(" "),l=r):(a=i.slice(0,o+1).join(" "),l="...");var s=t[a];if(!s)t[a]=l;else if(s!=l)throw new Error("Inconsistent bindings for "+a)}delete e[n]}for(var u in t)e[u]=t[u];return e}function Ur(e,t,n,r){t=li(t);var i=t.call?t.call(e,r):t[e];if(i===!1)return"nothing";if(i==="...")return"multi";if(i!=null&&n(i))return"handled";if(t.fallthrough){if(Object.prototype.toString.call(t.fallthrough)!="[object Array]")return Ur(e,t.fallthrough,n,r);for(var o=0;o<t.fallthrough.length;o++){var l=Ur(e,t.fallthrough[o],n,r);if(l)return l}}}function Bl(e){var t=typeof e=="string"?e:sr[e.keyCode];return t=="Ctrl"||t=="Alt"||t=="Shift"||t=="Mod"}function Rl(e,t,n){var r=e;return t.altKey&&r!="Alt"&&(e="Alt-"+e),(rt?t.metaKey:t.ctrlKey)&&r!="Ctrl"&&(e="Ctrl-"+e),(rt?t.ctrlKey:t.metaKey)&&r!="Mod"&&(e="Cmd-"+e),!n&&t.shiftKey&&r!="Shift"&&(e="Shift-"+e),e}function Kl(e,t){if(J&&e.keyCode==34&&e.char)return!1;var n=sr[e.keyCode];return n==null||e.altGraphKey?!1:(e.keyCode==3&&e.code&&(n=e.code),Rl(n,e,t))}function li(e){return typeof e=="string"?Qt[e]:e}function Gr(e,t){for(var n=e.doc.sel.ranges,r=[],i=0;i<n.length;i++){for(var o=t(n[i]);r.length&&M(o.from,ee(r).to)<=0;){var l=r.pop();if(M(l.from,o.from)<0){o.from=l.from;break}}r.push(o)}ht(e,function(){for(var a=r.length-1;a>=0;a--)Kr(e.doc,"",r[a].from,r[a].to,"+delete");Er(e)})}function Zi(e,t,n){var r=Ue(e.text,t+n,n);return r<0||r>e.text.length?null:r}function Ji(e,t,n){var r=Zi(e,t.ch,n);return r==null?null:new v(t.line,r,n<0?"after":"before")}function Qi(e,t,n,r,i){if(e){t.doc.direction=="rtl"&&(i=-i);var o=nt(n,t.doc.direction);if(o){var l=i<0?ee(o):o[0],a=i<0==(l.level==1),s=a?"after":"before",u;if(l.level>0||t.doc.direction=="rtl"){var d=Wr(t,n);u=i<0?n.text.length-1:0;var h=Bt(t,d,u).top;u=cr(function(m){return Bt(t,d,m).top==h},i<0==(l.level==1)?l.from:l.to-1,u),s=="before"&&(u=Zi(n,u,1))}else u=i<0?l.to:l.from;return new v(r,u,s)}}return new v(r,i<0?n.text.length:0,i<0?"before":"after")}function _s(e,t,n,r){var i=nt(t,e.doc.direction);if(!i)return Ji(t,n,r);n.ch>=t.text.length?(n.ch=t.text.length,n.sticky="before"):n.ch<=0&&(n.ch=0,n.sticky="after");var o=Lt(i,n.ch,n.sticky),l=i[o];if(e.doc.direction=="ltr"&&l.level%2==0&&(r>0?l.to>n.ch:l.from<n.ch))return Ji(t,n,r);var a=function(N,O){return Zi(t,N instanceof v?N.ch:N,O)},s,u=function(N){return e.options.lineWrapping?(s=s||Wr(e,t),qo(e,t,s,N)):{begin:0,end:t.text.length}},d=u(n.sticky=="before"?a(n,-1):n.ch);if(e.doc.direction=="rtl"||l.level==1){var h=l.level==1==r<0,m=a(n,h?1:-1);if(m!=null&&(h?m<=l.to&&m<=d.end:m>=l.from&&m>=d.begin)){var g=h?"before":"after";return new v(n.line,m,g)}}var b=function(N,O,A){for(var W=function(ge,Xe){return Xe?new v(n.line,a(ge,1),"before"):new v(n.line,ge,"after")};N>=0&&N<i.length;N+=O){var q=i[N],R=O>0==(q.level!=1),V=R?A.begin:a(A.end,-1);if(q.from<=V&&V<q.to||(V=R?q.from:a(q.to,-1),A.begin<=V&&V<A.end))return W(V,R)}},S=b(o+r,r,d);if(S)return S;var C=r>0?d.end:a(d.begin,-1);return C!=null&&!(r>0&&C==t.text.length)&&(S=b(r>0?0:i.length-1,r,u(C)),S)?S:null}var Cn={selectAll:Ll,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),Qe)},killLine:function(e){return Gr(e,function(t){if(t.empty()){var n=B(e.doc,t.head.line).text.length;return t.head.ch==n&&t.head.line<e.lastLine()?{from:t.head,to:v(t.head.line+1,0)}:{from:t.head,to:v(t.head.line,n)}}else return{from:t.from(),to:t.to()}})},deleteLine:function(e){return Gr(e,function(t){return{from:v(t.from().line,0),to:Z(e.doc,v(t.to().line+1,0))}})},delLineLeft:function(e){return Gr(e,function(t){return{from:v(t.from().line,0),to:t.from()}})},delWrappedLineLeft:function(e){return Gr(e,function(t){var n=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:n},"div");return{from:r,to:t.from()}})},delWrappedLineRight:function(e){return Gr(e,function(t){var n=e.charCoords(t.head,"div").top+5,r=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div");return{from:t.from(),to:r}})},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(v(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(v(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy(function(t){return _l(e,t.head.line)},{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy(function(t){return Ul(e,t.head)},{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy(function(t){return Us(e,t.head.line)},{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy(function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div")},ie)},goLineLeft:function(e){return e.extendSelectionsBy(function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:n},"div")},ie)},goLineLeftSmart:function(e){return e.extendSelectionsBy(function(t){var n=e.cursorCoords(t.head,"div").top+5,r=e.coordsChar({left:0,top:n},"div");return r.ch<e.getLine(r.line).search(/\S/)?Ul(e,t.head):r},ie)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"codepoint")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("	")},insertSoftTab:function(e){for(var t=[],n=e.listSelections(),r=e.options.tabSize,i=0;i<n.length;i++){var o=n[i].from(),l=be(e.getLine(o.line),o.ch,r);t.push(Ct(r-l%r))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return ht(e,function(){for(var t=e.listSelections(),n=[],r=0;r<t.length;r++)if(t[r].empty()){var i=t[r].head,o=B(e.doc,i.line).text;if(o){if(i.ch==o.length&&(i=new v(i.line,i.ch-1)),i.ch>0)i=new v(i.line,i.ch+1),e.replaceRange(o.charAt(i.ch-1)+o.charAt(i.ch-2),v(i.line,i.ch-2),i,"+transpose");else if(i.line>e.doc.first){var l=B(e.doc,i.line-1).text;l&&(i=new v(i.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+l.charAt(l.length-1),v(i.line-1,l.length-1),i,"+transpose"))}}n.push(new ue(i,i))}e.setSelections(n)})},newlineAndIndent:function(e){return ht(e,function(){for(var t=e.listSelections(),n=t.length-1;n>=0;n--)e.replaceRange(e.doc.lineSeparator(),t[n].anchor,t[n].head,"+input");t=e.listSelections();for(var r=0;r<t.length;r++)e.indentLine(t[r].from().line,null,!0);Er(e)})},openLine:function(e){return e.replaceSelection(`
`,"start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function _l(e,t){var n=B(e.doc,t),r=Ft(n);return r!=n&&(t=ae(r)),Qi(!0,e,r,t,1)}function Us(e,t){var n=B(e.doc,t),r=Ta(n);return r!=n&&(t=ae(r)),Qi(!0,e,n,t,-1)}function Ul(e,t){var n=_l(e,t.line),r=B(e.doc,n.line),i=nt(r,e.doc.direction);if(!i||i[0].level==0){var o=Math.max(n.ch,r.text.search(/\S/)),l=t.line==n.line&&t.ch<=o&&t.ch;return v(n.line,l?0:o,n.sticky)}return n}function ai(e,t,n){if(typeof t=="string"&&(t=Cn[t],!t))return!1;e.display.input.ensurePolled();var r=e.display.shift,i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n&&(e.display.shift=!1),i=t(e)!=ye}finally{e.display.shift=r,e.state.suppressEdits=!1}return i}function Gs(e,t,n){for(var r=0;r<e.state.keyMaps.length;r++){var i=Ur(t,e.state.keyMaps[r],n,e);if(i)return i}return e.options.extraKeys&&Ur(t,e.options.extraKeys,n,e)||Ur(t,e.options.keyMap,n,e)}var qs=new K;function Ln(e,t,n,r){var i=e.state.keySeq;if(i){if(Bl(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:qs.set(50,function(){e.state.keySeq==i&&(e.state.keySeq=null,e.display.input.reset())}),Gl(e,i+" "+t,n,r))return!0}return Gl(e,t,n,r)}function Gl(e,t,n,r){var i=Gs(e,t,r);return i=="multi"&&(e.state.keySeq=t),i=="handled"&&je(e,"keyHandled",e,t,n),(i=="handled"||i=="multi")&&(Be(n),Pi(e)),!!i}function ql(e,t){var n=Kl(t,!0);return n?t.shiftKey&&!e.state.keySeq?Ln(e,"Shift-"+n,t,function(r){return ai(e,r,!0)})||Ln(e,n,t,function(r){if(typeof r=="string"?/^go[A-Z]/.test(r):r.motion)return ai(e,r)}):Ln(e,n,t,function(r){return ai(e,r)}):!1}function Xs(e,t,n){return Ln(e,"'"+n+"'",t,function(r){return ai(e,r,!0)})}var Vi=null;function Xl(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField())&&(t.curOp.focus=ve(),!xe(t,e))){P&&I<11&&e.keyCode==27&&(e.returnValue=!1);var n=e.keyCode;t.display.shift=n==16||e.shiftKey;var r=ql(t,e);J&&(Vi=r?n:null,!r&&n==88&&!er&&(Y?e.metaKey:e.ctrlKey)&&t.replaceSelection("",null,"cut")),pe&&!Y&&!r&&n==46&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),n==18&&!/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)&&Ys(t)}}function Ys(e){var t=e.display.lineDiv;De(t,"CodeMirror-crosshair");function n(r){(r.keyCode==18||!r.altKey)&&(j(t,"CodeMirror-crosshair"),te(document,"keyup",n),te(document,"mouseover",n))}G(document,"keyup",n),G(document,"mouseover",n)}function Yl(e){e.keyCode==16&&(this.doc.sel.shift=!1),xe(this,e)}function jl(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField())&&!(Jt(t.display,e)||xe(t,e)||e.ctrlKey&&!e.altKey||Y&&e.metaKey)){var n=e.keyCode,r=e.charCode;if(J&&n==Vi){Vi=null,Be(e);return}if(!(J&&(!e.which||e.which<10)&&ql(t,e))){var i=String.fromCharCode(r??n);i!="\b"&&(Xs(t,e,i)||t.display.input.onKeyPress(e))}}}var js=400,$i=function(e,t,n){this.time=e,this.pos=t,this.button=n};$i.prototype.compare=function(e,t,n){return this.time+js>e&&M(t,this.pos)==0&&n==this.button};var Tn,Mn;function Zs(e,t){var n=+new Date;return Mn&&Mn.compare(n,e,t)?(Tn=Mn=null,"triple"):Tn&&Tn.compare(n,e,t)?(Mn=new $i(n,e,t),Tn=null,"double"):(Tn=new $i(n,e,t),Mn=null,"single")}function Zl(e){var t=this,n=t.display;if(!(xe(t,e)||n.activeTouch&&n.input.supportsTouch())){if(n.input.ensurePolled(),n.shift=e.shiftKey,Jt(n,e)){se||(n.scroller.draggable=!1,setTimeout(function(){return n.scroller.draggable=!0},100));return}if(!eo(t,e)){var r=vr(t,e),i=Pn(e),o=r?Zs(r,i):"single";window.focus(),i==1&&t.state.selectingText&&t.state.selectingText(e),!(r&&Js(t,i,r,o,e))&&(i==1?r?Vs(t,r,o,e):Lr(e)==n.scroller&&Be(e):i==2?(r&&ti(t.doc,r),setTimeout(function(){return n.input.focus()},20)):i==3&&(Ie?t.display.input.onContextMenu(e):zi(t)))}}}function Js(e,t,n,r,i){var o="Click";return r=="double"?o="Double"+o:r=="triple"&&(o="Triple"+o),o=(t==1?"Left":t==2?"Middle":"Right")+o,Ln(e,Rl(o,i),i,function(l){if(typeof l=="string"&&(l=Cn[l]),!l)return!1;var a=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),a=l(e,n)!=ye}finally{e.state.suppressEdits=!1}return a})}function Qs(e,t,n){var r=e.getOption("configureMouse"),i=r?r(e,t,n):{};if(i.unit==null){var o=Te?n.shiftKey&&n.metaKey:n.altKey;i.unit=o?"rectangle":t=="single"?"char":t=="double"?"word":"line"}return(i.extend==null||e.doc.extend)&&(i.extend=e.doc.extend||n.shiftKey),i.addNew==null&&(i.addNew=Y?n.metaKey:n.ctrlKey),i.moveOnDrag==null&&(i.moveOnDrag=!(Y?n.altKey:n.ctrlKey)),i}function Vs(e,t,n,r){P?setTimeout(wt(Jo,e),0):e.curOp.focus=ve();var i=Qs(e,n,r),o=e.doc.sel,l;e.options.dragDrop&&Wt&&!e.isReadOnly()&&n=="single"&&(l=o.contains(t))>-1&&(M((l=o.ranges[l]).from(),t)<0||t.xRel>0)&&(M(l.to(),t)>0||t.xRel<0)?$s(e,r,t,i):eu(e,r,t,i)}function $s(e,t,n,r){var i=e.display,o=!1,l=Ge(e,function(u){se&&(i.scroller.draggable=!1),e.state.draggingText=!1,e.state.delayingBlurEvent&&(e.hasFocus()?e.state.delayingBlurEvent=!1:zi(e)),te(i.wrapper.ownerDocument,"mouseup",l),te(i.wrapper.ownerDocument,"mousemove",a),te(i.scroller,"dragstart",s),te(i.scroller,"drop",l),o||(Be(u),r.addNew||ti(e.doc,n,null,null,r.extend),se&&!fe||P&&I==9?setTimeout(function(){i.wrapper.ownerDocument.body.focus({preventScroll:!0}),i.input.focus()},20):i.input.focus())}),a=function(u){o=o||Math.abs(t.clientX-u.clientX)+Math.abs(t.clientY-u.clientY)>=10},s=function(){return o=!0};se&&(i.scroller.draggable=!0),e.state.draggingText=l,l.copy=!r.moveOnDrag,G(i.wrapper.ownerDocument,"mouseup",l),G(i.wrapper.ownerDocument,"mousemove",a),G(i.scroller,"dragstart",s),G(i.scroller,"drop",l),e.state.delayingBlurEvent=!0,setTimeout(function(){return i.input.focus()},20),i.scroller.dragDrop&&i.scroller.dragDrop()}function Jl(e,t,n){if(n=="char")return new ue(t,t);if(n=="word")return e.findWordAt(t);if(n=="line")return new ue(v(t.line,0),Z(e.doc,v(t.line+1,0)));var r=n(e,t);return new ue(r.from,r.to)}function eu(e,t,n,r){P&&zi(e);var i=e.display,o=e.doc;Be(t);var l,a,s=o.sel,u=s.ranges;if(r.addNew&&!r.extend?(a=o.sel.contains(n),a>-1?l=u[a]:l=new ue(n,n)):(l=o.sel.primary(),a=o.sel.primIndex),r.unit=="rectangle")r.addNew||(l=new ue(n,n)),n=vr(e,t,!0,!0),a=-1;else{var d=Jl(e,n,r.unit);r.extend?l=Yi(l,d.anchor,d.head,r.extend):l=d}r.addNew?a==-1?(a=u.length,et(o,Nt(e,u.concat([l]),a),{scroll:!1,origin:"*mouse"})):u.length>1&&u[a].empty()&&r.unit=="char"&&!r.extend?(et(o,Nt(e,u.slice(0,a).concat(u.slice(a+1)),0),{scroll:!1,origin:"*mouse"}),s=o.sel):ji(o,a,l,H):(a=0,et(o,new yt([l],0),H),s=o.sel);var h=n;function m(A){if(M(h,A)!=0)if(h=A,r.unit=="rectangle"){for(var W=[],q=e.options.tabSize,R=be(B(o,n.line).text,n.ch,q),V=be(B(o,A.line).text,A.ch,q),ge=Math.min(R,V),Xe=Math.max(R,V),Ce=Math.min(n.line,A.line),pt=Math.min(e.lastLine(),Math.max(n.line,A.line));Ce<=pt;Ce++){var ut=B(o,Ce).text,Ee=_t(ut,ge,q);ge==Xe?W.push(new ue(v(Ce,Ee),v(Ce,Ee))):ut.length>Ee&&W.push(new ue(v(Ce,Ee),v(Ce,_t(ut,Xe,q))))}W.length||W.push(new ue(n,n)),et(o,Nt(e,s.ranges.slice(0,a).concat(W),a),{origin:"*mouse",scroll:!1}),e.scrollIntoView(A)}else{var ft=l,Ze=Jl(e,A,r.unit),Ke=ft.anchor,Fe;M(Ze.anchor,Ke)>0?(Fe=Ze.head,Ke=lt(ft.from(),Ze.anchor)):(Fe=Ze.anchor,Ke=Re(ft.to(),Ze.head));var Ne=s.ranges.slice(0);Ne[a]=tu(e,new ue(Z(o,Ke),Fe)),et(o,Nt(e,Ne,a),H)}}var g=i.wrapper.getBoundingClientRect(),b=0;function S(A){var W=++b,q=vr(e,A,!0,r.unit=="rectangle");if(q)if(M(q,h)!=0){e.curOp.focus=ve(),m(q);var R=Zn(i,o);(q.line>=R.to||q.line<R.from)&&setTimeout(Ge(e,function(){b==W&&S(A)}),150)}else{var V=A.clientY<g.top?-20:A.clientY>g.bottom?20:0;V&&setTimeout(Ge(e,function(){b==W&&(i.scroller.scrollTop+=V,S(A))}),50)}}function C(A){e.state.selectingText=!1,b=1/0,A&&(Be(A),i.input.focus()),te(i.wrapper.ownerDocument,"mousemove",N),te(i.wrapper.ownerDocument,"mouseup",O),o.history.lastSelOrigin=null}var N=Ge(e,function(A){A.buttons===0||!Pn(A)?C(A):S(A)}),O=Ge(e,C);e.state.selectingText=O,G(i.wrapper.ownerDocument,"mousemove",N),G(i.wrapper.ownerDocument,"mouseup",O)}function tu(e,t){var n=t.anchor,r=t.head,i=B(e.doc,n.line);if(M(n,r)==0&&n.sticky==r.sticky)return t;var o=nt(i);if(!o)return t;var l=Lt(o,n.ch,n.sticky),a=o[l];if(a.from!=n.ch&&a.to!=n.ch)return t;var s=l+(a.from==n.ch==(a.level!=1)?0:1);if(s==0||s==o.length)return t;var u;if(r.line!=n.line)u=(r.line-n.line)*(e.doc.direction=="ltr"?1:-1)>0;else{var d=Lt(o,r.ch,r.sticky),h=d-l||(r.ch-n.ch)*(a.level==1?-1:1);d==s-1||d==s?u=h<0:u=h>0}var m=o[s+(u?-1:0)],g=u==(m.level==1),b=g?m.from:m.to,S=g?"after":"before";return n.ch==b&&n.sticky==S?t:new ue(new v(n.line,b,S),r)}function Ql(e,t,n,r){var i,o;if(t.touches)i=t.touches[0].clientX,o=t.touches[0].clientY;else try{i=t.clientX,o=t.clientY}catch{return!1}if(i>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;r&&Be(t);var l=e.display,a=l.lineDiv.getBoundingClientRect();if(o>a.bottom||!He(e,n))return dr(t);o-=a.top-l.viewOffset;for(var s=0;s<e.display.gutterSpecs.length;++s){var u=l.gutters.childNodes[s];if(u&&u.getBoundingClientRect().right>=i){var d=f(e.doc,o),h=e.display.gutterSpecs[s];return Me(e,n,e,d,h.className,t),dr(t)}}}function eo(e,t){return Ql(e,t,"gutterClick",!0)}function Vl(e,t){Jt(e.display,t)||ru(e,t)||xe(e,t,"contextmenu")||Ie||e.display.input.onContextMenu(t)}function ru(e,t){return He(e,"gutterContextMenu")?Ql(e,t,"gutterContextMenu",!1):!1}function $l(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),an(e)}var qr={toString:function(){return"CodeMirror.Init"}},ea={},si={};function nu(e){var t=e.optionHandlers;function n(r,i,o,l){e.defaults[r]=i,o&&(t[r]=l?function(a,s,u){u!=qr&&o(a,s,u)}:o)}e.defineOption=n,e.Init=qr,n("value","",function(r,i){return r.setValue(i)},!0),n("mode",null,function(r,i){r.doc.modeOption=i,Gi(r)},!0),n("indentUnit",2,Gi,!0),n("indentWithTabs",!1),n("smartIndent",!0),n("tabSize",4,function(r){vn(r),an(r),at(r)},!0),n("lineSeparator",null,function(r,i){if(r.doc.lineSep=i,!!i){var o=[],l=r.doc.first;r.doc.iter(function(s){for(var u=0;;){var d=s.text.indexOf(i,u);if(d==-1)break;u=d+i.length,o.push(v(l,d))}l++});for(var a=o.length-1;a>=0;a--)Kr(r.doc,i,o[a],v(o[a].line,o[a].ch+i.length))}}),n("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b-\u200c\u200e\u200f\u2028\u2029\ufeff\ufff9-\ufffc]/g,function(r,i,o){r.state.specialChars=new RegExp(i.source+(i.test("	")?"":"|	"),"g"),o!=qr&&r.refresh()}),n("specialCharPlaceholder",Wa,function(r){return r.refresh()},!0),n("electricChars",!0),n("inputStyle",Q?"contenteditable":"textarea",function(){throw new Error("inputStyle can not (yet) be changed in a running editor")},!0),n("spellcheck",!1,function(r,i){return r.getInputField().spellcheck=i},!0),n("autocorrect",!1,function(r,i){return r.getInputField().autocorrect=i},!0),n("autocapitalize",!1,function(r,i){return r.getInputField().autocapitalize=i},!0),n("rtlMoveVisually",!bt),n("wholeLineUpdateBefore",!0),n("theme","default",function(r){$l(r),pn(r)},!0),n("keyMap","default",function(r,i,o){var l=li(i),a=o!=qr&&li(o);a&&a.detach&&a.detach(r,l),l.attach&&l.attach(r,a||null)}),n("extraKeys",null),n("configureMouse",null),n("lineWrapping",!1,ou,!0),n("gutters",[],function(r,i){r.display.gutterSpecs=_i(i,r.options.lineNumbers),pn(r)},!0),n("fixedGutter",!0,function(r,i){r.display.gutters.style.left=i?Oi(r.display)+"px":"0",r.refresh()},!0),n("coverGutterNextToScrollbar",!1,function(r){return Fr(r)},!0),n("scrollbarStyle","native",function(r){rl(r),Fr(r),r.display.scrollbars.setScrollTop(r.doc.scrollTop),r.display.scrollbars.setScrollLeft(r.doc.scrollLeft)},!0),n("lineNumbers",!1,function(r,i){r.display.gutterSpecs=_i(r.options.gutters,i),pn(r)},!0),n("firstLineNumber",1,pn,!0),n("lineNumberFormatter",function(r){return r},pn,!0),n("showCursorWhenSelecting",!1,sn,!0),n("resetSelectionOnContextMenu",!0),n("lineWiseCopyCut",!0),n("pasteLinesPerSelection",!0),n("selectionsMayTouch",!1),n("readOnly",!1,function(r,i){i=="nocursor"&&(Hr(r),r.display.input.blur()),r.display.input.readOnlyChanged(i)}),n("screenReaderLabel",null,function(r,i){i=i===""?null:i,r.display.input.screenReaderLabelChanged(i)}),n("disableInput",!1,function(r,i){i||r.display.input.reset()},!0),n("dragDrop",!0,iu),n("allowDropFileTypes",null),n("cursorBlinkRate",530),n("cursorScrollMargin",0),n("cursorHeight",1,sn,!0),n("singleCursorHeightPerLine",!0,sn,!0),n("workTime",100),n("workDelay",100),n("flattenSpans",!0,vn,!0),n("addModeClass",!1,vn,!0),n("pollInterval",100),n("undoDepth",200,function(r,i){return r.doc.history.undoDepth=i}),n("historyEventDelay",1250),n("viewportMargin",10,function(r){return r.refresh()},!0),n("maxHighlightLength",1e4,vn,!0),n("moveInputWithCursor",!0,function(r,i){i||r.display.input.resetPosition()}),n("tabindex",null,function(r,i){return r.display.input.getField().tabIndex=i||""}),n("autofocus",null),n("direction","ltr",function(r,i){return r.doc.setDirection(i)},!0),n("phrases",null)}function iu(e,t,n){var r=n&&n!=qr;if(!t!=!r){var i=e.display.dragFunctions,o=t?G:te;o(e.display.scroller,"dragstart",i.start),o(e.display.scroller,"dragenter",i.enter),o(e.display.scroller,"dragover",i.over),o(e.display.scroller,"dragleave",i.leave),o(e.display.scroller,"drop",i.drop)}}function ou(e){e.options.lineWrapping?(De(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(j(e.display.wrapper,"CodeMirror-wrap"),xi(e)),Wi(e),at(e),an(e),setTimeout(function(){return Fr(e)},100)}function ke(e,t){var n=this;if(!(this instanceof ke))return new ke(e,t);this.options=t=t?vt(t):{},vt(ea,t,!1);var r=t.value;typeof r=="string"?r=new st(r,t.mode,null,t.lineSeparator,t.direction):t.mode&&(r.modeOption=t.mode),this.doc=r;var i=new ke.inputStyles[t.inputStyle](this),o=this.display=new ys(e,r,i,t);o.wrapper.CodeMirror=this,$l(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),rl(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new K,keySeq:null,specialChars:null},t.autofocus&&!Q&&o.input.focus(),P&&I<11&&setTimeout(function(){return n.display.input.reset(!0)},20),lu(this),Fs(),br(this),this.curOp.forceUpdate=!0,dl(this,r),t.autofocus&&!Q||this.hasFocus()?setTimeout(function(){n.hasFocus()&&!n.state.focused&&Hi(n)},20):Hr(this);for(var l in si)si.hasOwnProperty(l)&&si[l](this,t[l],qr);ol(this),t.finishInit&&t.finishInit(this);for(var a=0;a<to.length;++a)to[a](this);xr(this),se&&t.lineWrapping&&getComputedStyle(o.lineDiv).textRendering=="optimizelegibility"&&(o.lineDiv.style.textRendering="auto")}ke.defaults=ea,ke.optionHandlers=si;function lu(e){var t=e.display;G(t.scroller,"mousedown",Ge(e,Zl)),P&&I<11?G(t.scroller,"dblclick",Ge(e,function(s){if(!xe(e,s)){var u=vr(e,s);if(!(!u||eo(e,s)||Jt(e.display,s))){Be(s);var d=e.findWordAt(u);ti(e.doc,d.anchor,d.head)}}})):G(t.scroller,"dblclick",function(s){return xe(e,s)||Be(s)}),G(t.scroller,"contextmenu",function(s){return Vl(e,s)}),G(t.input.getField(),"contextmenu",function(s){t.scroller.contains(s.target)||Vl(e,s)});var n,r={end:0};function i(){t.activeTouch&&(n=setTimeout(function(){return t.activeTouch=null},1e3),r=t.activeTouch,r.end=+new Date)}function o(s){if(s.touches.length!=1)return!1;var u=s.touches[0];return u.radiusX<=1&&u.radiusY<=1}function l(s,u){if(u.left==null)return!0;var d=u.left-s.left,h=u.top-s.top;return d*d+h*h>20*20}G(t.scroller,"touchstart",function(s){if(!xe(e,s)&&!o(s)&&!eo(e,s)){t.input.ensurePolled(),clearTimeout(n);var u=+new Date;t.activeTouch={start:u,moved:!1,prev:u-r.end<=300?r:null},s.touches.length==1&&(t.activeTouch.left=s.touches[0].pageX,t.activeTouch.top=s.touches[0].pageY)}}),G(t.scroller,"touchmove",function(){t.activeTouch&&(t.activeTouch.moved=!0)}),G(t.scroller,"touchend",function(s){var u=t.activeTouch;if(u&&!Jt(t,s)&&u.left!=null&&!u.moved&&new Date-u.start<300){var d=e.coordsChar(t.activeTouch,"page"),h;!u.prev||l(u,u.prev)?h=new ue(d,d):!u.prev.prev||l(u,u.prev.prev)?h=e.findWordAt(d):h=new ue(v(d.line,0),Z(e.doc,v(d.line+1,0))),e.setSelection(h.anchor,h.head),e.focus(),Be(s)}i()}),G(t.scroller,"touchcancel",i),G(t.scroller,"scroll",function(){t.scroller.clientHeight&&(fn(e,t.scroller.scrollTop),mr(e,t.scroller.scrollLeft,!0),Me(e,"scroll",e))}),G(t.scroller,"mousewheel",function(s){return sl(e,s)}),G(t.scroller,"DOMMouseScroll",function(s){return sl(e,s)}),G(t.wrapper,"scroll",function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0}),t.dragFunctions={enter:function(s){xe(e,s)||$e(s)},over:function(s){xe(e,s)||(Es(e,s),$e(s))},start:function(s){return Hs(e,s)},drop:Ge(e,zs),leave:function(s){xe(e,s)||El(e)}};var a=t.input.getField();G(a,"keyup",function(s){return Yl.call(e,s)}),G(a,"keydown",Ge(e,Xl)),G(a,"keypress",Ge(e,jl)),G(a,"focus",function(s){return Hi(e,s)}),G(a,"blur",function(s){return Hr(e,s)})}var to=[];ke.defineInitHook=function(e){return to.push(e)};function Nn(e,t,n,r){var i=e.doc,o;n==null&&(n="add"),n=="smart"&&(i.mode.indent?o=tn(e,t).state:n="prev");var l=e.options.tabSize,a=B(i,t),s=be(a.text,null,l);a.stateAfter&&(a.stateAfter=null);var u=a.text.match(/^\s*/)[0],d;if(!r&&!/\S/.test(a.text))d=0,n="not";else if(n=="smart"&&(d=i.mode.indent(o,a.text.slice(u.length),a.text),d==ye||d>150)){if(!r)return;n="prev"}n=="prev"?t>i.first?d=be(B(i,t-1).text,null,l):d=0:n=="add"?d=s+e.options.indentUnit:n=="subtract"?d=s-e.options.indentUnit:typeof n=="number"&&(d=s+n),d=Math.max(0,d);var h="",m=0;if(e.options.indentWithTabs)for(var g=Math.floor(d/l);g;--g)m+=l,h+="	";if(m<d&&(h+=Ct(d-m)),h!=u)return Kr(i,h,v(t,0),v(t,u.length),"+input"),a.stateAfter=null,!0;for(var b=0;b<i.sel.ranges.length;b++){var S=i.sel.ranges[b];if(S.head.line==t&&S.head.ch<u.length){var C=v(t,u.length);ji(i,b,new ue(C,C));break}}}var At=null;function ui(e){At=e}function ro(e,t,n,r,i){var o=e.doc;e.display.shift=!1,r||(r=o.sel);var l=+new Date-200,a=i=="paste"||e.state.pasteIncoming>l,s=Pt(t),u=null;if(a&&r.ranges.length>1)if(At&&At.text.join(`
`)==t){if(r.ranges.length%At.text.length==0){u=[];for(var d=0;d<At.text.length;d++)u.push(o.splitLines(At.text[d]))}}else s.length==r.ranges.length&&e.options.pasteLinesPerSelection&&(u=ce(s,function(N){return[N]}));for(var h=e.curOp.updateInput,m=r.ranges.length-1;m>=0;m--){var g=r.ranges[m],b=g.from(),S=g.to();g.empty()&&(n&&n>0?b=v(b.line,b.ch-n):e.state.overwrite&&!a?S=v(S.line,Math.min(B(o,S.line).text.length,S.ch+ee(s).length)):a&&At&&At.lineWise&&At.text.join(`
`)==s.join(`
`)&&(b=S=v(b.line,0)));var C={from:b,to:S,text:u?u[m%u.length]:s,origin:i||(a?"paste":e.state.cutIncoming>l?"cut":"+input")};Rr(e.doc,C),je(e,"inputRead",e,C)}t&&!a&&ra(e,t),Er(e),e.curOp.updateInput<2&&(e.curOp.updateInput=h),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function ta(e,t){var n=e.clipboardData&&e.clipboardData.getData("Text");if(n)return e.preventDefault(),!t.isReadOnly()&&!t.options.disableInput&&ht(t,function(){return ro(t,n,0,null,"paste")}),!0}function ra(e,t){if(!(!e.options.electricChars||!e.options.smartIndent))for(var n=e.doc.sel,r=n.ranges.length-1;r>=0;r--){var i=n.ranges[r];if(!(i.head.ch>100||r&&n.ranges[r-1].head.line==i.head.line)){var o=e.getModeAt(i.head),l=!1;if(o.electricChars){for(var a=0;a<o.electricChars.length;a++)if(t.indexOf(o.electricChars.charAt(a))>-1){l=Nn(e,i.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(B(e.doc,i.head.line).text.slice(0,i.head.ch))&&(l=Nn(e,i.head.line,"smart"));l&&je(e,"electricInput",e,i.head.line)}}}function na(e){for(var t=[],n=[],r=0;r<e.doc.sel.ranges.length;r++){var i=e.doc.sel.ranges[r].head.line,o={anchor:v(i,0),head:v(i+1,0)};n.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:n}}function ia(e,t,n,r){e.setAttribute("autocorrect",n?"":"off"),e.setAttribute("autocapitalize",r?"":"off"),e.setAttribute("spellcheck",!!t)}function oa(){var e=c("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; outline: none"),t=c("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return se?e.style.width="1000px":e.setAttribute("wrap","off"),$&&(e.style.border="1px solid black"),ia(e),t}function au(e){var t=e.optionHandlers,n=e.helpers={};e.prototype={constructor:e,focus:function(){window.focus(),this.display.input.focus()},setOption:function(r,i){var o=this.options,l=o[r];o[r]==i&&r!="mode"||(o[r]=i,t.hasOwnProperty(r)&&Ge(this,t[r])(this,i,l),Me(this,"optionChange",this,r))},getOption:function(r){return this.options[r]},getDoc:function(){return this.doc},addKeyMap:function(r,i){this.state.keyMaps[i?"push":"unshift"](li(r))},removeKeyMap:function(r){for(var i=this.state.keyMaps,o=0;o<i.length;++o)if(i[o]==r||i[o].name==r)return i.splice(o,1),!0},addOverlay:it(function(r,i){var o=r.token?r:e.getMode(this.options,r);if(o.startState)throw new Error("Overlays may not be stateful.");Ve(this.state.overlays,{mode:o,modeSpec:r,opaque:i&&i.opaque,priority:i&&i.priority||0},function(l){return l.priority}),this.state.modeGen++,at(this)}),removeOverlay:it(function(r){for(var i=this.state.overlays,o=0;o<i.length;++o){var l=i[o].modeSpec;if(l==r||typeof r=="string"&&l.name==r){i.splice(o,1),this.state.modeGen++,at(this);return}}}),indentLine:it(function(r,i,o){typeof i!="string"&&typeof i!="number"&&(i==null?i=this.options.smartIndent?"smart":"prev":i=i?"add":"subtract"),p(this.doc,r)&&Nn(this,r,i,o)}),indentSelection:it(function(r){for(var i=this.doc.sel.ranges,o=-1,l=0;l<i.length;l++){var a=i[l];if(a.empty())a.head.line>o&&(Nn(this,a.head.line,r,!0),o=a.head.line,l==this.doc.sel.primIndex&&Er(this));else{var s=a.from(),u=a.to(),d=Math.max(o,s.line);o=Math.min(this.lastLine(),u.line-(u.ch?0:1))+1;for(var h=d;h<o;++h)Nn(this,h,r);var m=this.doc.sel.ranges;s.ch==0&&i.length==m.length&&m[l].from().ch>0&&ji(this.doc,l,new ue(s,m[l].to()),Qe)}}}),getTokenAt:function(r,i){return ho(this,r,i)},getLineTokens:function(r,i){return ho(this,v(r),i,!0)},getTokenTypeAt:function(r){r=Z(this.doc,r);var i=uo(this,B(this.doc,r.line)),o=0,l=(i.length-1)/2,a=r.ch,s;if(a==0)s=i[2];else for(;;){var u=o+l>>1;if((u?i[u*2-1]:0)>=a)l=u;else if(i[u*2+1]<a)o=u+1;else{s=i[u*2+2];break}}var d=s?s.indexOf("overlay "):-1;return d<0?s:d==0?null:s.slice(0,d-1)},getModeAt:function(r){var i=this.doc.mode;return i.innerMode?e.innerMode(i,this.getTokenAt(r).state).mode:i},getHelper:function(r,i){return this.getHelpers(r,i)[0]},getHelpers:function(r,i){var o=[];if(!n.hasOwnProperty(i))return o;var l=n[i],a=this.getModeAt(r);if(typeof a[i]=="string")l[a[i]]&&o.push(l[a[i]]);else if(a[i])for(var s=0;s<a[i].length;s++){var u=l[a[i][s]];u&&o.push(u)}else a.helperType&&l[a.helperType]?o.push(l[a.helperType]):l[a.name]&&o.push(l[a.name]);for(var d=0;d<l._global.length;d++){var h=l._global[d];h.pred(a,this)&&E(o,h.val)==-1&&o.push(h.val)}return o},getStateAfter:function(r,i){var o=this.doc;return r=En(o,r??o.first+o.size-1),tn(this,r+1,i).state},cursorCoords:function(r,i){var o,l=this.doc.sel.primary();return r==null?o=l.head:typeof r=="object"?o=Z(this.doc,r):o=r?l.from():l.to(),Mt(this,o,i||"page")},charCoords:function(r,i){return Ti(this,Z(this.doc,r),i||"page")},coordsChar:function(r,i){return r=_o(this,r,i||"page"),Ni(this,r.left,r.top)},lineAtHeight:function(r,i){return r=_o(this,{top:r,left:0},i||"page").top,f(this.doc,r+this.display.viewOffset)},heightAtLine:function(r,i,o){var l=!1,a;if(typeof r=="number"){var s=this.doc.first+this.doc.size-1;r<this.doc.first?r=this.doc.first:r>s&&(r=s,l=!0),a=B(this.doc,r)}else a=r;return qn(this,a,{top:0,left:0},i||"page",o||l).top+(l?this.doc.height-Zt(a):0)},defaultTextHeight:function(){return Pr(this.display)},defaultCharWidth:function(){return zr(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(r,i,o,l,a){var s=this.display;r=Mt(this,Z(this.doc,r));var u=r.bottom,d=r.left;if(i.style.position="absolute",i.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(i),s.sizer.appendChild(i),l=="over")u=r.top;else if(l=="above"||l=="near"){var h=Math.max(s.wrapper.clientHeight,this.doc.height),m=Math.max(s.sizer.clientWidth,s.lineSpace.clientWidth);(l=="above"||r.bottom+i.offsetHeight>h)&&r.top>i.offsetHeight?u=r.top-i.offsetHeight:r.bottom+i.offsetHeight<=h&&(u=r.bottom),d+i.offsetWidth>m&&(d=m-i.offsetWidth)}i.style.top=u+"px",i.style.left=i.style.right="",a=="right"?(d=s.sizer.clientWidth-i.offsetWidth,i.style.right="0px"):(a=="left"?d=0:a=="middle"&&(d=(s.sizer.clientWidth-i.offsetWidth)/2),i.style.left=d+"px"),o&&is(this,{left:d,top:u,right:d+i.offsetWidth,bottom:u+i.offsetHeight})},triggerOnKeyDown:it(Xl),triggerOnKeyPress:it(jl),triggerOnKeyUp:Yl,triggerOnMouseDown:it(Zl),execCommand:function(r){if(Cn.hasOwnProperty(r))return Cn[r].call(null,this)},triggerElectric:it(function(r){ra(this,r)}),findPosH:function(r,i,o,l){var a=1;i<0&&(a=-1,i=-i);for(var s=Z(this.doc,r),u=0;u<i&&(s=no(this.doc,s,a,o,l),!s.hitSide);++u);return s},moveH:it(function(r,i){var o=this;this.extendSelectionsBy(function(l){return o.display.shift||o.doc.extend||l.empty()?no(o.doc,l.head,r,i,o.options.rtlMoveVisually):r<0?l.from():l.to()},ie)}),deleteH:it(function(r,i){var o=this.doc.sel,l=this.doc;o.somethingSelected()?l.replaceSelection("",null,"+delete"):Gr(this,function(a){var s=no(l,a.head,r,i,!1);return r<0?{from:s,to:a.head}:{from:a.head,to:s}})}),findPosV:function(r,i,o,l){var a=1,s=l;i<0&&(a=-1,i=-i);for(var u=Z(this.doc,r),d=0;d<i;++d){var h=Mt(this,u,"div");if(s==null?s=h.left:h.left=s,u=la(this,h,a,o),u.hitSide)break}return u},moveV:it(function(r,i){var o=this,l=this.doc,a=[],s=!this.display.shift&&!l.extend&&l.sel.somethingSelected();if(l.extendSelectionsBy(function(d){if(s)return r<0?d.from():d.to();var h=Mt(o,d.head,"div");d.goalColumn!=null&&(h.left=d.goalColumn),a.push(h.left);var m=la(o,h,r,i);return i=="page"&&d==l.sel.primary()&&Fi(o,Ti(o,m,"div").top-h.top),m},ie),a.length)for(var u=0;u<l.sel.ranges.length;u++)l.sel.ranges[u].goalColumn=a[u]}),findWordAt:function(r){var i=this.doc,o=B(i,r.line).text,l=r.ch,a=r.ch;if(o){var s=this.getHelper(r,"wordChars");(r.sticky=="before"||a==o.length)&&l?--l:++a;for(var u=o.charAt(l),d=y(u,s)?function(h){return y(h,s)}:/\s/.test(u)?function(h){return/\s/.test(h)}:function(h){return!/\s/.test(h)&&!y(h)};l>0&&d(o.charAt(l-1));)--l;for(;a<o.length&&d(o.charAt(a));)++a}return new ue(v(r.line,l),v(r.line,a))},toggleOverwrite:function(r){r!=null&&r==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?De(this.display.cursorDiv,"CodeMirror-overwrite"):j(this.display.cursorDiv,"CodeMirror-overwrite"),Me(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==ve()},isReadOnly:function(){return!!(this.options.readOnly||this.doc.cantEdit)},scrollTo:it(function(r,i){un(this,r,i)}),getScrollInfo:function(){var r=this.display.scroller;return{left:r.scrollLeft,top:r.scrollTop,height:r.scrollHeight-It(this)-this.display.barHeight,width:r.scrollWidth-It(this)-this.display.barWidth,clientHeight:Si(this),clientWidth:hr(this)}},scrollIntoView:it(function(r,i){r==null?(r={from:this.doc.sel.primary().head,to:null},i==null&&(i=this.options.cursorScrollMargin)):typeof r=="number"?r={from:v(r,0),to:null}:r.from==null&&(r={from:r,to:null}),r.to||(r.to=r.from),r.margin=i||0,r.from.line!=null?os(this,r):Vo(this,r.from,r.to,r.margin)}),setSize:it(function(r,i){var o=this,l=function(s){return typeof s=="number"||/^\d+$/.test(String(s))?s+"px":s};r!=null&&(this.display.wrapper.style.width=l(r)),i!=null&&(this.display.wrapper.style.height=l(i)),this.options.lineWrapping&&Bo(this);var a=this.display.viewFrom;this.doc.iter(a,this.display.viewTo,function(s){if(s.widgets){for(var u=0;u<s.widgets.length;u++)if(s.widgets[u].noHScroll){rr(o,a,"widget");break}}++a}),this.curOp.forceUpdate=!0,Me(this,"refresh",this)}),operation:function(r){return ht(this,r)},startOperation:function(){return br(this)},endOperation:function(){return xr(this)},refresh:it(function(){var r=this.display.cachedTextHeight;at(this),this.curOp.forceUpdate=!0,an(this),un(this,this.doc.scrollLeft,this.doc.scrollTop),Ri(this.display),(r==null||Math.abs(r-Pr(this.display))>.5||this.options.lineWrapping)&&Wi(this),Me(this,"refresh",this)}),swapDoc:it(function(r){var i=this.doc;return i.cm=null,this.state.selectingText&&this.state.selectingText(),dl(this,r),an(this),this.display.input.reset(),un(this,r.scrollLeft,r.scrollTop),this.curOp.forceScroll=!0,je(this,"swapDoc",this,i),i}),phrase:function(r){var i=this.options.phrases;return i&&Object.prototype.hasOwnProperty.call(i,r)?i[r]:r},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},$t(e),e.registerHelper=function(r,i,o){n.hasOwnProperty(r)||(n[r]=e[r]={_global:[]}),n[r][i]=o},e.registerGlobalHelper=function(r,i,o,l){e.registerHelper(r,i,l),n[r]._global.push({pred:o,val:l})}}function no(e,t,n,r,i){var o=t,l=n,a=B(e,t.line),s=i&&e.direction=="rtl"?-n:n;function u(){var O=t.line+s;return O<e.first||O>=e.first+e.size?!1:(t=new v(O,t.ch,t.sticky),a=B(e,O))}function d(O){var A;if(r=="codepoint"){var W=a.text.charCodeAt(t.ch+(r>0?0:-1));isNaN(W)?A=null:A=new v(t.line,Math.max(0,Math.min(a.text.length,t.ch+n*(W>=55296&&W<56320?2:1))),-n)}else i?A=_s(e.cm,a,t,n):A=Ji(a,t,n);if(A==null)if(!O&&u())t=Qi(i,e.cm,a,t.line,s);else return!1;else t=A;return!0}if(r=="char"||r=="codepoint")d();else if(r=="column")d(!0);else if(r=="word"||r=="group")for(var h=null,m=r=="group",g=e.cm&&e.cm.getHelper(t,"wordChars"),b=!0;!(n<0&&!d(!b));b=!1){var S=a.text.charAt(t.ch)||`
`,C=y(S,g)?"w":m&&S==`
`?"n":!m||/\s/.test(S)?null:"p";if(m&&!b&&!C&&(C="s"),h&&h!=C){n<0&&(n=1,d(),t.sticky="after");break}if(C&&(h=C),n>0&&!d(!b))break}var N=ni(e,t,o,l,!0);return le(o,N)&&(N.hitSide=!0),N}function la(e,t,n,r){var i=e.doc,o=t.left,l;if(r=="page"){var a=Math.min(e.display.wrapper.clientHeight,window.innerHeight||document.documentElement.clientHeight),s=Math.max(a-.5*Pr(e.display),3);l=(n>0?t.bottom:t.top)+n*s}else r=="line"&&(l=n>0?t.bottom+3:t.top-3);for(var u;u=Ni(e,o,l),!!u.outside;){if(n<0?l<=0:l>=i.height){u.hitSide=!0;break}l+=n*5}return u}var he=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new K,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};he.prototype.init=function(e){var t=this,n=this,r=n.cm,i=n.div=e.lineDiv;ia(i,r.options.spellcheck,r.options.autocorrect,r.options.autocapitalize);function o(a){for(var s=a.target;s;s=s.parentNode){if(s==i)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(s.className))break}return!1}G(i,"paste",function(a){!o(a)||xe(r,a)||ta(a,r)||I<=11&&setTimeout(Ge(r,function(){return t.updateFromDOM()}),20)}),G(i,"compositionstart",function(a){t.composing={data:a.data,done:!1}}),G(i,"compositionupdate",function(a){t.composing||(t.composing={data:a.data,done:!1})}),G(i,"compositionend",function(a){t.composing&&(a.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)}),G(i,"touchstart",function(){return n.forceCompositionEnd()}),G(i,"input",function(){t.composing||t.readFromDOMSoon()});function l(a){if(!(!o(a)||xe(r,a))){if(r.somethingSelected())ui({lineWise:!1,text:r.getSelections()}),a.type=="cut"&&r.replaceSelection("",null,"cut");else if(r.options.lineWiseCopyCut){var s=na(r);ui({lineWise:!0,text:s.text}),a.type=="cut"&&r.operation(function(){r.setSelections(s.ranges,0,Qe),r.replaceSelection("",null,"cut")})}else return;if(a.clipboardData){a.clipboardData.clearData();var u=At.text.join(`
`);if(a.clipboardData.setData("Text",u),a.clipboardData.getData("Text")==u){a.preventDefault();return}}var d=oa(),h=d.firstChild;r.display.lineSpace.insertBefore(d,r.display.lineSpace.firstChild),h.value=At.text.join(`
`);var m=document.activeElement;Kt(h),setTimeout(function(){r.display.lineSpace.removeChild(d),m.focus(),m==i&&n.showPrimarySelection()},50)}}G(i,"copy",l),G(i,"cut",l)},he.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},he.prototype.prepareSelection=function(){var e=jo(this.cm,!1);return e.focus=document.activeElement==this.div,e},he.prototype.showSelection=function(e,t){!e||!this.cm.display.view.length||((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},he.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},he.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,n=t.doc.sel.primary(),r=n.from(),i=n.to();if(t.display.viewTo==t.display.viewFrom||r.line>=t.display.viewTo||i.line<t.display.viewFrom){e.removeAllRanges();return}var o=fi(t,e.anchorNode,e.anchorOffset),l=fi(t,e.focusNode,e.focusOffset);if(!(o&&!o.bad&&l&&!l.bad&&M(lt(o,l),r)==0&&M(Re(o,l),i)==0)){var a=t.display.view,s=r.line>=t.display.viewFrom&&aa(t,r)||{node:a[0].measure.map[2],offset:0},u=i.line<t.display.viewTo&&aa(t,i);if(!u){var d=a[a.length-1].measure,h=d.maps?d.maps[d.maps.length-1]:d.map;u={node:h[h.length-1],offset:h[h.length-2]-h[h.length-3]}}if(!s||!u){e.removeAllRanges();return}var m=e.rangeCount&&e.getRangeAt(0),g;try{g=L(s.node,s.offset,u.offset,u.node)}catch{}g&&(!pe&&t.state.focused?(e.collapse(s.node,s.offset),g.collapsed||(e.removeAllRanges(),e.addRange(g))):(e.removeAllRanges(),e.addRange(g)),m&&e.anchorNode==null?e.addRange(m):pe&&this.startGracePeriod()),this.rememberSelection()}},he.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout(function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation(function(){return e.cm.curOp.selectionChanged=!0})},20)},he.prototype.showMultipleSelections=function(e){z(this.cm.display.cursorDiv,e.cursors),z(this.cm.display.selectionDiv,e.selection)},he.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},he.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return U(this.div,t)},he.prototype.focus=function(){this.cm.options.readOnly!="nocursor"&&((!this.selectionInEditor()||document.activeElement!=this.div)&&this.showSelection(this.prepareSelection(),!0),this.div.focus())},he.prototype.blur=function(){this.div.blur()},he.prototype.getField=function(){return this.div},he.prototype.supportsTouch=function(){return!0},he.prototype.receivedFocus=function(){var e=this;this.selectionInEditor()?this.pollSelection():ht(this.cm,function(){return e.cm.curOp.selectionChanged=!0});function t(){e.cm.state.focused&&(e.pollSelection(),e.polling.set(e.cm.options.pollInterval,t))}this.polling.set(this.cm.options.pollInterval,t)},he.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},he.prototype.pollSelection=function(){if(!(this.readDOMTimeout!=null||this.gracePeriod||!this.selectionChanged())){var e=this.getSelection(),t=this.cm;if(_e&&X&&this.cm.display.gutterSpecs.length&&su(e.anchorNode)){this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),this.focus();return}if(!this.composing){this.rememberSelection();var n=fi(t,e.anchorNode,e.anchorOffset),r=fi(t,e.focusNode,e.focusOffset);n&&r&&ht(t,function(){et(t.doc,ir(n,r),Qe),(n.bad||r.bad)&&(t.curOp.selectionChanged=!0)})}}},he.prototype.pollContent=function(){this.readDOMTimeout!=null&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e=this.cm,t=e.display,n=e.doc.sel.primary(),r=n.from(),i=n.to();if(r.ch==0&&r.line>e.firstLine()&&(r=v(r.line-1,B(e.doc,r.line-1).length)),i.ch==B(e.doc,i.line).text.length&&i.line<e.lastLine()&&(i=v(i.line+1,0)),r.line<t.viewFrom||i.line>t.viewTo-1)return!1;var o,l,a;r.line==t.viewFrom||(o=gr(e,r.line))==0?(l=ae(t.view[0].line),a=t.view[0].node):(l=ae(t.view[o].line),a=t.view[o-1].node.nextSibling);var s=gr(e,i.line),u,d;if(s==t.view.length-1?(u=t.viewTo-1,d=t.lineDiv.lastChild):(u=ae(t.view[s+1].line)-1,d=t.view[s+1].node.previousSibling),!a)return!1;for(var h=e.doc.splitLines(uu(e,a,d,l,u)),m=Ht(e.doc,v(l,0),v(u,B(e.doc,u).text.length));h.length>1&&m.length>1;)if(ee(h)==ee(m))h.pop(),m.pop(),u--;else if(h[0]==m[0])h.shift(),m.shift(),l++;else break;for(var g=0,b=0,S=h[0],C=m[0],N=Math.min(S.length,C.length);g<N&&S.charCodeAt(g)==C.charCodeAt(g);)++g;for(var O=ee(h),A=ee(m),W=Math.min(O.length-(h.length==1?g:0),A.length-(m.length==1?g:0));b<W&&O.charCodeAt(O.length-b-1)==A.charCodeAt(A.length-b-1);)++b;if(h.length==1&&m.length==1&&l==r.line)for(;g&&g>r.ch&&O.charCodeAt(O.length-b-1)==A.charCodeAt(A.length-b-1);)g--,b++;h[h.length-1]=O.slice(0,O.length-b).replace(/^\u200b+/,""),h[0]=h[0].slice(g).replace(/\u200b+$/,"");var q=v(l,g),R=v(u,m.length?ee(m).length-b:0);if(h.length>1||h[0]||M(q,R))return Kr(e.doc,h,q,R,"+input"),!0},he.prototype.ensurePolled=function(){this.forceCompositionEnd()},he.prototype.reset=function(){this.forceCompositionEnd()},he.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},he.prototype.readFromDOMSoon=function(){var e=this;this.readDOMTimeout==null&&(this.readDOMTimeout=setTimeout(function(){if(e.readDOMTimeout=null,e.composing)if(e.composing.done)e.composing=null;else return;e.updateFromDOM()},80))},he.prototype.updateFromDOM=function(){var e=this;(this.cm.isReadOnly()||!this.pollContent())&&ht(this.cm,function(){return at(e.cm)})},he.prototype.setUneditable=function(e){e.contentEditable="false"},he.prototype.onKeyPress=function(e){e.charCode==0||this.composing||(e.preventDefault(),this.cm.isReadOnly()||Ge(this.cm,ro)(this.cm,String.fromCharCode(e.charCode==null?e.keyCode:e.charCode),0))},he.prototype.readOnlyChanged=function(e){this.div.contentEditable=String(e!="nocursor")},he.prototype.onContextMenu=function(){},he.prototype.resetPosition=function(){},he.prototype.needsContentAttribute=!0;function aa(e,t){var n=Ci(e,t.line);if(!n||n.hidden)return null;var r=B(e.doc,t.line),i=zo(n,r,t.line),o=nt(r,e.doc.direction),l="left";if(o){var a=Lt(o,t.ch);l=a%2?"right":"left"}var s=Fo(i.map,t.ch,l);return s.offset=s.collapse=="right"?s.end:s.start,s}function su(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}function Xr(e,t){return t&&(e.bad=!0),e}function uu(e,t,n,r,i){var o="",l=!1,a=e.doc.lineSeparator(),s=!1;function u(g){return function(b){return b.id==g}}function d(){l&&(o+=a,s&&(o+=a),l=s=!1)}function h(g){g&&(d(),o+=g)}function m(g){if(g.nodeType==1){var b=g.getAttribute("cm-text");if(b){h(b);return}var S=g.getAttribute("cm-marker"),C;if(S){var N=e.findMarks(v(r,0),v(i+1,0),u(+S));N.length&&(C=N[0].find(0))&&h(Ht(e.doc,C.from,C.to).join(a));return}if(g.getAttribute("contenteditable")=="false")return;var O=/^(pre|div|p|li|table|br)$/i.test(g.nodeName);if(!/^br$/i.test(g.nodeName)&&g.textContent.length==0)return;O&&d();for(var A=0;A<g.childNodes.length;A++)m(g.childNodes[A]);/^(pre|p)$/i.test(g.nodeName)&&(s=!0),O&&(l=!0)}else g.nodeType==3&&h(g.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;m(t),t!=n;)t=t.nextSibling,s=!1;return o}function fi(e,t,n){var r;if(t==e.display.lineDiv){if(r=e.display.lineDiv.childNodes[n],!r)return Xr(e.clipPos(v(e.display.viewTo-1)),!0);t=null,n=0}else for(r=t;;r=r.parentNode){if(!r||r==e.display.lineDiv)return null;if(r.parentNode&&r.parentNode==e.display.lineDiv)break}for(var i=0;i<e.display.view.length;i++){var o=e.display.view[i];if(o.node==r)return fu(o,t,n)}}function fu(e,t,n){var r=e.text.firstChild,i=!1;if(!t||!U(r,t))return Xr(v(ae(e.line),0),!0);if(t==r&&(i=!0,t=r.childNodes[n],n=0,!t)){var o=e.rest?ee(e.rest):e.line;return Xr(v(ae(o),o.text.length),i)}var l=t.nodeType==3?t:null,a=t;for(!l&&t.childNodes.length==1&&t.firstChild.nodeType==3&&(l=t.firstChild,n&&(n=l.nodeValue.length));a.parentNode!=r;)a=a.parentNode;var s=e.measure,u=s.maps;function d(C,N,O){for(var A=-1;A<(u?u.length:0);A++)for(var W=A<0?s.map:u[A],q=0;q<W.length;q+=3){var R=W[q+2];if(R==C||R==N){var V=ae(A<0?e.line:e.rest[A]),ge=W[q]+O;return(O<0||R!=C)&&(ge=W[q+(O?1:0)]),v(V,ge)}}}var h=d(l,a,n);if(h)return Xr(h,i);for(var m=a.nextSibling,g=l?l.nodeValue.length-n:0;m;m=m.nextSibling){if(h=d(m,m.firstChild,0),h)return Xr(v(h.line,h.ch-g),i);g+=m.textContent.length}for(var b=a.previousSibling,S=n;b;b=b.previousSibling){if(h=d(b,b.firstChild,-1),h)return Xr(v(h.line,h.ch+S),i);S+=b.textContent.length}}var Oe=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new K,this.hasSelection=!1,this.composing=null};Oe.prototype.init=function(e){var t=this,n=this,r=this.cm;this.createField(e);var i=this.textarea;e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),$&&(i.style.width="0px"),G(i,"input",function(){P&&I>=9&&t.hasSelection&&(t.hasSelection=null),n.poll()}),G(i,"paste",function(l){xe(r,l)||ta(l,r)||(r.state.pasteIncoming=+new Date,n.fastPoll())});function o(l){if(!xe(r,l)){if(r.somethingSelected())ui({lineWise:!1,text:r.getSelections()});else if(r.options.lineWiseCopyCut){var a=na(r);ui({lineWise:!0,text:a.text}),l.type=="cut"?r.setSelections(a.ranges,null,Qe):(n.prevInput="",i.value=a.text.join(`
`),Kt(i))}else return;l.type=="cut"&&(r.state.cutIncoming=+new Date)}}G(i,"cut",o),G(i,"copy",o),G(e.scroller,"paste",function(l){if(!(Jt(e,l)||xe(r,l))){if(!i.dispatchEvent){r.state.pasteIncoming=+new Date,n.focus();return}var a=new Event("paste");a.clipboardData=l.clipboardData,i.dispatchEvent(a)}}),G(e.lineSpace,"selectstart",function(l){Jt(e,l)||Be(l)}),G(i,"compositionstart",function(){var l=r.getCursor("from");n.composing&&n.composing.range.clear(),n.composing={start:l,range:r.markText(l,r.getCursor("to"),{className:"CodeMirror-composing"})}}),G(i,"compositionend",function(){n.composing&&(n.poll(),n.composing.range.clear(),n.composing=null)})},Oe.prototype.createField=function(e){this.wrapper=oa(),this.textarea=this.wrapper.firstChild},Oe.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},Oe.prototype.prepareSelection=function(){var e=this.cm,t=e.display,n=e.doc,r=jo(e);if(e.options.moveInputWithCursor){var i=Mt(e,n.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),l=t.lineDiv.getBoundingClientRect();r.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,i.top+l.top-o.top)),r.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,i.left+l.left-o.left))}return r},Oe.prototype.showSelection=function(e){var t=this.cm,n=t.display;z(n.cursorDiv,e.cursors),z(n.selectionDiv,e.selection),e.teTop!=null&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},Oe.prototype.reset=function(e){if(!(this.contextMenuPending||this.composing)){var t=this.cm;if(t.somethingSelected()){this.prevInput="";var n=t.getSelection();this.textarea.value=n,t.state.focused&&Kt(this.textarea),P&&I>=9&&(this.hasSelection=n)}else e||(this.prevInput=this.textarea.value="",P&&I>=9&&(this.hasSelection=null))}},Oe.prototype.getField=function(){return this.textarea},Oe.prototype.supportsTouch=function(){return!1},Oe.prototype.focus=function(){if(this.cm.options.readOnly!="nocursor"&&(!Q||ve()!=this.textarea))try{this.textarea.focus()}catch{}},Oe.prototype.blur=function(){this.textarea.blur()},Oe.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},Oe.prototype.receivedFocus=function(){this.slowPoll()},Oe.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,function(){e.poll(),e.cm.state.focused&&e.slowPoll()})},Oe.prototype.fastPoll=function(){var e=!1,t=this;t.pollingFast=!0;function n(){var r=t.poll();!r&&!e?(e=!0,t.polling.set(60,n)):(t.pollingFast=!1,t.slowPoll())}t.polling.set(20,n)},Oe.prototype.poll=function(){var e=this,t=this.cm,n=this.textarea,r=this.prevInput;if(this.contextMenuPending||!t.state.focused||zt(n)&&!r&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var i=n.value;if(i==r&&!t.somethingSelected())return!1;if(P&&I>=9&&this.hasSelection===i||Y&&/[\uf700-\uf7ff]/.test(i))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=i.charCodeAt(0);if(o==8203&&!r&&(r="​"),o==8666)return this.reset(),this.cm.execCommand("undo")}for(var l=0,a=Math.min(r.length,i.length);l<a&&r.charCodeAt(l)==i.charCodeAt(l);)++l;return ht(t,function(){ro(t,i.slice(l),r.length-l,null,e.composing?"*compose":null),i.length>1e3||i.indexOf(`
`)>-1?n.value=e.prevInput="":e.prevInput=i,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))}),!0},Oe.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},Oe.prototype.onKeyPress=function(){P&&I>=9&&(this.hasSelection=null),this.fastPoll()},Oe.prototype.onContextMenu=function(e){var t=this,n=t.cm,r=n.display,i=t.textarea;t.contextMenuPending&&t.contextMenuPending();var o=vr(n,e),l=r.scroller.scrollTop;if(!o||J)return;var a=n.options.resetSelectionOnContextMenu;a&&n.doc.sel.contains(o)==-1&&Ge(n,et)(n.doc,ir(o),Qe);var s=i.style.cssText,u=t.wrapper.style.cssText,d=t.wrapper.offsetParent.getBoundingClientRect();t.wrapper.style.cssText="position: static",i.style.cssText=`position: absolute; width: 30px; height: 30px;
      top: `+(e.clientY-d.top-5)+"px; left: "+(e.clientX-d.left-5)+`px;
      z-index: 1000; background: `+(P?"rgba(255, 255, 255, .05)":"transparent")+`;
      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);`;var h;se&&(h=window.scrollY),r.input.focus(),se&&window.scrollTo(null,h),r.input.reset(),n.somethingSelected()||(i.value=t.prevInput=" "),t.contextMenuPending=g,r.selForContextMenu=n.doc.sel,clearTimeout(r.detectingSelectAll);function m(){if(i.selectionStart!=null){var S=n.somethingSelected(),C="​"+(S?i.value:"");i.value="⇚",i.value=C,t.prevInput=S?"":"​",i.selectionStart=1,i.selectionEnd=C.length,r.selForContextMenu=n.doc.sel}}function g(){if(t.contextMenuPending==g&&(t.contextMenuPending=!1,t.wrapper.style.cssText=u,i.style.cssText=s,P&&I<9&&r.scrollbars.setScrollTop(r.scroller.scrollTop=l),i.selectionStart!=null)){(!P||P&&I<9)&&m();var S=0,C=function(){r.selForContextMenu==n.doc.sel&&i.selectionStart==0&&i.selectionEnd>0&&t.prevInput=="​"?Ge(n,Ll)(n):S++<10?r.detectingSelectAll=setTimeout(C,500):(r.selForContextMenu=null,r.input.reset())};r.detectingSelectAll=setTimeout(C,200)}}if(P&&I>=9&&m(),Ie){$e(e);var b=function(){te(window,"mouseup",b),setTimeout(g,20)};G(window,"mouseup",b)}else setTimeout(g,50)},Oe.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled=e=="nocursor",this.textarea.readOnly=!!e},Oe.prototype.setUneditable=function(){},Oe.prototype.needsContentAttribute=!1;function cu(e,t){if(t=t?vt(t):{},t.value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),t.autofocus==null){var n=ve();t.autofocus=n==e||e.getAttribute("autofocus")!=null&&n==document.body}function r(){e.value=a.getValue()}var i;if(e.form&&(G(e.form,"submit",r),!t.leaveSubmitMethodAlone)){var o=e.form;i=o.submit;try{var l=o.submit=function(){r(),o.submit=i,o.submit(),o.submit=l}}catch{}}t.finishInit=function(s){s.save=r,s.getTextArea=function(){return e},s.toTextArea=function(){s.toTextArea=isNaN,r(),e.parentNode.removeChild(s.getWrapperElement()),e.style.display="",e.form&&(te(e.form,"submit",r),!t.leaveSubmitMethodAlone&&typeof e.form.submit=="function"&&(e.form.submit=i))}},e.style.display="none";var a=ke(function(s){return e.parentNode.insertBefore(s,e.nextSibling)},t);return a}function du(e){e.off=te,e.on=G,e.wheelEventPixels=bs,e.Doc=st,e.splitLines=Pt,e.countColumn=be,e.findColumn=_t,e.isWordChar=me,e.Pass=ye,e.signal=Me,e.Line=Dr,e.changeEnd=or,e.scrollbarModel=tl,e.Pos=v,e.cmpPos=M,e.modes=Vr,e.mimeModes=qt,e.resolveMode=Xt,e.getMode=$r,e.modeExtensions=Yt,e.extendMode=hi,e.copyState=kt,e.startState=Hn,e.innerMode=Ar,e.commands=Cn,e.keyMap=Qt,e.keyName=Kl,e.isModifierKey=Bl,e.lookupKey=Ur,e.normalizeKeyMap=Ks,e.StringStream=we,e.SharedTextMarker=wn,e.TextMarker=ar,e.LineWidget=xn,e.e_preventDefault=Be,e.e_stopPropagation=Wn,e.e_stop=$e,e.addClass=De,e.contains=U,e.rmClass=j,e.keyNames=sr}nu(ke),au(ke);var hu="iter insert remove copy getEditor constructor".split(" ");for(var ci in st.prototype)st.prototype.hasOwnProperty(ci)&&E(hu,ci)<0&&(ke.prototype[ci]=function(e){return function(){return e.apply(this.doc,arguments)}}(st.prototype[ci]));return $t(st),ke.inputStyles={textarea:Oe,contenteditable:he},ke.defineMode=function(e){!ke.defaults.mode&&e!="null"&&(ke.defaults.mode=e),Nr.apply(this,arguments)},ke.defineMIME=Tt,ke.defineMode("null",function(){return{token:function(e){return e.skipToEnd()}}}),ke.defineMIME("text/plain","null"),ke.defineExtension=function(e,t){ke.prototype[e]=t},ke.defineDocExtension=function(e,t){st.prototype[e]=t},ke.fromTextArea=cu,du(ke),ke.version="5.58.3",ke})}(lo)),lo.exports}var gu=An();const wu=vu(gu);var ua={exports:{}},fa;function mu(){return fa||(fa=1,function(Zr,Dn){(function(F){F(An())})(function(F){var We={autoSelfClosers:{area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0,menuitem:!0},implicitlyClosed:{dd:!0,li:!0,optgroup:!0,option:!0,p:!0,rp:!0,rt:!0,tbody:!0,td:!0,tfoot:!0,th:!0,tr:!0},contextGrabbers:{dd:{dd:!0,dt:!0},dt:{dd:!0,dt:!0},li:{li:!0},option:{option:!0,optgroup:!0},optgroup:{optgroup:!0},p:{address:!0,article:!0,aside:!0,blockquote:!0,dir:!0,div:!0,dl:!0,fieldset:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,menu:!0,nav:!0,ol:!0,p:!0,pre:!0,section:!0,table:!0,ul:!0},rp:{rp:!0,rt:!0},rt:{rp:!0,rt:!0},tbody:{tbody:!0,tfoot:!0},td:{td:!0,th:!0},tfoot:{tbody:!0},th:{td:!0,th:!0},thead:{tbody:!0,tfoot:!0},tr:{tr:!0}},doNotIndent:{pre:!0},allowUnquoted:!0,allowMissing:!0,caseFold:!0},pe={autoSelfClosers:{},implicitlyClosed:{},contextGrabbers:{},doNotIndent:{},allowUnquoted:!1,allowMissing:!1,allowMissingTagName:!1,caseFold:!1};F.defineMode("xml",function(Je,dt){var Pe=Je.indentUnit,P={},I=dt.htmlMode?We:pe;for(var se in I)P[se]=I[se];for(var se in dt)P[se]=dt[se];var re,X;function J(c,D){function L(De){return D.tokenize=De,De(c,D)}var U=c.next();if(U=="<")return c.eat("!")?c.eat("[")?c.match("CDATA[")?L(ze("atom","]]>")):null:c.match("--")?L(ze("comment","-->")):c.match("DOCTYPE",!0,!0)?(c.eatWhile(/[\w\._\-]/),L($(1))):null:c.eat("?")?(c.eatWhile(/[\w\._\-]/),D.tokenize=ze("meta","?>"),"meta"):(re=c.eat("/")?"closeTag":"openTag",D.tokenize=fe,"tag bracket");if(U=="&"){var ve;return c.eat("#")?c.eat("x")?ve=c.eatWhile(/[a-fA-F\d]/)&&c.eat(";"):ve=c.eatWhile(/[\d]/)&&c.eat(";"):ve=c.eatWhile(/[\w\.\-:]/)&&c.eat(";"),ve?"atom":"error"}else return c.eatWhile(/[^&<]/),null}J.isInText=!0;function fe(c,D){var L=c.next();if(L==">"||L=="/"&&c.eat(">"))return D.tokenize=J,re=L==">"?"endTag":"selfcloseTag","tag bracket";if(L=="=")return re="equals",null;if(L=="<"){D.tokenize=J,D.state=Te,D.tagName=D.tagStart=null;var U=D.tokenize(c,D);return U?U+" tag error":"tag error"}else return/[\'\"]/.test(L)?(D.tokenize=Le(L),D.stringStartCol=c.column(),D.tokenize(c,D)):(c.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/),"word")}function Le(c){var D=function(L,U){for(;!L.eol();)if(L.next()==c){U.tokenize=fe;break}return"string"};return D.isInAttribute=!0,D}function ze(c,D){return function(L,U){for(;!L.eol();){if(L.match(D)){U.tokenize=J;break}L.next()}return c}}function $(c){return function(D,L){for(var U;(U=D.next())!=null;){if(U=="<")return L.tokenize=$(c+1),L.tokenize(D,L);if(U==">")if(c==1){L.tokenize=J;break}else return L.tokenize=$(c-1),L.tokenize(D,L)}return"meta"}}function _e(c,D,L){this.prev=c.context,this.tagName=D||"",this.indent=c.indented,this.startOfLine=L,(P.doNotIndent.hasOwnProperty(D)||c.context&&c.context.noIndent)&&(this.noIndent=!0)}function Q(c){c.context&&(c.context=c.context.prev)}function Y(c,D){for(var L;;){if(!c.context||(L=c.context.tagName,!P.contextGrabbers.hasOwnProperty(L)||!P.contextGrabbers[L].hasOwnProperty(D)))return;Q(c)}}function Te(c,D,L){return c=="openTag"?(L.tagStart=D.column(),bt):c=="closeTag"?Ae:Te}function bt(c,D,L){return c=="word"?(L.tagName=D.current(),X="tag",_):P.allowMissingTagName&&c=="endTag"?(X="tag bracket",_(c,D,L)):(X="error",bt)}function Ae(c,D,L){if(c=="word"){var U=D.current();return L.context&&L.context.tagName!=U&&P.implicitlyClosed.hasOwnProperty(L.context.tagName)&&Q(L),L.context&&L.context.tagName==U||P.matchClosing===!1?(X="tag",rt):(X="tag error",Ie)}else return P.allowMissingTagName&&c=="endTag"?(X="tag bracket",rt(c,D,L)):(X="error",Ie)}function rt(c,D,L){return c!="endTag"?(X="error",rt):(Q(L),Te)}function Ie(c,D,L){return X="error",rt(c,D,L)}function _(c,D,L){if(c=="word")return X="attribute",j;if(c=="endTag"||c=="selfcloseTag"){var U=L.tagName,ve=L.tagStart;return L.tagName=L.tagStart=null,c=="selfcloseTag"||P.autoSelfClosers.hasOwnProperty(U)?Y(L,U):(Y(L,U),L.context=new _e(L,U,ve==L.indented)),Te}return X="error",_}function j(c,D,L){return c=="equals"?k:(P.allowMissing||(X="error"),_(c,D,L))}function k(c,D,L){return c=="string"?z:c=="word"&&P.allowUnquoted?(X="string",_):(X="error",_(c,D,L))}function z(c,D,L){return c=="string"?z:_(c,D,L)}return{startState:function(c){var D={tokenize:J,state:Te,indented:c||0,tagName:null,tagStart:null,context:null};return c!=null&&(D.baseIndent=c),D},token:function(c,D){if(!D.tagName&&c.sol()&&(D.indented=c.indentation()),c.eatSpace())return null;re=null;var L=D.tokenize(c,D);return(L||re)&&L!="comment"&&(X=null,D.state=D.state(re||L,c,D),X&&(L=X=="error"?L+" error":X)),L},indent:function(c,D,L){var U=c.context;if(c.tokenize.isInAttribute)return c.tagStart==c.indented?c.stringStartCol+1:c.indented+Pe;if(U&&U.noIndent)return F.Pass;if(c.tokenize!=fe&&c.tokenize!=J)return L?L.match(/^(\s*)/)[0].length:0;if(c.tagName)return P.multilineTagIndentPastTag!==!1?c.tagStart+c.tagName.length+2:c.tagStart+Pe*(P.multilineTagIndentFactor||1);if(P.alignCDATA&&/<!\[CDATA\[/.test(D))return 0;var ve=D&&/^<(\/)?([\w_:\.-]*)/.exec(D);if(ve&&ve[1])for(;U;)if(U.tagName==ve[2]){U=U.prev;break}else if(P.implicitlyClosed.hasOwnProperty(U.tagName))U=U.prev;else break;else if(ve)for(;U;){var De=P.contextGrabbers[U.tagName];if(De&&De.hasOwnProperty(ve[2]))U=U.prev;else break}for(;U&&U.prev&&!U.startOfLine;)U=U.prev;return U?U.indent+Pe:c.baseIndent||0},electricInput:/<\/[\s\w:]+>$/,blockCommentStart:"<!--",blockCommentEnd:"-->",configuration:P.htmlMode?"html":"xml",helperType:P.htmlMode?"html":"xml",skipAttribute:function(c){c.state==k&&(c.state=_)},xmlCurrentTag:function(c){return c.tagName?{name:c.tagName,close:c.type=="closeTag"}:null},xmlCurrentContext:function(c){for(var D=[],L=c.context;L;L=L.prev)D.push(L.tagName);return D.reverse()}}}),F.defineMIME("text/xml","xml"),F.defineMIME("application/xml","xml"),F.mimeModes.hasOwnProperty("text/html")||F.defineMIME("text/html",{name:"xml",htmlMode:!0})})}()),ua.exports}var ca={exports:{}},da;function yu(){return da||(da=1,function(Zr,Dn){(function(F){F(An())})(function(F){F.defineMode("javascript",function(We,pe){var Je=We.indentUnit,dt=pe.statementIndent,Pe=pe.jsonld,P=pe.json||Pe,I=pe.typescript,se=pe.wordCharacters||/[\w$\xa1-\uffff]/,re=function(){function f(Re){return{type:Re,style:"keyword"}}var p=f("keyword a"),w=f("keyword b"),v=f("keyword c"),M=f("keyword d"),le=f("operator"),de={type:"atom",style:"atom"};return{if:f("if"),while:p,with:p,else:w,do:w,try:w,finally:w,return:M,break:M,continue:M,new:f("new"),delete:v,void:v,throw:v,debugger:f("debugger"),var:f("var"),const:f("var"),let:f("var"),function:f("function"),catch:f("catch"),for:f("for"),switch:f("switch"),case:f("case"),default:f("default"),in:le,typeof:le,instanceof:le,true:de,false:de,null:de,undefined:de,NaN:de,Infinity:de,this:f("this"),class:f("class"),super:f("atom"),yield:v,export:f("export"),import:f("import"),extends:v,await:v}}(),X=/[+\-*&%=<>!?|~^@]/,J=/^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)"/;function fe(f){for(var p=!1,w,v=!1;(w=f.next())!=null;){if(!p){if(w=="/"&&!v)return;w=="["?v=!0:v&&w=="]"&&(v=!1)}p=!p&&w=="\\"}}var Le,ze;function $(f,p,w){return Le=f,ze=w,p}function _e(f,p){var w=f.next();if(w=='"'||w=="'")return p.tokenize=Q(w),p.tokenize(f,p);if(w=="."&&f.match(/^\d[\d_]*(?:[eE][+\-]?[\d_]+)?/))return $("number","number");if(w=="."&&f.match(".."))return $("spread","meta");if(/[\[\]{}\(\),;\:\.]/.test(w))return $(w);if(w=="="&&f.eat(">"))return $("=>","operator");if(w=="0"&&f.match(/^(?:x[\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/))return $("number","number");if(/\d/.test(w))return f.match(/^[\d_]*(?:n|(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)?/),$("number","number");if(w=="/")return f.eat("*")?(p.tokenize=Y,Y(f,p)):f.eat("/")?(f.skipToEnd(),$("comment","comment")):ae(f,p,1)?(fe(f),f.match(/^\b(([gimyus])(?![gimyus]*\2))+\b/),$("regexp","string-2")):(f.eat("="),$("operator","operator",f.current()));if(w=="`")return p.tokenize=Te,Te(f,p);if(w=="#"&&f.peek()=="!")return f.skipToEnd(),$("meta","meta");if(w=="#"&&f.eatWhile(se))return $("variable","property");if(w=="<"&&f.match("!--")||w=="-"&&f.match("->")&&!/\S/.test(f.string.slice(0,f.start)))return f.skipToEnd(),$("comment","comment");if(X.test(w))return(w!=">"||!p.lexical||p.lexical.type!=">")&&(f.eat("=")?(w=="!"||w=="=")&&f.eat("="):/[<>*+\-|&?]/.test(w)&&(f.eat(w),w==">"&&f.eat(w))),w=="?"&&f.eat(".")?$("."):$("operator","operator",f.current());if(se.test(w)){f.eatWhile(se);var v=f.current();if(p.lastType!="."){if(re.propertyIsEnumerable(v)){var M=re[v];return $(M.type,M.style,v)}if(v=="async"&&f.match(/^(\s|\/\*([^*]|\*(?!\/))*?\*\/)*[\[\(\w]/,!1))return $("async","keyword",v)}return $("variable","variable",v)}}function Q(f){return function(p,w){var v=!1,M;if(Pe&&p.peek()=="@"&&p.match(J))return w.tokenize=_e,$("jsonld-keyword","meta");for(;(M=p.next())!=null&&!(M==f&&!v);)v=!v&&M=="\\";return v||(w.tokenize=_e),$("string","string")}}function Y(f,p){for(var w=!1,v;v=f.next();){if(v=="/"&&w){p.tokenize=_e;break}w=v=="*"}return $("comment","comment")}function Te(f,p){for(var w=!1,v;(v=f.next())!=null;){if(!w&&(v=="`"||v=="$"&&f.eat("{"))){p.tokenize=_e;break}w=!w&&v=="\\"}return $("quasi","string-2",f.current())}var bt="([{}])";function Ae(f,p){p.fatArrowAt&&(p.fatArrowAt=null);var w=f.string.indexOf("=>",f.start);if(!(w<0)){if(I){var v=/:\s*(?:\w+(?:<[^>]*>|\[\])?|\{[^}]*\})\s*$/.exec(f.string.slice(f.start,w));v&&(w=v.index)}for(var M=0,le=!1,de=w-1;de>=0;--de){var Re=f.string.charAt(de),lt=bt.indexOf(Re);if(lt>=0&&lt<3){if(!M){++de;break}if(--M==0){Re=="("&&(le=!0);break}}else if(lt>=3&&lt<6)++M;else if(se.test(Re))le=!0;else if(/["'\/`]/.test(Re))for(;;--de){if(de==0)return;var En=f.string.charAt(de-1);if(En==Re&&f.string.charAt(de-2)!="\\"){de--;break}}else if(le&&!M){++de;break}}le&&!M&&(p.fatArrowAt=de)}}var rt={atom:!0,number:!0,variable:!0,string:!0,regexp:!0,this:!0,"jsonld-keyword":!0};function Ie(f,p,w,v,M,le){this.indented=f,this.column=p,this.type=w,this.prev=M,this.info=le,v!=null&&(this.align=v)}function _(f,p){for(var w=f.localVars;w;w=w.next)if(w.name==p)return!0;for(var v=f.context;v;v=v.prev)for(var w=v.vars;w;w=w.next)if(w.name==p)return!0}function j(f,p,w,v,M){var le=f.cc;for(k.state=f,k.stream=M,k.marked=null,k.cc=le,k.style=p,f.lexical.hasOwnProperty("align")||(f.lexical.align=!0);;){var de=le.length?le.pop():P?H:ye;if(de(w,v)){for(;le.length&&le[le.length-1].lex;)le.pop()();return k.marked?k.marked:w=="variable"&&_(f,v)?"variable-2":p}}}var k={state:null,column:null,marked:null,cc:null};function z(){for(var f=arguments.length-1;f>=0;f--)k.cc.push(arguments[f])}function c(){return z.apply(null,arguments),!0}function D(f,p){for(var w=p;w;w=w.next)if(w.name==f)return!0;return!1}function L(f){var p=k.state;if(k.marked="def",p.context){if(p.lexical.info=="var"&&p.context&&p.context.block){var w=U(f,p.context);if(w!=null){p.context=w;return}}else if(!D(f,p.localVars)){p.localVars=new xt(f,p.localVars);return}}pe.globalVars&&!D(f,p.globalVars)&&(p.globalVars=new xt(f,p.globalVars))}function U(f,p){if(p)if(p.block){var w=U(f,p.prev);return w?w==p.prev?p:new De(w,p.vars,!0):null}else return D(f,p.vars)?p:new De(p.prev,new xt(f,p.vars),!1);else return null}function ve(f){return f=="public"||f=="private"||f=="protected"||f=="abstract"||f=="readonly"}function De(f,p,w){this.prev=f,this.vars=p,this.block=w}function xt(f,p){this.name=f,this.next=p}var Kt=new xt("this",new xt("arguments",null));function wt(){k.state.context=new De(k.state.context,k.state.localVars,!1),k.state.localVars=Kt}function vt(){k.state.context=new De(k.state.context,k.state.localVars,!0),k.state.localVars=null}function be(){k.state.localVars=k.state.context.vars,k.state.context=k.state.context.prev}be.lex=!0;function K(f,p){var w=function(){var v=k.state,M=v.indented;if(v.lexical.type=="stat")M=v.lexical.indented;else for(var le=v.lexical;le&&le.type==")"&&le.align;le=le.prev)M=le.indented;v.lexical=new Ie(M,k.stream.column(),f,null,v.lexical,p)};return w.lex=!0,w}function E(){var f=k.state;f.lexical.prev&&(f.lexical.type==")"&&(f.indented=f.lexical.indented),f.lexical=f.lexical.prev)}E.lex=!0;function ne(f){function p(w){return w==f?c():f==";"||w=="}"||w==")"||w=="]"?z():c(p)}return p}function ye(f,p){return f=="var"?c(K("vardef",p),dr,ne(";"),E):f=="keyword a"?c(K("form"),_t,ye,E):f=="keyword b"?c(K("form"),ye,E):f=="keyword d"?k.stream.match(/^\s*$/,!1)?c():c(K("stat"),Ct,ne(";"),E):f=="debugger"?c(ne(";")):f=="{"?c(K("}"),vt,Sr,E,be):f==";"?c():f=="if"?(k.state.lexical.info=="else"&&k.state.cc[k.state.cc.length-1]==E&&k.state.cc.pop()(),c(K("form"),_t,ye,E,zn)):f=="function"?c(zt):f=="for"?c(K("form"),Tr,ye,E):f=="class"||I&&p=="interface"?(k.marked="keyword",c(K("form",f=="class"?f:p),qt,E)):f=="variable"?I&&p=="declare"?(k.marked="keyword",c(ye)):I&&(p=="module"||p=="enum"||p=="type")&&k.stream.match(/^\s*\w/,!1)?(k.marked="keyword",p=="enum"?c(Ht):p=="type"?c(Mr,ne("operator"),te,ne(";")):c(K("form"),$e,ne("{"),K("}"),Sr,E,E)):I&&p=="namespace"?(k.marked="keyword",c(K("form"),H,ye,E)):I&&p=="abstract"?(k.marked="keyword",c(ye)):c(K("stat"),x):f=="switch"?c(K("form"),_t,ne("{"),K("}","switch"),vt,Sr,E,E,be):f=="case"?c(H,ne(":")):f=="default"?c(ne(":")):f=="catch"?c(K("form"),wt,Qe,ye,E,be):f=="export"?c(K("stat"),$r,E):f=="import"?c(K("stat"),hi,E):f=="async"?c(ye):p=="@"?c(H,ye):z(K("stat"),H,ne(";"),E)}function Qe(f){if(f=="(")return c(Gt,ne(")"))}function H(f,p){return Dt(f,p,!1)}function ie(f,p){return Dt(f,p,!0)}function _t(f){return f!="("?z():c(K(")"),Ct,ne(")"),E)}function Dt(f,p,w){if(k.state.fatArrowAt==k.stream.start){var v=w?fr:gt;if(f=="(")return c(wt,K(")"),Se(Gt,")"),E,ne("=>"),v,be);if(f=="variable")return z(wt,$e,ne("=>"),v,be)}var M=w?ce:ee;return rt.hasOwnProperty(f)?c(M):f=="function"?c(zt,M):f=="class"||I&&p=="interface"?(k.marked="keyword",c(K("form"),Vr,E)):f=="keyword c"||f=="async"?c(w?ie:H):f=="("?c(K(")"),Ct,ne(")"),E,M):f=="operator"||f=="spread"?c(w?ie:H):f=="["?c(K("]"),B,E,M):f=="{"?Lt(Ue,"}",null,M):f=="quasi"?z(Ve,M):f=="new"?c(me(w)):f=="import"?c(H):c()}function Ct(f){return f.match(/[;\}\)\],]/)?z():z(H)}function ee(f,p){return f==","?c(Ct):ce(f,p,!1)}function ce(f,p,w){var v=w==!1?ee:ce,M=w==!1?H:ie;if(f=="=>")return c(wt,w?fr:gt,be);if(f=="operator")return/\+\+|--/.test(p)||I&&p=="!"?c(v):I&&p=="<"&&k.stream.match(/^([^<>]|<[^<>]*>)*>\s*\(/,!1)?c(K(">"),Se(te,">"),E,v):p=="?"?c(H,ne(":"),M):c(M);if(f=="quasi")return z(Ve,v);if(f!=";"){if(f=="(")return Lt(ie,")","call",v);if(f==".")return c(oe,v);if(f=="[")return c(K("]"),Ct,ne("]"),E,v);if(I&&p=="as")return k.marked="keyword",c(te,v);if(f=="regexp")return k.state.lastType=k.marked="operator",k.stream.backUp(k.stream.pos-k.stream.start-1),c(M)}}function Ve(f,p){return f!="quasi"?z():p.slice(p.length-2)!="${"?c(Ve):c(H,Ot)}function Ot(f){if(f=="}")return k.marked="string-2",k.state.tokenize=Te,c(Ve)}function gt(f){return Ae(k.stream,k.state),z(f=="{"?ye:H)}function fr(f){return Ae(k.stream,k.state),z(f=="{"?ye:ie)}function me(f){return function(p){return p=="."?c(f?T:y):p=="variable"&&I?c($t,f?ce:ee):z(f?ie:H)}}function y(f,p){if(p=="target")return k.marked="keyword",c(ee)}function T(f,p){if(p=="target")return k.marked="keyword",c(ce)}function x(f){return f==":"?c(E,ye):z(ee,ne(";"),E)}function oe(f){if(f=="variable")return k.marked="property",c()}function Ue(f,p){if(f=="async")return k.marked="property",c(Ue);if(f=="variable"||k.style=="keyword"){if(k.marked="property",p=="get"||p=="set")return c(cr);var w;return I&&k.state.fatArrowAt==k.stream.start&&(w=k.stream.match(/^\s*:\s*/,!1))&&(k.state.fatArrowAt=k.stream.pos+w[0].length),c(Ut)}else{if(f=="number"||f=="string")return k.marked=Pe?"property":k.style+" property",c(Ut);if(f=="jsonld-keyword")return c(Ut);if(I&&ve(p))return k.marked="keyword",c(Ue);if(f=="[")return c(H,nt,ne("]"),Ut);if(f=="spread")return c(ie,Ut);if(p=="*")return k.marked="keyword",c(Ue);if(f==":")return z(Ut)}}function cr(f){return f!="variable"?z(Ut):(k.marked="property",c(zt))}function Ut(f){if(f==":")return c(ie);if(f=="(")return z(zt)}function Se(f,p,w){function v(M,le){if(w?w.indexOf(M)>-1:M==","){var de=k.state.lexical;return de.info=="call"&&(de.pos=(de.pos||0)+1),c(function(Re,lt){return Re==p||lt==p?z():z(f)},v)}return M==p||le==p?c():w&&w.indexOf(";")>-1?z(f):c(ne(p))}return function(M,le){return M==p||le==p?c():z(f,v)}}function Lt(f,p,w){for(var v=3;v<arguments.length;v++)k.cc.push(arguments[v]);return c(K(p,w),Se(f,p),E)}function Sr(f){return f=="}"?c():z(ye,Sr)}function nt(f,p){if(I){if(f==":")return c(te);if(p=="?")return c(nt)}}function On(f,p){if(I&&(f==":"||p=="in"))return c(te)}function G(f){if(I&&f==":")return k.stream.match(/^\s*\w+\s+is\b/,!1)?c(H,Jr,te):c(te)}function Jr(f,p){if(p=="is")return k.marked="keyword",c()}function te(f,p){if(p=="keyof"||p=="typeof"||p=="infer")return k.marked="keyword",c(p=="typeof"?ie:te);if(f=="variable"||p=="void")return k.marked="type",c(He);if(p=="|"||p=="&")return c(te);if(f=="string"||f=="number"||f=="atom")return c(He);if(f=="[")return c(K("]"),Se(te,"]",","),E,He);if(f=="{")return c(K("}"),Se(xe,"}",",;"),E,He);if(f=="(")return c(Se(Cr,")"),Me,He);if(f=="<")return c(Se(te,">"),te)}function Me(f){if(f=="=>")return c(te)}function xe(f,p){if(f=="variable"||k.style=="keyword")return k.marked="property",c(xe);if(p=="?"||f=="number"||f=="string")return c(xe);if(f==":")return c(te);if(f=="[")return c(ne("variable"),On,ne("]"),xe);if(f=="(")return z(er,xe)}function Cr(f,p){return f=="variable"&&k.stream.match(/^\s*[?:]/,!1)||p=="?"?c(Cr):f==":"?c(te):f=="spread"?c(Cr):z(te)}function He(f,p){if(p=="<")return c(K(">"),Se(te,">"),E,He);if(p=="|"||f=="."||p=="&")return c(te);if(f=="[")return c(te,ne("]"),He);if(p=="extends"||p=="implements")return k.marked="keyword",c(te);if(p=="?")return c(te,ne(":"),te)}function $t(f,p){if(p=="<")return c(K(">"),Se(te,">"),E,He)}function Be(){return z(te,Wn)}function Wn(f,p){if(p=="=")return c(te)}function dr(f,p){return p=="enum"?(k.marked="keyword",c(Ht)):z($e,nt,Wt,Qr)}function $e(f,p){if(I&&ve(p))return k.marked="keyword",c($e);if(f=="variable")return L(p),c();if(f=="spread")return c($e);if(f=="[")return Lt(Pn,"]");if(f=="{")return Lt(Lr,"}")}function Lr(f,p){return f=="variable"&&!k.stream.match(/^\s*:/,!1)?(L(p),c(Wt)):(f=="variable"&&(k.marked="property"),f=="spread"?c($e):f=="}"?z():f=="["?c(H,ne("]"),ne(":"),Lr):c(ne(":"),$e,Wt))}function Pn(){return z($e,Wt)}function Wt(f,p){if(p=="=")return c(ie)}function Qr(f){if(f==",")return c(dr)}function zn(f,p){if(f=="keyword b"&&p=="else")return c(K("form","else"),ye,E)}function Tr(f,p){if(p=="await")return c(Tr);if(f=="(")return c(K(")"),di,E)}function di(f){return f=="var"?c(dr,Pt):f=="variable"?c(Pt):z(Pt)}function Pt(f,p){return f==")"?c():f==";"?c(Pt):p=="in"||p=="of"?(k.marked="keyword",c(H,Pt)):z(H,Pt)}function zt(f,p){if(p=="*")return k.marked="keyword",c(zt);if(f=="variable")return L(p),c(zt);if(f=="(")return c(wt,K(")"),Se(Gt,")"),E,G,ye,be);if(I&&p=="<")return c(K(">"),Se(Be,">"),E,zt)}function er(f,p){if(p=="*")return k.marked="keyword",c(er);if(f=="variable")return L(p),c(er);if(f=="(")return c(wt,K(")"),Se(Gt,")"),E,G,be);if(I&&p=="<")return c(K(">"),Se(Be,">"),E,er)}function Mr(f,p){if(f=="keyword"||f=="variable")return k.marked="type",c(Mr);if(p=="<")return c(K(">"),Se(Be,">"),E)}function Gt(f,p){return p=="@"&&c(H,Gt),f=="spread"?c(Gt):I&&ve(p)?(k.marked="keyword",c(Gt)):I&&f=="this"?c(nt,Wt):z($e,nt,Wt)}function Vr(f,p){return f=="variable"?qt(f,p):Nr(f,p)}function qt(f,p){if(f=="variable")return L(p),c(Nr)}function Nr(f,p){if(p=="<")return c(K(">"),Se(Be,">"),E,Nr);if(p=="extends"||p=="implements"||I&&f==",")return p=="implements"&&(k.marked="keyword"),c(I?te:H,Nr);if(f=="{")return c(K("}"),Tt,E)}function Tt(f,p){if(f=="async"||f=="variable"&&(p=="static"||p=="get"||p=="set"||I&&ve(p))&&k.stream.match(/^\s+[\w$\xa1-\uffff]/,!1))return k.marked="keyword",c(Tt);if(f=="variable"||k.style=="keyword")return k.marked="property",c(Xt,Tt);if(f=="number"||f=="string")return c(Xt,Tt);if(f=="[")return c(H,nt,ne("]"),Xt,Tt);if(p=="*")return k.marked="keyword",c(Tt);if(I&&f=="(")return z(er,Tt);if(f==";"||f==",")return c(Tt);if(f=="}")return c();if(p=="@")return c(H,Tt)}function Xt(f,p){if(p=="?")return c(Xt);if(f==":")return c(te,Wt);if(p=="=")return c(ie);var w=k.state.lexical.prev,v=w&&w.info=="interface";return z(v?er:zt)}function $r(f,p){return p=="*"?(k.marked="keyword",c(we,ne(";"))):p=="default"?(k.marked="keyword",c(H,ne(";"))):f=="{"?c(Se(Yt,"}"),we,ne(";")):z(ye)}function Yt(f,p){if(p=="as")return k.marked="keyword",c(ne("variable"));if(f=="variable")return z(ie,Yt)}function hi(f){return f=="string"?c():f=="("?z(H):z(kt,Ar,we)}function kt(f,p){return f=="{"?Lt(kt,"}"):(f=="variable"&&L(p),p=="*"&&(k.marked="keyword"),c(Hn))}function Ar(f){if(f==",")return c(kt,Ar)}function Hn(f,p){if(p=="as")return k.marked="keyword",c(kt)}function we(f,p){if(p=="from")return k.marked="keyword",c(H)}function B(f){return f=="]"?c():z(Se(ie,"]"))}function Ht(){return z(K("form"),$e,ne("{"),K("}"),Se(en,"}"),E,E)}function en(){return z($e,Wt)}function St(f,p){return f.lastType=="operator"||f.lastType==","||X.test(p.charAt(0))||/[,.]/.test(p.charAt(0))}function ae(f,p,w){return p.tokenize==_e&&/^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\[{}\(,;:]|=>)$/.test(p.lastType)||p.lastType=="quasi"&&/\{\s*$/.test(f.string.slice(0,f.pos-(w||0)))}return{startState:function(f){var p={tokenize:_e,lastType:"sof",cc:[],lexical:new Ie((f||0)-Je,0,"block",!1),localVars:pe.localVars,context:pe.localVars&&new De(null,null,!1),indented:f||0};return pe.globalVars&&typeof pe.globalVars=="object"&&(p.globalVars=pe.globalVars),p},token:function(f,p){if(f.sol()&&(p.lexical.hasOwnProperty("align")||(p.lexical.align=!1),p.indented=f.indentation(),Ae(f,p)),p.tokenize!=Y&&f.eatSpace())return null;var w=p.tokenize(f,p);return Le=="comment"?w:(p.lastType=Le=="operator"&&(ze=="++"||ze=="--")?"incdec":Le,j(p,w,Le,ze,f))},indent:function(f,p){if(f.tokenize==Y||f.tokenize==Te)return F.Pass;if(f.tokenize!=_e)return 0;var w=p&&p.charAt(0),v=f.lexical,M;if(!/^\s*else\b/.test(p))for(var le=f.cc.length-1;le>=0;--le){var de=f.cc[le];if(de==E)v=v.prev;else if(de!=zn)break}for(;(v.type=="stat"||v.type=="form")&&(w=="}"||(M=f.cc[f.cc.length-1])&&(M==ee||M==ce)&&!/^[,\.=+\-*:?[\(]/.test(p));)v=v.prev;dt&&v.type==")"&&v.prev.type=="stat"&&(v=v.prev);var Re=v.type,lt=w==Re;return Re=="vardef"?v.indented+(f.lastType=="operator"||f.lastType==","?v.info.length+1:0):Re=="form"&&w=="{"?v.indented:Re=="form"?v.indented+Je:Re=="stat"?v.indented+(St(f,p)?dt||Je:0):v.info=="switch"&&!lt&&pe.doubleIndentSwitch!=!1?v.indented+(/^(?:case|default)\b/.test(p)?Je:2*Je):v.align?v.column+(lt?0:1):v.indented+(lt?0:Je)},electricInput:/^\s*(?:case .*?:|default:|\{|\})$/,blockCommentStart:P?null:"/*",blockCommentEnd:P?null:"*/",blockCommentContinue:P?null:" * ",lineComment:P?null:"//",fold:"brace",closeBrackets:"()[]{}''\"\"``",helperType:P?"json":"javascript",jsonldMode:Pe,jsonMode:P,expressionAllowed:ae,skipExpression:function(f){var p=f.cc[f.cc.length-1];(p==H||p==ie)&&f.cc.pop()}}}),F.registerHelper("wordChars","javascript",/[\w$]/),F.defineMIME("text/javascript","javascript"),F.defineMIME("text/ecmascript","javascript"),F.defineMIME("application/javascript","javascript"),F.defineMIME("application/x-javascript","javascript"),F.defineMIME("application/ecmascript","javascript"),F.defineMIME("application/json",{name:"javascript",json:!0}),F.defineMIME("application/x-json",{name:"javascript",json:!0}),F.defineMIME("application/ld+json",{name:"javascript",jsonld:!0}),F.defineMIME("text/typescript",{name:"javascript",typescript:!0}),F.defineMIME("application/typescript",{name:"javascript",typescript:!0})})}()),ca.exports}var ha={exports:{}},pa;function bu(){return pa||(pa=1,function(Zr,Dn){(function(F){F(An())})(function(F){F.defineMode("css",function(_,j){var k=j.inline;j.propertyKeywords||(j=F.resolveMode("text/css"));var z=_.indentUnit,c=j.tokenHooks,D=j.documentTypes||{},L=j.mediaTypes||{},U=j.mediaFeatures||{},ve=j.mediaValueKeywords||{},De=j.propertyKeywords||{},xt=j.nonStandardPropertyKeywords||{},Kt=j.fontProperties||{},wt=j.counterDescriptors||{},vt=j.colorKeywords||{},be=j.valueKeywords||{},K=j.allowNested,E=j.lineComment,ne=j.supportsAtComponent===!0,ye=_.highlightNonStandardPropertyKeywords!==!1,Qe,H;function ie(y,T){return Qe=T,y}function _t(y,T){var x=y.next();if(c[x]){var oe=c[x](y,T);if(oe!==!1)return oe}if(x=="@")return y.eatWhile(/[\w\\\-]/),ie("def",y.current());if(x=="="||(x=="~"||x=="|")&&y.eat("="))return ie(null,"compare");if(x=='"'||x=="'")return T.tokenize=Dt(x),T.tokenize(y,T);if(x=="#")return y.eatWhile(/[\w\\\-]/),ie("atom","hash");if(x=="!")return y.match(/^\s*\w*/),ie("keyword","important");if(/\d/.test(x)||x=="."&&y.eat(/\d/))return y.eatWhile(/[\w.%]/),ie("number","unit");if(x==="-"){if(/[\d.]/.test(y.peek()))return y.eatWhile(/[\w.%]/),ie("number","unit");if(y.match(/^-[\w\\\-]*/))return y.eatWhile(/[\w\\\-]/),y.match(/^\s*:/,!1)?ie("variable-2","variable-definition"):ie("variable-2","variable");if(y.match(/^\w+-/))return ie("meta","meta")}else return/[,+>*\/]/.test(x)?ie(null,"select-op"):x=="."&&y.match(/^-?[_a-z][_a-z0-9-]*/i)?ie("qualifier","qualifier"):/[:;{}\[\]\(\)]/.test(x)?ie(null,x):y.match(/[\w-.]+(?=\()/)?(/^(url(-prefix)?|domain|regexp)$/.test(y.current().toLowerCase())&&(T.tokenize=Ct),ie("variable callee","variable")):/[\w\\\-]/.test(x)?(y.eatWhile(/[\w\\\-]/),ie("property","word")):ie(null,null)}function Dt(y){return function(T,x){for(var oe=!1,Ue;(Ue=T.next())!=null;){if(Ue==y&&!oe){y==")"&&T.backUp(1);break}oe=!oe&&Ue=="\\"}return(Ue==y||!oe&&y!=")")&&(x.tokenize=null),ie("string","string")}}function Ct(y,T){return y.next(),y.match(/\s*[\"\')]/,!1)?T.tokenize=null:T.tokenize=Dt(")"),ie(null,"(")}function ee(y,T,x){this.type=y,this.indent=T,this.prev=x}function ce(y,T,x,oe){return y.context=new ee(x,T.indentation()+(oe===!1?0:z),y.context),x}function Ve(y){return y.context.prev&&(y.context=y.context.prev),y.context.type}function Ot(y,T,x){return me[x.context.type](y,T,x)}function gt(y,T,x,oe){for(var Ue=oe||1;Ue>0;Ue--)x.context=x.context.prev;return Ot(y,T,x)}function fr(y){var T=y.current().toLowerCase();be.hasOwnProperty(T)?H="atom":vt.hasOwnProperty(T)?H="keyword":H="variable"}var me={};return me.top=function(y,T,x){if(y=="{")return ce(x,T,"block");if(y=="}"&&x.context.prev)return Ve(x);if(ne&&/@component/i.test(y))return ce(x,T,"atComponentBlock");if(/^@(-moz-)?document$/i.test(y))return ce(x,T,"documentTypes");if(/^@(media|supports|(-moz-)?document|import)$/i.test(y))return ce(x,T,"atBlock");if(/^@(font-face|counter-style)/i.test(y))return x.stateArg=y,"restricted_atBlock_before";if(/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(y))return"keyframes";if(y&&y.charAt(0)=="@")return ce(x,T,"at");if(y=="hash")H="builtin";else if(y=="word")H="tag";else{if(y=="variable-definition")return"maybeprop";if(y=="interpolation")return ce(x,T,"interpolation");if(y==":")return"pseudo";if(K&&y=="(")return ce(x,T,"parens")}return x.context.type},me.block=function(y,T,x){if(y=="word"){var oe=T.current().toLowerCase();return De.hasOwnProperty(oe)?(H="property","maybeprop"):xt.hasOwnProperty(oe)?(H=ye?"string-2":"property","maybeprop"):K?(H=T.match(/^\s*:(?:\s|$)/,!1)?"property":"tag","block"):(H+=" error","maybeprop")}else return y=="meta"?"block":!K&&(y=="hash"||y=="qualifier")?(H="error","block"):me.top(y,T,x)},me.maybeprop=function(y,T,x){return y==":"?ce(x,T,"prop"):Ot(y,T,x)},me.prop=function(y,T,x){if(y==";")return Ve(x);if(y=="{"&&K)return ce(x,T,"propBlock");if(y=="}"||y=="{")return gt(y,T,x);if(y=="(")return ce(x,T,"parens");if(y=="hash"&&!/^#([0-9a-fA-f]{3,4}|[0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/.test(T.current()))H+=" error";else if(y=="word")fr(T);else if(y=="interpolation")return ce(x,T,"interpolation");return"prop"},me.propBlock=function(y,T,x){return y=="}"?Ve(x):y=="word"?(H="property","maybeprop"):x.context.type},me.parens=function(y,T,x){return y=="{"||y=="}"?gt(y,T,x):y==")"?Ve(x):y=="("?ce(x,T,"parens"):y=="interpolation"?ce(x,T,"interpolation"):(y=="word"&&fr(T),"parens")},me.pseudo=function(y,T,x){return y=="meta"?"pseudo":y=="word"?(H="variable-3",x.context.type):Ot(y,T,x)},me.documentTypes=function(y,T,x){return y=="word"&&D.hasOwnProperty(T.current())?(H="tag",x.context.type):me.atBlock(y,T,x)},me.atBlock=function(y,T,x){if(y=="(")return ce(x,T,"atBlock_parens");if(y=="}"||y==";")return gt(y,T,x);if(y=="{")return Ve(x)&&ce(x,T,K?"block":"top");if(y=="interpolation")return ce(x,T,"interpolation");if(y=="word"){var oe=T.current().toLowerCase();oe=="only"||oe=="not"||oe=="and"||oe=="or"?H="keyword":L.hasOwnProperty(oe)?H="attribute":U.hasOwnProperty(oe)?H="property":ve.hasOwnProperty(oe)?H="keyword":De.hasOwnProperty(oe)?H="property":xt.hasOwnProperty(oe)?H=ye?"string-2":"property":be.hasOwnProperty(oe)?H="atom":vt.hasOwnProperty(oe)?H="keyword":H="error"}return x.context.type},me.atComponentBlock=function(y,T,x){return y=="}"?gt(y,T,x):y=="{"?Ve(x)&&ce(x,T,K?"block":"top",!1):(y=="word"&&(H="error"),x.context.type)},me.atBlock_parens=function(y,T,x){return y==")"?Ve(x):y=="{"||y=="}"?gt(y,T,x,2):me.atBlock(y,T,x)},me.restricted_atBlock_before=function(y,T,x){return y=="{"?ce(x,T,"restricted_atBlock"):y=="word"&&x.stateArg=="@counter-style"?(H="variable","restricted_atBlock_before"):Ot(y,T,x)},me.restricted_atBlock=function(y,T,x){return y=="}"?(x.stateArg=null,Ve(x)):y=="word"?(x.stateArg=="@font-face"&&!Kt.hasOwnProperty(T.current().toLowerCase())||x.stateArg=="@counter-style"&&!wt.hasOwnProperty(T.current().toLowerCase())?H="error":H="property","maybeprop"):"restricted_atBlock"},me.keyframes=function(y,T,x){return y=="word"?(H="variable","keyframes"):y=="{"?ce(x,T,"top"):Ot(y,T,x)},me.at=function(y,T,x){return y==";"?Ve(x):y=="{"||y=="}"?gt(y,T,x):(y=="word"?H="tag":y=="hash"&&(H="builtin"),"at")},me.interpolation=function(y,T,x){return y=="}"?Ve(x):y=="{"||y==";"?gt(y,T,x):(y=="word"?H="variable":y!="variable"&&y!="("&&y!=")"&&(H="error"),"interpolation")},{startState:function(y){return{tokenize:null,state:k?"block":"top",stateArg:null,context:new ee(k?"block":"top",y||0,null)}},token:function(y,T){if(!T.tokenize&&y.eatSpace())return null;var x=(T.tokenize||_t)(y,T);return x&&typeof x=="object"&&(Qe=x[1],x=x[0]),H=x,Qe!="comment"&&(T.state=me[T.state](Qe,y,T)),H},indent:function(y,T){var x=y.context,oe=T&&T.charAt(0),Ue=x.indent;return x.type=="prop"&&(oe=="}"||oe==")")&&(x=x.prev),x.prev&&(oe=="}"&&(x.type=="block"||x.type=="top"||x.type=="interpolation"||x.type=="restricted_atBlock")?(x=x.prev,Ue=x.indent):(oe==")"&&(x.type=="parens"||x.type=="atBlock_parens")||oe=="{"&&(x.type=="at"||x.type=="atBlock"))&&(Ue=Math.max(0,x.indent-z))),Ue},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:E,fold:"brace"}});function We(_){for(var j={},k=0;k<_.length;++k)j[_[k].toLowerCase()]=!0;return j}var pe=["domain","regexp","url","url-prefix"],Je=We(pe),dt=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],Pe=We(dt),P=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","orientation","device-pixel-ratio","min-device-pixel-ratio","max-device-pixel-ratio","pointer","any-pointer","hover","any-hover","prefers-color-scheme"],I=We(P),se=["landscape","portrait","none","coarse","fine","on-demand","hover","interlace","progressive","dark","light"],re=We(se),X=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","all","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","binding","bleed","block-size","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","gap","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","left","letter-spacing","line-break","line-height","line-height-step","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","place-content","place-items","place-self","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotate","rotation","rotation-point","row-gap","ruby-align","ruby-overhang","ruby-position","ruby-span","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-type","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-orientation","text-outline","text-overflow","text-rendering","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","touch-action","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-select","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","paint-order","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode"],J=We(X),fe=["border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","margin-block","margin-block-end","margin-block-start","margin-inline","margin-inline-end","margin-inline-start","padding-block","padding-block-end","padding-block-start","padding-inline","padding-inline-end","padding-inline-start","scroll-snap-stop","scrollbar-3d-light-color","scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-track-color","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","shape-inside","zoom"],Le=We(fe),ze=["font-display","font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"],$=We(ze),_e=["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"],Q=We(_e),Y=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],Te=We(Y),bt=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","bullets","button","button-bevel","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","contain","content","contents","content-box","context-menu","continuous","copy","counter","counters","cover","crop","cross","crosshair","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","devanagari","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","georgian","graytext","grid","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hard-light","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","hue","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","luminosity","malayalam","manipulation","match","matrix","matrix3d","media-controls-background","media-current-time-display","media-fullscreen-button","media-mute-button","media-play-button","media-return-to-realtime-button","media-rewind-button","media-seek-back-button","media-seek-forward-button","media-slider","media-sliderthumb","media-time-remaining-display","media-volume-slider","media-volume-slider-container","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menulist-text","menulist-textfield","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","multiple_mask_images","multiply","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","opacity","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","self-start","self-end","semi-condensed","semi-expanded","separate","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","somali","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","square-button","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"],Ae=We(bt),rt=pe.concat(dt).concat(P).concat(se).concat(X).concat(fe).concat(Y).concat(bt);F.registerHelper("hintWords","css",rt);function Ie(_,j){for(var k=!1,z;(z=_.next())!=null;){if(k&&z=="/"){j.tokenize=null;break}k=z=="*"}return["comment","comment"]}F.defineMIME("text/css",{documentTypes:Je,mediaTypes:Pe,mediaFeatures:I,mediaValueKeywords:re,propertyKeywords:J,nonStandardPropertyKeywords:Le,fontProperties:$,counterDescriptors:Q,colorKeywords:Te,valueKeywords:Ae,tokenHooks:{"/":function(_,j){return _.eat("*")?(j.tokenize=Ie,Ie(_,j)):!1}},name:"css"}),F.defineMIME("text/x-scss",{mediaTypes:Pe,mediaFeatures:I,mediaValueKeywords:re,propertyKeywords:J,nonStandardPropertyKeywords:Le,colorKeywords:Te,valueKeywords:Ae,fontProperties:$,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(_,j){return _.eat("/")?(_.skipToEnd(),["comment","comment"]):_.eat("*")?(j.tokenize=Ie,Ie(_,j)):["operator","operator"]},":":function(_){return _.match(/\s*\{/,!1)?[null,null]:!1},$:function(_){return _.match(/^[\w-]+/),_.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"]},"#":function(_){return _.eat("{")?[null,"interpolation"]:!1}},name:"css",helperType:"scss"}),F.defineMIME("text/x-less",{mediaTypes:Pe,mediaFeatures:I,mediaValueKeywords:re,propertyKeywords:J,nonStandardPropertyKeywords:Le,colorKeywords:Te,valueKeywords:Ae,fontProperties:$,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(_,j){return _.eat("/")?(_.skipToEnd(),["comment","comment"]):_.eat("*")?(j.tokenize=Ie,Ie(_,j)):["operator","operator"]},"@":function(_){return _.eat("{")?[null,"interpolation"]:_.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\b/i,!1)?!1:(_.eatWhile(/[\w\\\-]/),_.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"])},"&":function(){return["atom","atom"]}},name:"css",helperType:"less"}),F.defineMIME("text/x-gss",{documentTypes:Je,mediaTypes:Pe,mediaFeatures:I,propertyKeywords:J,nonStandardPropertyKeywords:Le,fontProperties:$,counterDescriptors:Q,colorKeywords:Te,valueKeywords:Ae,supportsAtComponent:!0,tokenHooks:{"/":function(_,j){return _.eat("*")?(j.tokenize=Ie,Ie(_,j)):!1}},name:"css",helperType:"gss"})})}()),ha.exports}(function(Zr,Dn){(function(F){F(An(),mu(),yu(),bu())})(function(F){var We={script:[["lang",/(javascript|babel)/i,"javascript"],["type",/^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i,"javascript"],["type",/./,"text/plain"],[null,null,"javascript"]],style:[["lang",/^css$/i,"css"],["type",/^(text\/)?(x-)?(stylesheet|css)$/i,"css"],["type",/./,"text/plain"],[null,null,"css"]]};function pe(re,X,J){var fe=re.current(),Le=fe.search(X);return Le>-1?re.backUp(fe.length-Le):fe.match(/<\/?$/)&&(re.backUp(fe.length),re.match(X,!1)||re.match(fe)),J}var Je={};function dt(re){var X=Je[re];return X||(Je[re]=new RegExp("\\s+"+re+`\\s*=\\s*('|")?([^'"]+)('|")?\\s*`))}function Pe(re,X){var J=re.match(dt(X));return J?/^\s*(.*?)\s*$/.exec(J[2])[1]:""}function P(re,X){return new RegExp((X?"^":"")+"</s*"+re+"s*>","i")}function I(re,X){for(var J in re)for(var fe=X[J]||(X[J]=[]),Le=re[J],ze=Le.length-1;ze>=0;ze--)fe.unshift(Le[ze])}function se(re,X){for(var J=0;J<re.length;J++){var fe=re[J];if(!fe[0]||fe[1].test(Pe(X,fe[0])))return fe[2]}}F.defineMode("htmlmixed",function(re,X){var J=F.getMode(re,{name:"xml",htmlMode:!0,multilineTagIndentFactor:X.multilineTagIndentFactor,multilineTagIndentPastTag:X.multilineTagIndentPastTag,allowMissingTagName:X.allowMissingTagName}),fe={},Le=X&&X.tags,ze=X&&X.scriptTypes;if(I(We,fe),Le&&I(Le,fe),ze)for(var $=ze.length-1;$>=0;$--)fe.script.unshift(["type",ze[$].matches,ze[$].mode]);function _e(Q,Y){var Te=J.token(Q,Y.htmlState),bt=/\btag\b/.test(Te),Ae;if(bt&&!/[<>\s\/]/.test(Q.current())&&(Ae=Y.htmlState.tagName&&Y.htmlState.tagName.toLowerCase())&&fe.hasOwnProperty(Ae))Y.inTag=Ae+" ";else if(Y.inTag&&bt&&/>$/.test(Q.current())){var rt=/^([\S]+) (.*)/.exec(Y.inTag);Y.inTag=null;var Ie=Q.current()==">"&&se(fe[rt[1]],rt[2]),_=F.getMode(re,Ie),j=P(rt[1],!0),k=P(rt[1],!1);Y.token=function(z,c){return z.match(j,!1)?(c.token=_e,c.localState=c.localMode=null,null):pe(z,k,c.localMode.token(z,c.localState))},Y.localMode=_,Y.localState=F.startState(_,J.indent(Y.htmlState,"",""))}else Y.inTag&&(Y.inTag+=Q.current(),Q.eol()&&(Y.inTag+=" "));return Te}return{startState:function(){var Q=F.startState(J);return{token:_e,inTag:null,localMode:null,localState:null,htmlState:Q}},copyState:function(Q){var Y;return Q.localState&&(Y=F.copyState(Q.localMode,Q.localState)),{token:Q.token,inTag:Q.inTag,localMode:Q.localMode,localState:Y,htmlState:F.copyState(J,Q.htmlState)}},token:function(Q,Y){return Y.token(Q,Y)},indent:function(Q,Y,Te){return!Q.localMode||/^\s*<\//.test(Y)?J.indent(Q.htmlState,Y,Te):Q.localMode.indent?Q.localMode.indent(Q.localState,Y,Te):F.Pass},innerMode:function(Q){return{state:Q.localState||Q.htmlState,mode:Q.localMode||J}}}},"xml","javascript","css"),F.defineMIME("text/html","htmlmixed")})})();export{wu as C};
