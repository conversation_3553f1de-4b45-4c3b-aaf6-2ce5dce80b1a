import{_ as O,C as d,y as v,u as b,c as g,a as f,h as q,l as P,m as T,F as S,r as I,b as j,o as w,j as D,t as N,q as R,$ as e,O as A,P as x,V as U,A as J}from"./main-Dzav4vK7.js";import{c as C,u as E}from"../graphs-PjETjWU7.js";import{C as F}from"../CommentBox-CTosYRj8.js";import"../echarts.common-RoIwlQp8.js";const z={name:"UserAddForm",props:{team_id:Number},data:function(){return{searchedName:"",awaitingSearch:!1,emptyResults:!1,userResults:[],selectedResultIdx:0,selectedUsers:[]}},methods:{searchUsers:function(){if(this.selectedResultIdx=0,this.searchedName==""){this.userResults=[];return}d.fetch(`/api/v1/users?view=admin&field=name&q=${this.searchedName}`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(t=>t.json()).then(t=>{t.success&&(this.userResults=t.data.slice(0,10))})},moveCursor:function(t){switch(t){case"up":this.selectedResultIdx&&(this.selectedResultIdx-=1);break;case"down":this.selectedResultIdx<this.userResults.length-1&&(this.selectedResultIdx+=1);break}},selectUser:function(t){t===void 0&&(t=this.selectedResultIdx);let s=this.userResults[t];this.selectedUsers.some(i=>i.id===s.id)===!1&&this.selectedUsers.push(s),this.userResults=[],this.searchedName=""},removeSelectedUser:function(t){this.selectedUsers=this.selectedUsers.filter(s=>s.id!==t)},handleAddUsersRequest:function(){let t=[];return this.selectedUsers.forEach(s=>{let a={user_id:s.id};t.push(d.fetch(`/api/v1/teams/${this.$props.team_id}/members`,{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(a)}))}),Promise.all(t)},handleRemoveUsersFromTeams:function(){let t=[];return this.selectedUsers.forEach(s=>{let a={user_id:s.id};t.push(d.fetch(`/api/v1/teams/${s.team_id}/members`,{method:"DELETE",body:JSON.stringify(a)}))}),Promise.all(t)},addUsers:function(){let t=[];if(this.selectedUsers.forEach(s=>{s.team_id&&t.push(s.name)}),t.length){let s=v(t.join(", "));b({title:"Confirm Team Removal",body:`The following users are currently in teams:<br><br> ${s} <br><br>Are you sure you want to remove them from their current teams and add them to this one? <br><br>All of their challenge solves, attempts, awards, and unlocked hints will also be deleted!`,success:()=>{this.handleRemoveUsersFromTeams().then(a=>{this.handleAddUsersRequest().then(i=>{window.location.reload()})})}})}else this.handleAddUsersRequest().then(s=>{window.location.reload()})}},watch:{searchedName:function(t){this.awaitingSearch===!1&&setTimeout(()=>{this.searchUsers(),this.awaitingSearch=!1},1e3),this.awaitingSearch=!0}}},B={class:"form-group"},V=f("label",null,"Search Users",-1),L={class:"form-group"},G=["onClick"],H={class:"form-group"},K={key:0,class:"text-center"},Q=f("span",{class:"text-muted"}," No users found ",-1),W=[Q],Y={class:"list-group"},X=["onClick"],Z={class:"form-group"};function ee(t,s,a,i,r,o){return w(),g("div",null,[f("div",B,[V,q(f("input",{type:"text",class:"form-control",placeholder:"Search for users","onUpdate:modelValue":s[0]||(s[0]=n=>t.searchedName=n),onKeyup:[s[1]||(s[1]=T(n=>o.moveCursor("down"),["down"])),s[2]||(s[2]=T(n=>o.moveCursor("up"),["up"])),s[3]||(s[3]=T(n=>o.selectUser(),["enter"]))]},null,544),[[P,t.searchedName]])]),f("div",L,[(w(!0),g(S,null,I(t.selectedUsers,n=>(w(),g("span",{class:"badge badge-primary mr-1",key:n.id},[D(N(n.name)+" ",1),f("a",{class:"btn-fa",onClick:c=>o.removeSelectedUser(n.id)}," ×",8,G)]))),128))]),f("div",H,[t.userResults.length==0&&this.searchedName!=""&&t.awaitingSearch==!1?(w(),g("div",K,W)):j("",!0),f("ul",Y,[(w(!0),g(S,null,I(t.userResults,(n,c)=>(w(),g("li",{class:R({"list-group-item":!0,active:c===t.selectedResultIdx}),key:n.id,onClick:y=>o.selectUser(c)},[D(N(n.name)+" ",1),n.team_id?(w(),g("small",{key:0,class:R({"float-right":!0,"text-white":c===t.selectedResultIdx,"text-muted":c!==t.selectedResultIdx})}," already in a team ",2)):j("",!0)],10,X))),128))])]),f("div",Z,[f("button",{class:"btn btn-success d-inline-block float-right",onClick:s[4]||(s[4]=n=>o.addUsers())}," Add Users ")])])}const te=O(z,[["render",ee]]);function se(t){t.preventDefault();const s=e("#team-info-create-form").serializeJSON(!0);s.fields=[];for(const a in s)if(a.match(/fields\[\d+\]/)){let i={},r=parseInt(a.slice(7,-1));i.field_id=r,i.value=s[a],s.fields.push(i),delete s[a]}d.fetch("/api/v1/teams",{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(s)}).then(function(a){return a.json()}).then(function(a){if(a.success){const i=a.data.id;window.location=d.config.urlRoot+"/admin/teams/"+i}else e("#team-info-create-form > #results").empty(),Object.keys(a.errors).forEach(function(i,r){e("#team-info-create-form > #results").append(A({type:"error",body:a.errors[i]}));const o=e("#team-info-create-form").find("input[name={0}]".format(i)),n=e(o);n.addClass("input-filled-invalid"),n.removeClass("input-filled-valid")})})}function ae(t){t.preventDefault();let s=e("#team-info-edit-form").serializeJSON(!0);s.fields=[];for(const a in s)if(a.match(/fields\[\d+\]/)){let i={},r=parseInt(a.slice(7,-1));i.field_id=r,i.value=s[a],s.fields.push(i),delete s[a]}d.fetch("/api/v1/teams/"+window.TEAM_ID,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(s)}).then(function(a){return a.json()}).then(function(a){a.success?window.location.reload():(e("#team-info-form > #results").empty(),Object.keys(a.errors).forEach(function(i,r){e("#team-info-form > #results").append(A({type:"error",body:a.errors[i]}));const o=e("#team-info-form").find("input[name={0}]".format(i)),n=e(o);n.addClass("input-filled-invalid"),n.removeClass("input-filled-valid")}))})}function ie(t){let a=e("input[data-submission-type=incorrect]:checked").map(function(){return e(this).data("submission-id")}),i=a.length===1?"submission":"submissions";b({title:"Correct Submissions",body:`Are you sure you want to mark ${a.length} ${i} correct?`,success:function(){const r=[];for(var o of a){let n=d.fetch(`/api/v1/submissions/${o}`,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify({type:"correct"})});r.push(n)}Promise.all(r).then(n=>{window.location.reload()})}})}function $(t,s){let a,i,r;switch(s){case"solves":a=e("input[data-submission-type=correct]:checked"),i="solve",r="Solves";break;case"fails":a=e("input[data-submission-type=incorrect]:checked"),i="fail",r="Fails";break}let o=a.map(function(){return e(this).data("submission-id")}),n=o.length===1?i:i+"s";b({title:`Delete ${r}`,body:`Are you sure you want to delete ${o.length} ${n}?`,success:function(){const c=[];for(var y of o)c.push(d.api.delete_submission({submissionId:y}));Promise.all(c).then(l=>{window.location.reload()})}})}function ne(t){let s=e("input[data-award-id]:checked").map(function(){return e(this).data("award-id")}),a=s.length===1?"award":"awards";b({title:"Delete Awards",body:`Are you sure you want to delete ${s.length} ${a}?`,success:function(){const i=[];for(var r of s){let o=d.fetch("/api/v1/awards/"+r,{method:"DELETE",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}});i.push(o)}Promise.all(i).then(o=>{window.location.reload()})}})}function oe(t){t.preventDefault();let s=e("input[data-missing-challenge-id]:checked").map(function(){return e(this).data("missing-challenge-id")}),a=s.length===1?"challenge":"challenges";b({title:"Mark Correct",body:`Are you sure you want to mark ${s.length} ${a} correct for ${v(window.TEAM_NAME)}?`,success:function(){J({title:"User Attribution",body:`
        Which user on ${v(window.TEAM_NAME)} solved these challenges?
        <div class="pb-3" id="query-team-member-solve">
        ${e("#team-member-select").html()}
        </div>
        `,button:"Mark Correct",success:function(){const i=e("#query-team-member-solve > select").val(),r=[];for(var o of s){let n={provided:"MARKED AS SOLVED BY ADMIN",user_id:i,team_id:window.TEAM_ID,challenge_id:o,type:"correct"},c=d.fetch("/api/v1/submissions",{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(n)});r.push(c)}Promise.all(r).then(n=>{window.location.reload()})}})}})}const M={team:[t=>d.api.get_team_solves({teamId:t}),t=>d.api.get_team_fails({teamId:t}),t=>d.api.get_team_awards({teamId:t})],user:[t=>d.api.get_user_solves({userId:t}),t=>d.api.get_user_fails({userId:t}),t=>d.api.get_user_awards({userId:t})]},re=(t,s,a,i)=>{let[r,o,n]=M[t];Promise.all([r(i),o(i),n(i)]).then(c=>{C("score_graph","#score-graph",c,t,s,a,i),C("category_breakdown","#categories-pie-graph",c,t,s,a,i),C("solve_percentages","#keys-pie-graph",c,t,s,a,i)})},le=(t,s,a,i)=>{let[r,o,n]=M[t];Promise.all([r(i),o(i),n(i)]).then(c=>{E("score_graph","#score-graph",c,t,s,a,i),E("category_breakdown","#categories-pie-graph",c,t,s,a,i),E("solve_percentages","#keys-pie-graph",c,t,s,a,i)})};e(()=>{e("#team-captain-form").submit(function(l){l.preventDefault();const m=e("#team-captain-form").serializeJSON(!0);d.fetch("/api/v1/teams/"+window.TEAM_ID,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(m)}).then(function(u){return u.json()}).then(function(u){u.success?window.location.reload():(e("#team-captain-form > #results").empty(),Object.keys(u.errors).forEach(function(p,k){e("#team-captain-form > #results").append(A({type:"error",body:u.errors[p]}));const h=e("#team-captain-form").find("select[name={0}]".format(p)),_=e(h);_.addClass("input-filled-invalid"),_.removeClass("input-filled-valid")}))})}),e(".edit-team").click(function(l){e("#team-info-edit-modal").modal("toggle")}),e(".invite-team").click(function(l){d.fetch(`/api/v1/teams/${window.TEAM_ID}/members`,{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(function(m){return m.json()}).then(function(m){if(m.success){let u=m.data.code,p=`${window.location.origin}${d.config.urlRoot}/teams/invite?code=${u}`;e("#team-invite-modal input[name=link]").val(p),e("#team-invite-modal").modal("toggle")}})}),e("#team-invite-link-copy").click(function(l){x(l,"#team-invite-link")}),e(".members-team").click(function(l){e("#team-add-modal").modal("toggle")}),e(".edit-captain").click(function(l){e("#team-captain-modal").modal("toggle")}),e(".award-team").click(function(l){e("#team-award-modal").modal("toggle")}),e(".addresses-team").click(function(l){e("#team-addresses-modal").modal("toggle")}),e("#user-award-form").submit(function(l){l.preventDefault();const m=e("#user-award-form").serializeJSON(!0);if(m.user_id=e("#award-member-input").val(),m.team_id=window.TEAM_ID,e("#user-award-form > #results").empty(),!m.user_id){e("#user-award-form > #results").append(A({type:"error",body:"Please select a team member"}));return}m.user_id=parseInt(m.user_id),d.fetch("/api/v1/awards",{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(m)}).then(function(u){return u.json()}).then(function(u){u.success?window.location.reload():(e("#user-award-form > #results").empty(),Object.keys(u.errors).forEach(function(p,k){e("#user-award-form > #results").append(A({type:"error",body:u.errors[p]}));const h=e("#user-award-form").find("input[name={0}]".format(p)),_=e(h);_.addClass("input-filled-invalid"),_.removeClass("input-filled-valid")}))})}),e(".delete-member").click(function(l){l.preventDefault();const m=e(this).attr("member-id"),u=e(this).attr("member-name"),p={user_id:m},k=e(this).parent().parent();b({title:"Remove Member",body:"Are you sure you want to remove {0} from {1}? <br><br><strong>All of their challenge solves, attempts, awards, and unlocked hints will also be deleted!</strong>".format("<strong>"+v(u)+"</strong>","<strong>"+v(window.TEAM_NAME)+"</strong>"),success:function(){d.fetch("/api/v1/teams/"+window.TEAM_ID+"/members",{method:"DELETE",body:JSON.stringify(p)}).then(function(h){return h.json()}).then(function(h){h.success&&k.remove()})}})}),e(".delete-team").click(function(l){b({title:"Delete Team",body:"Are you sure you want to delete {0}".format("<strong>"+v(window.TEAM_NAME)+"</strong>"),success:function(){d.fetch("/api/v1/teams/"+window.TEAM_ID,{method:"DELETE"}).then(function(m){return m.json()}).then(function(m){m.success&&(window.location=d.config.urlRoot+"/admin/teams")})}})}),e("#solves-delete-button").click(function(l){$(l,"solves")}),e("#correct-fail-button").click(ie),e("#fails-delete-button").click(function(l){$(l,"fails")}),e("#awards-delete-button").click(function(l){ne()}),e("#missing-solve-button").click(function(l){oe(l)}),e("#team-info-create-form").submit(se),e("#team-info-edit-form").submit(ae);const t=U.extend(F);let s=document.createElement("div");document.querySelector("#comment-box").appendChild(s),new t({propsData:{type:"team",id:window.TEAM_ID}}).$mount(s);const a=U.extend(te);let i=document.createElement("div");document.querySelector("#team-add-modal .modal-body").appendChild(i),new a({propsData:{team_id:window.TEAM_ID}}).$mount(i);let r,o,n,c;({type:r,id:o,name:n,account_id:c}=window.stats_data);let y;e("#team-statistics-modal").on("shown.bs.modal",function(l){re(r,o,n,c),y=setInterval(()=>{le(r,o,n,c)},3e5)}),e("#team-statistics-modal").on("hidden.bs.modal",function(l){clearInterval(y)}),e(".statistics-team").click(function(l){e("#team-statistics-modal").modal("toggle")})});
