import{_ as _export_sfc,$ as $$1,C as CTFd$1,n as nunjucks,o as openBlock,c as createElementBlock,a as createBaseVNode,F as Fragment,r as renderList,t as toDisplayString,w as withModifiers,b as createCommentVNode,d as createStaticVNode,e as resolveComponent,f as createVNode,g as withCtx,T as TransitionGroup,h as withDirectives,v as vModelSelect,i as vModelCheckbox,j as createTextVNode,p as pushScopeId,k as popScopeId,l as vModelText,m as withKeys,q as normalizeClass,s as helpers,u as ezQuery,x as bindMarkdownEditor,y as htmlEntities,V as Vue$1,z as bindMarkdownEditors,A as ezAlert,B as ezToast}from"./main-Dzav4vK7.js";import"../tab-DSoo2THN.js";import{C as CommentBox}from"../CommentBox-CTosYRj8.js";const _sfc_main$b={name:"FlagCreationForm",props:{challenge_id:Number},data:function(){return{types:{},selectedType:null,createForm:""}},methods:{selectType:function(event){let flagType=event.target.value;if(this.types[flagType]===void 0){this.selectedType=null,this.createForm="";return}let createFormURL=this.types[flagType].templates.create;$$1.get(CTFd$1.config.urlRoot+createFormURL,template_data=>{const template=nunjucks.compile(template_data);this.selectedType=flagType,this.createForm=template.render(),this.createForm.includes("<script")&&setTimeout(()=>{$$1("<div>"+this.createForm+"</div>").find("script").each(function(){eval($$1(this).html())})},100)})},loadTypes:function(){CTFd$1.fetch("/api/v1/flags/types",{method:"GET"}).then(e=>e.json()).then(e=>{this.types=e.data})},submitFlag:function(e){let o=$$1(e.target).serializeJSON(!0);o.challenge=this.$props.challenge_id,CTFd$1.fetch("/api/v1/flags",{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(o)}).then(n=>n.json()).then(n=>{this.$emit("refreshFlags",this.$options.name)})}},created(){this.loadTypes()}},_hoisted_1$b={id:"flag-create-modal",class:"modal fade",tabindex:"-1"},_hoisted_2$b={class:"modal-dialog modal-lg"},_hoisted_3$b={class:"modal-content"},_hoisted_4$b=createStaticVNode('<div class="modal-header text-center"><div class="container"><div class="row"><div class="col-md-12"><h3>Create Flag</h3></div></div></div><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button></div>',1),_hoisted_5$b={class:"modal-body"},_hoisted_6$a={class:"create-keys-select-div"},_hoisted_7$8=createBaseVNode("label",{for:"create-keys-select",class:"control-label"}," Choose Flag Type ",-1),_hoisted_8$8=createBaseVNode("option",null,"--",-1),_hoisted_9$8=["value"],_hoisted_10$7=createBaseVNode("br",null,null,-1),_hoisted_11$5=["innerHTML"],_hoisted_12$4={key:0,class:"btn btn-success float-right",type:"submit"};function _sfc_render$b(e,t,o,n,a,i){return openBlock(),createElementBlock("div",_hoisted_1$b,[createBaseVNode("div",_hoisted_2$b,[createBaseVNode("div",_hoisted_3$b,[_hoisted_4$b,createBaseVNode("div",_hoisted_5$b,[createBaseVNode("div",_hoisted_6$a,[_hoisted_7$8,createBaseVNode("select",{class:"form-control custom-select",onChange:t[0]||(t[0]=s=>i.selectType(s))},[_hoisted_8$8,(openBlock(!0),createElementBlock(Fragment,null,renderList(Object.keys(e.types),s=>(openBlock(),createElementBlock("option",{value:s,key:s},toDisplayString(s),9,_hoisted_9$8))),128))],32)]),_hoisted_10$7,createBaseVNode("form",{onSubmit:t[1]||(t[1]=withModifiers((...s)=>i.submitFlag&&i.submitFlag(...s),["prevent"]))},[createBaseVNode("div",{id:"create-flag-form",innerHTML:e.createForm},null,8,_hoisted_11$5),e.createForm?(openBlock(),createElementBlock("button",_hoisted_12$4," Create Flag ")):createCommentVNode("",!0)],32)])])])])}const FlagCreationForm=_export_sfc(_sfc_main$b,[["render",_sfc_render$b]]),_sfc_main$a={name:"FlagEditForm",props:{flag_id:Number},data:function(){return{flag:{},editForm:""}},watch:{flag_id:{immediate:!0,handler(e,t){e!==null&&this.loadFlag()}}},methods:{loadFlag:function(){CTFd$1.fetch(`/api/v1/flags/${this.$props.flag_id}`,{method:"GET"}).then(e=>e.json()).then(response=>{this.flag=response.data;let editFormURL=this.flag.templates.update;$$1.get(CTFd$1.config.urlRoot+editFormURL,template_data=>{const template=nunjucks.compile(template_data);this.editForm=template.render(this.flag),this.editForm.includes("<script")&&setTimeout(()=>{$$1("<div>"+this.editForm+"</div>").find("script").each(function(){eval($$1(this).html())})},100)})})},updateFlag:function(e){let o=$$1(e.target).serializeJSON(!0);CTFd$1.fetch(`/api/v1/flags/${this.$props.flag_id}`,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(o)}).then(n=>n.json()).then(n=>{this.$emit("refreshFlags",this.$options.name)})}},mounted(){this.flag_id&&this.loadFlag()},created(){this.flag_id&&this.loadFlag()}},_hoisted_1$a={id:"flag-edit-modal",class:"modal fade",tabindex:"-1"},_hoisted_2$a={class:"modal-dialog modal-lg"},_hoisted_3$a={class:"modal-content"},_hoisted_4$a=createStaticVNode('<div class="modal-header text-center"><div class="container"><div class="row"><div class="col-md-12"><h3 class="text-center">Edit Flag</h3></div></div></div><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button></div>',1),_hoisted_5$a={class:"modal-body"},_hoisted_6$9=["innerHTML"];function _sfc_render$a(e,t,o,n,a,i){return openBlock(),createElementBlock("div",_hoisted_1$a,[createBaseVNode("div",_hoisted_2$a,[createBaseVNode("div",_hoisted_3$a,[_hoisted_4$a,createBaseVNode("div",_hoisted_5$a,[createBaseVNode("form",{method:"POST",innerHTML:e.editForm,onSubmit:t[0]||(t[0]=withModifiers((...s)=>i.updateFlag&&i.updateFlag(...s),["prevent"]))},null,40,_hoisted_6$9)])])])])}const FlagEditForm=_export_sfc(_sfc_main$a,[["render",_sfc_render$a]]),_sfc_main$9={components:{FlagCreationForm,FlagEditForm},props:{challenge_id:Number},data:function(){return{flags:[],editing_flag_id:null}},methods:{loadFlags:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}/flags`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.flags=e.data)})},refreshFlags(e){this.loadFlags();let t;switch(e){case"FlagEditForm":t=this.$refs.FlagEditForm.$el,$$1(t).modal("hide");break;case"FlagCreationForm":t=this.$refs.FlagCreationForm.$el,$$1(t).modal("hide");break}},addFlag:function(){let e=this.$refs.FlagCreationForm.$el;$$1(e).modal()},editFlag:function(e){this.editing_flag_id=e;let t=this.$refs.FlagEditForm.$el;$$1(t).modal()},deleteFlag:function(e){confirm("Are you sure you'd like to delete this flag?")&&CTFd$1.fetch(`/api/v1/flags/${e}`,{method:"DELETE"}).then(t=>t.json()).then(t=>{t.success&&this.loadFlags()})}},created(){this.loadFlags()}},_hoisted_1$9={id:"flagsboard",class:"table table-striped"},_hoisted_2$9=createBaseVNode("thead",null,[createBaseVNode("tr",null,[createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Type")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Flag")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Settings")])])],-1),_hoisted_3$9=["name"],_hoisted_4$9={class:"text-center"},_hoisted_5$9={class:"text-break"},_hoisted_6$8={class:"flag-content"},_hoisted_7$7={class:"text-center"},_hoisted_8$7=["flag-id","flag-type","onClick"],_hoisted_9$7=["flag-id","onClick"],_hoisted_10$6={class:"col-md-12"};function _sfc_render$9(e,t,o,n,a,i){const s=resolveComponent("FlagCreationForm"),d=resolveComponent("FlagEditForm");return openBlock(),createElementBlock("div",null,[createBaseVNode("div",null,[createVNode(s,{ref:"FlagCreationForm",challenge_id:o.challenge_id,onRefreshFlags:i.refreshFlags},null,8,["challenge_id","onRefreshFlags"])]),createBaseVNode("div",null,[createVNode(d,{ref:"FlagEditForm",flag_id:e.editing_flag_id,onRefreshFlags:i.refreshFlags},null,8,["flag_id","onRefreshFlags"])]),createBaseVNode("table",_hoisted_1$9,[_hoisted_2$9,createBaseVNode("tbody",null,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.flags,l=>(openBlock(),createElementBlock("tr",{name:l.id,key:l.id},[createBaseVNode("td",_hoisted_4$9,toDisplayString(l.type),1),createBaseVNode("td",_hoisted_5$9,[createBaseVNode("pre",_hoisted_6$8,toDisplayString(l.content),1)]),createBaseVNode("td",_hoisted_7$7,[createBaseVNode("i",{role:"button",class:"btn-fa fas fa-edit edit-flag","flag-id":l.id,"flag-type":l.type,onClick:r=>i.editFlag(l.id)},null,8,_hoisted_8$7),createBaseVNode("i",{role:"button",class:"btn-fa fas fa-times delete-flag","flag-id":l.id,onClick:r=>i.deleteFlag(l.id)},null,8,_hoisted_9$7)])],8,_hoisted_3$9))),128))])]),createBaseVNode("div",_hoisted_10$6,[createBaseVNode("button",{id:"flag-add-button",class:"btn btn-success d-inline-block float-right",onClick:t[0]||(t[0]=l=>i.addFlag())}," Create Flag ")])])}const FlagList=_export_sfc(_sfc_main$9,[["render",_sfc_render$9]]),_sfc_main$8={props:{challenge_id:Number},data:function(){return{challenges:[],requirements:{},selectedRequirements:[],selectedAnonymize:!1}},computed:{newRequirements:function(){let e=this.requirements.prerequisites||[],t=this.requirements.anonymize||!1,o=JSON.stringify(e.sort())!==JSON.stringify(this.selectedRequirements.sort()),n=t!==this.selectedAnonymize;return o||n},requiredChallenges:function(){const e=this.requirements.prerequisites||[];return this.challenges.filter(t=>t.id!==this.$props.challenge_id&&e.includes(t.id))},otherChallenges:function(){const e=this.requirements.prerequisites||[];return this.challenges.filter(t=>t.id!==this.$props.challenge_id&&!e.includes(t.id))}},methods:{loadChallenges:function(){CTFd$1.fetch("/api/v1/challenges?view=admin",{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.challenges=e.data)})},getChallengeNameById:function(e){let t=this.challenges.find(o=>o.id===e);return t?t.name:""},loadRequirements:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}/requirements`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.requirements=e.data||{},this.selectedRequirements=this.requirements.prerequisites||[],this.selectedAnonymize=this.requirements.anonymize||!1)})},updateRequirements:function(){const t={requirements:{prerequisites:this.selectedRequirements}};this.selectedAnonymize&&(t.requirements.anonymize=!0),CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}`,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(t)}).then(o=>o.json()).then(o=>{o.success&&this.loadRequirements()})}},created(){this.loadChallenges(),this.loadRequirements()}},_withScopeId=e=>(pushScopeId("data-v-a3fa86b4"),e=e(),popScopeId(),e),_hoisted_1$8={class:"form-group scrollbox"},_hoisted_2$8={class:"form-check-label cursor-pointer"},_hoisted_3$8=["value"],_hoisted_4$8={class:"form-check-label cursor-pointer"},_hoisted_5$8=["value"],_hoisted_6$7={class:"form-group"},_hoisted_7$6=_withScopeId(()=>createBaseVNode("label",null,[createBaseVNode("b",null,"Behavior if not unlocked")],-1)),_hoisted_8$6=_withScopeId(()=>createBaseVNode("option",{value:!1},"Hidden",-1)),_hoisted_9$6=_withScopeId(()=>createBaseVNode("option",{value:!0},"Anonymized",-1)),_hoisted_10$5=[_hoisted_8$6,_hoisted_9$6],_hoisted_11$4={class:"form-group"},_hoisted_12$3=["disabled"];function _sfc_render$8(e,t,o,n,a,i){return openBlock(),createElementBlock("div",null,[createBaseVNode("form",{onSubmit:t[3]||(t[3]=withModifiers((...s)=>i.updateRequirements&&i.updateRequirements(...s),["prevent"]))},[createBaseVNode("div",_hoisted_1$8,[createVNode(TransitionGroup,{name:"flip-list"},{default:withCtx(()=>[(openBlock(!0),createElementBlock(Fragment,null,renderList(i.requiredChallenges,s=>(openBlock(),createElementBlock("div",{class:"form-check",key:s.id},[createBaseVNode("label",_hoisted_2$8,[withDirectives(createBaseVNode("input",{class:"form-check-input",type:"checkbox",value:s.id,"onUpdate:modelValue":t[0]||(t[0]=d=>e.selectedRequirements=d)},null,8,_hoisted_3$8),[[vModelCheckbox,e.selectedRequirements]]),createTextVNode(" "+toDisplayString(s.name),1)])]))),128)),(openBlock(!0),createElementBlock(Fragment,null,renderList(i.otherChallenges,s=>(openBlock(),createElementBlock("div",{class:"form-check",key:s.id},[createBaseVNode("label",_hoisted_4$8,[withDirectives(createBaseVNode("input",{class:"form-check-input",type:"checkbox",value:s.id,"onUpdate:modelValue":t[1]||(t[1]=d=>e.selectedRequirements=d)},null,8,_hoisted_5$8),[[vModelCheckbox,e.selectedRequirements]]),createTextVNode(" "+toDisplayString(s.name),1)])]))),128))]),_:1})]),createBaseVNode("div",_hoisted_6$7,[_hoisted_7$6,withDirectives(createBaseVNode("select",{class:"form-control custom-select",name:"anonymize","onUpdate:modelValue":t[2]||(t[2]=s=>e.selectedAnonymize=s)},_hoisted_10$5,512),[[vModelSelect,e.selectedAnonymize]])]),createBaseVNode("div",_hoisted_11$4,[createBaseVNode("button",{class:"btn btn-success float-right",disabled:!i.newRequirements}," Save ",8,_hoisted_12$3)])],32)])}const Requirements=_export_sfc(_sfc_main$8,[["render",_sfc_render$8],["__scopeId","data-v-a3fa86b4"]]),_sfc_main$7={props:{challenge_id:Number},data:function(){return{topics:[],topicValue:"",searchedTopic:"",topicResults:[],selectedResultIdx:0,awaitingSearch:!1}},methods:{loadTopics:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}/topics`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.topics=e.data)})},searchTopics:function(){if(this.selectedResultIdx=0,this.topicValue==""){this.topicResults=[];return}CTFd$1.fetch(`/api/v1/topics?field=value&q=${this.topicValue}`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.topicResults=e.data.slice(0,10))})},addTopic:function(){let e;if(this.selectedResultIdx===0)e=this.topicValue;else{let o=this.selectedResultIdx-1;e=this.topicResults[o].value}const t={value:e,challenge:this.$props.challenge_id,type:"challenge"};CTFd$1.fetch("/api/v1/topics",{method:"POST",body:JSON.stringify(t)}).then(o=>o.json()).then(o=>{o.success&&(this.topicValue="",this.loadTopics())})},deleteTopic:function(e){CTFd$1.fetch(`/api/v1/topics?type=challenge&target_id=${e}`,{method:"DELETE"}).then(t=>t.json()).then(t=>{t.success&&this.loadTopics()})},moveCursor:function(e){switch(e){case"up":this.selectedResultIdx&&(this.selectedResultIdx-=1);break;case"down":this.selectedResultIdx<this.topicResults.length&&(this.selectedResultIdx+=1);break}},selectTopic:function(e){e===void 0&&(e=this.selectedResultIdx);let t=this.topicResults[e];this.topicValue=t.value}},watch:{topicValue:function(e){this.awaitingSearch===!1&&setTimeout(()=>{this.searchTopics(),this.awaitingSearch=!1},500),this.awaitingSearch=!0}},created(){this.loadTopics()}},_hoisted_1$7={class:"col-md-12"},_hoisted_2$7={id:"challenge-topics",class:"my-3"},_hoisted_3$7={class:"mr-1"},_hoisted_4$7=["onClick"],_hoisted_5$7={class:"form-group"},_hoisted_6$6=createBaseVNode("label",null,[createTextVNode(" Topic "),createBaseVNode("br"),createBaseVNode("small",{class:"text-muted"},"Type topic and press Enter")],-1),_hoisted_7$5={class:"form-group"},_hoisted_8$5={class:"list-group"},_hoisted_9$5=["onClick"];function _sfc_render$7(e,t,o,n,a,i){return openBlock(),createElementBlock("div",_hoisted_1$7,[createBaseVNode("div",_hoisted_2$7,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.topics,s=>(openBlock(),createElementBlock("h5",{class:"challenge-tag",key:s.id},[createBaseVNode("span",_hoisted_3$7,toDisplayString(s.value),1),createBaseVNode("a",{class:"btn-fa delete-tag",onClick:d=>i.deleteTopic(s.id)}," ×",8,_hoisted_4$7)]))),128))]),createBaseVNode("div",_hoisted_5$7,[_hoisted_6$6,withDirectives(createBaseVNode("input",{id:"tags-add-input",maxlength:"255",type:"text",class:"form-control","onUpdate:modelValue":t[0]||(t[0]=s=>e.topicValue=s),onKeyup:[t[1]||(t[1]=withKeys(s=>i.moveCursor("down"),["down"])),t[2]||(t[2]=withKeys(s=>i.moveCursor("up"),["up"])),t[3]||(t[3]=withKeys(s=>i.addTopic(),["enter"]))]},null,544),[[vModelText,e.topicValue]])]),createBaseVNode("div",_hoisted_7$5,[createBaseVNode("ul",_hoisted_8$5,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.topicResults,(s,d)=>(openBlock(),createElementBlock("li",{class:normalizeClass({"list-group-item":!0,active:d+1===e.selectedResultIdx}),key:s.id,onClick:l=>i.selectTopic(d)},toDisplayString(s.value),11,_hoisted_9$5))),128))])])])}const TopicsList=_export_sfc(_sfc_main$7,[["render",_sfc_render$7]]),_sfc_main$6={props:{challenge_id:Number},data:function(){return{tags:[],tagValue:""}},methods:{loadTags:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}/tags`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.tags=e.data)})},addTag:function(){if(this.tagValue){const e={value:this.tagValue,challenge:this.$props.challenge_id};CTFd$1.api.post_tag_list({},e).then(t=>{t.success&&(this.tagValue="",this.loadTags())})}},deleteTag:function(e){CTFd$1.api.delete_tag({tagId:e}).then(t=>{t.success&&this.loadTags()})}},created(){this.loadTags()}},_hoisted_1$6={class:"col-md-12"},_hoisted_2$6={id:"challenge-tags",class:"my-3"},_hoisted_3$6=["onClick"],_hoisted_4$6={class:"form-group"},_hoisted_5$6=createBaseVNode("label",null,[createTextVNode("Tag "),createBaseVNode("br"),createBaseVNode("small",{class:"text-muted"},"Type tag and press Enter")],-1);function _sfc_render$6(e,t,o,n,a,i){return openBlock(),createElementBlock("div",_hoisted_1$6,[createBaseVNode("div",_hoisted_2$6,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.tags,s=>(openBlock(),createElementBlock("span",{class:"badge badge-primary mx-1 challenge-tag",key:s.id},[createBaseVNode("span",null,toDisplayString(s.value),1),createBaseVNode("a",{class:"btn-fa delete-tag",onClick:d=>i.deleteTag(s.id)}," ×",8,_hoisted_3$6)]))),128))]),createBaseVNode("div",_hoisted_4$6,[_hoisted_5$6,withDirectives(createBaseVNode("input",{id:"tags-add-input",maxlength:"80",type:"text",class:"form-control","onUpdate:modelValue":t[0]||(t[0]=s=>e.tagValue=s),onKeyup:t[1]||(t[1]=withKeys(s=>i.addTag(),["enter"]))},null,544),[[vModelText,e.tagValue]])])])}const TagsList=_export_sfc(_sfc_main$6,[["render",_sfc_render$6]]),_sfc_main$5={props:{challenge_id:Number},data:function(){return{files:[],urlRoot:CTFd$1.config.urlRoot}},methods:{loadFiles:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}/files`,{method:"GET"}).then(e=>e.json()).then(e=>{e.success&&(this.files=e.data)})},addFiles:function(){let e={challenge:this.$props.challenge_id,type:"challenge"},t=this.$refs.FileUploadForm;helpers.files.upload(t,e,o=>{setTimeout(()=>{this.loadFiles()},700)})},deleteFile:function(e){ezQuery({title:"Delete Files",body:"Are you sure you want to delete this file?",success:()=>{CTFd$1.fetch(`/api/v1/files/${e}`,{method:"DELETE"}).then(t=>t.json()).then(t=>{t.success&&this.loadFiles()})}})}},created(){this.loadFiles()}},_hoisted_1$5={id:"filesboard",class:"table table-striped"},_hoisted_2$5=createBaseVNode("thead",null,[createBaseVNode("tr",null,[createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"File")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Settings")])])],-1),_hoisted_3$5={class:"text-left"},_hoisted_4$5=["href"],_hoisted_5$5={class:"d-flex flex-row align-items-center"},_hoisted_6$5=createBaseVNode("strong",{class:"mr-2 small"}," SHA1: ",-1),_hoisted_7$4={class:"d-inline-block mr-2 small text-muted"},_hoisted_8$4={class:"text-center"},_hoisted_9$4=["onClick"],_hoisted_10$4={class:"col-md-12 mt-3"},_hoisted_11$3=createStaticVNode('<div class="form-group"><input class="form-control-file" id="file" multiple="" name="file" required="" type="file"><sub class="text-muted"> Attach multiple files using Control+Click or Cmd+Click. </sub></div><div class="form-group"><input class="btn btn-success float-right" id="_submit" name="_submit" type="submit" value="Upload"></div>',2),_hoisted_13$2=[_hoisted_11$3];function _sfc_render$5(e,t,o,n,a,i){return openBlock(),createElementBlock("div",null,[createBaseVNode("table",_hoisted_1$5,[_hoisted_2$5,createBaseVNode("tbody",null,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.files,s=>(openBlock(),createElementBlock("tr",{key:s.id},[createBaseVNode("td",_hoisted_3$5,[createBaseVNode("a",{href:`${e.urlRoot}/files/${s.location}`},toDisplayString(s.location.split("/").pop()),9,_hoisted_4$5),createBaseVNode("div",_hoisted_5$5,[_hoisted_6$5,createBaseVNode("span",_hoisted_7$4,toDisplayString(s.sha1sum||"null"),1)])]),createBaseVNode("td",_hoisted_8$4,[createBaseVNode("i",{role:"button",class:"btn-fa fas fa-times delete-file",onClick:d=>i.deleteFile(s.id)},null,8,_hoisted_9$4)])]))),128))])]),createBaseVNode("div",_hoisted_10$4,[createBaseVNode("form",{method:"POST",ref:"FileUploadForm",onSubmit:t[0]||(t[0]=withModifiers((...s)=>i.addFiles&&i.addFiles(...s),["prevent"]))},_hoisted_13$2,544)])])}const ChallengeFilesList=_export_sfc(_sfc_main$5,[["render",_sfc_render$5]]),_sfc_main$4={name:"HintCreationForm",props:{challenge_id:Number,hints:Array},data:function(){return{cost:0,selectedHints:[]}},methods:{clearForm:function(){this.$refs.title.value="",this.$refs.content&&this.$refs.content.mde&&(this.$refs.content.mde.value(""),this.$refs.content.mde.codemirror.refresh()),this.$refs.content.value="",this.cost=0,this.selectedHints=[]},getCost:function(){return this.cost||0},getContent:function(){return this.$refs.content.value},getTitle:function(){return this.$refs.title.value},submitHint:function(){let e={challenge_id:this.$props.challenge_id,content:this.getContent(),cost:this.getCost(),title:this.getTitle(),requirements:{prerequisites:this.selectedHints}};CTFd.fetch("/api/v1/hints",{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(e)}).then(t=>t.json()).then(t=>{t.success&&(this.$emit("refreshHints",this.$options.name),this.clearForm())})}}},_hoisted_1$4={class:"modal fade",tabindex:"-1"},_hoisted_2$4={class:"modal-dialog"},_hoisted_3$4={class:"modal-content"},_hoisted_4$4=createStaticVNode('<div class="modal-header text-center"><div class="container"><div class="row"><div class="col-md-12"><h3>Hint</h3></div></div></div><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button></div>',1),_hoisted_5$4={class:"modal-body"},_hoisted_6$4={class:"container"},_hoisted_7$3={class:"row"},_hoisted_8$3={class:"col-md-12"},_hoisted_9$3={class:"form-group"},_hoisted_10$3=createBaseVNode("label",null,[createTextVNode(" Title"),createBaseVNode("br"),createBaseVNode("small",null,"Content displayed before hint unlocking")],-1),_hoisted_11$2={type:"text",class:"form-control",name:"title",ref:"title"},_hoisted_12$2={class:"form-group"},_hoisted_13$1=createBaseVNode("label",null,[createTextVNode(" Hint"),createBaseVNode("br"),createBaseVNode("small",null,"Markdown & HTML are supported")],-1),_hoisted_14$1={type:"text",class:"form-control markdown",name:"content",rows:"7",ref:"content"},_hoisted_15$1={class:"form-group"},_hoisted_16$1=createBaseVNode("label",null,[createTextVNode(" Cost"),createBaseVNode("br"),createBaseVNode("small",null,"How many points it costs to see your hint.")],-1),_hoisted_17$1={class:"form-group"},_hoisted_18$1=createBaseVNode("label",null,[createTextVNode(" Requirements"),createBaseVNode("br"),createBaseVNode("small",null,"Hints that must be unlocked before unlocking this hint")],-1),_hoisted_19$1={class:"form-check-label cursor-pointer"},_hoisted_20$1=["value"],_hoisted_21$1=createBaseVNode("input",{type:"hidden",id:"hint-id-for-hint",name:"id"},null,-1),_hoisted_22=createStaticVNode('<div class="modal-footer"><div class="container"><div class="row"><div class="col-md-12"><button class="btn btn-primary float-right">Submit</button></div></div></div></div>',1);function _sfc_render$4(e,t,o,n,a,i){return openBlock(),createElementBlock("div",_hoisted_1$4,[createBaseVNode("div",_hoisted_2$4,[createBaseVNode("div",_hoisted_3$4,[_hoisted_4$4,createBaseVNode("form",{method:"POST",onSubmit:t[2]||(t[2]=withModifiers((...s)=>i.submitHint&&i.submitHint(...s),["prevent"]))},[createBaseVNode("div",_hoisted_5$4,[createBaseVNode("div",_hoisted_6$4,[createBaseVNode("div",_hoisted_7$3,[createBaseVNode("div",_hoisted_8$3,[createBaseVNode("div",_hoisted_9$3,[_hoisted_10$3,createBaseVNode("input",_hoisted_11$2,null,512)]),createBaseVNode("div",_hoisted_12$2,[_hoisted_13$1,createBaseVNode("textarea",_hoisted_14$1,null,512)]),createBaseVNode("div",_hoisted_15$1,[_hoisted_16$1,withDirectives(createBaseVNode("input",{type:"number",class:"form-control",name:"cost","onUpdate:modelValue":t[0]||(t[0]=s=>e.cost=s)},null,512),[[vModelText,e.cost,void 0,{lazy:!0}]])]),createBaseVNode("div",_hoisted_17$1,[_hoisted_18$1,(openBlock(!0),createElementBlock(Fragment,null,renderList(o.hints,s=>(openBlock(),createElementBlock("div",{class:"form-check",key:s.id},[createBaseVNode("label",_hoisted_19$1,[withDirectives(createBaseVNode("input",{class:"form-check-input",type:"checkbox",value:s.id,"onUpdate:modelValue":t[1]||(t[1]=d=>e.selectedHints=d)},null,8,_hoisted_20$1),[[vModelCheckbox,e.selectedHints]]),createTextVNode(" "+toDisplayString(s.cost)+" - "+toDisplayString(s.id),1)])]))),128))]),_hoisted_21$1])])])]),_hoisted_22],32)])])])}const HintCreationForm=_export_sfc(_sfc_main$4,[["render",_sfc_render$4]]),_sfc_main$3={name:"HintEditForm",props:{challenge_id:Number,hint_id:Number,hints:Array},data:function(){return{cost:0,title:null,content:null,selectedHints:[]}},computed:{otherHints:function(){return this.hints.filter(e=>e.id!==this.$props.hint_id)}},watch:{hint_id:{immediate:!0,handler(e,t){e!==null&&this.loadHint()}}},methods:{loadHint:function(){CTFd$1.fetch(`/api/v1/hints/${this.$props.hint_id}?preview=true`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{var t;if(e.success){let o=e.data;this.cost=o.cost,this.title=o.title,this.content=o.content,this.selectedHints=((t=o.requirements)==null?void 0:t.prerequisites)||[];let n=this.$refs.content;bindMarkdownEditor(n),setTimeout(()=>{n.mde.codemirror.getDoc().setValue(n.value),this._forceRefresh()},200)}})},_forceRefresh:function(){this.$refs.content.mde.codemirror.refresh()},getCost:function(){return this.cost||0},getContent:function(){return this._forceRefresh(),this.$refs.content.mde.codemirror.getDoc().getValue()},getTitle:function(){return this.$refs.title.value},updateHint:function(){let e={challenge_id:this.$props.challenge_id,content:this.getContent(),cost:this.getCost(),title:this.getTitle(),requirements:{prerequisites:this.selectedHints}};CTFd$1.fetch(`/api/v1/hints/${this.$props.hint_id}`,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(e)}).then(t=>t.json()).then(t=>{t.success&&this.$emit("refreshHints",this.$options.name)})}},mounted(){this.hint_id&&this.loadHint()},created(){this.hint_id&&this.loadHint()}},_hoisted_1$3={class:"modal fade",tabindex:"-1"},_hoisted_2$3={class:"modal-dialog"},_hoisted_3$3={class:"modal-content"},_hoisted_4$3=createStaticVNode('<div class="modal-header text-center"><div class="container"><div class="row"><div class="col-md-12"><h3>Hint</h3></div></div></div><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button></div>',1),_hoisted_5$3={class:"modal-body"},_hoisted_6$3={class:"container"},_hoisted_7$2={class:"row"},_hoisted_8$2={class:"col-md-12"},_hoisted_9$2={class:"form-group"},_hoisted_10$2=createBaseVNode("label",null,[createTextVNode(" Title"),createBaseVNode("br"),createBaseVNode("small",null,"Content displayed before hint unlocking")],-1),_hoisted_11$1=["value"],_hoisted_12$1={class:"form-group"},_hoisted_13=createBaseVNode("label",null,[createTextVNode(" Hint"),createBaseVNode("br"),createBaseVNode("small",null,"Markdown & HTML are supported")],-1),_hoisted_14=["value"],_hoisted_15={class:"form-group"},_hoisted_16=createBaseVNode("label",null,[createTextVNode(" Cost"),createBaseVNode("br"),createBaseVNode("small",null,"How many points it costs to see your hint.")],-1),_hoisted_17={class:"form-group"},_hoisted_18=createBaseVNode("label",null,[createTextVNode(" Requirements"),createBaseVNode("br"),createBaseVNode("small",null,"Hints that must be unlocked before unlocking this hint")],-1),_hoisted_19={class:"form-check-label cursor-pointer"},_hoisted_20=["value"],_hoisted_21=createStaticVNode('<div class="modal-footer"><div class="container"><div class="row"><div class="col-md-12"><button class="btn btn-primary float-right">Submit</button></div></div></div></div>',1);function _sfc_render$3(e,t,o,n,a,i){return openBlock(),createElementBlock("div",_hoisted_1$3,[createBaseVNode("div",_hoisted_2$3,[createBaseVNode("div",_hoisted_3$3,[_hoisted_4$3,createBaseVNode("form",{method:"POST",onSubmit:t[2]||(t[2]=withModifiers((...s)=>i.updateHint&&i.updateHint(...s),["prevent"]))},[createBaseVNode("div",_hoisted_5$3,[createBaseVNode("div",_hoisted_6$3,[createBaseVNode("div",_hoisted_7$2,[createBaseVNode("div",_hoisted_8$2,[createBaseVNode("div",_hoisted_9$2,[_hoisted_10$2,createBaseVNode("input",{type:"text",class:"form-control",name:"title",value:this.title,ref:"title"},null,8,_hoisted_11$1)]),createBaseVNode("div",_hoisted_12$1,[_hoisted_13,createBaseVNode("textarea",{type:"text",class:"form-control",name:"content",rows:"7",value:this.content,ref:"content"},null,8,_hoisted_14)]),createBaseVNode("div",_hoisted_15,[_hoisted_16,withDirectives(createBaseVNode("input",{type:"number",class:"form-control",name:"cost","onUpdate:modelValue":t[0]||(t[0]=s=>e.cost=s)},null,512),[[vModelText,e.cost,void 0,{lazy:!0}]])]),createBaseVNode("div",_hoisted_17,[_hoisted_18,(openBlock(!0),createElementBlock(Fragment,null,renderList(i.otherHints,s=>(openBlock(),createElementBlock("div",{class:"form-check",key:s.id},[createBaseVNode("label",_hoisted_19,[withDirectives(createBaseVNode("input",{class:"form-check-input",type:"checkbox",value:s.id,"onUpdate:modelValue":t[1]||(t[1]=d=>e.selectedHints=d)},null,8,_hoisted_20),[[vModelCheckbox,e.selectedHints]]),createTextVNode(" "+toDisplayString(s.content)+" - "+toDisplayString(s.cost),1)])]))),128))])])])])]),_hoisted_21],32)])])])}const HintEditForm=_export_sfc(_sfc_main$3,[["render",_sfc_render$3]]),_sfc_main$2={components:{HintCreationForm,HintEditForm},props:{challenge_id:Number},data:function(){return{hints:[],editing_hint_id:null}},methods:{loadHints:async function(){let t=await(await CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}/hints`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}})).json();return this.hints=t.data,t.success},addHint:function(){let e=this.$refs.HintCreationForm.$el;$(e).modal()},editHint:function(e){this.editing_hint_id=e;let t=this.$refs.HintEditForm.$el;$(t).modal()},refreshHints:function(e){this.loadHints().then(t=>{if(t){let o;switch(e){case"HintCreationForm":o=this.$refs.HintCreationForm.$el,console.log(o),$(o).modal("hide");break;case"HintEditForm":o=this.$refs.HintEditForm.$el,$(o).modal("hide");break}}else alert("An error occurred while updating this hint. Please try again.")})},deleteHint:function(e){ezQuery({title:"Delete Hint",body:"Are you sure you want to delete this hint?",success:()=>{CTFd$1.fetch(`/api/v1/hints/${e}`,{method:"DELETE"}).then(t=>t.json()).then(t=>{t.success&&this.loadHints()})}})}},created(){this.loadHints()}},_hoisted_1$2={class:"table table-striped"},_hoisted_2$2=createBaseVNode("thead",null,[createBaseVNode("tr",null,[createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"ID")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Title")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Hint")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Cost")]),createBaseVNode("td",{class:"text-center"},[createBaseVNode("b",null,"Settings")])])],-1),_hoisted_3$2={class:"text-center"},_hoisted_4$2={class:"text-center"},_hoisted_5$2={class:"text-break"},_hoisted_6$2={class:"text-center"},_hoisted_7$1={class:"text-center"},_hoisted_8$1=["onClick"],_hoisted_9$1=["onClick"],_hoisted_10$1={class:"col-md-12"};function _sfc_render$2(e,t,o,n,a,i){const s=resolveComponent("HintCreationForm"),d=resolveComponent("HintEditForm");return openBlock(),createElementBlock("div",null,[createBaseVNode("div",null,[createVNode(s,{ref:"HintCreationForm",challenge_id:o.challenge_id,hints:e.hints,onRefreshHints:i.refreshHints},null,8,["challenge_id","hints","onRefreshHints"])]),createBaseVNode("div",null,[createVNode(d,{ref:"HintEditForm",challenge_id:o.challenge_id,hint_id:e.editing_hint_id,hints:e.hints,onRefreshHints:i.refreshHints},null,8,["challenge_id","hint_id","hints","onRefreshHints"])]),createBaseVNode("table",_hoisted_1$2,[_hoisted_2$2,createBaseVNode("tbody",null,[(openBlock(!0),createElementBlock(Fragment,null,renderList(e.hints,l=>(openBlock(),createElementBlock("tr",{key:l.id},[createBaseVNode("td",_hoisted_3$2,toDisplayString(l.type),1),createBaseVNode("td",_hoisted_4$2,toDisplayString(l.title),1),createBaseVNode("td",_hoisted_5$2,[createBaseVNode("pre",null,toDisplayString(l.content),1)]),createBaseVNode("td",_hoisted_6$2,toDisplayString(l.cost),1),createBaseVNode("td",_hoisted_7$1,[createBaseVNode("i",{role:"button",class:"btn-fa fas fa-edit",onClick:r=>i.editHint(l.id)},null,8,_hoisted_8$1),createBaseVNode("i",{role:"button",class:"btn-fa fas fa-times",onClick:r=>i.deleteHint(l.id)},null,8,_hoisted_9$1)])]))),128))])]),createBaseVNode("div",_hoisted_10$1,[createBaseVNode("button",{class:"btn btn-success float-right",onClick:t[0]||(t[0]=(...l)=>i.addHint&&i.addHint(...l))}," Create Hint ")])])}const HintsList=_export_sfc(_sfc_main$2,[["render",_sfc_render$2]]),_sfc_main$1={props:{challenge_id:Number},data:function(){return{challenge:null,challenges:[],selected_id:null}},computed:{updateAvailable:function(){return this.challenge?this.selected_id!=this.challenge.next_id:!1},otherChallenges:function(){return this.challenges.filter(e=>e.id!==this.$props.challenge_id)}},methods:{loadData:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.challenge=e.data,this.selected_id=e.data.next_id)})},loadChallenges:function(){CTFd$1.fetch("/api/v1/challenges?view=admin",{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{e.success&&(this.challenges=e.data)})},updateNext:function(){CTFd$1.fetch(`/api/v1/challenges/${this.$props.challenge_id}`,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify({next_id:this.selected_id!="null"?this.selected_id:null})}).then(e=>e.json()).then(e=>{e.success&&(this.loadData(),this.loadChallenges())})}},created(){this.loadData(),this.loadChallenges()}},_hoisted_1$1={class:"form-group"},_hoisted_2$1=createBaseVNode("label",null,[createTextVNode(" Next Challenge "),createBaseVNode("br"),createBaseVNode("small",{class:"text-muted"},"Challenge to recommend after solving this challenge")],-1),_hoisted_3$1=createBaseVNode("option",{value:"null"},"--",-1),_hoisted_4$1=["value"],_hoisted_5$1={class:"form-group"},_hoisted_6$1=["disabled"];function _sfc_render$1(e,t,o,n,a,i){return openBlock(),createElementBlock("div",null,[createBaseVNode("form",{onSubmit:t[1]||(t[1]=withModifiers((...s)=>i.updateNext&&i.updateNext(...s),["prevent"]))},[createBaseVNode("div",_hoisted_1$1,[_hoisted_2$1,withDirectives(createBaseVNode("select",{class:"form-control custom-select","onUpdate:modelValue":t[0]||(t[0]=s=>e.selected_id=s)},[_hoisted_3$1,(openBlock(!0),createElementBlock(Fragment,null,renderList(i.otherChallenges,s=>(openBlock(),createElementBlock("option",{value:s.id,key:s.id},toDisplayString(s.name),9,_hoisted_4$1))),128))],512),[[vModelSelect,e.selected_id]])]),createBaseVNode("div",_hoisted_5$1,[createBaseVNode("button",{class:"btn btn-success float-right",disabled:!i.updateAvailable}," Save ",8,_hoisted_6$1)])],32)])}const NextChallenge=_export_sfc(_sfc_main$1,[["render",_sfc_render$1]]),_sfc_main={name:"SolutionEditor",props:{challenge_id:Number},data:function(){return{solution_id:null,content:"",state:"hidden",loading:!1}},watch:{solution_id:{handler(e,t){t==null&&this.loadSolution()}}},methods:{loadSolution:function(){CTFd$1.fetch(`/api/v1/solutions/${this.solution_id}`,{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(e=>e.json()).then(e=>{if(e.success){let t=e.data;this.content=t.content||"",this.state=t.state||"hidden";let o=this.$refs.content;bindMarkdownEditor(o),setTimeout(()=>{o.mde&&(o.mde.codemirror.getDoc().setValue(this.content),this._forceRefresh())},200)}}).catch(e=>{console.error("Error loading solution:",e)})},resetForm:function(){this.content="",this.state="hidden",setTimeout(()=>{let e=this.$refs.content;e&&(bindMarkdownEditor(e),setTimeout(()=>{e.mde&&(e.mde.codemirror.getDoc().setValue(""),this._forceRefresh())},200))},100)},_forceRefresh:function(){let e=this.$refs.content;e&&e.mde&&e.mde.codemirror.refresh()},getContent:function(){this._forceRefresh();let e=this.$refs.content;return e&&e.mde?e.mde.codemirror.getDoc().getValue():e.value},submitSolution:function(){this.loading=!0;let e={content:this.getContent(),state:this.state},t,o;this.solution_id?(t=`/api/v1/solutions/${this.solution_id}`,o="PATCH"):(e.challenge_id=this.challenge_id,t="/api/v1/solutions",o="POST"),CTFd$1.fetch(t,{method:o,credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(e)}).then(n=>n.json()).then(n=>{n.success?(this.solution_id||(this.solution_id=n.data.id),this.loading=!1):(this.loading=!1,console.error("Error submitting solution:",n.errors))}).catch(n=>{this.loading=!1,console.error("Network error:",n)})}},created(){this.solution_id=window.CHALLENGE_SOLUTION_ID,$("a[href='#solution']").on("shown.bs.tab",e=>{this._forceRefresh()}),this.solution_id?this.loadSolution():this.resetForm()}},_hoisted_1={class:"form-group"},_hoisted_2=createBaseVNode("label",null,[createTextVNode(" Content"),createBaseVNode("br"),createBaseVNode("small",null,"Markdown & HTML are supported")],-1),_hoisted_3=["value","media-id"],_hoisted_4={class:"form-group"},_hoisted_5=createBaseVNode("label",null,[createTextVNode(" State"),createBaseVNode("br"),createBaseVNode("small",null,"Controls who can view this solution")],-1),_hoisted_6=createBaseVNode("option",{value:"hidden"},"Hidden",-1),_hoisted_7=createBaseVNode("option",{value:"visible"},"Visible",-1),_hoisted_8=[_hoisted_6,_hoisted_7],_hoisted_9={class:"btn btn-primary float-right",type:"submit"},_hoisted_10={key:0,class:"spinner-border spinner-border-sm ml-2",role:"status"},_hoisted_11=createBaseVNode("span",{class:"sr-only"},"Loading...",-1),_hoisted_12=[_hoisted_11];function _sfc_render(e,t,o,n,a,i){return openBlock(),createElementBlock("div",null,[createBaseVNode("form",{method:"POST",onSubmit:t[1]||(t[1]=withModifiers((...s)=>i.submitSolution&&i.submitSolution(...s),["prevent"]))},[createBaseVNode("div",_hoisted_1,[_hoisted_2,createBaseVNode("textarea",{type:"text",class:"form-control",name:"content",rows:"10",value:this.content,"media-type":"solution","media-id-title":"solution_id","media-id":this.solution_id,ref:"content"},null,8,_hoisted_3)]),createBaseVNode("div",_hoisted_4,[_hoisted_5,withDirectives(createBaseVNode("select",{class:"form-control custom-select",name:"state","onUpdate:modelValue":t[0]||(t[0]=s=>e.state=s)},_hoisted_8,512),[[vModelSelect,e.state]])]),createBaseVNode("button",_hoisted_9,toDisplayString(e.solution_id?"Update":"Create")+" Solution ",1),e.loading?(openBlock(),createElementBlock("div",_hoisted_10,_hoisted_12)):createCommentVNode("",!0)],32)])}const SolutionEditor=_export_sfc(_sfc_main,[["render",_sfc_render]]);function loadChalTemplate(e){CTFd$1._internal.challenge={},$$1.getScript(CTFd$1.config.urlRoot+e.scripts.view,function(){let t=e.create;$$1("#create-chal-entry-div").html(t),bindMarkdownEditors(),$$1.getScript(CTFd$1.config.urlRoot+e.scripts.create,function(){$$1("#create-chal-entry-div form").submit(function(o){o.preventDefault();const n=$$1("#create-chal-entry-div form").serializeJSON();CTFd$1.fetch("/api/v1/challenges",{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(n)}).then(function(a){return a.json()}).then(function(a){if(a.success)$$1("#challenge-create-options #challenge_id").val(a.data.id),$$1("#challenge-create-options").modal();else{let i="";for(const s in a.errors)i+=a.errors[s].join(`
`),i+=`
`;ezAlert({title:"Error",body:i,button:"OK"})}})})})})}function handleChallengeOptions(e){e.preventDefault();var t=$$1(e.target).serializeJSON(!0);let o={challenge_id:t.challenge_id,content:t.flag||"",type:t.flag_type,data:t.flag_data?t.flag_data:""},n=function(){CTFd$1.fetch("/api/v1/challenges/"+t.challenge_id,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify({state:t.state})}).then(function(a){return a.json()}).then(function(a){a.success&&setTimeout(function(){window.location=CTFd$1.config.urlRoot+"/admin/challenges/"+t.challenge_id},700)})};Promise.all([new Promise(function(a,i){if(o.content.length==0){a();return}CTFd$1.fetch("/api/v1/flags",{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(o)}).then(function(s){a(s.json())})}),new Promise(function(a,i){let s=e.target,d={challenge:t.challenge_id,type:"challenge"};$$1(s.elements.file).val()&&helpers.files.upload(s,d),a()})]).then(a=>{n()})}$$1(()=>{if($$1(".preview-challenge").click(function(e){let t=`${CTFd$1.config.urlRoot}/admin/challenges/preview/${window.CHALLENGE_ID}`;$$1("#challenge-window").html(`<iframe src="${t}" height="100%" width="100%" frameBorder=0></iframe>`),$$1("#challenge-modal").modal()}),$$1(".comments-challenge").click(function(e){$$1("#challenge-comments-window").modal()}),$$1(".delete-challenge").click(function(e){ezQuery({title:"Delete Challenge",body:`Are you sure you want to delete <strong>${htmlEntities(window.CHALLENGE_NAME)}</strong>`,success:function(){CTFd$1.fetch("/api/v1/challenges/"+window.CHALLENGE_ID,{method:"DELETE"}).then(function(t){return t.json()}).then(function(t){t.success&&(window.location=CTFd$1.config.urlRoot+"/admin/challenges")})}})}),$$1("#challenge-update-container > form").submit(function(e){e.preventDefault();var t=$$1(e.target).serializeJSON(!0);CTFd$1.fetch("/api/v1/challenges/"+window.CHALLENGE_ID+"/flags",{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(function(o){return o.json()}).then(function(o){let n=function(){CTFd$1.fetch("/api/v1/challenges/"+window.CHALLENGE_ID,{method:"PATCH",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(t)}).then(function(a){return a.json()}).then(function(a){if(a.success){switch($$1(".challenge-state").text(a.data.state),a.data.state){case"visible":$$1(".challenge-state").removeClass("badge-danger").addClass("badge-success");break;case"hidden":$$1(".challenge-state").removeClass("badge-success").addClass("badge-danger");break}ezToast({title:"Success",body:"Your challenge has been updated!"})}else{let i="";for(const s in a.errors)i+=a.errors[s].join(`
`),i+=`
`;ezAlert({title:"Error",body:i,button:"OK"})}})};o.data.length===0&&t.state==="visible"?ezQuery({title:"Missing Flags",body:"This challenge does not have any flags meaning it may be unsolveable. Are you sure you'd like to update this challenge?",success:n}):n()})}),$$1("#challenge-create-options form").submit(handleChallengeOptions),document.querySelector("#challenge-flags")){const e=Vue$1.extend(FlagList);let t=document.createElement("div");document.querySelector("#challenge-flags").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#challenge-topics")){const e=Vue$1.extend(TopicsList);let t=document.createElement("div");document.querySelector("#challenge-topics").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#challenge-tags")){const e=Vue$1.extend(TagsList);let t=document.createElement("div");document.querySelector("#challenge-tags").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#prerequisite-add-form")){const e=Vue$1.extend(Requirements);let t=document.createElement("div");document.querySelector("#prerequisite-add-form").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#challenge-files")){const e=Vue$1.extend(ChallengeFilesList);let t=document.createElement("div");document.querySelector("#challenge-files").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#challenge-hints")){const e=Vue$1.extend(HintsList);let t=document.createElement("div");document.querySelector("#challenge-hints").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#next-add-form")){const e=Vue$1.extend(NextChallenge);let t=document.createElement("div");document.querySelector("#next-add-form").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#challenge-solution")){const e=Vue$1.extend(SolutionEditor);let t=document.createElement("div");document.querySelector("#challenge-solution").appendChild(t),new e({propsData:{challenge_id:window.CHALLENGE_ID}}).$mount(t)}if(document.querySelector("#comment-box")){const e=Vue$1.extend(CommentBox);let t=document.createElement("div");document.querySelector("#comment-box").appendChild(t),new e({propsData:{type:"challenge",id:window.CHALLENGE_ID}}).$mount(t)}$$1.get(CTFd$1.config.urlRoot+"/api/v1/challenges/types",function(e){const t=e.data;loadChalTemplate(t.standard),$$1("#create-chals-select input[name=type]").change(function(){let o=t[this.value];loadChalTemplate(o)})})});
