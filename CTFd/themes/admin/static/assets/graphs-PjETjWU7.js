import{$ as g,G as y,N as p}from"./pages/main-Dzav4vK7.js";import{e as d}from"./echarts.common-RoIwlQp8.js";function b(c){let i=c.concat();for(let a=0;a<c.length;a++)i[a]=c.slice(0,a+1).reduce(function(u,r){return u+r});return i}const m={score_graph:{format:(c,i,a,u,r)=>{let n={title:{left:"center",text:"Score over Time"},tooltip:{trigger:"axis",axisPointer:{type:"cross"}},legend:{type:"scroll",orient:"horizontal",align:"left",bottom:0,data:[a]},toolbox:{feature:{saveAsImage:{}}},grid:{containLabel:!0},xAxis:[{type:"category",boundaryGap:!1,data:[]}],yAxis:[{type:"value"}],dataZoom:[{id:"dataZoomX",type:"slider",xAxisIndex:[0],filterMode:"filter",height:20,top:35,fillerColor:"rgba(233, 236, 241, 0.4)"}],series:[]};const s=[],l=[],o=r[0].data,h=r[2].data,e=o.concat(h);e.sort((t,f)=>new Date(t.date)-new Date(f.date));for(let t=0;t<e.length;t++){const f=y(e[t].date);s.push(f.toDate());try{l.push(e[t].challenge.value)}catch{l.push(e[t].value)}}return s.forEach(t=>{n.xAxis[0].data.push(t)}),n.series.push({name:window.stats_data.name,type:"line",label:{normal:{show:!0,position:"top"}},areaStyle:{normal:{color:p(a+i)}},itemStyle:{normal:{color:p(a+i)}},data:b(l)}),n}},category_breakdown:{format:(c,i,a,u,r)=>{let n={title:{left:"center",text:"Category Breakdown"},tooltip:{trigger:"item"},toolbox:{show:!0,feature:{saveAsImage:{}}},legend:{type:"scroll",orient:"vertical",top:"middle",right:0,data:[]},series:[{name:"Category Breakdown",type:"pie",radius:["30%","50%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},itemStyle:{normal:{label:{show:!0,formatter:function(e){return`${e.percent}% (${e.value})`}},labelLine:{show:!0}},emphasis:{label:{show:!0,position:"center",textStyle:{fontSize:"14",fontWeight:"normal"}}}},emphasis:{label:{show:!0,fontSize:"30",fontWeight:"bold"}},labelLine:{show:!1},data:[]}]};const s=r[0].data,l=[];for(let e=0;e<s.length;e++)l.push(s[e].challenge.category);const o=l.filter((e,t)=>l.indexOf(e)==t),h=[];for(let e=0;e<o.length;e++){let t=0;for(let f=0;f<l.length;f++)l[f]==o[e]&&t++;h.push(t)}return o.forEach((e,t)=>{n.legend.data.push(e),n.series[0].data.push({value:h[t],name:e,itemStyle:{color:p(e)}})}),n}},solve_percentages:{format:(c,i,a,u,r)=>{const n=r[0].data.length,s=r[1].meta.count;return{title:{left:"center",text:"Solve Percentages"},tooltip:{trigger:"item"},toolbox:{show:!0,feature:{saveAsImage:{}}},legend:{orient:"vertical",top:"middle",right:0,data:["Fails","Solves"]},series:[{name:"Solve Percentages",type:"pie",radius:["30%","50%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},itemStyle:{normal:{label:{show:!0,formatter:function(o){return`${o.name} - ${o.value} (${o.percent}%)`}},labelLine:{show:!0}},emphasis:{label:{show:!0,position:"center",textStyle:{fontSize:"14",fontWeight:"normal"}}}},emphasis:{label:{show:!0,fontSize:"30",fontWeight:"bold"}},labelLine:{show:!1},data:[{value:s,name:"Fails",itemStyle:{color:"rgb(207, 38, 0)"}},{value:n,name:"Solves",itemStyle:{color:"rgb(0, 209, 64)"}}]}]}}}};function S(c,i,a,u,r,n,s){const l=m[c];let o=d.init(document.querySelector(i));o.setOption(l.format(u,r,n,s,a)),g(window).on("resize",function(){o!=null&&o!=null&&o.resize()})}function _(c,i,a,u,r,n,s){const l=m[c];d.init(document.querySelector(i)).setOption(l.format(u,r,n,s,a))}export{S as c,_ as u};
