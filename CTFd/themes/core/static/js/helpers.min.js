(window.webpackJsonp=window.webpackJsonp||[]).push([[0],{"./CTFd/themes/core/assets/js/helpers.js":function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var d=o(n("./node_modules/jquery/dist/jquery.js")),p=o(n("./CTFd/themes/core/assets/js/ezq.js")),r=n("./CTFd/themes/core/assets/js/utils.js");function o(e){return e&&e.__esModule?e:{default:e}}function s(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function a(o){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?s(Object(i),!0).forEach(function(e){var t,n,r;t=o,r=i[n=e],n in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(i)):s(Object(i)).forEach(function(e){Object.defineProperty(o,e,Object.getOwnPropertyDescriptor(i,e))})}return o}function f(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,o=!1,i=void 0;try{for(var s,a=e[Symbol.iterator]();!(r=(s=a.next()).done)&&(n.push(s.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{r||null==a.return||a.return()}finally{if(o)throw i}}return n}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return i(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var l={files:{upload:function(t,e,n){var r=window.CTFd;t instanceof d.default&&(t=t[0]);var o=new FormData(t);o.append("nonce",r.config.csrfNonce);for(var i=0,s=Object.entries(e);i<s.length;i++){var a=f(s[i],2),l=a[0],c=a[1];o.append(l,c)}var u=p.default.ezProgressBar({width:0,title:"Upload Progress"});d.default.ajax({url:r.config.urlRoot+"/api/v1/files",data:o,type:"POST",cache:!1,contentType:!1,processData:!1,xhr:function(){var e=d.default.ajaxSettings.xhr();return e.upload.onprogress=function(e){var t;e.lengthComputable&&(t=e.loaded/e.total*100,u=p.default.ezProgressBar({target:u,width:t}))},e},success:function(e){t.reset(),u=p.default.ezProgressBar({target:u,width:100}),setTimeout(function(){u.modal("hide")},500),n&&n(e)}})}},comments:{get_comments:function(e){return window.CTFd.fetch("/api/v1/comments?"+d.default.param(e),{method:"GET",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(function(e){return e.json()})},add_comment:function(e,t,n,r){var o=window.CTFd,i=a({content:e,type:t},n);o.fetch("/api/v1/comments",{method:"POST",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},body:JSON.stringify(i)}).then(function(e){return e.json()}).then(function(e){r&&r(e)})},delete_comment:function(e){return window.CTFd.fetch("/api/v1/comments/".concat(e),{method:"DELETE",credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}}).then(function(e){return e.json()})}},utils:{htmlEntities:r.htmlEntities,colorHash:r.colorHash,copyToClipboard:r.copyToClipboard},ezq:p.default};t.default=l},"./node_modules/markdown-it/lib/helpers/index.js":function(e,t,n){t.parseLinkLabel=n("./node_modules/markdown-it/lib/helpers/parse_link_label.js"),t.parseLinkDestination=n("./node_modules/markdown-it/lib/helpers/parse_link_destination.js"),t.parseLinkTitle=n("./node_modules/markdown-it/lib/helpers/parse_link_title.js")},"./node_modules/markdown-it/lib/helpers/parse_link_destination.js":function(e,t,n){var a=n("./node_modules/markdown-it/lib/common/utils.js").unescapeAll;e.exports=function(e,t,n){var r,o,i=t,s={ok:!1,pos:0,lines:0,str:""};if(60===e.charCodeAt(t)){for(t++;t<n;){if(10===(r=e.charCodeAt(t)))return s;if(62===r)return s.pos=t+1,s.str=a(e.slice(i+1,t)),s.ok=!0,s;92===r&&t+1<n?t+=2:t++}return s}for(o=0;t<n&&32!==(r=e.charCodeAt(t))&&!(r<32||127===r);)if(92===r&&t+1<n)t+=2;else{if(40===r&&o++,41===r){if(0===o)break;o--}t++}return i===t||0!==o||(s.str=a(e.slice(i,t)),s.lines=0,s.pos=t,s.ok=!0),s}},"./node_modules/markdown-it/lib/helpers/parse_link_label.js":function(e,t,n){e.exports=function(e,t,n){var r,o,i,s,a=-1,l=e.posMax,c=e.pos;for(e.pos=t+1,r=1;e.pos<l;){if(93===(i=e.src.charCodeAt(e.pos))&&0===--r){o=!0;break}if(s=e.pos,e.md.inline.skipToken(e),91===i)if(s===e.pos-1)r++;else if(n)return e.pos=c,-1}return o&&(a=e.pos),e.pos=c,a}},"./node_modules/markdown-it/lib/helpers/parse_link_title.js":function(e,t,n){var l=n("./node_modules/markdown-it/lib/common/utils.js").unescapeAll;e.exports=function(e,t,n){var r,o,i=0,s=t,a={ok:!1,pos:0,lines:0,str:""};if(n<=t)return a;if(34!==(o=e.charCodeAt(t))&&39!==o&&40!==o)return a;for(t++,40===o&&(o=41);t<n;){if((r=e.charCodeAt(t))===o)return a.pos=t+1,a.lines=i,a.str=l(e.slice(s+1,t)),a.ok=!0,a;10===r?i++:92===r&&t+1<n&&(t++,10===e.charCodeAt(t)&&i++),t++}return a}}}]);