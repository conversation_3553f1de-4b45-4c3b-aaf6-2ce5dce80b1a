!function(d){function e(e){for(var t,i,n=e[0],o=e[1],a=e[2],r=0,s=[];r<n.length;r++)i=n[r],c[i]&&s.push(c[i][0]),c[i]=0;for(t in o)Object.prototype.hasOwnProperty.call(o,t)&&(d[t]=o[t]);for(u&&u(e);s.length;)s.shift()();return p.push.apply(p,a||[]),l()}function l(){for(var e,t=0;t<p.length;t++){for(var i=p[t],n=!0,o=1;o<i.length;o++){var a=i[o];0!==c[a]&&(n=!1)}n&&(p.splice(t--,1),e=r(r.s=i[0]))}return e}var i={},c={3:0,4:0},p=[];function r(e){if(i[e])return i[e].exports;var t=i[e]={i:e,l:!1,exports:{}};return d[e].call(t.exports,t,t.exports,r),t.l=!0,t.exports}r.m=d,r.c=i,r.d=function(e,t,i){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:i})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(r.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)r.d(i,n,function(e){return t[e]}.bind(null,n));return i},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="/themes/core/static/js";var t=window.webpackJsonp=window.webpackJsonp||[],n=t.push.bind(t);t.push=e,t=t.slice();for(var o=0;o<t.length;o++)e(t[o]);var u=n;p.push(["./CTFd/themes/core/assets/js/pages/challenges.js",0,1]),l()}({"./CTFd/themes/core/assets/js/CTFd.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=p(i("./node_modules/jquery/dist/jquery.js")),o=p(i("./node_modules/dayjs/dayjs.min.js")),a=p(i("./node_modules/markdown-it/index.js"));i("./CTFd/themes/core/assets/js/patch.js");var r=p(i("./CTFd/themes/core/assets/js/fetch.js")),s=p(i("./CTFd/themes/core/assets/js/config.js")),d=i("./CTFd/themes/core/assets/js/api.js"),l=p(i("./CTFd/themes/core/assets/js/ezq.js")),c=i("./CTFd/themes/core/assets/js/utils.js");function p(e){return e&&e.__esModule?e:{default:e}}function u(t,e){var i,n=Object.keys(t);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(t),e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,i)),n}function f(o){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?u(Object(a),!0).forEach(function(e){var t,i,n;t=o,n=a[i=e],i in t?Object.defineProperty(t,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(a)):u(Object(a)).forEach(function(e){Object.defineProperty(o,e,Object.getOwnPropertyDescriptor(a,e))})}return o}var h=new d.API("/"),m={},g={ezq:l.default},v={$:n.default,markdown:function(e){var t=f(f({},{html:!0,linkify:!0}),e),i=(0,a.default)(t);return i.renderer.rules.link_open=function(e,t,i,n,o){return e[t].attrPush(["target","_blank"]),o.renderToken(e,t,i)},i},dayjs:o.default},y=!1,j={run:function(e){e(b)}};var _={ajax:{getScript:c.getScript},html:{createHtmlNode:c.createHtmlNode,htmlEntities:c.htmlEntities}},b={init:function(e){y||(y=!0,s.default.urlRoot=e.urlRoot||s.default.urlRoot,s.default.csrfNonce=e.csrfNonce||s.default.csrfNonce,s.default.userMode=e.userMode||s.default.userMode,h.domain=s.default.urlRoot+"/api/v1",m.id=e.userId)},config:s.default,fetch:r.default,user:m,ui:g,utils:_,api:h,lib:v,_internal:{},plugin:j};t.default=b},"./CTFd/themes/core/assets/js/api.js":function(e,t,i){var c=n(i("./CTFd/themes/core/assets/js/fetch.js")),s=n(i("./node_modules/q/q.js"));function n(e){return e&&e.__esModule?e:{default:e}}function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var a=function(){"use strict";function e(e){var t="object"===o(e)?e.domain:e;if(this.domain=t||"",0===this.domain.length)throw new Error("Domain parameter must be specified as a string.")}function r(i,n){return i.$queryParameters&&Object.keys(i.$queryParameters).forEach(function(e){var t=i.$queryParameters[e];n[e]=t}),n}return e.prototype.request=function(e,t,i,n,o,a,r,s){var d=a&&Object.keys(a).length?function(e){var t,i=[];for(t in e)e.hasOwnProperty(t)&&i.push(encodeURIComponent(t)+"="+encodeURIComponent(e[t]));return i.join("&")}(a):null,l=t+(d?"?"+d:"");n&&!Object.keys(n).length&&(n=void 0),(0,c.default)(l,{method:e,headers:o,body:JSON.stringify(n)}).then(function(e){return e.json()}).then(function(e){s.resolve(e)}).catch(function(e){s.reject(e)})},e.prototype.post_award_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/awards",e,{},o,n,{},t),t.promise},e.prototype.delete_award=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/awards/{award_id}".replace("{award_id}",e.awardId),void 0===e.awardId?i.reject(new Error("Missing required  parameter: awardId")):(o=r(e,o),this.request("DELETE",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_award=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/awards/{award_id}".replace("{award_id}",e.awardId),void 0===e.awardId?i.reject(new Error("Missing required  parameter: awardId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.post_challenge_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/challenges",e,{},o,n,{},t),t.promise},e.prototype.get_challenge_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/challenges",e,{},o,n,{},t),t.promise},e.prototype.post_challenge_attempt=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/challenges/attempt",e,{},o,n,{},t),t.promise},e.prototype.get_challenge_types=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/challenges/types",e,{},o,n,{},t),t.promise},e.prototype.patch_challenge=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/challenges/{challenge_id}".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(o=r(e,o),this.request("PATCH",n+t,e,{},a,o,{},i)),i.promise},e.prototype.delete_challenge=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/challenges/{challenge_id}".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(o=r(e,o),this.request("DELETE",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_challenge=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/challenges/{challenge_id}".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_challenge_files=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],void 0!==e.id&&(o.id=e.id),t="/challenges/{challenge_id}/files".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_challenge_flags=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],void 0!==e.id&&(o.id=e.id),t="/challenges/{challenge_id}/flags".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_challenge_hints=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],void 0!==e.id&&(o.id=e.id),t="/challenges/{challenge_id}/hints".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_challenge_solves=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],void 0!==e.id&&(o.id=e.id),t="/challenges/{challenge_id}/solves".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_challenge_tags=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],void 0!==e.id&&(o.id=e.id),t="/challenges/{challenge_id}/tags".replace("{challenge_id}",e.challengeId),void 0===e.challengeId?i.reject(new Error("Missing required  parameter: challengeId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.post_config_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/configs",e,{},o,n,{},t),t.promise},e.prototype.patch_config_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("PATCH",i+"/configs",e,{},o,n,{},t),t.promise},e.prototype.get_config_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/configs",e,{},o,n,{},t),t.promise},e.prototype.patch_config=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/configs/{config_key}".replace("{config_key}",e.configKey),void 0===e.configKey?i.reject(new Error("Missing required  parameter: configKey")):(o=r(e,o),this.request("PATCH",n+t,e,{},a,o,{},i)),i.promise},e.prototype.delete_config=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/configs/{config_key}".replace("{config_key}",e.configKey),void 0===e.configKey?i.reject(new Error("Missing required  parameter: configKey")):(o=r(e,o),this.request("DELETE",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_config=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/configs/{config_key}".replace("{config_key}",e.configKey),void 0===e.configKey?i.reject(new Error("Missing required  parameter: configKey")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.post_files_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/files",e,{},o,n,{},t),t.promise},e.prototype.get_files_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/files",e,{},o,n,{},t),t.promise},e.prototype.delete_files_detail=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/files/{file_id}".replace("{file_id}",e.fileId),void 0===e.fileId?i.reject(new Error("Missing required  parameter: fileId")):(o=r(e,o),this.request("DELETE",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_files_detail=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/files/{file_id}".replace("{file_id}",e.fileId),void 0===e.fileId?i.reject(new Error("Missing required  parameter: fileId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.post_flag_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/flags",e,{},o,n,{},t),t.promise},e.prototype.get_flag_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/flags",e,{},o,n,{},t),t.promise},e.prototype.get_flag_types=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/flags/types",e,{},o,n,{},t),t.promise},e.prototype.get_flag_types_1=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/flags/types/{type_name}".replace("{type_name}",e.typeName),void 0===e.typeName?i.reject(new Error("Missing required  parameter: typeName")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.patch_flag=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/flags/{flag_id}".replace("{flag_id}",e.flagId),void 0===e.flagId?i.reject(new Error("Missing required  parameter: flagId")):(o=r(e,o),this.request("PATCH",n+t,e,{},a,o,{},i)),i.promise},e.prototype.delete_flag=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/flags/{flag_id}".replace("{flag_id}",e.flagId),void 0===e.flagId?i.reject(new Error("Missing required  parameter: flagId")):(o=r(e,o),this.request("DELETE",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_flag=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/flags/{flag_id}".replace("{flag_id}",e.flagId),void 0===e.flagId?i.reject(new Error("Missing required  parameter: flagId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.post_hint_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/hints",e,{},o,n,{},t),t.promise},e.prototype.get_hint_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/hints",e,{},o,n,{},t),t.promise},e.prototype.patch_hint=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/hints/{hint_id}".replace("{hint_id}",e.hintId),void 0===e.hintId?i.reject(new Error("Missing required  parameter: hintId")):(o=r(e,o),this.request("PATCH",n+t,e,{},a,o,{},i)),i.promise},e.prototype.delete_hint=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/hints/{hint_id}".replace("{hint_id}",e.hintId),void 0===e.hintId?i.reject(new Error("Missing required  parameter: hintId")):(o=r(e,o),this.request("DELETE",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_hint=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/hints/{hint_id}".replace("{hint_id}",e.hintId),void 0===e.hintId?i.reject(new Error("Missing required  parameter: hintId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.post_notification_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/notifications",e,{},o,n,{},t),t.promise},e.prototype.get_notification_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/notifications",e,{},o,n,{},t),t.promise},e.prototype.delete_notification=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/notifications/{notification_id}".replace("{notification_id}",e.notificationId),void 0===e.notificationId?i.reject(new Error("Missing required  parameter: notificationId")):(o=r(e,o),this.request("DELETE",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_notification=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/notifications/{notification_id}".replace("{notification_id}",e.notificationId),void 0===e.notificationId?i.reject(new Error("Missing required  parameter: notificationId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.post_page_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/pages",e,{},o,n,{},t),t.promise},e.prototype.get_page_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/pages",e,{},o,n,{},t),t.promise},e.prototype.patch_page_detail=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/pages/{page_id}".replace("{page_id}",e.pageId),void 0===e.pageId?i.reject(new Error("Missing required  parameter: pageId")):(o=r(e,o),this.request("PATCH",n+t,e,{},a,o,{},i)),i.promise},e.prototype.delete_page_detail=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/pages/{page_id}".replace("{page_id}",e.pageId),void 0===e.pageId?i.reject(new Error("Missing required  parameter: pageId")):(o=r(e,o),this.request("DELETE",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_page_detail=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/pages/{page_id}".replace("{page_id}",e.pageId),void 0===e.pageId?i.reject(new Error("Missing required  parameter: pageId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_scoreboard_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/scoreboard",e,{},o,n,{},t),t.promise},e.prototype.get_scoreboard_detail=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/scoreboard/top/{count}".replace("{count}",e.count),void 0===e.count?i.reject(new Error("Missing required  parameter: count")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_challenge_solve_statistics=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/statistics/challenges/solves",e,{},o,n,{},t),t.promise},e.prototype.get_challenge_solve_percentages=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/statistics/challenges/solves/percentages",e,{},o,n,{},t),t.promise},e.prototype.get_challenge_property_counts=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/statistics/challenges/{column}".replace("{column}",e.column),void 0===e.column?i.reject(new Error("Missing required  parameter: column")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_submission_property_counts=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/statistics/submissions/{column}".replace("{column}",e.column),void 0===e.column?i.reject(new Error("Missing required  parameter: column")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_team_statistics=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/statistics/teams",e,{},o,n,{},t),t.promise},e.prototype.get_user_statistics=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/statistics/users",e,{},o,n,{},t),t.promise},e.prototype.get_user_property_counts=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/statistics/users/{column}".replace("{column}",e.column),void 0===e.column?i.reject(new Error("Missing required  parameter: column")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.post_submissions_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/submissions",e,{},o,n,{},t),t.promise},e.prototype.get_submissions_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/submissions",e,{},o,n,{},t),t.promise},e.prototype.delete_submission=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/submissions/{submission_id}".replace("{submission_id}",e.submissionId),void 0===e.submissionId?i.reject(new Error("Missing required  parameter: submissionId")):(o=r(e,o),this.request("DELETE",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_submission=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/submissions/{submission_id}".replace("{submission_id}",e.submissionId),void 0===e.submissionId?i.reject(new Error("Missing required  parameter: submissionId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.post_tag_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/tags",e,{},o,n,{},t),t.promise},e.prototype.get_tag_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/tags",e,{},o,n,{},t),t.promise},e.prototype.patch_tag=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/tags/{tag_id}".replace("{tag_id}",e.tagId),void 0===e.tagId?i.reject(new Error("Missing required  parameter: tagId")):(o=r(e,o),this.request("PATCH",n+t,e,{},a,o,{},i)),i.promise},e.prototype.delete_tag=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/tags/{tag_id}".replace("{tag_id}",e.tagId),void 0===e.tagId?i.reject(new Error("Missing required  parameter: tagId")):(o=r(e,o),this.request("DELETE",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_tag=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/tags/{tag_id}".replace("{tag_id}",e.tagId),void 0===e.tagId?i.reject(new Error("Missing required  parameter: tagId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.post_team_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/teams",e,{},o,n,{},t),t.promise},e.prototype.get_team_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/teams",e,{},o,n,{},t),t.promise},e.prototype.patch_team_private=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],void 0!==e.teamId&&(n.team_id=e.teamId),n=r(e,n),this.request("PATCH",i+"/teams/me",e,{},o,n,{},t),t.promise},e.prototype.get_team_private=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],void 0!==e.teamId&&(n.team_id=e.teamId),n=r(e,n),this.request("GET",i+"/teams/me",e,{},o,n,{},t),t.promise},e.prototype.patch_team_public=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/teams/{team_id}".replace("{team_id}",e.teamId),void 0===e.teamId?i.reject(new Error("Missing required  parameter: teamId")):(o=r(e,o),this.request("PATCH",n+t,e,{},a,o,{},i)),i.promise},e.prototype.delete_team_public=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/teams/{team_id}".replace("{team_id}",e.teamId),void 0===e.teamId?i.reject(new Error("Missing required  parameter: teamId")):(o=r(e,o),this.request("DELETE",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_team_public=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/teams/{team_id}".replace("{team_id}",e.teamId),void 0===e.teamId?i.reject(new Error("Missing required  parameter: teamId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_team_awards=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/teams/{team_id}/awards".replace("{team_id}",e.teamId),void 0===e.teamId?i.reject(new Error("Missing required  parameter: teamId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_team_fails=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/teams/{team_id}/fails".replace("{team_id}",e.teamId),void 0===e.teamId?i.reject(new Error("Missing required  parameter: teamId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_team_solves=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/teams/{team_id}/solves".replace("{team_id}",e.teamId),void 0===e.teamId?i.reject(new Error("Missing required  parameter: teamId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.post_unlock_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/unlocks",e,{},o,n,{},t),t.promise},e.prototype.get_unlock_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/unlocks",e,{},o,n,{},t),t.promise},e.prototype.post_user_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("POST",i+"/users",e,{},o,n,{},t),t.promise},e.prototype.get_user_list=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/users",e,{},o,n,{},t),t.promise},e.prototype.patch_user_private=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("PATCH",i+"/users/me",e,{},o,n,{},t),t.promise},e.prototype.get_user_private=function(e){void 0===e&&(e={});var t=s.default.defer(),i=this.domain,n={},o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],n=r(e,n),this.request("GET",i+"/users/me",e,{},o,n,{},t),t.promise},e.prototype.patch_user_public=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/users/{user_id}".replace("{user_id}",e.userId),void 0===e.userId?i.reject(new Error("Missing required  parameter: userId")):(o=r(e,o),this.request("PATCH",n+t,e,{},a,o,{},i)),i.promise},e.prototype.delete_user_public=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/users/{user_id}".replace("{user_id}",e.userId),void 0===e.userId?i.reject(new Error("Missing required  parameter: userId")):(o=r(e,o),this.request("DELETE",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_user_public=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/users/{user_id}".replace("{user_id}",e.userId),void 0===e.userId?i.reject(new Error("Missing required  parameter: userId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_user_awards=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/users/{user_id}/awards".replace("{user_id}",e.userId),void 0===e.userId?i.reject(new Error("Missing required  parameter: userId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_user_fails=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/users/{user_id}/fails".replace("{user_id}",e.userId),void 0===e.userId?i.reject(new Error("Missing required  parameter: userId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e.prototype.get_user_solves=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/users/{user_id}/solves".replace("{user_id}",e.userId),void 0===e.userId?i.reject(new Error("Missing required  parameter: userId")):(o=r(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise},e}();t.API=a},"./CTFd/themes/core/assets/js/config.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={urlRoot:"",csrfNonce:"",userMode:""}},"./CTFd/themes/core/assets/js/events.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=i("./node_modules/howler/dist/howler.js"),n=i("./node_modules/event-source-polyfill/src/eventsource.js"),r=i("./CTFd/themes/core/assets/js/ezq.js"),s=i("./CTFd/themes/core/assets/js/utils.js"),d=n.NativeEventSource||n.EventSourcePolyfill;t.default=function(e){var t=new d(e+"/events"),i=new s.WindowController,n=new a.Howl({src:[e+"/themes/core/static/sounds/notification.webm",e+"/themes/core/static/sounds/notification.mp3"]});function o(e){switch(e.type){case"toast":(0,s.inc_notification_counter)();var t=50<e.content.length?e.content.substring(0,47)+"...":e.content,i=!1;(0,r.ezToast)({title:e.title,body:t,onclick:function(){(0,r.ezAlert)({title:e.title,body:e.html,button:"Got it!",success:function(){i=!0,(0,s.dec_notification_counter)()}})},onclose:function(){i||(0,s.dec_notification_counter)()}});break;case"alert":(0,s.inc_notification_counter)(),(0,r.ezAlert)({title:e.title,body:e.html,button:"Got it!",success:function(){(0,s.dec_notification_counter)()}});break;case"background":default:(0,s.inc_notification_counter)()}}(0,s.init_notification_counter)(),i.alert=function(e){o(e)},i.toast=function(e){o(e)},i.background=function(e){o(e)},i.masterDidChange=function(){this.isMaster?t.addEventListener("notification",function(e){var t=JSON.parse(e.data);i.broadcast("notification",t),o(t),t.sound&&n.play()},!1):t&&t.close()}}},"./CTFd/themes/core/assets/js/ezq.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.ezAlert=h,t.ezToast=m,t.ezQuery=g,t.ezProgressBar=v,t.ezBadge=y,t.default=void 0,i("./node_modules/bootstrap/js/dist/modal.js");var s=n(i("./node_modules/jquery/dist/jquery.js")),a=n(i("./node_modules/highlight.js/lib/index.js"));function n(e){return e&&e.__esModule?e:{default:e}}var r='<div class="modal fade" tabindex="-1" role="dialog">  <div class="modal-dialog" role="document">    <div class="modal-content">      <div class="modal-header">        <h5 class="modal-title">{0}</h5>        <button type="button" class="close" data-dismiss="modal" aria-label="Close">          <span aria-hidden="true">&times;</span>        </button>      </div>      <div class="modal-body">      </div>      <div class="modal-footer">      </div>    </div>  </div></div>',d='<div class="toast m-3" role="alert">  <div class="toast-header">    <strong class="mr-auto">{0}</strong>    <button type="button" class="ml-2 mb-1 close" data-dismiss="toast" aria-label="Close">      <span aria-hidden="true">&times;</span>    </button>  </div>  <div class="toast-body">{1}</div></div>',l='<div class="progress">  <div class="progress-bar progress-bar-success progress-bar-striped progress-bar-animated" role="progressbar" style="width: {0}%">  </div></div>',o='<div class="alert alert-danger alert-dismissable" role="alert">\n  <span class="sr-only">Error:</span>\n  {0}\n  <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>\n</div>',c='<div class="alert alert-success alert-dismissable submit-row" role="alert">\n  <strong>Success!</strong>\n  {0}\n  <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>\n</div>',p='<button type="button" class="btn btn-primary" data-dismiss="modal">{0}</button>',u='<button type="button" class="btn btn-danger" data-dismiss="modal">No</button>',f='<button type="button" class="btn btn-primary" data-dismiss="modal">Yes</button>';function h(e){var t=r.format(e.title),i=(0,s.default)(t);"string"==typeof e.body?i.find(".modal-body").append("<p>".concat(e.body,"</p>")):i.find(".modal-body").append((0,s.default)(e.body));var n=(0,s.default)(p.format(e.button));return e.success&&(0,s.default)(n).click(function(){e.success()}),e.large&&i.find(".modal-dialog").addClass("modal-lg"),i.find(".modal-footer").append(n),i.find("pre code").each(function(e){a.default.highlightBlock(this)}),(0,s.default)("main").append(i),i.modal("show"),(0,s.default)(i).on("hidden.bs.modal",function(){(0,s.default)(this).modal("dispose")}),i}function m(e){(0,s.default)("#ezq--notifications-toast-container").length||(0,s.default)("body").append((0,s.default)("<div/>").attr({id:"ezq--notifications-toast-container"}).css({position:"fixed",bottom:"0",right:"0","min-width":"20%"}));var t,i=d.format(e.title,e.body),n=(0,s.default)(i);e.onclose&&(0,s.default)(n).find("button[data-dismiss=toast]").click(function(){e.onclose()}),e.onclick&&((t=(0,s.default)(n).find(".toast-body")).addClass("cursor-pointer"),t.click(function(){e.onclick()}));var o=!1!==e.autohide,a=!1!==e.animation,r=e.delay||1e4;return(0,s.default)("#ezq--notifications-toast-container").prepend(n),n.toast({autohide:o,delay:r,animation:a}),n.toast("show"),n}function g(e){var t=r.format(e.title),i=(0,s.default)(t);"string"==typeof e.body?i.find(".modal-body").append("<p>".concat(e.body,"</p>")):i.find(".modal-body").append((0,s.default)(e.body));var n=(0,s.default)(f),o=(0,s.default)(u);return i.find(".modal-footer").append(o),i.find(".modal-footer").append(n),i.find("pre code").each(function(e){a.default.highlightBlock(this)}),(0,s.default)("main").append(i),(0,s.default)(i).on("hidden.bs.modal",function(){(0,s.default)(this).modal("dispose")}),(0,s.default)(n).click(function(){e.success()}),i.modal("show"),i}function v(e){if(e.target){var t=(0,s.default)(e.target);return t.find(".progress-bar").css("width",e.width+"%"),t}var i=l.format(e.width),n=r.format(e.title),o=(0,s.default)(n);return o.find(".modal-body").append((0,s.default)(i)),(0,s.default)("main").append(o),o.modal("show")}function y(e){var t={success:c,error:o}[e.type].format(e.body);return(0,s.default)(t)}var j={ezAlert:h,ezToast:m,ezQuery:g,ezProgressBar:v,ezBadge:y};t.default=j},"./CTFd/themes/core/assets/js/fetch.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("./node_modules/whatwg-fetch/fetch.js");var n,o=(n=i("./CTFd/themes/core/assets/js/config.js"))&&n.__esModule?n:{default:n};var a=window.fetch;t.default=function(e,t){return void 0===t&&(t={method:"GET",credentials:"same-origin",headers:{}}),e=o.default.urlRoot+e,void 0===t.headers&&(t.headers={}),t.credentials="same-origin",t.headers.Accept="application/json",t.headers["Content-Type"]="application/json",t.headers["CSRF-Token"]=o.default.csrfNonce,a(e,t)}},"./CTFd/themes/core/assets/js/pages/challenges.js":function(e,t,i){i("./CTFd/themes/core/assets/js/pages/main.js"),i("./node_modules/bootstrap/js/dist/tab.js");var o=i("./CTFd/themes/core/assets/js/ezq.js"),d=i("./CTFd/themes/core/assets/js/utils.js"),l=c(i("./node_modules/dayjs/dayjs.min.js")),n=c(i("./node_modules/dayjs/plugin/relativeTime.js")),v=c(i("./node_modules/jquery/dist/jquery.js")),r=c(i("./CTFd/themes/core/assets/js/CTFd.js")),a=c(i("./CTFd/themes/core/assets/js/config.js")),s=c(i("./node_modules/highlight.js/lib/index.js"));function c(e){return e&&e.__esModule?e:{default:e}}l.default.extend(n.default),r.default._internal.challenge={};var y=[],j=[],_=function(t){var e=v.default.grep(y,function(e){return e.id==t})[0];"hidden"!==e.type?p(e):(0,o.ezAlert)({title:"Challenge Hidden!",body:"You haven't unlocked this challenge yet!",button:"Got it!"})},p=function(n){return Promise.all([r.default.api.get_challenge({challengeId:n.id}),v.default.getScript(a.default.urlRoot+n.script),v.default.get(a.default.urlRoot+n.template)]).then(function(e){var t=r.default._internal.challenge;(0,v.default)("#challenge-window").empty(),t.data=e[0].data,t.preRender(),(0,v.default)("#challenge-window").append(e[0].data.view),(0,v.default)("#challenge-window #challenge-input").addClass("form-control"),(0,v.default)("#challenge-window #challenge-submit").addClass("btn btn-md btn-outline-secondary float-right");var i=(0,v.default)("#challenge-window").find(".modal-dialog");if(window.init.theme_settings&&window.init.theme_settings.challenge_window_size)switch(window.init.theme_settings.challenge_window_size){case"sm":i.addClass("modal-sm");break;case"lg":i.addClass("modal-lg");break;case"xl":i.addClass("modal-xl")}(0,v.default)(".challenge-solves").click(function(e){h((0,v.default)("#challenge-id").val())}),(0,v.default)(".nav-tabs a").click(function(e){e.preventDefault(),(0,v.default)(this).tab("show")}),(0,v.default)("#challenge-window").on("hide.bs.modal",function(e){(0,v.default)("#challenge-input").removeClass("wrong"),(0,v.default)("#challenge-input").removeClass("correct"),(0,v.default)("#incorrect-key").slideUp(),(0,v.default)("#correct-key").slideUp(),(0,v.default)("#already-solved").slideUp(),(0,v.default)("#too-fast").slideUp()}),(0,v.default)(".load-hint").on("click",function(e){T((0,v.default)(this).data("hint-id"))}),(0,v.default)("#challenge-submit").click(function(e){e.preventDefault(),(0,v.default)("#challenge-submit").addClass("disabled-button"),(0,v.default)("#challenge-submit").prop("disabled",!0),r.default._internal.challenge.submit().then(u).then(m).then(f)}),(0,v.default)("#challenge-input").keyup(function(e){13==e.keyCode&&(0,v.default)("#challenge-submit").click()}),t.postRender(),(0,v.default)("#challenge-window").find("pre code").each(function(e){s.default.highlightBlock(this)}),window.location.replace(window.location.href.split("#")[0]+"#".concat(n.name,"-").concat(n.id)),(0,v.default)("#challenge-window").modal()})};function u(e){var t=e.data,i=(0,v.default)("#result-message"),n=(0,v.default)("#result-notification"),o=(0,v.default)("#challenge-input");n.removeClass(),i.text(t.message);var a=(0,v.default)("<div class='col-md-12 pb-3'><button class='btn btn-info w-100'>Next Challenge</button></div>").click(function(){(0,v.default)("#challenge-window").modal("toggle"),setTimeout(function(){_(r.default._internal.challenge.data.next_id)},500)});"authentication_required"!==t.status?("incorrect"===t.status?(n.addClass("alert alert-danger alert-dismissable text-center"),n.slideDown(),o.removeClass("correct"),o.addClass("wrong"),setTimeout(function(){o.removeClass("wrong")},3e3)):"correct"===t.status?(n.addClass("alert alert-success alert-dismissable text-center"),n.slideDown(),(0,v.default)(".challenge-solves").text().trim()&&(0,v.default)(".challenge-solves").text(parseInt((0,v.default)(".challenge-solves").text().split(" ")[0])+1+" Solves"),o.val(""),o.removeClass("wrong"),o.addClass("correct"),r.default._internal.challenge.data.next_id&&(0,v.default)(".submit-row").html(a)):"already_solved"===t.status?(n.addClass("alert alert-info alert-dismissable text-center"),n.slideDown(),o.addClass("correct"),r.default._internal.challenge.data.next_id&&(0,v.default)(".submit-row").html(a)):"paused"===t.status?(n.addClass("alert alert-warning alert-dismissable text-center"),n.slideDown()):"ratelimited"===t.status&&(n.addClass("alert alert-warning alert-dismissable text-center"),n.slideDown(),o.addClass("too-fast"),setTimeout(function(){o.removeClass("too-fast")},3e3)),setTimeout(function(){(0,v.default)(".alert").slideUp(),(0,v.default)("#challenge-submit").removeClass("disabled-button"),(0,v.default)("#challenge-submit").prop("disabled",!1)},3e3)):window.location=r.default.config.urlRoot+"/login?next="+r.default.config.urlRoot+window.location.pathname+window.location.hash}function f(){y.map(function(e){var t;e.solved_by_me&&((t=(0,v.default)('button[value="'.concat(e.id,'"]'))).addClass("solved-challenge"),t.prepend("<i class='fas fa-check corner-button-check'></i>"))})}function h(e){return r.default.api.get_challenge_solves({challengeId:e}).then(function(e){var t=e.data;(0,v.default)(".challenge-solves").text(parseInt(t.length)+" Solves");var i=(0,v.default)("#challenge-solves-names");i.empty();for(var n=0;n<t.length;n++){var o=t[n].account_id,a=t[n].name,r=(0,l.default)(t[n].date).fromNow(),s=t[n].account_url;i.append('<tr><td><a href="{0}">{2}</td><td>{3}</td></tr>'.format(s,o,(0,d.htmlEntities)(a),r))}})}function m(){return r.default.api.get_challenge_list().then(function(e){var t=[],i=(0,v.default)("#challenges-board");y=e.data,window.BETA_sortChallenges&&(y=window.BETA_sortChallenges(y)),i.empty();for(var n,o,a,r=y.length-1;0<=r;r--){-1==v.default.inArray(y[r].category,t)&&(n=y[r].category,t.push(n),o=n.replace(/ /g,"-").hashCode(),(a=(0,v.default)('<div id="{0}-row" class="pt-5">'.format(o)+'<div class="category-header col-md-12 mb-3"></div><div class="category-challenges col-md-12"><div class="challenges-row col-md-12"></div></div></div>')).find(".category-header").append((0,v.default)("<h3>"+n+"</h3>")),i.append(a))}for(var s=0;s<=y.length-1;s++){for(var d=y[s],l=d.name.replace(/ /g,"-").hashCode(),c=d.category.replace(/ /g,"-").hashCode(),p=(0,v.default)("<div id='{0}' class='col-md-3 d-inline-block'></div>".format(l)),u=void 0,u=-1==j.indexOf(d.id)?(0,v.default)("<button class='btn btn-dark challenge-button w-100 text-truncate pt-3 pb-3 mb-2' value='{0}'></button>".format(d.id)):(0,v.default)("<button class='btn btn-dark challenge-button solved-challenge w-100 text-truncate pt-3 pb-3 mb-2' value='{0}'><i class='fas fa-check corner-button-check'></i></button>".format(d.id)),f=(0,v.default)("<p>{0}</p>".format(d.name)),h=(0,v.default)("<span>{0}</span>".format(d.value)),m=0;m<d.tags.length;m++){var g="tag-"+d.tags[m].value.replace(/ /g,"-");p.addClass(g)}u.append(f),u.append(h),p.append(u),(0,v.default)("#"+c+"-row").find(".category-challenges > .challenges-row").append(p)}(0,v.default)(".challenge-button").click(function(e){_(this.value)})})}function g(){return m().then(f)}(0,v.default)(function(){g().then(function(){var e,t,i,n;0<window.location.hash.length&&(e=decodeURIComponent(window.location.hash.substring(1)),t=e.lastIndexOf("-"),i=[e.slice(0,t),e.slice(t+1)][1],n=v.default.grep(y,function(e){return e.id==i})[0],p(n))}),(0,v.default)("#challenge-input").keyup(function(e){13==e.keyCode&&(0,v.default)("#challenge-submit").click()}),(0,v.default)(".nav-tabs a").click(function(e){e.preventDefault(),(0,v.default)(this).tab("show")}),(0,v.default)("#challenge-window").on("hidden.bs.modal",function(e){(0,v.default)(".nav-tabs a:first").tab("show"),history.replaceState("",window.document.title,window.location.pathname)}),(0,v.default)(".challenge-solves").click(function(e){h((0,v.default)("#challenge-id").val())}),(0,v.default)("#challenge-window").on("hide.bs.modal",function(e){(0,v.default)("#challenge-input").removeClass("wrong"),(0,v.default)("#challenge-input").removeClass("correct"),(0,v.default)("#incorrect-key").slideUp(),(0,v.default)("#correct-key").slideUp(),(0,v.default)("#already-solved").slideUp(),(0,v.default)("#too-fast").slideUp()})}),setInterval(g,3e5);function b(e){(0,o.ezAlert)({title:"Hint",body:e.html,button:"Got it!"})}var T=function(n){r.default.api.get_hint({hintId:n}).then(function(e){var t,i;e.success?e.data.content?b(e.data):(t=n,(0,o.ezQuery)({title:"Unlock Hint?",body:"Are you sure you want to open this hint?",success:function(){var e={target:t,type:"hints"};r.default.api.post_unlock_list({},e).then(function(e){e.success?r.default.api.get_hint({hintId:t}).then(function(e){b(e.data)}):(0,o.ezAlert)({title:"Error",body:e.errors.score,button:"Got it!"})})}})):(i=Object.values(e.errors).join("\n"),alert(i))})};window.updateChallengeBoard=g},"./CTFd/themes/core/assets/js/pages/main.js":function(e,t,i){var n=h(i("./CTFd/themes/core/assets/js/CTFd.js")),o=h(i("./node_modules/jquery/dist/jquery.js")),a=h(i("./node_modules/dayjs/dayjs.min.js")),r=h(i("./node_modules/dayjs/plugin/advancedFormat.js")),s=h(i("./node_modules/nunjucks/browser/nunjucks.js")),d=i("./node_modules/howler/dist/howler.js"),l=h(i("./CTFd/themes/core/assets/js/events.js")),c=h(i("./CTFd/themes/core/assets/js/config.js")),p=h(i("./CTFd/themes/core/assets/js/styles.js")),u=h(i("./CTFd/themes/core/assets/js/times.js")),f=h(i("./CTFd/themes/core/assets/js/helpers.js"));function h(e){return e&&e.__esModule?e:{default:e}}a.default.extend(r.default),n.default.init(window.init),window.CTFd=n.default,window.helpers=f.default,window.$=o.default,window.dayjs=a.default,window.nunjucks=s.default,window.Howl=d.Howl,(0,o.default)(function(){(0,p.default)(),(0,u.default)(),(0,l.default)(c.default.urlRoot)})},"./CTFd/themes/core/assets/js/patch.js":function(e,t,i){var n,s=(n=i("./node_modules/q/q.js"))&&n.__esModule?n:{default:n},o=i("./CTFd/themes/core/assets/js/api.js");function r(t,e){var i,n=Object.keys(t);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(t),e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,i)),n}function a(o){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?r(Object(a),!0).forEach(function(e){var t,i,n;t=o,n=a[i=e],i in t?Object.defineProperty(t,i,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[i]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(a)):r(Object(a)).forEach(function(e){Object.defineProperty(o,e,Object.getOwnPropertyDescriptor(a,e))})}return o}function d(e,t){return a(a({},e),t)}o.API.prototype.requestRaw=function(e,t,i,n,o,a,r,s){var d=a&&Object.keys(a).length?function(e){var t,i=[];for(t in e)e.hasOwnProperty(t)&&i.push(encodeURIComponent(t)+"="+encodeURIComponent(e[t]));return i.join("&")}(a):null,l=t+(d?"?"+d:"");n&&!Object.keys(n).length&&(n=void 0),fetch(l,{method:e,headers:o,body:n}).then(function(e){return e.json()}).then(function(e){s.resolve(e)}).catch(function(e){s.reject(e)})},o.API.prototype.patch_user_public=function(e,t){void 0===e&&(e={});var i,n=s.default.defer(),o=this.domain,a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],i="/users/{user_id}".replace("{user_id}",e.userId),void 0===e.userId?n.reject(new Error("Missing required  parameter: userId")):this.request("PATCH",o+i,e,t,a,{},{},n),n.promise},o.API.prototype.patch_user_private=function(e,t){void 0===e&&(e={});var i=s.default.defer(),n=this.domain,o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],this.request("PATCH",n+"/users/me",e,t,o,{},{},i),i.promise},o.API.prototype.post_unlock_list=function(e,t){var i=s.default.defer(),n=this.domain,o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],this.request("POST",n+"/unlocks",e,t,o,{},{},i),i.promise},o.API.prototype.post_notification_list=function(e,t){void 0===e&&(e={});var i=s.default.defer(),n=this.domain,o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],this.request("POST",n+"/notifications",e,t,o,{},{},i),i.promise},o.API.prototype.post_files_list=function(e,t){var i=s.default.defer(),n=this.domain,o={};return o.Accept=["application/json"],o["Content-Type"]=["application/json"],this.requestRaw("POST",n+"/files",e,t,o,{},{},i),i.promise},o.API.prototype.patch_config=function(e,t){void 0===e&&(e={});var i,n=s.default.defer(),o=this.domain,a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],i="/configs/{config_key}".replace("{config_key}",e.configKey),void 0===e.configKey?n.reject(new Error("Missing required  parameter: configKey")):this.request("PATCH",o+i,e,t,a,{},{},n),n.promise},o.API.prototype.patch_config_list=function(e,t){void 0===e&&(e={});var i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],o=d(e,o),this.request("PATCH",n+"/configs",e,t,a,o,{},i),i.promise},o.API.prototype.post_tag_list=function(e,t){void 0===e&&(e={});var i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],o=d(e,o),this.request("POST",n+"/tags",e,t,a,o,{},i),i.promise},o.API.prototype.patch_team_public=function(e,t){void 0===e&&(e={});var i,n=s.default.defer(),o=this.domain,a={},r={};return r.Accept=["application/json"],r["Content-Type"]=["application/json"],i="/teams/{team_id}".replace("{team_id}",e.teamId),void 0===e.teamId?n.reject(new Error("Missing required  parameter: teamId")):(a=d(e,a),this.request("PATCH",o+i,e,t,r,a,{},n)),n.promise},o.API.prototype.post_challenge_attempt=function(e,t){void 0===e&&(e={});var i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],o=d(e,o),this.request("POST",n+"/challenges/attempt",e,t,a,o,{},i),i.promise},o.API.prototype.get_hint=function(e){void 0===e&&(e={});var t,i=s.default.defer(),n=this.domain,o={},a={};return a.Accept=["application/json"],a["Content-Type"]=["application/json"],t="/hints/{hint_id}".replace("{hint_id}",e.hintId),void 0===e.hintId?i.reject(new Error("Missing required  parameter: hintId")):(delete e.hintId,o=d(e,o),this.request("GET",n+t,e,{},a,o,{},i)),i.promise}},"./CTFd/themes/core/assets/js/styles.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,i("./node_modules/bootstrap/dist/js/bootstrap.bundle.js");var n=a(i("./node_modules/jquery/dist/jquery.js")),o=a(i("./node_modules/highlight.js/lib/index.js"));function a(e){return e&&e.__esModule?e:{default:e}}t.default=function(){(0,n.default)(":input").each(function(){(0,n.default)(this).data("initial",(0,n.default)(this).val())}),(0,n.default)(".form-control").bind({focus:function(){(0,n.default)(this).removeClass("input-filled-invalid"),(0,n.default)(this).addClass("input-filled-valid")},blur:function(){""===(0,n.default)(this).val()&&((0,n.default)(this).removeClass("input-filled-invalid"),(0,n.default)(this).removeClass("input-filled-valid"))}}),(0,n.default)(".form-control").each(function(){(0,n.default)(this).val()&&(0,n.default)(this).addClass("input-filled-valid")}),(0,n.default)(".page-select").change(function(){var e=new URL(window.location);e.searchParams.set("page",this.value),window.location.href=e.toString()}),(0,n.default)('[data-toggle="tooltip"]').tooltip(),(0,n.default)(function(){document.querySelectorAll("pre code").forEach(function(e){o.default.highlightBlock(e)})})}},"./CTFd/themes/core/assets/js/times.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=o(i("./node_modules/dayjs/dayjs.min.js")),n=o(i("./node_modules/dayjs/plugin/advancedFormat.js")),r=o(i("./node_modules/jquery/dist/jquery.js"));function o(e){return e&&e.__esModule?e:{default:e}}a.default.extend(n.default);t.default=function(){(0,r.default)("[data-time]").each(function(e,t){var i=(0,r.default)(t),n=i.data("time"),o=i.data("time-format")||"MMMM Do, h:mm:ss A";t.innerText=(0,a.default)(n).format(o)})}},"./CTFd/themes/core/assets/js/utils.js":function(e,t,i){Object.defineProperty(t,"__esModule",{value:!0}),t.WindowController=o,t.colorHash=function(e){for(var t=0,i=0;i<e.length;i++)t=e.charCodeAt(i)+((t<<5)-t),t&=t;var n=(t%25+25)%25+75,o=(t%20+20)%20+40;return"hsl(".concat((t%360+360)%360,", ").concat(n,"%, ").concat(o,"%)")},t.cumulativeSum=function(e){for(var t=e.concat(),i=0;i<e.length;i++)t[i]=e.slice(0,i+1).reduce(function(e,t){return e+t});return t},t.init_notification_counter=function(){var e=a.getItem(s);null===e?a.setItem(s,0):0<e&&(0,r.default)(".badge-notification").text(e)},t.set_notification_counter=function(e){a.setItem(s,e)},t.inc_notification_counter=function(){var e=a.getItem(s)||0;a.setItem(s,++e),(0,r.default)(".badge-notification").text(e)},t.dec_notification_counter=function(){var e=a.getItem(s)||0;0<e&&(a.setItem(s,--e),(0,r.default)(".badge-notification").text(e));0==e&&d()},t.clear_notification_counter=d,t.copyToClipboard=function(e,t){(0,r.default)(t).select(),document.execCommand("copy"),(0,r.default)(e.target).tooltip({title:"Copied!",trigger:"manual"}),(0,r.default)(e.target).tooltip("show"),setTimeout(function(){(0,r.default)(e.target).tooltip("hide")},1500)},t.makeSortableTables=function(){function a(e,t){return(0,r.default)(e).children("td").eq(t).text()}(0,r.default)("th.sort-col").append(' <i class="fas fa-sort"></i>'),(0,r.default)("th.sort-col").click(function(){var o,e=(0,r.default)(this).parents("table").eq(0),t=e.find("tr:gt(0)").toArray().sort((o=(0,r.default)(this).index(),function(e,t){var i=a(e,o),n=a(t,o);return r.default.isNumeric(i)&&r.default.isNumeric(n)?i-n:i.toString().localeCompare(n)}));this.asc=!this.asc,this.asc||(t=t.reverse());for(var i=0;i<t.length;i++)e.append(t[i])})},t.getScript=function(n){return new Promise(function(e,t){var i=document.createElement("script");document.body.appendChild(i),i.onload=e,i.onerror=t,i.async=!0,i.src=n})},t.createHtmlNode=function(e){var t=document.createElement("template");return t.innerHTML=e.trim(),t.content.firstChild},t.htmlEntities=function(e){return(0,r.default)("<div/>").text(e).html()};var n,r=(n=i("./node_modules/jquery/dist/jquery.js"))&&n.__esModule?n:{default:n};function o(){this.id=Math.random(),this.isMaster=!1,this.others={},window.addEventListener("storage",this,!1),window.addEventListener("unload",this,!1),this.broadcast("hello");var t=this;this._checkTimeout=setTimeout(function e(){t.check(),t._checkTimeout=setTimeout(e,9e3)},500),this._pingTimeout=setTimeout(function e(){t.sendPing(),t._pingTimeout=setTimeout(e,17e3)},17e3)}r.default.fn.serializeJSON=function(i){var n={},o=(0,r.default)(this),e=o.serializeArray();return(e=(e=e.concat(o.find("input[type=checkbox]:checked").map(function(){return{name:this.name,value:!0}}).get())).concat(o.find("input[type=checkbox]:not(:checked)").map(function(){return{name:this.name,value:!1}}).get())).map(function(e){var t;i&&(null===e.value||""===e.value)&&(t=o.find(":input[name='".concat(e.name,"']"))).data("initial")===t.val()||(n[e.name]=e.value)}),n},String.prototype.format=String.prototype.f=function(){for(var e=this,t=arguments.length;t--;)e=e.replace(new RegExp("\\{"+t+"\\}","gm"),arguments[t]);return e},String.prototype.hashCode=function(){var e,t,i=0;if(0==this.length)return i;for(e=0,t=this.length;e<t;e++)i=(i<<5)-i+this.charCodeAt(e),i|=0;return i},o.prototype.destroy=function(){clearTimeout(this._pingTimeout),clearTimeout(this._checkTimeout),window.removeEventListener("storage",this,!1),window.removeEventListener("unload",this,!1),this.broadcast("bye")},o.prototype.handleEvent=function(e){if("unload"===e.type)this.destroy();else if("broadcast"===e.key)try{var t=JSON.parse(e.newValue);t.id!==this.id&&this[t.type](t)}catch(e){}},o.prototype.sendPing=function(){this.broadcast("ping")},o.prototype.hello=function(e){this.ping(e),e.id<this.id?this.check():this.sendPing()},o.prototype.ping=function(e){this.others[e.id]=+new Date},o.prototype.bye=function(e){delete this.others[e.id],this.check()},o.prototype.check=function(e){var t,i=+new Date,n=!0;for(t in this.others)this.others[t]+23e3<i?delete this.others[t]:t<this.id&&(n=!1);this.isMaster!==n&&(this.isMaster=n,this.masterDidChange())},o.prototype.masterDidChange=function(){},o.prototype.broadcast=function(e,t){var i,n={id:this.id,type:e};for(i in t)n[i]=t[i];try{localStorage.setItem("broadcast",JSON.stringify(n))}catch(e){}};var a=window.localStorage,s="unread_notifications";function d(){a.setItem(s,0),(0,r.default)(".badge-notification").empty()}},0:function(e,t){},1:function(e,t){}});