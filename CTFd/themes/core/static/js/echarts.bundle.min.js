(window.webpackJsonp=window.webpackJsonp||[]).push([[2],{"./node_modules/echarts/dist/echarts-en.common.js":function(r,o,t){(function(VS){var t,e,n,i;function FS(t){return(FS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}i=function(t){"use strict";var e;"undefined"!=typeof window?e=window.__DEV__:void 0!==VS&&(e=VS.__DEV__),void 0===e&&(e=!0);var C=e,n=2311,i=function(){return n++},v="object"===("undefined"==typeof wx?"undefined":FS(wx))&&"function"==typeof wx.getSystemInfoSync?{browser:{},os:{},node:!1,wxa:!0,canvasSupported:!0,svgSupported:!1,touchEventsSupported:!0,domSupported:!1}:"undefined"==typeof document&&"undefined"!=typeof self?{browser:{},os:{},node:!1,worker:!0,canvasSupported:!0,domSupported:!1}:"undefined"==typeof navigator?{browser:{},os:{},node:!0,worker:!1,canvasSupported:!0,svgSupported:!0,domSupported:!1}:function(t){var e={},n=t.match(/Firefox\/([\d.]+)/),i=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),r=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);n&&(e.firefox=!0,e.version=n[1]);i&&(e.ie=!0,e.version=i[1]);r&&(e.edge=!0,e.version=r[1]);o&&(e.weChat=!0);return{browser:e,os:{},node:!1,canvasSupported:!!document.createElement("canvas").getContext,svgSupported:"undefined"!=typeof SVGRect,touchEventsSupported:"ontouchstart"in window&&!e.ie&&!e.edge,pointerEventsSupported:"onpointerdown"in window&&(e.edge||e.ie&&11<=e.version),domSupported:"undefined"!=typeof document}}(navigator.userAgent);var s={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},l={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},h=Object.prototype.toString,r=Array.prototype,a=r.forEach,u=r.filter,o=r.slice,c=r.map,d=r.reduce,f={};function p(t,e){"createCanvas"===t&&(_=null),f[t]=e}function T(t){if(null==t||"object"!==FS(t))return t;var e=t,n=h.call(t);if("[object Array]"===n){if(!Q(t)){e=[];for(var i=0,r=t.length;i<r;i++)e[i]=T(t[i])}}else if(l[n]){if(!Q(t)){var o=t.constructor;if(t.constructor.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=T(t[i])}}}else if(!s[n]&&!Q(t)&&!H(t))for(var a in e={},t)t.hasOwnProperty(a)&&(e[a]=T(t[a]));return e}function m(t,e,n){if(!B(e)||!B(t))return n?T(e):t;for(var i in e){var r,o;e.hasOwnProperty(i)&&(r=t[i],!B(o=e[i])||!B(r)||z(o)||z(r)||H(o)||H(r)||V(o)||V(r)||Q(o)||Q(r)?!n&&i in t||(t[i]=T(e[i])):m(r,o,n))}return t}function g(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=m(n,t[i],e);return n}function k(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function A(t,e,n){for(var i in e)e.hasOwnProperty(i)&&(n?null!=e[i]:null==t[i])&&(t[i]=e[i]);return t}function y(){return f.createCanvas()}var _;function x(){return _=_||y().getContext("2d")}function w(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function b(t,e){var n,i=t.prototype;function r(){}for(n in r.prototype=e.prototype,t.prototype=new r,i)i.hasOwnProperty(n)&&(t.prototype[n]=i[n]);(t.prototype.constructor=t).superClass=e}function S(t,e,n){A(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,n)}function E(t){if(t)return"string"!=typeof t&&"number"==typeof t.length}function D(t,e,n){if(t&&e)if(t.forEach&&t.forEach===a)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function P(t,e,n){if(t&&e){if(t.map&&t.map===c)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}}function M(t,e,n,i){if(t&&e){if(t.reduce&&t.reduce===d)return t.reduce(e,n,i);for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function I(t,e,n){if(t&&e){if(t.filter&&t.filter===u)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}}function L(t,e){var n=o.call(arguments,2);return function(){return t.apply(e,n.concat(o.call(arguments)))}}function O(t){var e=o.call(arguments,1);return function(){return t.apply(this,e.concat(o.call(arguments)))}}function z(t){return"[object Array]"===h.call(t)}function N(t){return"function"==typeof t}function R(t){return"[object String]"===h.call(t)}function B(t){var e=FS(t);return"function"===e||!!t&&"object"===e}function V(t){return!!s[h.call(t)]}function F(t){return!!l[h.call(t)]}function H(t){return"object"===FS(t)&&"number"==typeof t.nodeType&&"object"===FS(t.ownerDocument)}function W(t){return t!=t}function G(t){for(var e=0,n=arguments.length;e<n;e++)if(null!=arguments[e])return arguments[e]}function Z(t,e){return null!=t?t:e}function U(t,e,n){return null!=t?t:null!=e?e:n}function X(){return Function.call.apply(o,arguments)}function Y(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function j(t,e){if(!t)throw new Error(e)}function q(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}f.createCanvas=function(){return document.createElement("canvas")};var $="__ec_primitive__";function K(t){t[$]=!0}function Q(t){return t[$]}function J(t){var n=z(t);this.data={};var i=this;function e(t,e){n?i.set(t,e):i.set(e,t)}t instanceof J?t.each(e):t&&D(t,e)}function tt(t){return new J(t)}function et(){}J.prototype={constructor:J,get:function(t){return this.data.hasOwnProperty(t)?this.data[t]:null},set:function(t,e){return this.data[t]=e},each:function(t,e){for(var n in void 0!==e&&(t=L(t,e)),this.data)this.data.hasOwnProperty(n)&&t(this.data[n],n)},removeKey:function(t){delete this.data[t]}};var nt=(Object.freeze||Object)({$override:p,clone:T,merge:m,mergeAll:g,extend:k,defaults:A,createCanvas:y,getContext:x,indexOf:w,inherits:b,mixin:S,isArrayLike:E,each:D,map:P,reduce:M,filter:I,find:function(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]},bind:L,curry:O,isArray:z,isFunction:N,isString:R,isObject:B,isBuiltInObject:V,isTypedArray:F,isDom:H,eqNaN:W,retrieve:G,retrieve2:Z,retrieve3:U,slice:X,normalizeCssArray:Y,assert:j,trim:q,setAsPrimitive:K,isPrimitive:Q,createHashMap:tt,concatArray:function(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];for(var r=t.length,i=0;i<e.length;i++)n[i+r]=e[i];return n},noop:et}),it="undefined"==typeof Float32Array?Array:Float32Array;function rt(t,e){var n=new it(2);return null==t&&(t=0),null==e&&(e=0),n[0]=t,n[1]=e,n}function ot(t,e){return t[0]=e[0],t[1]=e[1],t}function at(t){var e=new it(2);return e[0]=t[0],e[1]=t[1],e}function st(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function lt(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t}function ht(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function ut(t){return Math.sqrt(ct(t))}function ct(t){return t[0]*t[0]+t[1]*t[1]}function dt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function ft(t,e){var n=ut(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function pt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var gt=pt;function mt(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var vt=mt;function yt(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}function _t(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function xt(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var wt=(Object.freeze||Object)({create:rt,copy:ot,clone:at,set:function(t,e,n){return t[0]=e,t[1]=n,t},add:st,scaleAndAdd:lt,sub:ht,len:ut,length:ut,lenSquare:ct,lengthSquare:ct,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:dt,normalize:ft,distance:pt,dist:gt,distanceSquare:mt,distSquare:vt,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:function(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t},applyTransform:yt,min:_t,max:xt});function bt(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this)}function St(t,e){return{target:t,topTarget:e&&e.topTarget}}bt.prototype={constructor:bt,_dragStart:function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent;e&&((this._draggingTarget=e).dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(St(e,t),"dragstart",t.event))},_drag:function(t){var e,n,i,r,o,a,s=this._draggingTarget;s&&(e=t.offsetX,n=t.offsetY,i=e-this._x,r=n-this._y,this._x=e,this._y=n,s.drift(i,r,t),this.dispatchToElement(St(s,t),"drag",t.event),o=this.findHover(e,n,s).target,a=this._dropTarget,s!==(this._dropTarget=o)&&(a&&o!==a&&this.dispatchToElement(St(a,t),"dragleave",t.event),o&&o!==a&&this.dispatchToElement(St(o,t),"dragenter",t.event)))},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(St(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(St(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}};var Mt=Array.prototype.slice,It=function(t){this._$handlers={},this._$eventProcessor=t};function Ct(t,e,n,i,r,o){var a,s,l=t._$handlers;if("function"==typeof n&&(r=i,i=n,n=null),!i||!e)return t;a=n,s=t._$eventProcessor,null!=a&&s&&s.normalizeQuery&&(a=s.normalizeQuery(a)),n=a,l[e]||(l[e]=[]);for(var h=0;h<l[e].length;h++)if(l[e][h].h===i)return t;var u={h:i,one:o,query:n,ctx:r||t,callAtLast:i.zrEventfulCallAtLast},c=l[e].length-1,d=l[e][c];return d&&d.callAtLast?l[e].splice(c,0,u):l[e].push(u),t}It.prototype={constructor:It,one:function(t,e,n,i){return Ct(this,t,e,n,i,!0)},on:function(t,e,n,i){return Ct(this,t,e,n,i,!1)},isSilent:function(t){var e=this._$handlers;return!e[t]||!e[t].length},off:function(t,e){var n=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},trigger:function(t){var e=this._$handlers[t],n=this._$eventProcessor;if(e){var i=arguments,r=i.length;3<r&&(i=Mt.call(i,1));for(var o=e.length,a=0;a<o;){var s=e[a];if(n&&n.filter&&null!=s.query&&!n.filter(t,s.query))a++;else{switch(r){case 1:s.h.call(s.ctx);break;case 2:s.h.call(s.ctx,i[1]);break;case 3:s.h.call(s.ctx,i[1],i[2]);break;default:s.h.apply(s.ctx,i)}s.one?(e.splice(a,1),o--):a++}}}return n&&n.afterTrigger&&n.afterTrigger(t),this},triggerWithContext:function(t){var e=this._$handlers[t],n=this._$eventProcessor;if(e){var i=arguments,r=i.length;4<r&&(i=Mt.call(i,1,i.length-1));for(var o=i[i.length-1],a=e.length,s=0;s<a;){var l=e[s];if(n&&n.filter&&null!=l.query&&!n.filter(t,l.query))s++;else{switch(r){case 1:l.h.call(o);break;case 2:l.h.call(o,i[1]);break;case 3:l.h.call(o,i[1],i[2]);break;default:l.h.apply(o,i)}l.one?(e.splice(s,1),a--):s++}}}return n&&n.afterTrigger&&n.afterTrigger(t),this}};var Tt=Math.log(2);function At(t,e,n,i,r,o){var a=i+"-"+r,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var l=Math.round(Math.log((1<<s)-1&~r)/Tt);return t[n][l]}for(var h=i|1<<n,u=n+1;i&1<<u;)u++;for(var c=0,d=0,f=0;d<s;d++){var p=1<<d;p&r||(c+=(f%2?-1:1)*t[n][d]*At(t,e-1,u,h,r|p,o),f++)}return o[a]=c}function Dt(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=At(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*At(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var kt="___zrEVENTSAVED",Pt=[];function Lt(t,e,n,i,r){if(e.getBoundingClientRect&&v.domSupported&&!Ot(e)){var o=e[kt]||(e[kt]={}),a=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=!0,s=[],l=[],h=0;h<4;h++){var u=t[h].getBoundingClientRect(),c=2*h,d=u.left,f=u.top;s.push(d,f),a=a&&o&&d===o[c]&&f===o[1+c],l.push(t[h].offsetLeft,t[h].offsetTop)}return a&&r?r:(e.srcCoords=s,e[i]=n?Dt(l,s):Dt(s,l))}(function(t,e){var n=e.markers;if(n)return n;n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=a.style,l=o%2,h=(o>>1)%2;s.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[l]+":0",r[h]+":0",i[1-l]+":auto",r[1-h]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}return n}(e,o),o,r);if(a)return a(t,n,i),!0}return!1}function Ot(t){return"CANVAS"===t.nodeName.toUpperCase()}var Et="undefined"!=typeof window&&!!window.addEventListener,zt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Nt=[];function Rt(t,e,n,i){return n=n||{},i||!v.canvasSupported?Bt(t,e,n):v.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):Bt(t,e,n),n}function Bt(t,e,n){if(v.domSupported&&t.getBoundingClientRect){var i=e.clientX,r=e.clientY;if(Ot(t)){var o=t.getBoundingClientRect();return n.zrX=i-o.left,void(n.zrY=r-o.top)}if(Lt(Nt,t,i,r))return n.zrX=Nt[0],void(n.zrY=Nt[1])}n.zrX=n.zrY=0}function Vt(t){return t||window.event}function Ft(t,e,n){if(null!=(e=Vt(e)).zrX)return e;var i,r=e.type;r&&0<=r.indexOf("touch")?(i="touchend"!==r?e.targetTouches[0]:e.changedTouches[0])&&Rt(t,i,e,n):(Rt(t,e,e,n),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3);var o=e.button;return null==e.which&&void 0!==o&&zt.test(e.type)&&(e.which=1&o?1:2&o?3:4&o?2:0),e}function Ht(t,e,n,i){Et?t.addEventListener(e,n,i):t.attachEvent("on"+e,n)}var Wt=Et?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0};function Gt(t){return 2===t.which||3===t.which}function Zt(){this._track=[]}function Ut(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}Zt.prototype={constructor:Zt,recognize:function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=Rt(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},_recognize:function(t){for(var e in Xt)if(Xt.hasOwnProperty(e)){var n=Xt[e](this._track,t);if(n)return n}}};var Xt={pinch:function(t,e){var n=t.length;if(n){var i,r=(t[n-1]||{}).points,o=(t[n-2]||{}).points||r;if(o&&1<o.length&&r&&1<r.length){var a=Ut(r)/Ut(o);isFinite(a)||(a=1),e.pinchScale=a;var s=[((i=r)[0][0]+i[1][0])/2,(i[0][1]+i[1][1])/2];return e.pinchX=s[0],e.pinchY=s[1],{type:"pinch",target:t[0].target,event:e}}}}},Yt="silent";function jt(){Wt(this.event)}function qt(){}qt.prototype.dispose=function(){};function $t(t,e,n,i){It.call(this),this.storage=t,this.painter=e,this.painterRoot=i,n=n||new qt,this.proxy=null,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,this._gestureMgr,bt.call(this),this.setHandlerProxy(n)}var Kt=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"];function Qt(t,e,n){var i=t.painter;return e<0||e>i.getWidth()||n<0||n>i.getHeight()}$t.prototype={constructor:$t,setHandlerProxy:function(e){this.proxy&&this.proxy.dispose(),e&&(D(Kt,function(t){e.on&&e.on(t,this[t],this)},this),e.handler=this),this.proxy=e},mousemove:function(t){var e=t.zrX,n=t.zrY,i=Qt(this,e,n),r=this._hovered,o=r.target;o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target);var a=this._hovered=i?{x:e,y:n}:this.findHover(e,n),s=a.target,l=this.proxy;l.setCursor&&l.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},mouseout:function(t){var e=t.zrEventControl,n=t.zrIsToLocalDOM;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&(n||this.trigger("globalout",{type:"globalout",event:t}))},resize:function(){this._hovered={}},dispatch:function(t,e){var n=this[t];n&&n.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r,o,a="on"+e,s={type:e,event:o=n,target:(r=t).target,topTarget:r.topTarget,cancelBubble:!1,offsetX:o.zrX,offsetY:o.zrY,gestureEvent:o.gestureEvent,pinchX:o.pinchX,pinchY:o.pinchY,pinchScale:o.pinchScale,wheelDelta:o.zrDelta,zrByTouch:o.zrByTouch,which:o.which,stop:jt};i&&(i[a]&&(s.cancelBubble=i[a].call(i,s)),i.trigger(e,s),i=i.parent,!s.cancelBubble););s.cancelBubble||(this.trigger(e,s),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[a]&&t[a].call(t,s),t.trigger&&t.trigger(e,s)}))}},findHover:function(t,e,n){for(var i,r=this.storage.getDisplayList(),o={x:t,y:e},a=r.length-1;0<=a;a--){if(r[a]!==n&&!r[a].ignore&&(i=function(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i,r=t;r;){if(r.clipPath&&!r.clipPath.contain(e,n))return!1;r.silent&&(i=!0),r=r.parent}return!i||Yt}return!1}(r[a],t,e))&&(o.topTarget||(o.topTarget=r[a]),i!==Yt)){o.target=r[a];break}}return o},processGesture:function(t,e){this._gestureMgr||(this._gestureMgr=new Zt);var n=this._gestureMgr;"start"===e&&n.clear();var i,r=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);"end"===e&&n.clear(),r&&(i=r.type,t.gestureEvent=i,this.dispatchToElement({target:r.target},i,r.event))}},D(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(a){$t.prototype[a]=function(t){var e,n,i=t.zrX,r=t.zrY,o=Qt(this,i,r);if("mouseup"===a&&o||(n=(e=this.findHover(i,r)).target),"mousedown"===a)this._downEl=n,this._downPoint=[t.zrX,t.zrY],this._upEl=n;else if("mouseup"===a)this._upEl=n;else if("click"===a){if(this._downEl!==this._upEl||!this._downPoint||4<gt(this._downPoint,[t.zrX,t.zrY]))return;this._downPoint=null}this.dispatchToElement(e,a,t)}}),S($t,It),S($t,bt);var Jt="undefined"==typeof Float32Array?Array:Float32Array;function te(){var t=new Jt(6);return ee(t),t}function ee(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function ne(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function ie(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=l,t}function re(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function oe(t,e,n){var i=e[0],r=e[2],o=e[4],a=e[1],s=e[3],l=e[5],h=Math.sin(n),u=Math.cos(n);return t[0]=i*u+a*h,t[1]=-i*h+a*u,t[2]=r*u+s*h,t[3]=-r*h+u*s,t[4]=u*o+h*l,t[5]=u*l-h*o,t}function ae(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t}function se(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],s=e[5],l=n*a-o*i;return l?(l=1/l,t[0]=a*l,t[1]=-o*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-a*r)*l,t[5]=(o*r-n*s)*l,t):null}var le=(Object.freeze||Object)({create:te,identity:ee,copy:ne,mul:ie,translate:re,rotate:oe,scale:ae,invert:se,clone:function(t){var e=te();return ne(e,t),e}}),he=ee;function ue(t){return 5e-5<t||t<-5e-5}var ce=function(t){(t=t||{}).position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},de=ce.prototype;de.transform=null,de.needLocalTransform=function(){return ue(this.rotation)||ue(this.position[0])||ue(this.position[1])||ue(this.scale[0]-1)||ue(this.scale[1]-1)};var fe=[];de.updateTransform=function(){var t,e,n,i,r,o=this.parent,a=o&&o.transform,s=this.needLocalTransform(),l=this.transform;s||a?(l=l||te(),s?this.getLocalTransform(l):he(l),a&&(s?ie(l,o.transform,l):ne(l,o.transform)),this.transform=l,null!=(t=this.globalScaleRatio)&&1!==t&&(this.getGlobalScale(fe),i=((fe[0]-(e=fe[0]<0?-1:1))*t+e)/fe[0]||0,r=((fe[1]-(n=fe[1]<0?-1:1))*t+n)/fe[1]||0,l[0]*=i,l[1]*=i,l[2]*=r,l[3]*=r),this.invTransform=this.invTransform||te(),se(this.invTransform,l)):l&&he(l)},de.getLocalTransform=function(t){return ce.getLocalTransform(this,t)},de.setTransform=function(t){var e=this.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)},de.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var pe=[],ge=te();de.setLocalTransform=function(t){var e,n,i,r;t&&(e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=this.position,r=this.scale,ue(e-1)&&(e=Math.sqrt(e)),ue(n-1)&&(n=Math.sqrt(n)),t[0]<0&&(e=-e),t[3]<0&&(n=-n),i[0]=t[4],i[1]=t[5],r[0]=e,r[1]=n,this.rotation=Math.atan2(-t[1]/n,t[0]/e))},de.decomposeTransform=function(){var t,e,n;this.transform&&(t=this.parent,e=this.transform,t&&t.transform&&(ie(pe,t.invTransform,e),e=pe),(n=this.origin)&&(n[0]||n[1])&&(ge[4]=n[0],ge[5]=n[1],ie(pe,e,ge),pe[4]-=n[0],pe[5]-=n[1],e=pe),this.setLocalTransform(e))},de.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1])):(t[0]=1,t[1]=1),t},de.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&yt(n,n,i),n},de.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&yt(n,n,i),n},ce.getLocalTransform=function(t,e){he(e=e||[]);var n=t.origin,i=t.scale||[1,1],r=t.rotation||0,o=t.position||[0,0];return n&&(e[4]-=n[0],e[5]-=n[1]),ae(e,e,i),r&&oe(e,e,r),n&&(e[4]+=n[0],e[5]+=n[1]),e[4]+=o[0],e[5]+=o[1],e};var me={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(e=!n||n<1?(n=1,.1):.4*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:n*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)},backIn:function(t){return t*t*(2.70158*t-1.70158)},backOut:function(t){return--t*t*(2.70158*t+1.70158)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)},bounceIn:function(t){return 1-me.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*me.bounceIn(2*t):.5*me.bounceOut(2*t-1)+.5}};function ve(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null!=t.loop&&t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}ve.prototype={constructor:ve,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)this._pausedTime+=e;else{var n=(t-this._startTime-this._pausedTime)/this._life;if(!(n<0)){n=Math.min(n,1);var i=this.easing,r="string"==typeof i?me[i]:i,o="function"==typeof r?r(n):n;return this.fire("frame",o),1===n?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){this[t="on"+t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}};function ye(){this.head=null,this.tail=null,this._len=0}var _e=ye.prototype;_e.insert=function(t){var e=new we(t);return this.insertEntry(e),e},_e.insertEntry=function(t){this.head?((this.tail.next=t).prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},_e.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},_e.len=function(){return this._len},_e.clear=function(){this.head=this.tail=null,this._len=0};function xe(t){this._list=new ye,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null}var we=function(t){this.value=t,this.next,this.prev},be=xe.prototype;be.put=function(t,e){var n,i,r,o=this._list,a=this._map,s=null;return null==a[t]&&(n=o.len(),i=this._lastRemovedEntry,n>=this._maxSize&&0<n&&(r=o.head,o.remove(r),delete a[r.key],s=r.value,this._lastRemovedEntry=r),i?i.value=e:i=new we(e),i.key=t,o.insertEntry(i),a[t]=i),s},be.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},be.clear=function(){this._list.clear(),this._map={}};var Se={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Me(t){return(t=Math.round(t))<0?0:255<t?255:t}function Ie(t){return t<0?0:1<t?1:t}function Ce(t){return t.length&&"%"===t.charAt(t.length-1)?Me(parseFloat(t)/100*255):Me(parseInt(t,10))}function Te(t){return t.length&&"%"===t.charAt(t.length-1)?Ie(parseFloat(t)/100):Ie(parseFloat(t))}function Ae(t,e,n){return n<0?n+=1:1<n&&--n,6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function De(t,e,n){return t+(e-t)*n}function ke(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function Pe(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var Le=new xe(20),Oe=null;function Ee(t,e){Oe&&Pe(Oe,e),Oe=Le.put(t,Oe||e.slice())}function ze(t,e){if(t){e=e||[];var n=Le.get(t);if(n)return Pe(e,n);var i,r=(t+="").replace(/ /g,"").toLowerCase();if(r in Se)return Pe(e,Se[r]),Ee(t,e),e;if("#"===r.charAt(0))return 4===r.length?0<=(i=parseInt(r.substr(1),16))&&i<=4095?(ke(e,(3840&i)>>4|(3840&i)>>8,240&i|(240&i)>>4,15&i|(15&i)<<4,1),Ee(t,e),e):void ke(e,0,0,0,1):7===r.length?0<=(i=parseInt(r.substr(1),16))&&i<=16777215?(ke(e,(16711680&i)>>16,(65280&i)>>8,255&i,1),Ee(t,e),e):void ke(e,0,0,0,1):void 0;var o=r.indexOf("("),a=r.indexOf(")");if(-1!==o&&a+1===r.length){var s=r.substr(0,o),l=r.substr(o+1,a-(o+1)).split(","),h=1;switch(s){case"rgba":if(4!==l.length)return void ke(e,0,0,0,1);h=Te(l.pop());case"rgb":return 3!==l.length?void ke(e,0,0,0,1):(ke(e,Ce(l[0]),Ce(l[1]),Ce(l[2]),h),Ee(t,e),e);case"hsla":return 4!==l.length?void ke(e,0,0,0,1):(l[3]=Te(l[3]),Ne(l,e),Ee(t,e),e);case"hsl":return 3!==l.length?void ke(e,0,0,0,1):(Ne(l,e),Ee(t,e),e);default:return}}ke(e,0,0,0,1)}}function Ne(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=Te(t[1]),r=Te(t[2]),o=r<=.5?r*(i+1):r+i-r*i,a=2*r-o;return ke(e=e||[],Me(255*Ae(a,o,n+1/3)),Me(255*Ae(a,o,n)),Me(255*Ae(a,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Re(t,e){var n=ze(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,255<n[i]?n[i]=255:t[i]<0&&(n[i]=0);return We(n,4===n.length?"rgba":"rgb")}}function Be(t){var e=ze(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)}function Ve(t,e,n){if(e&&e.length&&0<=t&&t<=1){n=n||[];var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=e[r],s=e[o],l=i-r;return n[0]=Me(De(a[0],s[0],l)),n[1]=Me(De(a[1],s[1],l)),n[2]=Me(De(a[2],s[2],l)),n[3]=Ie(De(a[3],s[3],l)),n}}function Fe(t,e,n){if(e&&e.length&&0<=t&&t<=1){var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=ze(e[r]),s=ze(e[o]),l=i-r,h=We([Me(De(a[0],s[0],l)),Me(De(a[1],s[1],l)),Me(De(a[2],s[2],l)),Ie(De(a[3],s[3],l))],"rgba");return n?{color:h,leftIndex:r,rightIndex:o,value:i}:h}}function He(t,e){if((t=ze(t))&&null!=e)return t[3]=Ie(e),We(t,"rgba")}function We(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}}var Ge=(Object.freeze||Object)({parse:ze,lift:Re,toHex:Be,fastLerp:Ve,fastMapToColor:Ve,lerp:Fe,mapToColor:Fe,modifyHSL:function(t,e,n,i){if(t=ze(t))return t=function(t){if(t){var e,n,i,r,o,a=t[0]/255,s=t[1]/255,l=t[2]/255,h=Math.min(a,s,l),u=Math.max(a,s,l),c=u-h,d=(u+h)/2;0==c?n=e=0:(n=d<.5?c/(u+h):c/(2-u-h),i=((u-a)/6+c/2)/c,r=((u-s)/6+c/2)/c,o=((u-l)/6+c/2)/c,a===u?e=o-r:s===u?e=1/3+i-o:l===u&&(e=2/3+r-i),e<0&&(e+=1),1<e&&--e);var f=[360*e,n,d];return null!=t[3]&&f.push(t[3]),f}}(t),null!=e&&(t[0]=(r=e,(r=Math.round(r))<0?0:360<r?360:r)),null!=n&&(t[1]=Te(n)),null!=i&&(t[2]=Te(i)),We(Ne(t),"rgba");var r},modifyAlpha:He,stringify:We}),Ze=Array.prototype.slice;function Ue(t,e){return t[e]}function Xe(t,e,n){t[e]=n}function Ye(t,e,n){return(e-t)*n+t}function je(t,e,n){return.5<n?e:t}function qe(t,e,n,i,r){var o=t.length;if(1===r)for(var a=0;a<o;a++)i[a]=Ye(t[a],e[a],n);else for(var s=o&&t[0].length,a=0;a<o;a++)for(var l=0;l<s;l++)i[a][l]=Ye(t[a][l],e[a][l],n)}function $e(t,e,n){var i=t.length,r=e.length;if(i!==r)if(r<i)t.length=r;else for(var o=i;o<r;o++)t.push(1===n?e[o]:Ze.call(e[o]));for(var a=t[0]&&t[0].length,o=0;o<t.length;o++)if(1===n)isNaN(t[o])&&(t[o]=e[o]);else for(var s=0;s<a;s++)isNaN(t[o][s])&&(t[o][s]=e[o][s])}function Ke(t,e,n,i,r,o,a,s,l){var h=t.length;if(1===l)for(var u=0;u<h;u++)s[u]=Qe(t[u],e[u],n[u],i[u],r,o,a);else for(var c=t[0].length,u=0;u<h;u++)for(var d=0;d<c;d++)s[u][d]=Qe(t[u][d],e[u][d],n[u][d],i[u][d],r,o,a)}function Qe(t,e,n,i,r,o,a){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*a+(-3*(e-n)-2*s-l)*o+s*r+e}function Je(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function tn(t,e,n,i,o,r){var a=t._getter,s=t._setter,l="spline"===e,h=i.length;if(h){var u,c,d,f=E(i[0].value),p=!1,g=!1,m=f?E((c=(u=i)[u.length-1].value)&&c[0])?2:1:0;i.sort(function(t,e){return t.time-e.time}),d=i[h-1].time;for(var v=[],y=[],_=i[0].value,x=!0,w=0;w<h;w++){v.push(i[w].time/d);var b,S=i[w].value;f&&function(t,e,n){if(t===e)return 1;var i=t.length;if(i===e.length){if(1===n){for(var r=0;r<i;r++)if(t[r]!==e[r])return}else for(var o=t[0].length,r=0;r<i;r++)for(var a=0;a<o;a++)if(t[r][a]!==e[r][a])return;return 1}}(S,_,m)||!f&&S===_||(x=!1),"string"==typeof(_=S)&&((b=ze(S))?(S=b,p=!0):g=!0),y.push(S)}if(r||!x){for(var M=y[h-1],w=0;w<h-1;w++)f?$e(y[w],M,m):!isNaN(y[w])||isNaN(M)||g||p||(y[w]=M);f&&$e(a(t._target,o),M,m);var I,C,T,A,D,k,P=0,L=0;p&&(k=[0,0,0,0]);var O=new ve({target:t._target,life:d,loop:t._loop,delay:t._delay,onframe:function(t,e){var n;if(e<0)n=0;else if(e<L){for(n=Math.min(P+1,h-1);0<=n&&!(v[n]<=e);n--);n=Math.min(n,h-2)}else{for(n=P;n<h&&!(v[n]>e);n++);n=Math.min(n-1,h-2)}L=e;var i,r=v[(P=n)+1]-v[n];if(0!=r)if(I=(e-v[n])/r,l)if(T=y[n],C=y[0===n?n:n-1],A=y[h-2<n?h-1:n+1],D=y[h-3<n?h-1:n+2],f)Ke(C,T,A,D,I,I*I,I*I*I,a(t,o),m);else{if(p)i=Ke(C,T,A,D,I,I*I,I*I*I,k,1),i=Je(k);else{if(g)return.5<I?A:T;i=Qe(C,T,A,D,I,I*I,I*I*I)}s(t,o,i)}else if(f)qe(y[n],y[n+1],I,a(t,o),m);else{if(p)qe(y[n],y[n+1],I,k,1),i=Je(k);else{if(g)return je(y[n],y[n+1],I);i=Ye(y[n],y[n+1],I)}s(t,o,i)}},ondestroy:n});return e&&"spline"!==e&&(O.easing=e),O}}}function en(t,e,n,i){this._tracks={},this._target=t,this._loop=e||!1,this._getter=n||Ue,this._setter=i||Xe,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]}en.prototype={when:function(t,e){var n,i=this._tracks;for(n in e)if(e.hasOwnProperty(n)){if(!i[n]){i[n]=[];var r=this._getter(this._target,n);if(null==r)continue;0!==t&&i[n].push({time:0,value:function(t){if(E(t)){var e=t.length;if(E(t[0])){for(var n=[],i=0;i<e;i++)n.push(Ze.call(t[i]));return n}return Ze.call(t)}return t}(r)})}i[n].push({time:t,value:e[n]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,n=0;n<e;n++)t[n].call(this)},start:function(t,e){function n(){--l||s._doneCallback()}var i,r,o,a,s=this,l=0;for(r in this._tracks){this._tracks.hasOwnProperty(r)&&((o=tn(this,t,n,this._tracks[r],r,e))&&(this._clipList.push(o),l++,this.animation&&this.animation.addClip(o),i=o))}return i&&(a=i.onframe,i.onframe=function(t,e){a(t,e);for(var n=0;n<s._onframeList.length;n++)s._onframeList[n](t,e)}),l||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,n=this.animation,i=0;i<e.length;i++){var r=e[i];t&&r.onframe(this._target,1),n&&n.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}};var nn=1;"undefined"!=typeof window&&(nn=Math.max(window.devicePixelRatio||1,1));var rn=nn,on=function(){};function an(){this.animators=[]}var sn=on;function ln(t,e,n,i,r,o,a,s){R(i)?(o=r,r=i,i=0):N(r)?(o=r,r="linear",i=0):N(i)?(o=i,i=0):n=N(n)?(o=n,500):n||500,t.stopAnimation(),function t(e,n,i,r,o,a,s){var l={};var h=0;for(var u in r)r.hasOwnProperty(u)&&(null!=i[u]?B(r[u])&&!E(r[u])?t(e,n?n+"."+u:u,i[u],r[u],o,a,s):(s?(l[u]=i[u],hn(e,n,u,r[u])):l[u]=r[u],h++):null==r[u]||s||hn(e,n,u,r[u]));0<h&&e.animate(n,!1).when(null==o?500:o,l).delay(a||0)}(t,"",t,e,n,i,s);var l=t.animators.slice(),h=l.length;function u(){--h||o&&o()}h||o&&o();for(var c=0;c<l.length;c++)l[c].done(u).start(r,a)}function hn(t,e,n,i){var r;e?((r={})[e]={},r[e][n]=i,t.attr(r)):t.attr(n,i)}an.prototype={constructor:an,animate:function(t,e){var n,i=!1,r=this,o=this.__zr;if(t){for(var a=t.split("."),s=r,i="shape"===a[0],l=0,h=a.length;l<h;l++)s=s&&s[a[l]];s&&(n=s)}else n=r;if(n){var u=r.animators,c=new en(n,e);return c.during(function(t){r.dirty(i)}).done(function(){u.splice(w(u,c),1)}),u.push(c),o&&o.animation.addAnimator(c),c}sn('Property "'+t+'" is not existed in element '+r.id)},stopAnimation:function(t){for(var e=this.animators,n=e.length,i=0;i<n;i++)e[i].stop(t);return e.length=0,this},animateTo:function(t,e,n,i,r,o){ln(this,t,e,n,i,r,o)},animateFrom:function(t,e,n,i,r,o){ln(this,t,e,n,i,r,o,!0)}};var un=function(t){ce.call(this,t),It.call(this,t),an.call(this,t),this.id=t.id||i()};un.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,isGroup:!1,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var n=this.transform;(n=n||(this.transform=[1,0,0,1,0,0]))[4]+=t,n[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(){},attrKV:function(t,e){var n;"position"===t||"scale"===t||"origin"===t?e&&((n=(n=this[t])||(this[t]=[]))[0]=e[0],n[1]=e[1]):this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(B(t))for(var n in t)t.hasOwnProperty(n)&&this.attrKV(n,t[n]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),(this.clipPath=t).__zr=e,(t.__clipTarget=this).dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},S(un,an),S(un,ce),S(un,It);var cn,dn,fn,pn,gn=yt,mn=Math.min,vn=Math.max;function yn(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}yn.prototype={constructor:yn,union:function(t){var e=mn(t.x,this.x),n=mn(t.y,this.y);this.width=vn(t.x+t.width,this.x+this.width)-e,this.height=vn(t.y+t.height,this.y+this.height)-n,this.x=e,this.y=n},applyTransform:(cn=[],dn=[],fn=[],pn=[],function(t){var e,n;t&&(cn[0]=fn[0]=this.x,cn[1]=pn[1]=this.y,dn[0]=pn[0]=this.x+this.width,dn[1]=fn[1]=this.y+this.height,gn(cn,cn,t),gn(dn,dn,t),gn(fn,fn,t),gn(pn,pn,t),this.x=mn(cn[0],dn[0],fn[0],pn[0]),this.y=mn(cn[1],dn[1],fn[1],pn[1]),e=vn(cn[0],dn[0],fn[0],pn[0]),n=vn(cn[1],dn[1],fn[1],pn[1]),this.width=e-this.x,this.height=n-this.y)}),calculateTransform:function(t){var e=t.width/this.width,n=t.height/this.height,i=te();return re(i,i,[-this.x,-this.y]),ae(i,i,[e,n]),re(i,i,[t.x,t.y]),i},intersect:function(t){if(!t)return!1;t instanceof yn||(t=yn.create(t));var e=this,n=e.x,i=e.x+e.width,r=e.y,o=e.y+e.height,a=t.x,s=t.x+t.width,l=t.y,h=t.y+t.height;return!(i<a||s<n||o<l||h<r)},contain:function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},clone:function(){return new yn(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},yn.create=function(t){return new yn(t.x,t.y,t.width,t.height)};var _n=function(t){for(var e in t=t||{},un.call(this,t),t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};_n.prototype={constructor:_n,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},childCount:function(){return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){var n,i;return t&&t!==this&&t.parent!==this&&e&&e.parent===this&&(0<=(i=(n=this._children).indexOf(e))&&(n.splice(i,0,t),this._doAdd(t))),this},_doAdd:function(t){t.parent&&t.parent.remove(t);var e=(t.parent=this).__storage,n=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof _n&&t.addChildrenToStorage(e)),n&&n.refresh()},remove:function(t){var e=this.__zr,n=this.__storage,i=this._children,r=w(i,t);return r<0||(i.splice(r,1),t.parent=null,n&&(n.delFromStorage(t),t instanceof _n&&t.delChildrenFromStorage(n)),e&&e.refresh()),this},removeAll:function(){for(var t,e=this._children,n=this.__storage,i=0;i<e.length;i++)t=e[i],n&&(n.delFromStorage(t),t instanceof _n&&t.delChildrenFromStorage(n)),t.parent=null;return e.length=0,this},eachChild:function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},traverse:function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n];t.call(e,i),"group"===i.type&&i.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.addToStorage(n),n instanceof _n&&n.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.delFromStorage(n),n instanceof _n&&n.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,n=new yn(0,0,0,0),i=t||this._children,r=[],o=0;o<i.length;o++){var a,s,l=i[o];l.ignore||l.invisible||(a=l.getBoundingRect(),(s=l.getLocalTransform(r))?(n.copy(a),n.applyTransform(s),(e=e||n.clone()).union(n)):(e=e||a.clone()).union(a))}return e||n}},b(_n,un);var xn=32,wn=7;function bn(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;!function(t,e,n){n--;for(;e<n;){var i=t[e];t[e++]=t[n],t[n--]=i}}(t,e,r)}else for(;r<n&&0<=i(t[r],t[r-1]);)r++;return r-e}function Sn(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=1+o;var h=i-s;switch(h){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;0<h;)t[s+h]=t[s+h-1],h--}t[s]=a}}function Mn(t,e,n,i,r,o){var a=0,s=0,l=1;if(0<o(t,e[n+r])){for(s=i-r;l<s&&0<o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s);var h=a,a=r-l,l=r-h}for(a++;a<l;){var u=a+(l-a>>>1);0<o(t,e[n+u])?a=u+1:l=u}return l}function In(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s);var h=a,a=r-l,l=r-h}else{for(s=i-r;l<s&&0<=o(t,e[n+r+l]);)(l=1+((a=l)<<1))<=0&&(l=s);s<l&&(l=s),a+=r,l+=r}for(a++;a<l;){var u=a+(l-a>>>1);o(t,e[n+u])<0?l=u:a=u+1}return l}function Cn(p,g){var a,s,m=wn,l=0,v=[];function e(t){var e=a[t],n=s[t],i=a[t+1],r=s[t+1];s[t]=n+r,t===l-3&&(a[t+1]=a[t+2],s[t+1]=s[t+2]),l--;var o=In(p[i],p,e,n,0,g);e+=o,0!=(n-=o)&&0!==(r=Mn(p[e+n-1],p,i,r,r-1,g))&&(n<=r?function(t,e,n,i){var r=0;for(r=0;r<e;r++)v[r]=p[t+r];var o=0,a=n,s=t;if(p[s++]=p[a++],0==--i){for(r=0;r<e;r++)p[s+r]=v[o+r];return}if(1===e){for(r=0;r<i;r++)p[s+r]=p[a+r];return p[s+i]=v[o]}var l,h,u,c=m;for(;;){h=l=0,u=!1;do{if(g(p[a],v[o])<0){if(p[s++]=p[a++],h++,(l=0)==--i){u=!0;break}}else if(p[s++]=v[o++],l++,h=0,1==--e){u=!0;break}}while((l|h)<c);if(u)break;do{if(0!==(l=In(p[a],v,o,e,0,g))){for(r=0;r<l;r++)p[s+r]=v[o+r];if(s+=l,o+=l,(e-=l)<=1){u=!0;break}}if(p[s++]=p[a++],0==--i){u=!0;break}if(0!==(h=Mn(v[o],p,a,i,0,g))){for(r=0;r<h;r++)p[s+r]=p[a+r];if(s+=h,a+=h,0===(i-=h)){u=!0;break}}if(p[s++]=v[o++],1==--e){u=!0;break}c--}while(wn<=l||wn<=h);if(u)break;c<0&&(c=0),c+=2}if((m=c)<1&&(m=1),1===e){for(r=0;r<i;r++)p[s+r]=p[a+r];p[s+i]=v[o]}else{if(0===e)throw new Error;for(r=0;r<e;r++)p[s+r]=v[o+r]}}:function(t,e,n,i){var r=0;for(r=0;r<i;r++)v[r]=p[n+r];var o=t+e-1,a=i-1,s=n+i-1,l=0,h=0;if(p[s--]=p[o--],0==--e){for(l=s-(i-1),r=0;r<i;r++)p[l+r]=v[r];return}if(1===i){for(h=(s-=e)+1,l=(o-=e)+1,r=e-1;0<=r;r--)p[h+r]=p[l+r];return p[s]=v[a]}var u=m;for(;;){var c=0,d=0,f=!1;do{if(g(v[a],p[o])<0){if(p[s--]=p[o--],c++,(d=0)==--e){f=!0;break}}else if(p[s--]=v[a--],d++,c=0,1==--i){f=!0;break}}while((c|d)<u);if(f)break;do{if(0!==(c=e-In(v[a],p,t,e,e-1,g))){for(e-=c,h=(s-=c)+1,l=(o-=c)+1,r=c-1;0<=r;r--)p[h+r]=p[l+r];if(0===e){f=!0;break}}if(p[s--]=v[a--],1==--i){f=!0;break}if(0!==(d=i-Mn(p[o],v,0,i,i-1,g))){for(i-=d,h=(s-=d)+1,l=(a-=d)+1,r=0;r<d;r++)p[h+r]=v[l+r];if(i<=1){f=!0;break}}if(p[s--]=p[o--],0==--e){f=!0;break}u--}while(wn<=c||wn<=d);if(f)break;u<0&&(u=0),u+=2}(m=u)<1&&(m=1);if(1===i){for(h=(s-=e)+1,l=(o-=e)+1,r=e-1;0<=r;r--)p[h+r]=p[l+r];p[s]=v[a]}else{if(0===i)throw new Error;for(l=s-(i-1),r=0;r<i;r++)p[l+r]=v[r]}})(e,n,i,r)}a=[],s=[],this.mergeRuns=function(){for(;1<l;){var t=l-2;if(1<=t&&s[t-1]<=s[t]+s[t+1]||2<=t&&s[t-2]<=s[t]+s[t-1])s[t-1]<s[t+1]&&t--;else if(s[t]>s[t+1])break;e(t)}},this.forceMergeRuns=function(){for(;1<l;){var t=l-2;0<t&&s[t-1]<s[t+1]&&t--,e(t)}},this.pushRun=function(t,e){a[l]=t,s[l]=e,l+=1}}function Tn(t,e,n,i){n=n||0;var r=(i=i||t.length)-n;if(!(r<2)){var o=0;if(r<xn)Sn(t,n,i,n+(o=bn(t,n,i,e)),e);else{var a,s=new Cn(t,e),l=function(t){for(var e=0;xn<=t;)e|=1&t,t>>=1;return t+e}(r);do{(o=bn(t,n,i,e))<l&&(l<(a=r)&&(a=l),Sn(t,n,n+a,n+o,e),o=a),s.pushRun(n,o),s.mergeRuns(),r-=o,n+=o}while(0!==r);s.forceMergeRuns()}}}function An(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}function Dn(){this._roots=[],this._displayList=[],this._displayListLen=0}Dn.prototype={constructor:Dn,traverse:function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,v.canvasSupported&&Tn(n,An)},_updateAndAddDisplayable:function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var i=t.clipPath;if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),r=(o=r).clipPath}if(t.isGroup){for(var a=t._children,s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,n)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof _n&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var n=this._roots[e];n instanceof _n&&n.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var e=0,i=t.length;e<i;e++)this.delRoot(t[e]);else{var r=w(this._roots,t);0<=r&&(this.delFromStorage(t),this._roots.splice(r,1),t instanceof _n&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t&&(t.__storage=this,t.dirty(!1)),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:An};var kn={shadowBlur:1,shadowOffsetX:1,shadowOffsetY:1,textShadowBlur:1,textShadowOffsetX:1,textShadowOffsetY:1,textBoxShadowBlur:1,textBoxShadowOffsetX:1,textBoxShadowOffsetY:1},Pn=function(t,e,n){return kn.hasOwnProperty(e)?n*t.dpr:n},Ln={NONE:0,STYLE_BIND:1,PLAIN_TEXT:2},On=9,En=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],zn=function(t){this.extendFrom(t,!1)};function Nn(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;return e.global||(i=i*n.width+n.x,r=r*n.width+n.x,o=o*n.height+n.y,a=a*n.height+n.y),i=isNaN(i)?0:i,r=isNaN(r)?1:r,o=isNaN(o)?0:o,a=isNaN(a)?0:a,t.createLinearGradient(i,o,r,a)}function Rn(t,e,n){var i=n.width,r=n.height,o=Math.min(i,r),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;return e.global||(a=a*i+n.x,s=s*r+n.y,l*=o),t.createRadialGradient(a,s,0,a,s,l)}zn.prototype={constructor:zn,fill:"#000",stroke:null,opacity:1,fillOpacity:null,strokeOpacity:null,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,n){var i=this,r=n&&n.style,o=!r||t.__attrCachedBy!==Ln.STYLE_BIND;t.__attrCachedBy=Ln.STYLE_BIND;for(var a,s=0;s<En.length;s++){var l=En[s],h=l[0];!o&&i[h]===r[h]||(t[h]=Pn(t,h,i[h]||l[1]))}!o&&i.fill===r.fill||(t.fillStyle=i.fill),!o&&i.stroke===r.stroke||(t.strokeStyle=i.stroke),!o&&i.opacity===r.opacity||(t.globalAlpha=null==i.opacity?1:i.opacity),!o&&i.blend===r.blend||(t.globalCompositeOperation=i.blend||"source-over"),this.hasStroke()&&(a=i.lineWidth,t.lineWidth=a/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1))},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&0<this.lineWidth},extendFrom:function(t,e){if(t)for(var n in t)!t.hasOwnProperty(n)||!0!==e&&(!1===e?this.hasOwnProperty(n):null==t[n])||(this[n]=t[n])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,n){for(var i=("radial"===e.type?Rn:Nn)(t,e,n),r=e.colorStops,o=0;o<r.length;o++)i.addColorStop(r[o].offset,r[o].color);return i}};for(var Bn=zn.prototype,Vn=0;Vn<En.length;Vn++){var Fn=En[Vn];Fn[0]in Bn||(Bn[Fn[0]]=Fn[1])}zn.getGradient=Bn.getGradient;function Hn(t,e){this.image=t,this.repeat=e,this.type="pattern"}function Wn(){return!1}function Gn(t,e,n){var i=y(),r=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left=0,a.top=0,a.width=r+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=o*n,i}function Zn(t,e,n){var i;n=n||rn,"string"==typeof t?i=Gn(t,e,n):B(t)&&(t=(i=t).id),this.id=t;var r=(this.dom=i).style;r&&(i.onselectstart=Wn,r["-webkit-user-select"]="none",r["user-select"]="none",r["-webkit-touch-callout"]="none",r["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",r.padding=0,r.margin=0,r["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=n}Zn.prototype={constructor:Zn,__dirty:!0,__used:!(Hn.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")}),__drawIndex:0,__startIndex:0,__endIndex:0,incremental:!1,getElementCount:function(){return this.__endIndex-this.__startIndex},initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=Gn("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},resize:function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n&&this.ctxBack.scale(n,n))},clear:function(t,e){var n,i,r=this.dom,o=this.ctx,a=r.width,s=r.height,e=e||this.clearColor,l=this.motionBlur&&!t,h=this.lastFrameAlpha,u=this.dpr;l&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(r,0,0,a/u,s/u)),o.clearRect(0,0,a,s),e&&"transparent"!==e&&(e.colorStops?(n=e.__canvasGradient||zn.getGradient(o,e,{x:0,y:0,width:a,height:s}),e.__canvasGradient=n):e.image&&(n=Hn.prototype.getCanvasPattern.call(e,o)),o.save(),o.fillStyle=n||e,o.fillRect(0,0,a,s),o.restore()),l&&(i=this.domBack,o.save(),o.globalAlpha=h,o.drawImage(i,0,0,a,s),o.restore())}};var Un="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)},Xn=new xe(50);function Yn(t,e,n,i,r){if(t){if("string"!=typeof t)return t;if(e&&e.__zrImageSrc===t||!n)return e;var o=Xn.get(t),a={hostEl:n,cb:i,cbPayload:r};return o?qn(e=o.image)||o.pending.push(a):((e=new Image).onload=e.onerror=jn,Xn.put(t,e.__cachedImgObj={image:e,pending:[a]}),e.src=e.__zrImageSrc=t),e}return e}function jn(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function qn(t){return t&&t.width&&t.height}var $n={},Kn=0,Qn=5e3,Jn=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,ti="12px sans-serif",ei={};function ni(t,e){var n=t+":"+(e=e||ti);if($n[n])return $n[n];for(var i,r,o=(t+"").split("\n"),a=0,s=0,l=o.length;s<l;s++)a=Math.max((i=o[s],r=e,ei.measureText(i,r)).width,a);return Qn<Kn&&(Kn=0,$n={}),Kn++,$n[n]=a}function ii(t,e,n,i,r,o,a,s){return a?(h=i,u=di(t,{rich:a,truncate:s,font:e,textAlign:l=n,textPadding:r,textLineHeight:o}),c=u.outerWidth,d=u.outerHeight,f=ri(0,c,l),p=oi(0,d,h),new yn(f,p,c,d)):function(t,e,n,i,r,o,a){var s=ci(t,e,r,o,a),l=ni(t,e);r&&(l+=r[1]+r[3]);var h=s.outerHeight,u=ri(0,l,n),c=oi(0,h,i),d=new yn(u,c,l,h);return d.lineHeight=s.lineHeight,d}(t,e,n,i,r,o,s);var l,h,u,c,d,f,p}function ri(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function oi(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function ai(t,e,n){var i=e.textPosition,r=e.textDistance,o=n.x,a=n.y,r=r||0,s=n.height,l=n.width,h=s/2,u="left",c="top";switch(i){case"left":o-=r,a+=h,u="right",c="middle";break;case"right":o+=r+l,a+=h,c="middle";break;case"top":o+=l/2,a-=r,u="center",c="bottom";break;case"bottom":o+=l/2,a+=s+r,u="center";break;case"inside":o+=l/2,a+=h,u="center",c="middle";break;case"insideLeft":o+=r,a+=h,c="middle";break;case"insideRight":o+=l-r,a+=h,u="right",c="middle";break;case"insideTop":o+=l/2,a+=r,u="center";break;case"insideBottom":o+=l/2,a+=s-r,u="center",c="bottom";break;case"insideTopLeft":o+=r,a+=r;break;case"insideTopRight":o+=l-r,a+=r,u="right";break;case"insideBottomLeft":o+=r,a+=s-r,c="bottom";break;case"insideBottomRight":o+=l-r,a+=s-r,u="right",c="bottom"}return(t=t||{}).x=o,t.y=a,t.textAlign=u,t.textVerticalAlign=c,t}function si(t,e,n,i,r){if(!e)return"";var o=(t+"").split("\n");r=li(e,n,i,r);for(var a=0,s=o.length;a<s;a++)o[a]=hi(o[a],r);return o.join("\n")}function li(t,e,n,i){(i=k({},i)).font=e;n=Z(n,"...");i.maxIterations=Z(i.maxIterations,2);var r=i.minChar=Z(i.minChar,0);i.cnCharWidth=ni("国",e);var o=i.ascCharWidth=ni("a",e);i.placeholder=Z(i.placeholder,"");for(var a=t=Math.max(0,t-1),s=0;s<r&&o<=a;s++)a-=o;var l=ni(n,e);return a<l&&(n="",l=0),a=t-l,i.ellipsis=n,i.ellipsisWidth=l,i.contentWidth=a,i.containerWidth=t,i}function hi(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";if((s=ni(t,i))<=n)return t;for(var o=0;;o++){if(s<=r||o>=e.maxIterations){t+=e.ellipsis;break}var a=0===o?function(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}(t,r,e.ascCharWidth,e.cnCharWidth):0<s?Math.floor(t.length*r/s):0,s=ni(t=t.substr(0,a),i)}return""===t&&(t=e.placeholder),t}function ui(t){return ni("国",t)}function ci(t,e,n,i,r){null!=t&&(t+="");var o=Z(i,ui(e)),a=t?t.split("\n"):[],s=a.length*o,l=s,h=!0;if(n&&(l+=n[0]+n[2]),t&&r){h=!1;var u=r.outerHeight,c=r.outerWidth;if(null!=u&&u<l)t="",a=[];else if(null!=c)for(var d=li(c-(n?n[1]+n[3]:0),e,r.ellipsis,{minChar:r.minChar,placeholder:r.placeholder}),f=0,p=a.length;f<p;f++)a[f]=hi(a[f],d)}return{lines:a,height:s,outerHeight:l,lineHeight:o,canCacheByTextString:h}}function di(t,e){var n={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return n;for(var i,r=Jn.lastIndex=0;null!=(i=Jn.exec(t));){var o=i.index;r<o&&fi(n,t.substring(r,o)),fi(n,i[2],i[1]),r=Jn.lastIndex}r<t.length&&fi(n,t.substring(r,t.length));var a=n.lines,s=0,l=0,h=[],u=e.textPadding,c=e.truncate,d=c&&c.outerWidth,f=c&&c.outerHeight;u&&(null!=d&&(d-=u[1]+u[3]),null!=f&&(f-=u[0]+u[2]));for(var p=0;p<a.length;p++){for(var g=a[p],m=0,v=0,y=0;y<g.tokens.length;y++){var _=(D=g.tokens[y]).styleName&&e.rich[D.styleName]||{},x=D.textPadding=_.textPadding,w=D.font=_.font||e.font,b=D.textHeight=Z(_.textHeight,ui(w));if(x&&(b+=x[0]+x[2]),D.height=b,D.lineHeight=U(_.textLineHeight,e.textLineHeight,b),D.textAlign=_&&_.textAlign||e.textAlign,D.textVerticalAlign=_&&_.textVerticalAlign||"middle",null!=f&&s+D.lineHeight>f)return{lines:[],width:0,height:0};D.textWidth=ni(D.text,w);var S,M,I,C,T=_.textWidth,A=null==T||"auto"===T;"string"==typeof T&&"%"===T.charAt(T.length-1)?(D.percentWidth=T,h.push(D),T=0):(A&&(T=D.textWidth,(M=(S=_.textBackgroundColor)&&S.image)&&qn(M=function(t){if("string"!=typeof t)return t;var e=Xn.get(t);return e&&e.image}(M))&&(T=Math.max(T,M.width*b/M.height))),T+=I=x?x[1]+x[3]:0,null!=(C=null!=d?d-v:null)&&C<T&&(!A||C<I?(D.text="",D.textWidth=T=0):(D.text=si(D.text,C-I,w,c.ellipsis,{minChar:c.minChar}),D.textWidth=ni(D.text,w),T=D.textWidth+I))),v+=D.width=T,_&&(m=Math.max(m,D.lineHeight))}g.width=v,s+=g.lineHeight=m,l=Math.max(l,v)}n.outerWidth=n.width=Z(e.textWidth,l),n.outerHeight=n.height=Z(e.textHeight,s),u&&(n.outerWidth+=u[1]+u[3],n.outerHeight+=u[0]+u[2]);for(p=0;p<h.length;p++){var D,k=(D=h[p]).percentWidth;D.width=parseInt(k,10)/100*l}return n}function fi(t,e,n){for(var i=""===e,r=e.split("\n"),o=t.lines,a=0;a<r.length;a++){var s,l,h=r[a],u={styleName:n,text:h,isLineHolder:!h&&!i};a?o.push({tokens:[u]}):1===(l=(s=(o[o.length-1]||(o[0]={tokens:[]})).tokens).length)&&s[0].isLineHolder?s[0]=u:!h&&l&&!i||s.push(u)}}function pi(t){var e=(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ");return e&&q(e)||t.textFont||t.font}function gi(t,e){var n,i,r,o,a,s=e.x,l=e.y,h=e.width,u=e.height,c=e.r;h<0&&(s+=h,h=-h),u<0&&(l+=u,u=-u),"number"==typeof c?n=i=r=o=c:c instanceof Array?1===c.length?n=i=r=o=c[0]:2===c.length?(n=r=c[0],i=o=c[1]):3===c.length?(n=c[0],i=o=c[1],r=c[2]):(n=c[0],i=c[1],r=c[2],o=c[3]):n=i=r=o=0,h<n+i&&(n*=h/(a=n+i),i*=h/a),h<r+o&&(r*=h/(a=r+o),o*=h/a),u<i+r&&(i*=u/(a=i+r),r*=u/a),u<n+o&&(n*=u/(a=n+o),o*=u/a),t.moveTo(s+n,l),t.lineTo(s+h-i,l),0!==i&&t.arc(s+h-i,l+i,i,-Math.PI/2,0),t.lineTo(s+h,l+u-r),0!==r&&t.arc(s+h-r,l+u-r,r,0,Math.PI/2),t.lineTo(s+o,l+u),0!==o&&t.arc(s+o,l+u-o,o,Math.PI/2,Math.PI),t.lineTo(s,l+n),0!==n&&t.arc(s+n,l+n,n,Math.PI,1.5*Math.PI)}ei.measureText=function(t,e){var n=x();return n.font=e||ti,n.measureText(t)};var mi=ti,vi={left:1,right:1,center:1},yi={top:1,bottom:1,middle:1},_i=[["textShadowBlur","shadowBlur",0],["textShadowOffsetX","shadowOffsetX",0],["textShadowOffsetY","shadowOffsetY",0],["textShadowColor","shadowColor","transparent"]],xi={},wi={};function bi(t){return Si(t),D(t.rich,Si),t}function Si(t){var e,n;t&&(t.font=pi(t),"middle"===(e=t.textAlign)&&(e="center"),t.textAlign=null==e||vi[e]?e:"left","center"===(n=t.textVerticalAlign||t.textBaseline)&&(n="middle"),t.textVerticalAlign=null==n||yi[n]?n:"top",t.textPadding&&(t.textPadding=Y(t.textPadding)))}function Mi(t,e,n,i,r,o){(i.rich?function(t,e,n,i,r,o){o!==On&&(e.__attrCachedBy=Ln.NONE);var a=t.__textCotentBlock;a&&!t.__dirtyText||(a=t.__textCotentBlock=di(n,i));!function(t,e,n,i,r){var o=n.width,a=n.outerWidth,s=n.outerHeight,l=i.textPadding,h=ki(wi,t,i,r),u=h.baseX,c=h.baseY,d=h.textAlign,f=h.textVerticalAlign;Ii(e,i,r,u,c);var p=ri(u,a,d),g=oi(c,s,f),m=p,v=g;l&&(m+=l[3],v+=l[0]);var y=m+o;Ti(i)&&Ai(t,e,i,p,g,a,s);for(var _,x=0;x<n.lines.length;x++){for(var w=n.lines[x],b=w.tokens,S=b.length,M=w.lineHeight,I=w.width,C=0,T=m,A=y,D=S-1;C<S&&(!(_=b[C]).textAlign||"left"===_.textAlign);)Ci(t,e,_,i,M,v,T,"left"),I-=_.width,T+=_.width,C++;for(;0<=D&&"right"===(_=b[D]).textAlign;)Ci(t,e,_,i,M,v,A,"right"),I-=_.width,A-=_.width,D--;for(T+=(o-(T-m)-(y-A)-I)/2;C<=D;)_=b[C],Ci(t,e,_,i,M,v,T+_.width/2,"center"),T+=_.width,C++;v+=M}}(t,e,a,i,r)}:function(t,e,n,i,r,o){var a,s=Ti(i),l=!1,h=e.__attrCachedBy===Ln.PLAIN_TEXT;o!==On?(o&&(a=o.style,l=!s&&h&&a),e.__attrCachedBy=s?Ln.NONE:Ln.PLAIN_TEXT):h&&(e.__attrCachedBy=Ln.NONE);var u=i.font||mi;l&&u===(a.font||mi)||(e.font=u);var c=t.__computedFont;t.__styleFont!==u&&(t.__styleFont=u,c=t.__computedFont=e.font);var d=i.textPadding,f=i.textLineHeight,p=t.__textCotentBlock;p&&!t.__dirtyText||(p=t.__textCotentBlock=ci(n,c,d,f,i.truncate));var g=p.outerHeight,m=p.lines,v=p.lineHeight,y=ki(wi,t,i,r),_=y.baseX,x=y.baseY,w=y.textAlign||"left",b=y.textVerticalAlign;Ii(e,i,r,_,x);var S=oi(x,g,b),M=_,I=S;{var C,T;(s||d)&&(C=ni(n,c),d&&(C+=d[1]+d[3]),T=ri(_,C,w),s&&Ai(t,e,i,T,S,C,g),d&&(M=zi(_,w,d),I+=d[0]))}e.textAlign=w,e.textBaseline="middle",e.globalAlpha=i.opacity||1;for(var A=0;A<_i.length;A++){var D=_i[A],k=D[0],P=D[1],L=i[k];l&&L===a[k]||(e[P]=Pn(e,P,L||D[2]))}I+=v/2;var O=i.textStrokeWidth,E=l?a.textStrokeWidth:null,z=!l||O!==E,N=!l||z||i.textStroke!==a.textStroke,R=Li(i.textStroke,O),B=Oi(i.textFill);R&&(z&&(e.lineWidth=O),N&&(e.strokeStyle=R));B&&(l&&i.textFill===a.textFill||(e.fillStyle=B));if(1===m.length)R&&e.strokeText(m[0],M,I),B&&e.fillText(m[0],M,I);else for(A=0;A<m.length;A++)R&&e.strokeText(m[A],M,I),B&&e.fillText(m[A],M,I),I+=v})(t,e,n,i,r,o)}function Ii(t,e,n,i,r){var o;n&&e.textRotation&&("center"===(o=e.textOrigin)?(i=n.width/2+n.x,r=n.height/2+n.y):o&&(i=o[0]+n.x,r=o[1]+n.y),t.translate(i,r),t.rotate(-e.textRotation),t.translate(-i,-r))}function Ci(t,e,n,i,r,o,a,s){var l=i.rich[n.styleName]||{};l.text=n.text;var h=n.textVerticalAlign,u=o+r/2;"top"===h?u=o+n.height/2:"bottom"===h&&(u=o+r-n.height/2),!n.isLineHolder&&Ti(l)&&Ai(t,e,l,"right"===s?a-n.width:"center"===s?a-n.width/2:a,u-n.height/2,n.width,n.height);var c=n.textPadding;c&&(a=zi(a,s,c),u-=n.height/2-c[2]-n.textHeight/2),Pi(e,"shadowBlur",U(l.textShadowBlur,i.textShadowBlur,0)),Pi(e,"shadowColor",l.textShadowColor||i.textShadowColor||"transparent"),Pi(e,"shadowOffsetX",U(l.textShadowOffsetX,i.textShadowOffsetX,0)),Pi(e,"shadowOffsetY",U(l.textShadowOffsetY,i.textShadowOffsetY,0)),Pi(e,"textAlign",s),Pi(e,"textBaseline","middle"),Pi(e,"font",n.font||mi);var d=Li(l.textStroke||i.textStroke,void 0),f=Oi(l.textFill||i.textFill),p=Z(l.textStrokeWidth,i.textStrokeWidth);d&&(Pi(e,"lineWidth",p),Pi(e,"strokeStyle",d),e.strokeText(n.text,a,u)),f&&(Pi(e,"fillStyle",f),e.fillText(n.text,a,u))}function Ti(t){return!!(t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor)}function Ai(t,e,n,i,r,o,a){var s,l,h,u=n.textBackgroundColor,c=n.textBorderWidth,d=n.textBorderColor,f=R(u);Pi(e,"shadowBlur",n.textBoxShadowBlur||0),Pi(e,"shadowColor",n.textBoxShadowColor||"transparent"),Pi(e,"shadowOffsetX",n.textBoxShadowOffsetX||0),Pi(e,"shadowOffsetY",n.textBoxShadowOffsetY||0),(f||c&&d)&&(e.beginPath(),(s=n.textBorderRadius)?gi(e,{x:i,y:r,width:o,height:a,r:s}):e.rect(i,r,o,a),e.closePath()),f?(Pi(e,"fillStyle",u),null!=n.fillOpacity?(h=e.globalAlpha,e.globalAlpha=n.fillOpacity*n.opacity,e.fill(),e.globalAlpha=h):e.fill()):!B(u)||(l=Yn(l=u.image,null,t,Di,u))&&qn(l)&&e.drawImage(l,i,r,o,a),c&&d&&(Pi(e,"lineWidth",c),Pi(e,"strokeStyle",d),null!=n.strokeOpacity?(h=e.globalAlpha,e.globalAlpha=n.strokeOpacity*n.opacity,e.stroke(),e.globalAlpha=h):e.stroke())}function Di(t,e){e.image=t}function ki(t,e,n,i){var r,o,a,s=n.x||0,l=n.y||0,h=n.textAlign,u=n.textVerticalAlign;return i&&((r=n.textPosition)instanceof Array?(s=i.x+Ei(r[0],i.width),l=i.y+Ei(r[1],i.height)):(s=(o=e&&e.calculateTextPosition?e.calculateTextPosition(xi,n,i):ai(xi,n,i)).x,l=o.y,h=h||o.textAlign,u=u||o.textVerticalAlign),(a=n.textOffset)&&(s+=a[0],l+=a[1])),(t=t||{}).baseX=s,t.baseY=l,t.textAlign=h,t.textVerticalAlign=u,t}function Pi(t,e,n){return t[e]=Pn(t,e,n),t[e]}function Li(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function Oi(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function Ei(t,e){return"string"==typeof t?0<=t.lastIndexOf("%")?parseFloat(t)/100*e:parseFloat(t):t}function zi(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function Ni(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)}function Ri(){}var Bi=new yn;function Vi(t){for(var e in t=t||{},un.call(this,t),t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new zn(t.style,this),this._rect=null,this.__clipPaths=null}function Fi(t){Vi.call(this,t)}Vi.prototype={constructor:Vi,type:"displayable",__dirty:!0,invisible:!(Ri.prototype={constructor:Ri,drawRectText:function(t,e){var n=this.style;e=n.textRect||e,this.__dirty&&bi(n);var i,r=n.text;null!=r&&(r+=""),Ni(r,n)&&(t.save(),i=this.transform,n.transformText?this.setTransform(t):i&&(Bi.copy(e),Bi.applyTransform(i),e=Bi),Mi(this,t,r,n,e,On),t.restore())}}),z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:!1,incremental:!1,globalScaleRatio:1,beforeBrush:function(){},afterBrush:function(){},brush:function(){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var n=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(n[0],n[1])},dirty:function(){this.__dirty=this.__dirtyText=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?un.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new zn(t,this),this.dirty(!1),this},calculateTextPosition:null},b(Vi,un),S(Vi,Ri),Fi.prototype={constructor:Fi,type:"image",brush:function(t,e){var n=this.style,i=n.image;n.bind(t,this,e);var r,o,a,s,l,h,u,c,d,f=this._image=Yn(i,this._image,this,this.onload);f&&qn(f)&&(r=n.x||0,o=n.y||0,a=n.width,s=n.height,l=f.width/f.height,null==a&&null!=s?a=s*l:null==s&&null!=a?s=a/l:null==a&&null==s&&(a=f.width,s=f.height),this.setTransform(t),n.sWidth&&n.sHeight?(h=n.sx||0,u=n.sy||0,t.drawImage(f,h,u,n.sWidth,n.sHeight,r,o,a,s)):n.sx&&n.sy?(c=a-(h=n.sx),d=s-(u=n.sy),t.drawImage(f,h,u,c,d,r,o,a,s)):t.drawImage(f,r,o,a,s),null!=n.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect())))},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new yn(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},b(Fi,Vi);var Hi=314159;function Wi(t){return parseInt(t,10)}var Gi=new yn(0,0,0,0),Zi=new yn(0,0,0,0);function Ui(t,e,n){this.type="canvas";var i=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=k({},n||{}),this.dpr=n.devicePixelRatio||rn,this._singleCanvas=i;var r=(this.root=t).style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var o,a,s,l,h,u,c,d=this._zlevelList=[],f=this._layers={};this._layerConfig={},this._needsManuallyCompositing=!1,i?(o=t.width,a=t.height,null!=n.width&&(o=n.width),null!=n.height&&(a=n.height),this.dpr=n.devicePixelRatio||1,t.width=o*this.dpr,t.height=a*this.dpr,this._width=o,this._height=a,(s=new Zn(t,this,this.dpr)).__builtin__=!0,s.initContext(),(f[Hi]=s).zlevel=Hi,d.push(Hi),this._domRoot=t):(this._width=this._getSize(0),this._height=this._getSize(1),l=this._domRoot=(h=this._width,u=this._height,(c=document.createElement("div")).style.cssText=["position:relative","width:"+h+"px","height:"+u+"px","padding:0","margin:0","border-width:0"].join(";")+";",c),t.appendChild(l)),this._hoverlayer=null,this._hoverElements=[]}Ui.prototype={constructor:Ui,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(t){var e=this.storage.getDisplayList(!0),n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,t,this._redrawId);for(var i=0;i<n.length;i++){var r,o=n[i],a=this._layers[o];!a.__builtin__&&a.refresh&&(r=0===i?this._backgroundColor:null,a.refresh(r))}return this.refreshHover(),this},addHover:function(t,e){if(!t.__hoverMir){var n=new t.constructor({style:t.style,shape:t.shape,z:t.z,z2:t.z2,silent:t.silent});return(n.__from=t).__hoverMir=n,e&&n.setStyle(e),this._hoverElements.push(n),n}},removeHover:function(t){var e=t.__hoverMir,n=this._hoverElements,i=w(n,e);0<=i&&n.splice(i,1),t.__hoverMir=null},clearHover:function(){for(var t=this._hoverElements,e=0;e<t.length;e++){var n=t[e].__from;n&&(n.__hoverMir=null)}t.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){Tn(t,this.storage.displayableSortFunc);var i={};(n=n||(this._hoverlayer=this.getLayer(1e5))).ctx.save();for(var r=0;r<e;){var o=t[r],a=o.__from;a&&a.__zr?(r++,a.invisible||(o.transform=a.transform,o.invTransform=a.invTransform,o.__clipPaths=a.__clipPaths,this._doPaintEl(o,n,!0,i))):(t.splice(r,1),a.__hoverMir=null,e--)}n.ctx.restore()}},getHoverLayer:function(){return this.getLayer(1e5)},_paintList:function(t,e,n){var i,r;this._redrawId===n&&(e=e||!1,this._updateLayerStatus(t),i=this._doPaintList(t,e),this._needsManuallyCompositing&&this._compositeManually(),i||(r=this,Un(function(){r._paintList(t,e,n)})))},_compositeManually:function(){var e=this.getLayer(Hi).ctx,n=this._domRoot.width,i=this._domRoot.height;e.clearRect(0,0,n,i),this.eachBuiltinLayer(function(t){t.virtual&&e.drawImage(t.dom,0,0,n,i)})},_doPaintList:function(t,e){for(var n=[],i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];(s=this._layers[r]).__builtin__&&s!==this._hoverlayer&&(s.__dirty||e)&&n.push(s)}for(var o=!0,a=0;a<n.length;a++){var s,l=(s=n[a]).ctx,h={};l.save();var u,c=e?s.__startIndex:s.__drawIndex,d=!e&&s.incremental&&Date.now,f=d&&Date.now(),p=s.zlevel===this._zlevelList[0]?this._backgroundColor:null;s.__startIndex===s.__endIndex?s.clear(!1,p):c===s.__startIndex&&((u=t[c]).incremental&&u.notClear&&!e||s.clear(!1,p)),-1===c&&(c=s.__startIndex);for(var g=c;g<s.__endIndex;g++){var m=t[g];if(this._doPaintEl(m,s,e,h),m.__dirty=m.__dirtyText=!1,d)if(15<Date.now()-f)break}s.__drawIndex=g,s.__drawIndex<s.__endIndex&&(o=!1),h.prevElClipPaths&&l.restore(),l.restore()}return v.wxa&&D(this._layers,function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()}),o},_doPaintEl:function(t,e,n,i){var r,o,a,s,l,h=e.ctx,u=t.transform;!e.__dirty&&!n||t.invisible||0===t.style.opacity||u&&!u[0]&&!u[3]||t.culling&&(a=t,s=this._width,l=this._height,Gi.copy(a.getBoundingRect()),a.transform&&Gi.applyTransform(a.transform),Zi.width=s,Zi.height=l,!Gi.intersect(Zi))||(r=t.__clipPaths,(o=i.prevElClipPaths)&&!function(t,e){if(t!==e){if(!t||!e||t.length!==e.length)return 1;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return 1}}(r,o)||(o&&(h.restore(),i.prevElClipPaths=null,i.prevEl=null),r&&(h.save(),function(t,e){for(var n=0;n<t.length;n++){var i=t[n];i.setTransform(e),e.beginPath(),i.buildPath(e,i.shape),e.clip(),i.restoreTransform(e)}}(r,h),i.prevElClipPaths=r)),t.beforeBrush&&t.beforeBrush(h),t.brush(h,i.prevEl||null),(i.prevEl=t).afterBrush&&t.afterBrush(h))},getLayer:function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=Hi);var n=this._layers[t];return n||((n=new Zn("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?m(n,this._layerConfig[t],!0):this._layerConfig[t-.01]&&m(n,this._layerConfig[t-.01],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},insertLayer:function(t,e){var n,i=this._layers,r=this._zlevelList,o=r.length,a=null,s=-1,l=this._domRoot;if(i[t])sn("ZLevel "+t+" has been used already");else if(function(t){if(t){if(t.__builtin__)return 1;if("function"==typeof t.resize&&"function"==typeof t.refresh)return 1}}(e)){if(0<o&&t>r[0]){for(s=0;s<o-1&&!(r[s]<t&&r[s+1]>t);s++);a=i[r[s]]}r.splice(s+1,0,t),(i[t]=e).virtual||(a?(n=a.dom).nextSibling?l.insertBefore(e.dom,n.nextSibling):l.appendChild(e.dom):l.firstChild?l.insertBefore(e.dom,l.firstChild):l.appendChild(e.dom))}else sn("Layer of zlevel "+t+" is not valid")},eachLayer:function(t,e){for(var n,i=this._zlevelList,r=0;r<i.length;r++)n=i[r],t.call(e,this._layers[n],n)},eachBuiltinLayer:function(t,e){for(var n,i,r=this._zlevelList,o=0;o<r.length;o++)i=r[o],(n=this._layers[i]).__builtin__&&t.call(e,n,i)},eachOtherLayer:function(t,e){for(var n,i,r=this._zlevelList,o=0;o<r.length;o++)i=r[o],(n=this._layers[i]).__builtin__||t.call(e,n,i)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){function e(t){r&&(r.__endIndex!==t&&(r.__dirty=!0),r.__endIndex=t)}if(this.eachBuiltinLayer(function(t,e){t.__dirty=t.__used=!1}),this._singleCanvas)for(var n=1;n<t.length;n++){if((a=t[n]).zlevel!==t[n-1].zlevel||a.incremental){this._needsManuallyCompositing=!0;break}}for(var i,r=null,o=0,n=0;n<t.length;n++){var a,s,l=(a=t[n]).zlevel;i!==l&&(i=l,o=0),a.incremental?((s=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,o=1):s=this.getLayer(l+(0<o?.01:0),this._needsManuallyCompositing),s.__builtin__||sn("ZLevel "+l+" has been used by unkown layer "+s.id),s!==r&&(s.__used=!0,s.__startIndex!==n&&(s.__dirty=!0),s.__startIndex=n,s.incremental?s.__drawIndex=-1:s.__drawIndex=n,e(n),r=s),a.__dirty&&(s.__dirty=!0,s.incremental&&s.__drawIndex<0&&(s.__drawIndex=n))}e(n),this.eachBuiltinLayer(function(t,e){!t.__used&&0<t.getElementCount()&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},setBackgroundColor:function(t){this._backgroundColor=t},configLayer:function(t,e){if(e){var n=this._layerConfig;n[t]?m(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];r!==t&&r!==t+.01||m(this._layers[r],n[t],!0)}}},delLayer:function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(w(n,t),1))},resize:function(e,n){if(this._domRoot.style){var t=this._domRoot;t.style.display="none";var i=this._opts;if(null!=e&&(i.width=e),null!=n&&(i.height=n),e=this._getSize(0),n=this._getSize(1),t.style.display="",this._width!==e||n!==this._height){for(var r in t.style.width=e+"px",t.style.height=n+"px",this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(e,n);D(this._progressiveLayers,function(t){t.resize(e,n)}),this.refresh(!0)}this._width=e,this._height=n}else{if(null==e||null==n)return;this._width=e,this._height=n,this.getLayer(Hi).resize(e,n)}return this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[Hi].dom;var e=new Zn("image",this,t.pixelRatio||this.dpr);if(e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor),t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,i=e.dom.height,r=e.ctx;this.eachLayer(function(t){t.__builtin__?r.drawImage(t.dom,0,0,n,i):t.renderToCanvas&&(e.ctx.save(),t.renderToCanvas(e.ctx),e.ctx.restore())})}else for(var o={},a=this.storage.getDisplayList(!0),s=0;s<a.length;s++){var l=a[s];this._doPaintEl(l,e,!0,o)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,n=["width","height"][t],i=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=e[n]&&"auto"!==e[n])return parseFloat(e[n]);var a=this.root,s=document.defaultView.getComputedStyle(a);return(a[i]||Wi(s[n])||Wi(a.style[n]))-(Wi(s[r])||0)-(Wi(s[o])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var n=document.createElement("canvas"),i=n.getContext("2d"),r=t.getBoundingRect(),o=t.style,a=o.shadowBlur*e,s=o.shadowOffsetX*e,l=o.shadowOffsetY*e,h=o.hasStroke()?o.lineWidth:0,u=Math.max(h/2,a-s),c=Math.max(h/2,s+a),d=Math.max(h/2,a-l),f=Math.max(h/2,l+a),p=r.width+u+c,g=r.height+d+f;n.width=p*e,n.height=g*e,i.scale(e,e),i.clearRect(0,0,p,g),i.dpr=e;var m={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[u-r.x,d-r.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(i);var v=new Fi({style:{x:0,y:0,image:n}});return null!=m.position&&(v.position=t.position=m.position),null!=m.rotation&&(v.rotation=t.rotation=m.rotation),null!=m.scale&&(v.scale=t.scale=m.scale),v}};function Xi(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,It.call(this)}Xi.prototype={constructor:Xi,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),n=0;n<e.length;n++)this.addClip(e[n])},removeClip:function(t){var e=w(this._clips,t);0<=e&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),n=0;n<e.length;n++)this.removeClip(e[n]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,n=this._clips,i=n.length,r=[],o=[],a=0;a<i;a++){var s=n[a],l=s.step(t,e);l&&(r.push(l),o.push(s))}for(a=0;a<i;)n[a]._needsRemove?(n[a]=n[i-1],n.pop(),i--):a++;i=r.length;for(a=0;a<i;a++)o[a].fire(r[a]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){var e=this;this._running=!0,Un(function t(){e._running&&(Un(t),e._paused||e._update())})},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},isFinished:function(){return!this._clips.length},animate:function(t,e){var n=new en(t,(e=e||{}).loop,e.getter,e.setter);return this.addAnimator(n),n}},S(Xi,It);var Yi,ji,qi=v.domSupported,$i=(ji={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:Yi=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:P(Yi,function(t){var e=t.replace("mouse","pointer");return ji.hasOwnProperty(e)?e:t})}),Ki={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]};function Qi(t){return"mousewheel"===t&&v.browser.firefox?"DOMMouseScroll":t}function Ji(t){var e=t.pointerType;return"pen"===e||"touch"===e}function tr(t){t&&(t.zrByTouch=!0)}function er(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}function nr(t,e){this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}var ir=nr.prototype;ir.stopPropagation=ir.stopImmediatePropagation=ir.preventDefault=et;var rr={mousedown:function(t){t=Ft(this.dom,t),this._mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=Ft(this.dom,t);var e=this._mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||ur(this,!0),this.trigger("mousemove",t)},mouseup:function(t){t=Ft(this.dom,t),ur(this,!1),this.trigger("mouseup",t)},mouseout:function(t){t=Ft(this.dom,t),this._pointerCapturing&&(t.zrEventControl="no_globalout");var e=t.toElement||t.relatedTarget;t.zrIsToLocalDOM=er(this,e),this.trigger("mouseout",t)},touchstart:function(t){tr(t=Ft(this.dom,t)),this._lastTouchMoment=new Date,this.handler.processGesture(t,"start"),rr.mousemove.call(this,t),rr.mousedown.call(this,t)},touchmove:function(t){tr(t=Ft(this.dom,t)),this.handler.processGesture(t,"change"),rr.mousemove.call(this,t)},touchend:function(t){tr(t=Ft(this.dom,t)),this.handler.processGesture(t,"end"),rr.mouseup.call(this,t),new Date-this._lastTouchMoment<300&&rr.click.call(this,t)},pointerdown:function(t){rr.mousedown.call(this,t)},pointermove:function(t){Ji(t)||rr.mousemove.call(this,t)},pointerup:function(t){rr.mouseup.call(this,t)},pointerout:function(t){Ji(t)||rr.mouseout.call(this,t)}};D(["click","mousewheel","dblclick","contextmenu"],function(e){rr[e]=function(t){t=Ft(this.dom,t),this.trigger(e,t)}});var or={pointermove:function(t){Ji(t)||or.mousemove.call(this,t)},pointerup:function(t){or.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this._pointerCapturing;ur(this,!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function ar(i,r){var o=r.domHandlers;v.pointerEventsSupported?D($i.pointer,function(e){lr(r,e,function(t){o[e].call(i,t)})}):(v.touchEventsSupported&&D($i.touch,function(n){lr(r,n,function(t){var e;o[n].call(i,t),(e=r).touching=!0,null!=e.touchTimer&&(clearTimeout(e.touchTimer),e.touchTimer=null),e.touchTimer=setTimeout(function(){e.touching=!1,e.touchTimer=null},700)})}),D($i.mouse,function(e){lr(r,e,function(t){t=Vt(t),r.touching||o[e].call(i,t)})}))}function sr(r,o){function t(i){lr(o,i,function(t){var e,n;t=Vt(t),er(r,t.target)||(n=t,t=Ft((e=r).dom,new nr(e,n),!0),o.domHandlers[i].call(r,t))},{capture:!0})}v.pointerEventsSupported?D(Ki.pointer,t):v.touchEventsSupported||D(Ki.mouse,t)}function lr(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,Ht(t.domTarget,Qi(e),n,i)}function hr(t){var e,n,i,r,o,a=t.mounted;for(e in a)a.hasOwnProperty(e)&&(n=t.domTarget,i=Qi(e),r=a[e],o=t.listenerOpts[e],Et?n.removeEventListener(i,r,o):n.detachEvent("on"+i,r));t.mounted={}}function ur(t,e){var n;t._mayPointerCapture=null,qi&&t._pointerCapturing^e&&(t._pointerCapturing=e,n=t._globalHandlerScope,e?sr(t,n):hr(n))}function cr(t,e){this.domTarget=t,this.domHandlers=e,this.mounted={},this.listenerOpts={},this.touchTimer=null,this.touching=!1}function dr(t,e){It.call(this),this.dom=t,this.painterRoot=e,this._localHandlerScope=new cr(t,rr),qi&&(this._globalHandlerScope=new cr(document,or)),this._pointerCapturing=!1,this._mayPointerCapture=null,ar(this,this._localHandlerScope)}var fr=dr.prototype;fr.dispose=function(){hr(this._localHandlerScope),qi&&hr(this._globalHandlerScope)},fr.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},S(dr,It);
/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var pr=!v.canvasSupported,gr={canvas:Ui},mr={},vr="4.3.2";function yr(t,e){var n=new xr(i(),t,e);return mr[n.id]=n}function _r(t,e){gr[t]=e}var xr=function(t,e,n){n=n||{},this.dom=e,this.id=t;var i=this,r=new Dn,o=n.renderer;if(pr){if(!gr.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");o="vml"}else o&&gr[o]||(o="canvas");var a=new gr[o](e,r,n,t);this.storage=r,this.painter=a;var s=v.node||v.worker?null:new dr(a.getViewportRoot(),a.root);this.handler=new $t(r,a,s,a.root),this.animation=new Xi({stage:{update:L(this.flush,this)}}),this.animation.start(),this._needsRefresh;var l=r.delFromStorage,h=r.addToStorage;r.delFromStorage=function(t){l.call(r,t),t&&t.removeSelfFromZr(i)},r.addToStorage=function(t){h.call(r,t),t.addSelfToZr(i)}};xr.prototype={constructor:xr,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this._needsRefresh=!0},setBackgroundColor:function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=this._needsRefreshHover=!1,this.painter.refresh(),this._needsRefresh=this._needsRefreshHover=!1},refresh:function(){this._needsRefresh=!0},flush:function(){var t;this._needsRefresh&&(t=!0,this.refreshImmediately()),this._needsRefreshHover&&(t=!0,this.refreshHoverImmediately()),t&&this.trigger("rendered")},addHover:function(t,e){if(this.painter.addHover){var n=this.painter.addHover(t,e);return this.refreshHover(),n}},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,n){this.handler.on(t,e,n)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){var t;this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,t=this.id,delete mr[t]}};var wr=(Object.freeze||Object)({version:vr,init:yr,dispose:function(t){if(t)t.dispose();else{for(var e in mr)mr.hasOwnProperty(e)&&mr[e].dispose();mr={}}return this},getInstance:function(t){return mr[t]},registerPainter:_r}),br=D,Sr=B,Mr=z,Ir="series\0";function Cr(t){return t instanceof Array?t:null==t?[]:[t]}function Tr(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var Ar=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function Dr(t){return!Sr(t)||Mr(t)||t instanceof Date?t:t.value}function kr(t,r){r=(r||[]).slice();var o=P(t||[],function(t,e){return{exist:t}});return br(r,function(t,e){if(Sr(t)){for(var n=0;n<o.length;n++)if(!o[n].option&&null!=t.id&&o[n].exist.id===t.id+"")return o[n].option=t,void(r[e]=null);for(n=0;n<o.length;n++){var i=o[n].exist;if(!(o[n].option||null!=i.id&&null!=t.id||null==t.name||Or(t)||Or(i)||i.name!==t.name+""))return o[n].option=t,void(r[e]=null)}}}),br(r,function(t,e){if(Sr(t)){for(var n=0;n<o.length;n++){var i=o[n].exist;if(!o[n].option&&!Or(i)&&null==t.id){o[n].option=t;break}}n>=o.length&&o.push({option:t})}}),o}function Pr(t){var a=tt();br(t,function(t,e){var n=t.exist;n&&a.set(n.id,t)}),br(t,function(t,e){var n=t.option;j(!n||null==n.id||!a.get(n.id)||a.get(n.id)===t,"id duplicates: "+(n&&n.id)),n&&null!=n.id&&a.set(n.id,t),t.keyInfo||(t.keyInfo={})}),br(t,function(t,e){var n=t.exist,i=t.option,r=t.keyInfo;if(Sr(i)){if(r.name=null!=i.name?i.name+"":n?n.name:Ir+e,n)r.id=n.id;else if(null!=i.id)r.id=i.id+"";else for(var o=0;r.id="\0"+r.name+"\0"+o++,a.get(r.id););a.set(r.id,t)}})}function Lr(t){var e=t.name;return e&&e.indexOf(Ir)}function Or(t){return Sr(t)&&t.id&&0===(t.id+"").indexOf("\0_ec_\0")}function Er(e,t){return null!=t.dataIndexInside?t.dataIndexInside:null!=t.dataIndex?z(t.dataIndex)?P(t.dataIndex,function(t){return e.indexOfRawIndex(t)}):e.indexOfRawIndex(t.dataIndex):null!=t.name?z(t.name)?P(t.name,function(t){return e.indexOfName(t)}):e.indexOfName(t.name):void 0}function zr(){var e="__\0ec_inner_"+Nr+++"_"+Math.random().toFixed(5);return function(t){return t[e]||(t[e]={})}}var Nr=0;function Rr(s,l,h){var t;R(l)&&((t={})[l+"Index"]=0,l=t);var e=h&&h.defaultMainType;!e||Br(l,e+"Index")||Br(l,e+"Id")||Br(l,e+"Name")||(l[e+"Index"]=0);var u={};return br(l,function(t,e){var n,i,r,o,a,t=l[e];"dataIndex"!==e&&"dataIndexInside"!==e?(i=(n=e.match(/^(\w+)(Index|Id|Name)$/)||[])[1],r=(n[2]||"").toLowerCase(),!i||!r||null==t||"index"===r&&"none"===t||h&&h.includeMainTypes&&w(h.includeMainTypes,i)<0||(o={mainType:i},"index"===r&&"all"===t||(o[r]=t),a=s.queryComponents(o),u[i+"Models"]=a,u[i+"Model"]=a[0])):u[e]=t}),u}function Br(t,e){return t&&t.hasOwnProperty(e)}function Vr(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}function Fr(t){return"auto"===t?v.domSupported?"html":"richText":t||"html"}var Hr=".",Wr="___EC__COMPONENT__CONTAINER___";function Gr(t){var e={main:"",sub:""};return t&&(t=t.split(Hr),e.main=t[0]||"",e.sub=t[1]||""),e}function Zr(t,i){(t.$constructor=t).extend=function(e){C&&D(i,function(t){e[t]});function t(){e.$constructor?e.$constructor.apply(this,arguments):n.apply(this,arguments)}var n=this;return k(t.prototype,e),t.extend=this.extend,t.superCall=Yr,t.superApply=jr,b(t,this),t.superClass=n,t}}var Ur=0;function Xr(t){var e=["__\0is_clz",Ur++,Math.random().toFixed(3)].join("_");t.prototype[e]=!0,C&&j(!t.isInstance,'The method "is" can not be defined.'),t.isInstance=function(t){return!(!t||!t[e])}}function Yr(t,e){var n=X(arguments,2);return this.superClass.prototype[e].apply(t,n)}function jr(t,e,n){return this.superClass.prototype[e].apply(t,n)}function qr(n,t){t=t||{};var i,r={};return n.registerClass=function(t,e){var n;return e&&(j(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(n=e),'componentType "'+n+'" illegal'),(e=Gr(e)).sub?e.sub!==Wr&&(function(t){var e=r[t.main];e&&e[Wr]||((e=r[t.main]={})[Wr]=!0);return e}(e)[e.sub]=t):(C&&r[e.main],r[e.main]=t)),t},n.getClass=function(t,e,n){var i=r[t];if(i&&i[Wr]&&(i=e?i[e]:null),n&&!i)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return i},n.getClassesByMainType=function(t){t=Gr(t);var n=[],e=r[t.main];return e&&e[Wr]?D(e,function(t,e){e!==Wr&&n.push(t)}):n.push(e),n},n.hasClass=function(t){return t=Gr(t),!!r[t.main]},n.getAllClassMainTypes=function(){var n=[];return D(r,function(t,e){n.push(e)}),n},n.hasSubTypes=function(t){t=Gr(t);var e=r[t.main];return e&&e[Wr]},n.parseClassType=Gr,!t.registerWhenExtend||(i=n.extend)&&(n.extend=function(t){var e=i.call(this,t);return n.registerClass(e,t.type)}),n}function $r(s){for(var t=0;t<s.length;t++)s[t][1]||(s[t][1]=s[t][0]);return function(t,e,n){for(var i={},r=0;r<s.length;r++){var o,a=s[r][1];e&&0<=w(e,a)||n&&w(n,a)<0||null!=(o=t.getShallow(a))&&(i[s[r][0]]=o)}return i}}var Kr=$r([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),Qr={getLineStyle:function(t){var e=Kr(this,t);return e.lineDash=this.getLineDash(e.lineWidth),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),n=Math.max(t,2),i=4*t;return"solid"!==e&&null!=e&&("dashed"===e?[i,i]:[n,n])}},Jr=$r([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),to={getAreaStyle:function(t,e){return Jr(this,t,e)}},eo=Math.pow,no=Math.sqrt,io=1e-8,ro=1e-4,oo=no(3),ao=1/3,so=rt(),lo=rt(),ho=rt();function uo(t){return-io<t&&t<io}function co(t){return io<t||t<-io}function fo(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function po(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function go(t,e,n,i,r){var o,a,s,l,h=6*n-12*e+6*t,u=9*e+3*i-3*t-9*n,c=3*e-3*t,d=0;return uo(u)?co(h)&&0<=(s=-c/h)&&s<=1&&(r[d++]=s):uo(o=h*h-4*u*c)?r[0]=-h/(2*u):0<o&&(l=(-h-(a=no(o)))/(2*u),0<=(s=(-h+a)/(2*u))&&s<=1&&(r[d++]=s),0<=l&&l<=1&&(r[d++]=l)),d}function mo(t,e,n,i,r,o){var a=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,h=(s-a)*r+a,u=(l-s)*r+s,c=(u-h)*r+h;o[0]=t,o[1]=a,o[2]=h,o[3]=c,o[4]=c,o[5]=u,o[6]=l,o[7]=i}function vo(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function yo(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function _o(t,e,n){var i=t+n-2*e;return 0==i?.5:(t-e)/i}function xo(t,e,n,i,r){var o=(e-t)*i+t,a=(n-e)*i+e,s=(a-o)*i+o;r[0]=t,r[1]=o,r[2]=s,r[3]=s,r[4]=a,r[5]=n}var wo=Math.min,bo=Math.max,So=Math.sin,Mo=Math.cos,Io=2*Math.PI,Co=rt(),To=rt(),Ao=rt();function Do(t,e,n){if(0!==t.length){for(var i=t[0],r=i[0],o=i[0],a=i[1],s=i[1],l=1;l<t.length;l++)i=t[l],r=wo(r,i[0]),o=bo(o,i[0]),a=wo(a,i[1]),s=bo(s,i[1]);e[0]=r,e[1]=a,n[0]=o,n[1]=s}}function ko(t,e,n,i,r,o){r[0]=wo(t,n),r[1]=wo(e,i),o[0]=bo(t,n),o[1]=bo(e,i)}var Po=[],Lo=[];var Oo={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},Eo=[],zo=[],No=[],Ro=[],Bo=Math.min,Vo=Math.max,Fo=Math.cos,Ho=Math.sin,Wo=Math.sqrt,Go=Math.abs,Zo="undefined"!=typeof Float32Array,Uo=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};function Xo(t,e,n,i,r,o,a){if(0!==r){var s=r,l=0;if(!(e+s<a&&i+s<a||a<e-s&&a<i-s||t+s<o&&n+s<o||o<t-s&&o<n-s)){if(t===n)return Math.abs(o-t)<=s/2;var h=(l=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n);return h*h/(l*l+1)<=s/2*s/2}}}function Yo(t,e,n,i,r,o,a,s,l,h,u){if(0!==l){var c=l;if(!(e+c<u&&i+c<u&&o+c<u&&s+c<u||u<e-c&&u<i-c&&u<o-c&&u<s-c||t+c<h&&n+c<h&&r+c<h&&a+c<h||h<t-c&&h<n-c&&h<r-c&&h<a-c))return function(t,e,n,i,r,o,a,s,l,h,u){var c,d,f,p,g,m=.005,v=1/0;so[0]=l,so[1]=h;for(var y=0;y<1;y+=.05)lo[0]=fo(t,n,r,a,y),lo[1]=fo(e,i,o,s,y),(p=vt(so,lo))<v&&(c=y,v=p);v=1/0;for(var _=0;_<32&&!(m<ro);_++)d=c-m,f=c+m,lo[0]=fo(t,n,r,a,d),lo[1]=fo(e,i,o,s,d),p=vt(lo,so),0<=d&&p<v?(c=d,v=p):(ho[0]=fo(t,n,r,a,f),ho[1]=fo(e,i,o,s,f),g=vt(ho,so),f<=1&&g<v?(c=f,v=g):m*=.5);return u&&(u[0]=fo(t,n,r,a,c),u[1]=fo(e,i,o,s,c)),no(v)}(t,e,n,i,r,o,a,s,h,u,null)<=c/2}}function jo(t,e,n,i,r,o,a,s,l){if(0!==a){var h=a;if(!(e+h<l&&i+h<l&&o+h<l||l<e-h&&l<i-h&&l<o-h||t+h<s&&n+h<s&&r+h<s||s<t-h&&s<n-h&&s<r-h))return function(t,e,n,i,r,o,a,s,l){var h,u=.005,c=1/0;so[0]=a,so[1]=s;for(var d=0;d<1;d+=.05){lo[0]=vo(t,n,r,d),lo[1]=vo(e,i,o,d),(v=vt(so,lo))<c&&(h=d,c=v)}c=1/0;for(var f=0;f<32&&!(u<ro);f++){var p=h-u,g=h+u;lo[0]=vo(t,n,r,p),lo[1]=vo(e,i,o,p);var m,v=vt(lo,so);0<=p&&v<c?(h=p,c=v):(ho[0]=vo(t,n,r,g),ho[1]=vo(e,i,o,g),m=vt(ho,so),g<=1&&m<c?(h=g,c=m):u*=.5)}return l&&(l[0]=vo(t,n,r,h),l[1]=vo(e,i,o,h)),no(c)}(t,e,n,i,r,o,s,l,null)<=h/2}}Uo.prototype={constructor:Uo,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e,n){n=n||0,this._ux=Go(n/rn/t)||0,this._uy=Go(n/rn/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return(this._ctx=t)&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(Oo.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var n=Go(t-this._xi)>this._ux||Go(e-this._yi)>this._uy||this._len<5;return this.addData(Oo.L,t,e),this._ctx&&n&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),n&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,n,i,r,o){return this.addData(Oo.C,t,e,n,i,r,o),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,n,i,r,o):this._ctx.bezierCurveTo(t,e,n,i,r,o)),this._xi=r,this._yi=o,this},quadraticCurveTo:function(t,e,n,i){return this.addData(Oo.Q,t,e,n,i),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,n,i):this._ctx.quadraticCurveTo(t,e,n,i)),this._xi=n,this._yi=i,this},arc:function(t,e,n,i,r,o){return this.addData(Oo.A,t,e,n,n,i,r-i,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=Fo(r)*n+t,this._yi=Ho(r)*n+e,this},arcTo:function(t,e,n,i,r){return this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},rect:function(t,e,n,i){return this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Oo.R,t,e,n,i),this},closePath:function(){this.addData(Oo.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,n),t.closePath()),this._xi=e,this._yi=n,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t;for(var e=this._dashIdx=0,n=0;n<t.length;n++)e+=t[n];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length===e||!Zo||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();Zo&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var n=0;n<arguments.length;n++)e[this._len++]=arguments[n];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var n,i,r=this._dashSum,o=this._dashOffset,a=this._lineDash,s=this._ctx,l=this._xi,h=this._yi,u=t-l,c=e-h,d=Wo(u*u+c*c),f=l,p=h,g=a.length;for(o<0&&(o=r+o),f-=(o%=r)*(u/=d),p-=o*(c/=d);0<u&&f<=t||u<0&&t<=f||0===u&&(0<c&&p<=e||c<0&&e<=p);)f+=u*(n=a[i=this._dashIdx]),p+=c*n,this._dashIdx=(i+1)%g,0<u&&f<l||u<0&&l<f||0<c&&p<h||c<0&&h<p||s[i%2?"moveTo":"lineTo"]((0<=u?Bo:Vo)(f,t),(0<=c?Bo:Vo)(p,e));u=f-t,c=p-e,this._dashOffset=-Wo(u*u+c*c)},_dashedBezierTo:function(t,e,n,i,r,o){var a,s,l,h,u,c=this._dashSum,d=this._dashOffset,f=this._lineDash,p=this._ctx,g=this._xi,m=this._yi,v=fo,y=0,_=this._dashIdx,x=f.length,w=0;for(d<0&&(d=c+d),d%=c,a=0;a<1;a+=.1)s=v(g,t,n,r,a+.1)-v(g,t,n,r,a),l=v(m,e,i,o,a+.1)-v(m,e,i,o,a),y+=Wo(s*s+l*l);for(;_<x&&!(d<(w+=f[_]));_++);for(a=(w-d)/y;a<=1;)h=v(g,t,n,r,a),u=v(m,e,i,o,a),_%2?p.moveTo(h,u):p.lineTo(h,u),a+=f[_]/y,_=(_+1)%x;_%2!=0&&p.lineTo(r,o),s=r-h,l=o-u,this._dashOffset=-Wo(s*s+l*l)},_dashedQuadraticTo:function(t,e,n,i){var r=n,o=i;n=(n+2*t)/3,i=(i+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,n,i,r,o)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,Zo&&(this.data=new Float32Array(t)))},getBoundingRect:function(){Eo[0]=Eo[1]=No[0]=No[1]=Number.MAX_VALUE,zo[0]=zo[1]=Ro[0]=Ro[1]=-Number.MAX_VALUE;for(var t,e,n,i,r,o,a,s,l,h,u,c,d,f,p=this.data,g=0,m=0,v=0,y=0,_=0;_<p.length;){var x=p[_++];switch(1===_&&(v=g=p[_],y=m=p[_+1]),x){case Oo.M:g=v=p[_++],m=y=p[_++],No[0]=v,No[1]=y,Ro[0]=v,Ro[1]=y;break;case Oo.L:ko(g,m,p[_],p[_+1],No,Ro),g=p[_++],m=p[_++];break;case Oo.C:!function(t,e,n,i,r,o,a,s,l,h){var u,c=go,d=fo,f=c(t,n,r,a,Po);for(l[0]=1/0,l[1]=1/0,h[0]=-1/0,h[1]=-1/0,u=0;u<f;u++){var p=d(t,n,r,a,Po[u]);l[0]=wo(p,l[0]),h[0]=bo(p,h[0])}for(f=c(e,i,o,s,Lo),u=0;u<f;u++){var g=d(e,i,o,s,Lo[u]);l[1]=wo(g,l[1]),h[1]=bo(g,h[1])}l[0]=wo(t,l[0]),h[0]=bo(t,h[0]),l[0]=wo(a,l[0]),h[0]=bo(a,h[0]),l[1]=wo(e,l[1]),h[1]=bo(e,h[1]),l[1]=wo(s,l[1]),h[1]=bo(s,h[1])}(g,m,p[_++],p[_++],p[_++],p[_++],p[_],p[_+1],No,Ro),g=p[_++],m=p[_++];break;case Oo.Q:t=g,e=m,n=p[_++],i=p[_++],r=p[_],o=p[_+1],a=No,s=Ro,f=d=c=u=h=l=void 0,h=vo,u=bo(wo((l=_o)(t,n,r),1),0),c=bo(wo(l(e,i,o),1),0),d=h(t,n,r,u),f=h(e,i,o,c),a[0]=wo(t,r,d),a[1]=wo(e,o,f),s[0]=bo(t,r,d),s[1]=bo(e,o,f),g=p[_++],m=p[_++];break;case Oo.A:var w=p[_++],b=p[_++],S=p[_++],M=p[_++],I=p[_++],C=p[_++]+I;_+=1;var T=1-p[_++];1===_&&(v=Fo(I)*S+w,y=Ho(I)*M+b),function(t,e,n,i,r,o,a,s,l){var h,u=_t,c=xt,d=Math.abs(r-o);if(d%Io<1e-4&&1e-4<d)return s[0]=t-n,s[1]=e-i,l[0]=t+n,l[1]=e+i;Co[0]=Mo(r)*n+t,Co[1]=So(r)*i+e,To[0]=Mo(o)*n+t,To[1]=So(o)*i+e,u(s,Co,To),c(l,Co,To),(r%=Io)<0&&(r+=Io),(o%=Io)<0&&(o+=Io),o<r&&!a?o+=Io:r<o&&a&&(r+=Io),a&&(h=o,o=r,r=h);for(var f=0;f<o;f+=Math.PI/2)r<f&&(Ao[0]=Mo(f)*n+t,Ao[1]=So(f)*i+e,u(s,Ao,s),c(l,Ao,l))}(w,b,S,M,I,C,T,No,Ro),g=Fo(C)*S+w,m=Ho(C)*M+b;break;case Oo.R:ko(v=g=p[_++],y=m=p[_++],v+p[_++],y+p[_++],No,Ro);break;case Oo.Z:g=v,m=y}_t(Eo,Eo,No),xt(zo,zo,Ro)}return 0===_&&(Eo[0]=Eo[1]=zo[0]=zo[1]=0),new yn(Eo[0],Eo[1],zo[0]-Eo[0],zo[1]-Eo[1])},rebuildPath:function(t){for(var e,n,i,r,o,a,s=this.data,l=this._ux,h=this._uy,u=this._len,c=0;c<u;){var d=s[c++];switch(1===c&&(e=i=s[c],n=r=s[c+1]),d){case Oo.M:e=i=s[c++],n=r=s[c++],t.moveTo(i,r);break;case Oo.L:o=s[c++],a=s[c++],(Go(o-i)>l||Go(a-r)>h||c===u-1)&&(t.lineTo(o,a),i=o,r=a);break;case Oo.C:t.bezierCurveTo(s[c++],s[c++],s[c++],s[c++],s[c++],s[c++]),i=s[c-2],r=s[c-1];break;case Oo.Q:t.quadraticCurveTo(s[c++],s[c++],s[c++],s[c++]),i=s[c-2],r=s[c-1];break;case Oo.A:var f=s[c++],p=s[c++],g=s[c++],m=s[c++],v=s[c++],y=s[c++],_=s[c++],x=s[c++],w=m<g?g:m,b=m<g?1:g/m,S=m<g?m/g:1,M=v+y;.001<Math.abs(g-m)?(t.translate(f,p),t.rotate(_),t.scale(b,S),t.arc(0,0,w,v,M,1-x),t.scale(1/b,1/S),t.rotate(-_),t.translate(-f,-p)):t.arc(f,p,w,v,M,1-x),1===c&&(e=Fo(v)*g+f,n=Ho(v)*m+p),i=Fo(M)*g+f,r=Ho(M)*m+p;break;case Oo.R:e=i=s[c],n=r=s[c+1],t.rect(s[c++],s[c++],s[c++],s[c++]);break;case Oo.Z:t.closePath(),i=e,r=n}}}},Uo.CMD=Oo;var qo=2*Math.PI;function $o(t){return(t%=qo)<0&&(t+=qo),t}var Ko=2*Math.PI;function Qo(t,e,n,i,r,o){if(e<o&&i<o||o<e&&o<i)return 0;if(i===e)return 0;var a=i<e?1:-1,s=(o-e)/(i-e);1!=s&&0!=s||(a=i<e?.5:-.5);var l=s*(n-t)+t;return l===r?1/0:r<l?a:0}var Jo=Uo.CMD,ta=2*Math.PI,ea=1e-4;var na=[-1,-1,-1],ia=[-1,-1];function ra(t,e,n,i,r,o,a,s,l,h){if(e<h&&i<h&&o<h&&s<h||h<e&&h<i&&h<o&&h<s)return 0;var u,c,d,f,p,g,m,v,y,_,x,w,b,S,M,I,C,T,A,D,k,P,L,O,E=(f=na,P=(T=3*((d=o)-2*(c=i)+(u=e)))*(A=3*(c-u))-9*(C=s+3*(c-d)-u)*(D=u-h),L=A*A-3*T*D,O=0,uo(k=T*T-3*C*A)&&uo(P)?uo(T)?f[0]=0:0<=(S=-A/T)&&S<=1&&(f[O++]=S):uo(p=P*P-4*k*L)?(M=-(g=P/k)/2,0<=(S=-T/C+g)&&S<=1&&(f[O++]=S),0<=M&&M<=1&&(f[O++]=M)):0<p?(y=k*T+1.5*C*(-P-(m=no(p))),0<=(S=(-T-((v=(v=k*T+1.5*C*(-P+m))<0?-eo(-v,ao):eo(v,ao))+(y=y<0?-eo(-y,ao):eo(y,ao))))/(3*C))&&S<=1&&(f[O++]=S)):(_=(2*k*T-3*C*P)/(2*no(k*k*k)),x=Math.acos(_)/3,S=(-T-2*(w=no(k))*(b=Math.cos(x)))/(3*C),M=(-T+w*(b+oo*Math.sin(x)))/(3*C),I=(-T+w*(b-oo*Math.sin(x)))/(3*C),0<=S&&S<=1&&(f[O++]=S),0<=M&&M<=1&&(f[O++]=M),0<=I&&I<=1&&(f[O++]=I)),O);if(0===E)return 0;for(var z,N,R,B=0,V=-1,F=0;F<E;F++){var H=na[F],W=0===H||1===H?.5:1;fo(t,n,r,a,H)<l||(V<0&&(V=go(e,i,o,s,ia),ia[1]<ia[0]&&1<V&&(R=void 0,R=ia[0],ia[0]=ia[1],ia[1]=R),z=fo(e,i,o,s,ia[0]),1<V&&(N=fo(e,i,o,s,ia[1]))),2===V?H<ia[0]?B+=z<e?W:-W:H<ia[1]?B+=N<z?W:-W:B+=s<N?W:-W:H<ia[0]?B+=z<e?W:-W:B+=s<z?W:-W)}return B}function oa(t,e,n,i,r,o,a,s){if(e<s&&i<s&&o<s||s<e&&s<i&&s<o)return 0;var l,h,u,c,d,f,p,g,m,v,y,_=(u=na,m=2*((h=i)-(l=e)),v=l-s,y=0,uo(g=l-2*h+o)?co(m)&&0<=(f=-v/m)&&f<=1&&(u[y++]=f):uo(c=m*m-4*g*v)?0<=(f=-m/(2*g))&&f<=1&&(u[y++]=f):0<c&&(p=(-m-(d=no(c)))/(2*g),0<=(f=(-m+d)/(2*g))&&f<=1&&(u[y++]=f),0<=p&&p<=1&&(u[y++]=p)),y);if(0===_)return 0;var x=_o(e,i,o);if(0<=x&&x<=1){for(var w=0,b=vo(e,i,o,x),S=0;S<_;S++){var M=0===na[S]||1===na[S]?.5:1;vo(t,n,r,na[S])<a||(na[S]<x?w+=b<e?M:-M:w+=o<b?M:-M)}return w}M=0===na[0]||1===na[0]?.5:1;return vo(t,n,r,na[0])<a?0:o<e?M:-M}function aa(t,e,n,i,r){for(var o,a,s=0,l=0,h=0,u=0,c=0,d=0;d<t.length;){var f=t[d++];switch(f===Jo.M&&1<d&&(n||(s+=Qo(l,h,u,c,i,r))),1===d&&(u=l=t[d],c=h=t[d+1]),f){case Jo.M:l=u=t[d++],h=c=t[d++];break;case Jo.L:if(n){if(Xo(l,h,t[d],t[d+1],e,i,r))return!0}else s+=Qo(l,h,t[d],t[d+1],i,r)||0;l=t[d++],h=t[d++];break;case Jo.C:if(n){if(Yo(l,h,t[d++],t[d++],t[d++],t[d++],t[d],t[d+1],e,i,r))return!0}else s+=ra(l,h,t[d++],t[d++],t[d++],t[d++],t[d],t[d+1],i,r)||0;l=t[d++],h=t[d++];break;case Jo.Q:if(n){if(jo(l,h,t[d++],t[d++],t[d],t[d+1],e,i,r))return!0}else s+=oa(l,h,t[d++],t[d++],t[d],t[d+1],i,r)||0;l=t[d++],h=t[d++];break;case Jo.A:var p=t[d++],g=t[d++],m=t[d++],v=t[d++],y=t[d++],_=t[d++];d+=1;var x=1-t[d++],w=Math.cos(y)*m+p,b=Math.sin(y)*v+g;1<d?s+=Qo(l,h,w,b,i,r):(u=w,c=b);var S=(i-p)*v/m+p;if(n){if(function(t,e,n,i,r,o,a,s,l){if(0!==a){var h=a;s-=t,l-=e;var u,c=Math.sqrt(s*s+l*l);if(!(n<c-h||c+h<n)){if(Math.abs(i-r)%Ko<1e-4)return 1;(r=o?(u=i,i=$o(r),$o(u)):(i=$o(i),$o(r)))<i&&(r+=Ko);var d=Math.atan2(l,s);return d<0&&(d+=Ko),i<=d&&d<=r||i<=d+Ko&&d+Ko<=r}}}(p,g,v,y,y+_,x,e,S,r))return!0}else s+=function(t,e,n,i,r,o,a,s){if(n<(s-=e)||s<-n)return 0;var l=Math.sqrt(n*n-s*s);na[0]=-l,na[1]=l;var h=Math.abs(i-r);if(h<1e-4)return 0;if(h%ta<1e-4){r=ta;var u=o?1:-1;return a>=na[i=0]+t&&a<=na[1]+t?u:0}(r=o?(l=i,i=$o(r),$o(l)):(i=$o(i),$o(r)))<i&&(r+=ta);for(var c=0,d=0;d<2;d++){var f,p=na[d];a<p+t&&(u=o?1:-1,(f=Math.atan2(s,p))<0&&(f=ta+f),(i<=f&&f<=r||i<=f+ta&&f+ta<=r)&&(f>Math.PI/2&&f<1.5*Math.PI&&(u=-u),c+=u))}return c}(p,g,v,y,y+_,x,S,r);l=Math.cos(y+_)*m+p,h=Math.sin(y+_)*v+g;break;case Jo.R:u=l=t[d++],c=h=t[d++];w=u+t[d++],b=c+t[d++];if(n){if(Xo(u,c,w,c,e,i,r)||Xo(w,c,w,b,e,i,r)||Xo(w,b,u,b,e,i,r)||Xo(u,b,u,c,e,i,r))return!0}else s+=Qo(w,c,w,b,i,r),s+=Qo(u,b,u,c,i,r);break;case Jo.Z:if(n){if(Xo(l,h,u,c,e,i,r))return!0}else s+=Qo(l,h,u,c,i,r);l=u,h=c}}return n||(o=h,a=c,Math.abs(o-a)<ea)||(s+=Qo(l,h,u,c,i,r)||0),0!==s}var sa=Hn.prototype.getCanvasPattern,la=Math.abs,ha=new Uo(!0);function ua(t){Vi.call(this,t),this.path=null}ua.prototype={constructor:ua,type:"path",__dirtyPath:!0,strokeContainThreshold:5,segmentIgnoreThreshold:0,subPixelOptimize:!1,brush:function(t,e){var n,i=this.style,r=this.path||ha,o=i.hasStroke(),a=i.hasFill(),s=i.fill,l=i.stroke,h=a&&!!s.colorStops,u=o&&!!l.colorStops,c=a&&!!s.image,d=o&&!!l.image;i.bind(t,this,e),this.setTransform(t),this.__dirty&&(h&&(n=n||this.getBoundingRect(),this._fillGradient=i.getGradient(t,s,n)),u&&(n=n||this.getBoundingRect(),this._strokeGradient=i.getGradient(t,l,n))),h?t.fillStyle=this._fillGradient:c&&(t.fillStyle=sa.call(s,t)),u?t.strokeStyle=this._strokeGradient:d&&(t.strokeStyle=sa.call(l,t));var f,p=i.lineDash,g=i.lineDashOffset,m=!!t.setLineDash,v=this.getGlobalScale();r.setScale(v[0],v[1],this.segmentIgnoreThreshold),this.__dirtyPath||p&&!m&&o?(r.beginPath(t),p&&!m&&(r.setLineDash(p),r.setLineDashOffset(g)),this.buildPath(r,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),a&&(null!=i.fillOpacity?(f=t.globalAlpha,t.globalAlpha=i.fillOpacity*i.opacity,r.fill(t),t.globalAlpha=f):r.fill(t)),p&&m&&(t.setLineDash(p),t.lineDashOffset=g),o&&(null!=i.strokeOpacity?(f=t.globalAlpha,t.globalAlpha=i.strokeOpacity*i.opacity,r.stroke(t),t.globalAlpha=f):r.stroke(t)),p&&m&&t.setLineDash([]),null!=i.text&&(this.restoreTransform(t),this.drawRectText(t,this.getBoundingRect()))},buildPath:function(){},createPathProxy:function(){this.path=new Uo},getBoundingRect:function(){var t,e=this._rect,n=this.style,i=!e;if(i&&(t=(t=this.path)||(this.path=new Uo),this.__dirtyPath&&(t.beginPath(),this.buildPath(t,this.shape,!1)),e=t.getBoundingRect()),this._rect=e,n.hasStroke()){var r,o,a=this._rectWithStroke||(this._rectWithStroke=e.clone());return(this.__dirty||i)&&(a.copy(e),r=n.lineWidth,o=n.strokeNoScale?this.getLineScale():1,n.hasFill()||(r=Math.max(r,this.strokeContainThreshold||4)),1e-10<o&&(a.width+=r/o,a.height+=r/o,a.x-=r/o/2,a.y-=r/o/2)),a}return e},contain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var o=this.path.data;if(r.hasStroke()){var a=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(1e-10<s&&(r.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),aa(o,a/s,!0,t,e)))return!0}if(r.hasFill())return aa(o,0,!1,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=this.__dirtyText=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):Vi.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var n=this.shape;if(n){if(B(t))for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);else n[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&1e-10<la(t[0]-1)&&1e-10<la(t[3]-1)?Math.sqrt(la(t[0]*t[3]-t[2]*t[1])):1}},ua.extend=function(r){function t(t){ua.call(this,t),r.style&&this.style.extendFrom(r.style,!1);var e=r.shape;if(e){this.shape=this.shape||{};var n,i=this.shape;for(n in e)!i.hasOwnProperty(n)&&e.hasOwnProperty(n)&&(i[n]=e[n])}r.init&&r.init.call(this,t)}for(var e in b(t,ua),r)"style"!==e&&"shape"!==e&&(t.prototype[e]=r[e]);return t},b(ua,Vi);function ca(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}var da=Uo.CMD,fa=[[],[],[]],pa=Math.sqrt,ga=Math.atan2,ma=function(t,e){for(var n,i,r,o,a=t.data,s=da.M,l=da.C,h=da.L,u=da.R,c=da.A,d=da.Q,f=0,p=0;f<a.length;){switch(n=a[f++],p=f,i=0,n){case s:case h:i=1;break;case l:i=3;break;case d:i=2;break;case c:var g=e[4],m=e[5],v=pa(e[0]*e[0]+e[1]*e[1]),y=pa(e[2]*e[2]+e[3]*e[3]),_=ga(-e[1]/y,e[0]/v);a[f]*=v,a[f++]+=g,a[f]*=y,a[f++]+=m,a[f++]*=v,a[f++]*=y,a[f++]+=_,a[f++]+=_,p=f+=2;break;case u:o[0]=a[f++],o[1]=a[f++],yt(o,o,e),a[p++]=o[0],a[p++]=o[1],o[0]+=a[f++],o[1]+=a[f++],yt(o,o,e),a[p++]=o[0],a[p++]=o[1]}for(r=0;r<i;r++){(o=fa[r])[0]=a[f++],o[1]=a[f++],yt(o,o,e),a[p++]=o[0],a[p++]=o[1]}}},va=Math.sqrt,ya=Math.sin,_a=Math.cos,xa=Math.PI,wa=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(ca(t)*ca(e))},ba=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(wa(t,e))};function Sa(t,e,n,i,r,o,a,s,l,h,u){var c=l*(xa/180),d=_a(c)*(t-n)/2+ya(c)*(e-i)/2,f=-1*ya(c)*(t-n)/2+_a(c)*(e-i)/2,p=d*d/(a*a)+f*f/(s*s);1<p&&(a*=va(p),s*=va(p));var g=(r===o?-1:1)*va((a*a*(s*s)-a*a*(f*f)-s*s*(d*d))/(a*a*(f*f)+s*s*(d*d)))||0,m=g*a*f/s,v=g*-s*d/a,y=(t+n)/2+_a(c)*m-ya(c)*v,_=(e+i)/2+ya(c)*m+_a(c)*v,x=ba([1,0],[(d-m)/a,(f-v)/s]),w=[(d-m)/a,(f-v)/s],b=[(-1*d-m)/a,(-1*f-v)/s],S=ba(w,b);wa(w,b)<=-1&&(S=xa),1<=wa(w,b)&&(S=0),0===o&&0<S&&(S-=2*xa),1===o&&S<0&&(S+=2*xa),u.addData(h,y,_,a,s,x,S,c,o)}var Ma=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,Ia=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Ca(t,e){var n=function(t){if(!t)return new Uo;for(var e,n=0,i=0,r=n,o=i,a=new Uo,s=Uo.CMD,l=t.match(Ma),h=0;h<l.length;h++){for(var u,c=l[h],d=c.charAt(0),f=c.match(Ia)||[],p=f.length,g=0;g<p;g++)f[g]=parseFloat(f[g]);for(var m=0;m<p;){var v,y,_,x,w,b,S,M=n,I=i;switch(d){case"l":n+=f[m++],i+=f[m++],u=s.L,a.addData(u,n,i);break;case"L":n=f[m++],i=f[m++],u=s.L,a.addData(u,n,i);break;case"m":n+=f[m++],i+=f[m++],u=s.M,a.addData(u,n,i),r=n,o=i,d="l";break;case"M":n=f[m++],i=f[m++],u=s.M,a.addData(u,n,i),r=n,o=i,d="L";break;case"h":n+=f[m++],u=s.L,a.addData(u,n,i);break;case"H":n=f[m++],u=s.L,a.addData(u,n,i);break;case"v":i+=f[m++],u=s.L,a.addData(u,n,i);break;case"V":i=f[m++],u=s.L,a.addData(u,n,i);break;case"C":u=s.C,a.addData(u,f[m++],f[m++],f[m++],f[m++],f[m++],f[m++]),n=f[m-2],i=f[m-1];break;case"c":u=s.C,a.addData(u,f[m++]+n,f[m++]+i,f[m++]+n,f[m++]+i,f[m++]+n,f[m++]+i),n+=f[m-2],i+=f[m-1];break;case"S":v=n,y=i;var C=a.len(),T=a.data;e===s.C&&(v+=n-T[C-4],y+=i-T[C-3]),u=s.C,M=f[m++],I=f[m++],n=f[m++],i=f[m++],a.addData(u,v,y,M,I,n,i);break;case"s":v=n,y=i;C=a.len(),T=a.data;e===s.C&&(v+=n-T[C-4],y+=i-T[C-3]),u=s.C,M=n+f[m++],I=i+f[m++],n+=f[m++],i+=f[m++],a.addData(u,v,y,M,I,n,i);break;case"Q":M=f[m++],I=f[m++],n=f[m++],i=f[m++],u=s.Q,a.addData(u,M,I,n,i);break;case"q":M=f[m++]+n,I=f[m++]+i,n+=f[m++],i+=f[m++],u=s.Q,a.addData(u,M,I,n,i);break;case"T":v=n,y=i;C=a.len(),T=a.data;e===s.Q&&(v+=n-T[C-4],y+=i-T[C-3]),n=f[m++],i=f[m++],u=s.Q,a.addData(u,v,y,n,i);break;case"t":v=n,y=i;C=a.len(),T=a.data;e===s.Q&&(v+=n-T[C-4],y+=i-T[C-3]),n+=f[m++],i+=f[m++],u=s.Q,a.addData(u,v,y,n,i);break;case"A":_=f[m++],x=f[m++],w=f[m++],b=f[m++],S=f[m++],Sa(M=n,I=i,n=f[m++],i=f[m++],b,S,_,x,w,u=s.A,a);break;case"a":_=f[m++],x=f[m++],w=f[m++],b=f[m++],S=f[m++],Sa(M=n,I=i,n+=f[m++],i+=f[m++],b,S,_,x,w,u=s.A,a)}}"z"!==d&&"Z"!==d||(u=s.Z,a.addData(u),n=r,i=o),e=u}return a.toStatic(),a}(t);return(e=e||{}).buildPath=function(t){var e;t.setData?(t.setData(n.data),(e=t.getContext())&&t.rebuildPath(e)):(e=t,n.rebuildPath(e))},e.applyTransform=function(t){ma(n,t),this.dirty(!0)},e}function Ta(t,e){return new ua(Ca(t,e))}var Aa=function(t){Vi.call(this,t)};Aa.prototype={constructor:Aa,type:"text",brush:function(t,e){var n=this.style;this.__dirty&&bi(n),n.fill=n.stroke=n.shadowBlur=n.shadowColor=n.shadowOffsetX=n.shadowOffsetY=null;var i=n.text;null!=i&&(i+=""),Ni(i,n)?(this.setTransform(t),Mi(this,t,i,n,null,e),this.restoreTransform(t)):t.__attrCachedBy=Ln.NONE},getBoundingRect:function(){var t,e,n=this.style;return this.__dirty&&bi(n),this._rect||(null!=n.text?0:0,(t=ii(n.text+"",n.font,n.textAlign,n.textVerticalAlign,n.textPadding,n.textLineHeight,n.rich)).x+=n.x||0,t.y+=n.y||0,Li(n.textStroke,n.textStrokeWidth)&&(e=n.textStrokeWidth,t.x-=e/2,t.y-=e/2,t.width+=e,t.height+=e),this._rect=t),this._rect}},b(Aa,Vi);function Da(l){return v.browser.ie&&11<=v.browser.version?function(){var t,e=this.__clipPaths,n=this.style;if(e)for(var i=0;i<e.length;i++){var r=e[i],o=r&&r.shape,a=r&&r.type;if(o&&("sector"===a&&o.startAngle===o.endAngle||"rect"===a&&(!o.width||!o.height))){for(var s=0;s<Pa.length;s++)Pa[s][2]=n[Pa[s][0]],n[Pa[s][0]]=Pa[s][1];t=!0;break}}if(l.apply(this,arguments),t)for(s=0;s<Pa.length;s++)n[Pa[s][0]]=Pa[s][2]}:l}var ka=ua.extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,n){n&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}}),Pa=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]],La=ua.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:Da(ua.prototype.brush),buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=e.startAngle,s=e.endAngle,l=e.clockwise,h=Math.cos(a),u=Math.sin(a);t.moveTo(h*r+n,u*r+i),t.lineTo(h*o+n,u*o+i),t.arc(n,i,o,a,s,!l),t.lineTo(Math.cos(s)*r+n,Math.sin(s)*r+i),0!==r&&t.arc(n,i,r,s,a,l),t.closePath()}}),Oa=ua.extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)}});function Ea(t,e,n,i,r,o,a){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*a+(-3*(e-n)-2*s-l)*o+s*r+e}function za(t,e,n){var i=e.points,r=e.smooth;if(i&&2<=i.length){if(r&&"spline"!==r){var o=function(t,e,n,i){var r,o,a,s,l=[],h=[],u=[],c=[];if(i){a=[1/0,1/0],s=[-1/0,-1/0];for(var d=0,f=t.length;d<f;d++)_t(a,a,t[d]),xt(s,s,t[d]);_t(a,a,i[0]),xt(s,s,i[1])}for(d=0,f=t.length;d<f;d++){var p=t[d];if(n)r=t[d?d-1:f-1],o=t[(d+1)%f];else{if(0===d||d===f-1){l.push(at(t[d]));continue}r=t[d-1],o=t[d+1]}ht(h,o,r),dt(h,h,e);var g=pt(p,r),m=pt(p,o),v=g+m;0!==v&&(g/=v,m/=v),dt(u,h,-g),dt(c,h,m);var y=st([],p,u),_=st([],p,c);i&&(xt(y,y,a),_t(y,y,s),xt(_,_,a),_t(_,_,s)),l.push(y),l.push(_)}return n&&l.push(l.shift()),l}(i,r,n,e.smoothConstraint);t.moveTo(i[0][0],i[0][1]);for(var a=i.length,s=0;s<(n?a:a-1);s++){var l=o[2*s],h=o[2*s+1],u=i[(s+1)%a];t.bezierCurveTo(l[0],l[1],h[0],h[1],u[0],u[1])}}else{"spline"===r&&(i=function(t,e){for(var n=t.length,i=[],r=0,o=1;o<n;o++)r+=pt(t[o-1],t[o]);for(var a=(a=r/2)<n?n:a,o=0;o<a;o++){var s,l,h=o/(a-1)*(e?n:n-1),u=Math.floor(h),c=h-u,d=t[u%n],f=e?(s=t[(u-1+n)%n],l=t[(u+1)%n],t[(u+2)%n]):(s=t[0===u?u:u-1],l=t[n-2<u?n-1:u+1],t[n-3<u?n-1:u+2]),p=c*c,g=c*p;i.push([Ea(s[0],d[0],l[0],f[0],c,p,g),Ea(s[1],d[1],l[1],f[1],c,p,g)])}return i}(i,n)),t.moveTo(i[0][0],i[0][1]);for(var s=1,c=i.length;s<c;s++)t.lineTo(i[s][0],i[s][1])}n&&t.closePath()}}var Na=ua.extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){za(t,e,!0)}}),Ra=ua.extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){za(t,e,!1)}}),Ba=Math.round;function Va(t,e,n){var i,r,o,a,s;e&&(i=e.x1,r=e.x2,o=e.y1,a=e.y2,t.x1=i,t.x2=r,t.y1=o,t.y2=a,(s=n&&n.lineWidth)&&(Ba(2*i)===Ba(2*r)&&(t.x1=t.x2=Ha(i,s,!0)),Ba(2*o)===Ba(2*a)&&(t.y1=t.y2=Ha(o,s,!0))))}function Fa(t,e,n){var i,r,o,a,s;e&&(i=e.x,r=e.y,o=e.width,a=e.height,t.x=i,t.y=r,t.width=o,t.height=a,(s=n&&n.lineWidth)&&(t.x=Ha(i,s,!0),t.y=Ha(r,s,!0),t.width=Math.max(Ha(i+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(Ha(r+a,s,!1)-t.y,0===a?0:1)))}function Ha(t,e,n){if(!e)return t;var i=Ba(2*t);return(i+Ba(e))%2==0?i/2:(i+(n?1:-1))/2}var Wa={},Ga=ua.extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var n,i,r,o;this.subPixelOptimize?(Fa(Wa,e,this.style),n=Wa.x,i=Wa.y,r=Wa.width,o=Wa.height,Wa.r=e.r,e=Wa):(n=e.x,i=e.y,r=e.width,o=e.height),e.r?gi(t,e):t.rect(n,i,r,o),t.closePath()}}),Za={},Ua=ua.extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n,i,r,o=this.subPixelOptimize?(Va(Za,e,this.style),n=Za.x1,i=Za.y1,r=Za.x2,Za.y2):(n=e.x1,i=e.y1,r=e.x2,e.y2),a=e.percent;0!==a&&(t.moveTo(n,i),a<1&&(r=n*(1-a)+r*a,o=i*(1-a)+o*a),t.lineTo(r,o))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}}),Xa=[];function Ya(t,e,n){var i=t.cpx2,r=t.cpy2;return null===i||null===r?[(n?po:fo)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?po:fo)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?yo:vo)(t.x1,t.cpx1,t.x2,e),(n?yo:vo)(t.y1,t.cpy1,t.y2,e)]}function ja(t){this.colorStops=t||[]}var qa=ua.extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,h=e.cpy2,u=e.percent;0!==u&&(t.moveTo(n,i),null==l||null==h?(u<1&&(xo(n,a,r,u,Xa),a=Xa[1],r=Xa[2],xo(i,s,o,u,Xa),s=Xa[1],o=Xa[2]),t.quadraticCurveTo(a,s,r,o)):(u<1&&(mo(n,a,l,r,u,Xa),a=Xa[1],l=Xa[2],r=Xa[3],mo(i,s,h,o,u,Xa),s=Xa[1],h=Xa[2],o=Xa[3]),t.bezierCurveTo(a,s,l,h,r,o)))},pointAt:function(t){return Ya(this.shape,t,!1)},tangentAt:function(t){var e=Ya(this.shape,t,!0);return ft(e,e)}}),$a=ua.extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,l=Math.cos(o),h=Math.sin(o);t.moveTo(l*r+n,h*r+i),t.arc(n,i,r,o,a,!s)}}),Ka=ua.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,n=0;n<e.length;n++)t=t||e[n].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},buildPath:function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},afterBrush:function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),ua.prototype.getBoundingRect.call(this)}});ja.prototype={constructor:ja,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}};function Qa(t,e,n,i,r,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==n?1:n,this.y2=null==i?0:i,this.type="linear",this.global=o||!1,ja.call(this,r)}Qa.prototype={constructor:Qa},b(Qa,ja);function Ja(t,e,n,i,r){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==n?.5:n,this.type="radial",this.global=r||!1,ja.call(this,i)}function ts(t){Vi.call(this,t),this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.notClear=!0}Ja.prototype={constructor:Ja},b(Ja,ja),ts.prototype.incremental=!0,ts.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.dirty(),this.notClear=!1},ts.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.dirty()},ts.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},ts.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},ts.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){(e=this._displayables[t]).parent=this,e.update(),e.parent=null}for(var e,t=0;t<this._temporaryDisplayables.length;t++){(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null}},ts.prototype.brush=function(t,e){for(var n=this._cursor;n<this._displayables.length;n++){(i=this._displayables[n]).beforeBrush&&i.beforeBrush(t),i.brush(t,n===this._cursor?null:this._displayables[n-1]),i.afterBrush&&i.afterBrush(t)}this._cursor=n;for(var i,n=0;n<this._temporaryDisplayables.length;n++){(i=this._temporaryDisplayables[n]).beforeBrush&&i.beforeBrush(t),i.brush(t,0===n?null:this._temporaryDisplayables[n-1]),i.afterBrush&&i.afterBrush(t)}this._temporaryDisplayables=[],this.notClear=!0};var es=[];ts.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new yn(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(es)),t.union(i)}this._rect=t}return this._rect},ts.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++){if(this._displayables[i].contain(t,e))return!0}return!1},b(ts,Vi);var ns=Math.max,is=Math.min,rs={},os=1,as="emphasis",ss="normal",ls=1,hs={},us={};function cs(t){return ua.extend(t)}function ds(t,e){us[t]=e}function fs(t){if(us.hasOwnProperty(t))return us[t]}function ps(t,e,n,i){var r=Ta(t,e);return n&&("center"===i&&(n=ms(n,r.getBoundingRect())),ys(r,n)),r}function gs(t,n,i){var r=new Fi({style:{image:t,x:n.x,y:n.y,width:n.width,height:n.height},onload:function(t){var e;"center"===i&&(e={width:t.width,height:t.height},r.setStyle(ms(n,e)))}});return r}function ms(t,e){var n=e.width/e.height,i=t.height*n,r=i<=t.width?t.height:(i=t.width)/n;return{x:t.x+t.width/2-i/2,y:t.y+t.height/2-r/2,width:i,height:r}}function vs(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];o.path||o.createPathProxy(),o.__dirtyPath&&o.buildPath(o.path,o.shape,!0),n.push(o.path)}var a=new ua(e);return a.createPathProxy(),a.buildPath=function(t){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e)},a}function ys(t,e){var n;t.applyTransform&&(n=t.getBoundingRect().calculateTransform(e),t.applyTransform(n))}var _s=Ha;function xs(t){return null!=t&&"none"!==t}var ws=tt(),bs=0;function Ss(t){var e,n,i,r,o=t.__hoverStl;o&&!t.__highlighted&&(e=t.__zr,n=t.useHoverLayer&&e&&"canvas"===e.painter.type,t.__highlighted=n?"layer":"plain",t.isGroup||!e&&t.useHoverLayer||(r=(i=t).style,n&&(r=(i=e.addHover(t)).style),Gs(r),n||function(t){if(t.__hoverStlDirty){t.__hoverStlDirty=!1;var e=t.__hoverStl;if(e){var n=t.__cachedNormalStl={};t.__cachedNormalZ2=t.z2;var i,r=t.style;for(i in e)null!=e[i]&&(n[i]=r[i]);n.fill=r.fill,n.stroke=r.stroke}else t.__cachedNormalStl=t.__cachedNormalZ2=null}}(i),r.extendFrom(o),Ms(r,o,"fill"),Ms(r,o,"stroke"),Ws(r),n||(t.dirty(!1),t.z2+=os)))}function Ms(t,e,n){!xs(e[n])&&xs(t[n])&&(t[n]=function(t){if("string"!=typeof t)return t;var e=ws.get(t);return e||(e=Re(t,-.1),bs<1e4&&(ws.set(t,e),bs++)),e}(t[n]))}function Is(t){var e,n,i,r=t.__highlighted;r&&(t.__highlighted=!1,t.isGroup||("layer"===r?t.__zr&&t.__zr.removeHover(t):(e=t.style,(n=t.__cachedNormalStl)&&(Gs(e),t.setStyle(n),Ws(e)),null!=(i=t.__cachedNormalZ2)&&t.z2-i===os&&(t.z2=i))))}function Cs(t,e,n){var i,r=ss,o=ss;t.__highlighted&&(r=as,i=!0),e(t,n),t.__highlighted&&(o=as,i=!0),t.isGroup&&t.traverse(function(t){t.isGroup||e(t,n)}),i&&t.__highDownOnUpdate&&t.__highDownOnUpdate(r,o)}function Ts(t,e){e=t.__hoverStl=!1!==e&&(t.hoverStyle||e||{}),t.__hoverStlDirty=!0,t.__highlighted&&(t.__cachedNormalStl=null,Is(t),Ss(t))}function As(t){Ls(this,t)||this.__highByOuter||Cs(this,Ss)}function Ds(t){Ls(this,t)||this.__highByOuter||Cs(this,Is)}function ks(t){this.__highByOuter|=1<<(t||0),Cs(this,Ss)}function Ps(t){(this.__highByOuter&=~(1<<(t||0)))||Cs(this,Is)}function Ls(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Os(t,e){Es(t,!0),Cs(t,Ts,e)}function Es(t,e){var n,i=!1===e;t.__highDownSilentOnTouch=t.highDownSilentOnTouch,t.__highDownOnUpdate=t.highDownOnUpdate,i&&!t.__highDownDispatcher||(t[n=i?"off":"on"]("mouseover",As)[n]("mouseout",Ds),t[n]("emphasis",ks)[n]("normal",Ps),t.__highByOuter=t.__highByOuter||0,t.__highDownDispatcher=!i)}function zs(t){return!(!t||!t.__highDownDispatcher)}function Ns(t){var e=hs[t];return null==e&&ls<=32&&(e=hs[t]=ls++),e}function Rs(t,e,n,i,r,o,a){var s,l=(r=r||rs).labelFetcher,h=r.labelDataIndex,u=r.labelDimIndex,c=r.labelProp,d=n.getShallow("show"),f=i.getShallow("show");(d||f)&&(l&&(s=l.getFormattedLabel(h,"normal",null,u,c)),null==s&&(s=N(r.defaultText)?r.defaultText(h,r):r.defaultText));var p=d?s:null,g=f?Z(l?l.getFormattedLabel(h,"emphasis",null,u,c):null,s):null;null==p&&null==g||(Bs(t,n,o,r),Bs(e,i,a,r,!0)),t.text=p,e.text=g}function Bs(t,e,n,i,r){return Vs(t,e,i,r),n&&k(t,n),t}function Vs(t,e,n,i){var r,o;(n=n||rs).isRectText&&(n.getTextPosition?r=n.getTextPosition(e,i):"outside"===(r=e.getShallow("position")||(i?null:"inside"))&&(r="top"),t.textPosition=r,t.textOffset=e.getShallow("offset"),null!=(o=e.getShallow("rotate"))&&(o*=Math.PI/180),t.textRotation=o,t.textDistance=Z(e.getShallow("distance"),i?null:5));var a,s,l=e.ecModel,h=l&&l.option.textStyle,u=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||rs).rich;if(n)for(var i in e=e||{},n)n.hasOwnProperty(i)&&(e[i]=1);t=t.parentModel}return e}(e);if(u)for(var c in a={},u){u.hasOwnProperty(c)&&(s=e.getModel(["rich",c]),Fs(a[c]={},s,h,n,i))}return t.rich=a,Fs(t,e,h,n,i,!0),n.forceRich&&!n.textStyle&&(n.textStyle={}),t}function Fs(t,e,n,i,r,o){n=!r&&n||rs,t.textFill=Hs(e.getShallow("color"),i)||n.color,t.textStroke=Hs(e.getShallow("textBorderColor"),i)||n.textBorderColor,t.textStrokeWidth=Z(e.getShallow("textBorderWidth"),n.textBorderWidth),r||(o&&(t.insideRollbackOpt=i,Ws(t)),null==t.textFill&&(t.textFill=i.autoColor)),t.fontStyle=e.getShallow("fontStyle")||n.fontStyle,t.fontWeight=e.getShallow("fontWeight")||n.fontWeight,t.fontSize=e.getShallow("fontSize")||n.fontSize,t.fontFamily=e.getShallow("fontFamily")||n.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),o&&i.disableBox||(t.textBackgroundColor=Hs(e.getShallow("backgroundColor"),i),t.textPadding=e.getShallow("padding"),t.textBorderColor=Hs(e.getShallow("borderColor"),i),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||n.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||n.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||n.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||n.textShadowOffsetY}function Hs(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function Ws(t){var e,n,i,r,o,a,s=t.textPosition,l=t.insideRollbackOpt;l&&null==t.textFill&&(n=l.autoColor,i=l.isRectText,a=!(o=!1!==(r=l.useInsideStyle)&&(!0===r||i&&s&&"string"==typeof s&&0<=s.indexOf("inside")))&&null!=n,(o||a)&&(e={textFill:t.textFill,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth}),o&&(t.textFill="#fff",null==t.textStroke&&(t.textStroke=n,null==t.textStrokeWidth&&(t.textStrokeWidth=2))),a&&(t.textFill=n)),t.insideRollback=e}function Gs(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth,t.insideRollback=null)}function Zs(t,e){var n=e&&e.getModel("textStyle");return q([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "))}function Us(t,e,n,i,r,o){var a,s,l,h;"function"==typeof r&&(o=r,r=null),i&&i.isAnimationEnabled()?(a=t?"Update":"",s=i.getShallow("animationDuration"+a),l=i.getShallow("animationEasing"+a),"function"==typeof(h=i.getShallow("animationDelay"+a))&&(h=h(r,i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null)),"function"==typeof s&&(s=s(r)),0<s?e.animateTo(n,s,h||0,l,o,!!o):(e.stopAnimation(),e.attr(n),o&&o())):(e.stopAnimation(),e.attr(n),o&&o())}function Xs(t,e,n,i,r){Us(!0,t,e,n,i,r)}function Ys(t,e,n,i,r){Us(!1,t,e,n,i,r)}function js(t,e){for(var n=ee([]);t&&t!==e;)ie(n,t.getLocalTransform(),n),t=t.parent;return n}function qs(t,e,n){return e&&!E(e)&&(e=ce.getLocalTransform(e)),n&&(e=se([],e)),yt([],t,e)}function $s(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),o=qs(o=["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0],e,n);return Math.abs(o[0])>Math.abs(o[1])?0<o[0]?"right":"left":0<o[1]?"bottom":"top"}function Ks(t,e,i,n){var r,o;function a(t){var e={position:at(t.position),rotation:t.rotation};return t.shape&&(e.shape=k({},t.shape)),e}t&&e&&(o={},t.traverse(function(t){!t.isGroup&&t.anid&&(o[t.anid]=t)}),r=o,e.traverse(function(t){var e,n;t.isGroup||!t.anid||(e=r[t.anid])&&(n=a(t),t.attr(a(e)),Xs(t,n,i,t.dataIndex))}))}function Qs(t,i){return P(t,function(t){var e=t[0],e=ns(e,i.x);e=is(e,i.x+i.width);var n=t[1],n=ns(n,i.y);return[e,is(n,i.y+i.height)]})}function Js(t,e,n){var i=(e=k({rectHover:!0},e)).style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),A(i,n),new Fi(e)):ps(t.replace("path://",""),e,n,"center")}function tl(t,e,n,i,r,o,a,s){var l,h=n-t,u=i-e,c=a-r,d=s-o,f=c*u-h*d;if((l=f)<=1e-6&&-1e-6<=l)return!1;var p=t-r,g=e-o,m=(p*u-h*g)/f;if(m<0||1<m)return!1;var v=(p*d-c*g)/f;return!(v<0||1<v)}ds("circle",ka),ds("sector",La),ds("ring",Oa),ds("polygon",Na),ds("polyline",Ra),ds("rect",Ga),ds("line",Ua),ds("bezierCurve",qa),ds("arc",$a);var el=(Object.freeze||Object)({Z2_EMPHASIS_LIFT:os,CACHED_LABEL_STYLE_PROPERTIES:{color:"textFill",textBorderColor:"textStroke",textBorderWidth:"textStrokeWidth"},extendShape:cs,extendPath:function(t,e){return ua.extend(Ca(t,e))},registerShape:ds,getShapeClass:fs,makePath:ps,makeImage:gs,mergePath:vs,resizePath:ys,subPixelOptimizeLine:function(t){return Va(t.shape,t.shape,t.style),t},subPixelOptimizeRect:function(t){return Fa(t.shape,t.shape,t.style),t},subPixelOptimize:_s,setElementHoverStyle:Ts,setHoverStyle:Os,setAsHighDownDispatcher:Es,isHighDownDispatcher:zs,getHighlightDigit:Ns,setLabelStyle:Rs,modifyLabelStyle:function(t,e,n){var i=t.style;e&&(Gs(i),t.setStyle(e),Ws(i)),i=t.__hoverStl,n&&i&&(Gs(i),k(i,n),Ws(i))},setTextStyle:Bs,setText:function(t,e,n){var i,r={isRectText:!0};!1===n?i=!0:r.autoColor=n,Vs(t,e,r,i)},getFont:Zs,updateProps:Xs,initProps:Ys,getTransform:js,applyTransform:qs,transformDirection:$s,groupTransition:Ks,clipPointsByRect:Qs,clipRectByRect:function(t,e){var n=ns(t.x,e.x),i=is(t.x+t.width,e.x+e.width),r=ns(t.y,e.y),o=is(t.y+t.height,e.y+e.height);if(n<=i&&r<=o)return{x:n,y:r,width:i-n,height:o-r}},createIcon:Js,linePolygonIntersect:function(t,e,n,i,r){for(var o=0,a=r[r.length-1];o<r.length;o++){var s=r[o];if(tl(t,e,n,i,s[0],s[1],a[0],a[1]))return!0;a=s}},lineLineIntersect:tl,Group:_n,Image:Fi,Text:Aa,Circle:ka,Sector:La,Ring:Oa,Polygon:Na,Polyline:Ra,Rect:Ga,Line:Ua,BezierCurve:qa,Arc:$a,IncrementalDisplayable:ts,CompoundPath:Ka,LinearGradient:Qa,RadialGradient:Ja,BoundingRect:yn}),nl=["textStyle","color"],il={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(nl):null)},getFont:function(){return Zs({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return ii(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("lineHeight"),this.getShallow("rich"),this.getShallow("truncateText"))}},rl=$r([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]),ol={getItemStyle:function(t,e){var n=rl(this,t,e),i=this.getBorderLineDash();return i&&(n.lineDash=i),n},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}},al=S,sl=zr();function ll(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}function hl(t,e,n){for(var i=0;i<e.length&&(!e[i]||null!=(t=t&&"object"===FS(t)?t[e[i]]:null));i++);return null==t&&n&&(t=n.get(e)),t}function ul(t,e){var n=sl(t).getParent;return n?n.call(t,e):t.parentModel}ll.prototype={constructor:ll,init:null,mergeOption:function(t){m(this.option,t,!0)},get:function(t,e){return null==t?this.option:hl(this.option,this.parsePath(t),!e&&ul(this,t))},getShallow:function(t,e){var n=this.option,i=null==n?n:n[t],r=!e&&ul(this,t);return null==i&&r&&(i=r.getShallow(t)),i},getModel:function(t,e){var n;return new ll(null==t?this.option:hl(this.option,t=this.parsePath(t)),e=e||(n=ul(this,t))&&n.getModel(t),this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){return new this.constructor(T(this.option))},setReadOnly:function(){},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){sl(this).getParent=t},isAnimationEnabled:function(){if(!v.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},Zr(ll),Xr(ll),al(ll,Qr),al(ll,to),al(ll,il),al(ll,ol);var cl=0;function dl(t){return[t||"",cl++,Math.random().toFixed(5)].join("_")}var fl=1e-4;function pl(t,e,n,i){var r=e[1]-e[0],o=n[1]-n[0];if(0==r)return 0==o?n[0]:(n[0]+n[1])/2;if(i)if(0<r){if(t<=e[0])return n[0];if(t>=e[1])return n[1]}else{if(t>=e[0])return n[0];if(t<=e[1])return n[1]}else{if(t===e[0])return n[0];if(t===e[1])return n[1]}return(t-e[0])/r*o+n[0]}function gl(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?t.replace(/^\s+|\s+$/g,"").match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t}function ml(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function vl(t){return t.sort(function(t,e){return t-e}),t}function yl(t){if(t=+t,isNaN(t))return 0;for(var e=1,n=0;Math.round(t*e)/e!==t;)e*=10,n++;return n}function _l(t){var e=t.toString(),n=e.indexOf("e");if(0<n){var i=+e.slice(n+1);return i<0?-i:0}var r=e.indexOf(".");return r<0?0:e.length-1-r}function xl(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),o=Math.round(n(Math.abs(e[1]-e[0]))/i),a=Math.min(Math.max(-r+o,0),20);return isFinite(a)?a:20}function wl(t,e,n){if(!t[e])return 0;var i=M(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===i)return 0;for(var r=Math.pow(10,n),o=P(t,function(t){return(isNaN(t)?0:t)/i*r*100}),a=100*r,s=P(o,function(t){return Math.floor(t)}),l=M(s,function(t,e){return t+e},0),h=P(o,function(t,e){return t-s[e]});l<a;){for(var u=Number.NEGATIVE_INFINITY,c=null,d=0,f=h.length;d<f;++d)h[d]>u&&(u=h[d],c=d);++s[c],h[c]=0,++l}return s[e]/r}function bl(t){var e=2*Math.PI;return(t%e+e)%e}function Sl(t){return-fl<t&&t<fl}var Ml=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Il(t){if(t instanceof Date)return t;if("string"!=typeof t)return null==t?new Date(NaN):new Date(Math.round(t));var e=Ml.exec(t);if(!e)return new Date(NaN);if(e[8]){var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}function Cl(t){return Math.pow(10,Tl(t))}function Tl(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return 10<=t/Math.pow(10,e)&&e++,e}function Al(t,e){var n=Tl(t),i=Math.pow(10,n),r=t/i,o=e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10;return t=o*i,-20<=n?+t.toFixed(n<0?-n:0):t}var Dl=(Object.freeze||Object)({linearMap:pl,parsePercent:gl,round:ml,asc:vl,getPrecision:yl,getPrecisionSafe:_l,getPixelPrecision:xl,getPercentWithPrecision:wl,MAX_SAFE_INTEGER:9007199254740991,remRadian:bl,isRadianAroundZero:Sl,parseDate:Il,quantity:Cl,quantityExponent:Tl,nice:Al,quantile:function(t,e){var n=(t.length-1)*e+1,i=Math.floor(n),r=+t[i-1],o=n-i;return o?r+o*(t[i]-r):r},reformIntervals:function(t){t.sort(function(t,e){return function t(e,n,i){return e.interval[i]<n.interval[i]||e.interval[i]===n.interval[i]&&(e.close[i]-n.close[i]==(i?-1:1)||!i&&t(e,n,1))}(t,e,0)?-1:1});for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t},isNumeric:function(t){return 0<=t-parseFloat(t)}});function kl(t){return isNaN(t)?"-":(t=(t+"").split("."))[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(1<t.length?"."+t[1]:"")}function Pl(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t}var Ll=Y,Ol=/([&<>"'])/g,El={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function zl(t){return null==t?"":(t+"").replace(Ol,function(t,e){return El[e]})}function Nl(t,e){return"{"+t+(null==e?"":e)+"}"}var Rl=["a","b","c","d","e","f","g"];function Bl(t,e,n){z(e)||(e=[e]);var i=e.length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=Rl[o];t=t.replace(Nl(a),Nl(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var h=e[s][r[l]];t=t.replace(Nl(Rl[l],s),n?zl(h):h)}return t}function Vl(t,e){var n=(t=R(t)?{color:t,extraCssText:e}:t||{}).color,i=t.type,e=t.extraCssText,r=t.renderMode||"html",o=t.markerId||"X";return n?"html"===r?"subItem"===i?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+zl(n)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:'+zl(n)+";"+(e||"")+'"></span>':{renderMode:r,content:"{marker"+o+"|}  ",style:{color:n}}:""}function Fl(t,e){return"0000".substr(0,e-(t+="").length)+t}function Hl(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=Il(e),r=n?"UTC":"",o=i["get"+r+"FullYear"](),a=i["get"+r+"Month"]()+1,s=i["get"+r+"Date"](),l=i["get"+r+"Hours"](),h=i["get"+r+"Minutes"](),u=i["get"+r+"Seconds"](),c=i["get"+r+"Milliseconds"]();return t=t.replace("MM",Fl(a,2)).replace("M",a).replace("yyyy",o).replace("yy",o%100).replace("dd",Fl(s,2)).replace("d",s).replace("hh",Fl(l,2)).replace("h",l).replace("mm",Fl(h,2)).replace("m",h).replace("ss",Fl(u,2)).replace("s",u).replace("SSS",Fl(c,3))}function Wl(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t}var Gl=si;function Zl(t,e){var n;"_blank"===e||"blank"===e?((n=window.open()).opener=null,n.location=t):window.open(t,e)}var Ul=(Object.freeze||Object)({addCommas:kl,toCamelCase:Pl,normalizeCssArray:Ll,encodeHTML:zl,formatTpl:Bl,formatTplSimple:function(n,t,i){return D(t,function(t,e){n=n.replace("{"+e+"}",i?zl(t):t)}),n},getTooltipMarker:Vl,formatTime:Hl,capitalFirst:Wl,truncateText:Gl,getTextBoundingRect:function(t){return ii(t.text,t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.textLineHeight,t.rich,t.truncate)},getTextRect:function(t,e,n,i,r,o,a,s){return ii(t,e,n,i,r,s,o,a)},windowOpen:Zl}),Xl=D,Yl=["left","right","top","bottom","width","height"],jl=[["width","left","right"],["height","top","bottom"]];function ql(u,c,d,f,p){var g=0,m=0;null==f&&(f=1/0),null==p&&(p=1/0);var v=0;c.eachChild(function(t,e){var n,i,r,o,a=t.position,s=t.getBoundingRect(),l=c.childAt(e+1),h=l&&l.getBoundingRect();v="horizontal"===u?(n=s.width+(h?-h.x+s.x:0),f<(i=g+n)||t.newline?(g=0,i=n,m+=v+d,s.height):Math.max(v,s.height)):(r=s.height+(h?-h.y+s.y:0),p<(o=m+r)||t.newline?(g+=v+d,m=0,o=r,s.width):Math.max(v,s.width)),t.newline||(a[0]=g,a[1]=m,"horizontal"===u?g=i+d:m=o+d)})}var $l=ql;O(ql,"vertical"),O(ql,"horizontal");function Kl(t,e,n){n=Ll(n||0);var i=e.width,r=e.height,o=gl(t.left,i),a=gl(t.top,r),s=gl(t.right,i),l=gl(t.bottom,r),h=gl(t.width,i),u=gl(t.height,r),c=n[2]+n[0],d=n[1]+n[3],f=t.aspect;switch(isNaN(h)&&(h=i-s-d-o),isNaN(u)&&(u=r-l-c-a),null!=f&&(isNaN(h)&&isNaN(u)&&(i/r<f?h=.8*i:u=.8*r),isNaN(h)&&(h=f*u),isNaN(u)&&(u=h/f)),isNaN(o)&&(o=i-s-h-d),isNaN(a)&&(a=r-l-u-c),t.left||t.right){case"center":o=i/2-h/2-n[3];break;case"right":o=i-h-d}switch(t.top||t.bottom){case"middle":case"center":a=r/2-u/2-n[0];break;case"bottom":a=r-u-c}o=o||0,a=a||0,isNaN(h)&&(h=i-d-o-(s||0)),isNaN(u)&&(u=r-c-a-(l||0));var p=new yn(o+n[3],a+n[0],h,u);return p.margin=n,p}function Ql(t,e,n,i,r){var o,a,s,l,h,u=!r||!r.hv||r.hv[0],c=!r||!r.hv||r.hv[1],d=r&&r.boundingMode||"all";(u||c)&&("raw"===d?a="group"===t.type?new yn(0,0,+e.width||0,+e.height||0):t.getBoundingRect():(a=t.getBoundingRect(),t.needLocalTransform()&&(o=t.getLocalTransform(),(a=a.clone()).applyTransform(o))),e=Kl(A({width:a.width,height:a.height},e),n,i),s=t.position,l=u?e.x-a.x:0,h=c?e.y-a.y:0,t.attr("position","raw"===d?[l,h]:[s[0]+l,s[1]+h]))}function Jl(l,h,t){B(t)||(t={});var u=t.ignoreSize;z(u)||(u=[u,u]);var e=i(jl[0],0),n=i(jl[1],1);function i(t,e){var n={},i=0,r={},o=0;if(Xl(t,function(t){r[t]=l[t]}),Xl(t,function(t){c(h,t)&&(n[t]=r[t]=h[t]),d(n,t)&&i++,d(r,t)&&o++}),u[e])return d(h,t[1])?r[t[2]]=null:d(h,t[2])&&(r[t[1]]=null),r;if(2!==o&&i){if(2<=i)return n;for(var a=0;a<t.length;a++){var s=t[a];if(!c(n,s)&&c(l,s)){n[s]=l[s];break}}return n}return r}function c(t,e){return t.hasOwnProperty(e)}function d(t,e){return null!=t[e]&&"auto"!==t[e]}function r(t,e,n){Xl(t,function(t){e[t]=n[t]})}r(jl[0],l,e),r(jl[1],l,n)}function th(t){return eh({},t)}function eh(e,n){return n&&e&&Xl(Yl,function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e}var nh,ih,rh,oh=zr(),ah=ll.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,n,i){ll.call(this,t,e,n,i),this.uid=dl("ec_cpt_model")},init:function(t,e,n){this.mergeDefaultAndTheme(t,n)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?th(t):{};m(t,e.getTheme().get(this.mainType)),m(t,this.getDefaultOption()),n&&Jl(t,i,n)},mergeOption:function(t){m(this.option,t,!0);var e=this.layoutMode;e&&Jl(this.option,t,e)},optionUpdated:function(){},getDefaultOption:function(){var t=oh(this);if(!t.defaultOption){for(var e=[],n=this.constructor;n;){var i=n.prototype.defaultOption;i&&e.push(i),n=n.superClass}for(var r={},o=e.length-1;0<=o;o--)r=m(r,e[o],!0);t.defaultOption=r}return t.defaultOption},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});function sh(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}qr(ah,{registerWhenExtend:!0}),ih={},(nh=ah).registerSubTypeDefaulter=function(t,e){t=Gr(t),ih[t.main]=e},nh.determineSubType=function(t,e){var n,i=e.type;return i||(n=Gr(t).main,nh.hasSubTypes(t)&&ih[n]&&(i=ih[n](e))),i},rh=function(t){var e=[];D(ah.getClassesByMainType(t),function(t){e=e.concat(t.prototype.dependencies||[])}),e=P(e,function(t){return Gr(t).main}),"dataset"!==t&&w(e,"dataset")<=0&&e.unshift("dataset");return e},ah.topologicalTravel=function(t,e,n,i){if(t.length){var a,s,l,r=(s={},l=[],D(a=e,function(n){var e,i,r=sh(s,n),t=r.originalDeps=rh(n),o=(e=a,i=[],D(t,function(t){0<=w(e,t)&&i.push(t)}),i);r.entryCount=o.length,0===r.entryCount&&l.push(n),D(o,function(t){w(r.predecessor,t)<0&&r.predecessor.push(t);var e=sh(s,t);w(e.successor,t)<0&&e.successor.push(n)})}),{graph:s,noEntryList:l}),o=r.graph,h=r.noEntryList,u={};for(D(t,function(t){u[t]=!0});h.length;){var c=h.pop(),d=o[c],f=!!u[c];f&&(n.call(i,c,d.originalDeps.slice()),delete u[c]),D(d.successor,f?g:p)}D(u,function(){throw new Error("Circle dependency may exists")})}function p(t){o[t].entryCount--,0===o[t].entryCount&&h.push(t)}function g(t){u[t]=!0,p(t)}},S(ah,{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}});var lh="";"undefined"!=typeof navigator&&(lh=navigator.platform||"");var hh={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],gradientColor:["#f6efa6","#d88273","#bf444c"],textStyle:{fontFamily:lh.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},uh=zr();var ch={clearColorPalette:function(){uh(this).colorIdx=0,uh(this).colorNameMap={}},getColorFromPalette:function(t,e,n){var i=uh(e=e||this),r=i.colorIdx||0,o=i.colorNameMap=i.colorNameMap||{};if(o.hasOwnProperty(t))return o[t];var a=Cr(this.get("color",!0)),s=this.get("colorLayer",!0),l=null!=n&&s?function(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}(s,n):a;if((l=l||a)&&l.length){var h=l[r];return t&&(o[t]=h),i.colorIdx=(r+1)%l.length,h}}},dh="original",fh="arrayRows",ph="objectRows",gh="keyedColumns",mh="unknown",vh="typedArray",yh="column",_h="row";function xh(t){this.fromDataset=t.fromDataset,this.data=t.data||(t.sourceFormat===gh?{}:[]),this.sourceFormat=t.sourceFormat||mh,this.seriesLayoutBy=t.seriesLayoutBy||yh,this.dimensionsDefine=t.dimensionsDefine,this.encodeDefine=t.encodeDefine&&tt(t.encodeDefine),this.startIndex=t.startIndex||0,this.dimensionsDetectCount=t.dimensionsDetectCount}xh.seriesDataToSource=function(t){return new xh({data:t,sourceFormat:F(t)?vh:dh,fromDataset:!1})},Xr(xh);var wh={Must:1,Might:2,Not:3},bh=zr();function Sh(t){var e,n=t.option,i=n.data,r=F(i)?vh:dh,o=!1,a=n.seriesLayoutBy,s=n.sourceHeader,l=n.dimensions,h=Ah(t);h&&(i=(e=h.option).source,r=bh(h).sourceFormat,o=!0,a=a||e.seriesLayoutBy,null==s&&(s=e.sourceHeader),l=l||e.dimensions);var u=function(t,e,n,i,r){if(!t)return{dimensionsDefine:Mh(r)};var o,a;{var s;e===fh?("auto"===i||null==i?Ih(function(t){null!=t&&"-"!==t&&(R(t)?null==a&&(a=1):a=0)},n,t,10):a=i?1:0,r||1!==a||(r=[],Ih(function(t,e){r[e]=null!=t?t:""},n,t)),o=r?r.length:n===_h?t.length:t[0]?t[0].length:null):e===ph?r=r||function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););if(e){var i=[];return D(e,function(t,e){i.push(e)}),i}}(t):e===gh?r||(r=[],D(t,function(t,e){r.push(e)})):e===dh?(s=Dr(t[0]),o=z(s)&&s.length||1):e===vh&&C&&j(!!r,"dimensions must be given if data is TypedArray.")}return{startIndex:a,dimensionsDefine:Mh(r),dimensionsDetectCount:o}}(i,r,a,s,l);bh(t).source=new xh({data:i,fromDataset:o,seriesLayoutBy:a,sourceFormat:r,dimensionsDefine:u.dimensionsDefine,startIndex:u.startIndex,dimensionsDetectCount:u.dimensionsDetectCount,encodeDefine:n.encode})}function Mh(t){if(t){var i=tt();return P(t,function(t,e){if(null==(t=k({},B(t)?t:{name:t})).name)return t;t.name+="",null==t.displayName&&(t.displayName=t.name);var n=i.get(t.name);return n?t.name+="-"+n.count++:i.set(t.name,{count:1}),t})}}function Ih(t,e,n,i){if(null==i&&(i=1/0),e===_h)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else for(var o=n[0]||[],r=0;r<o.length&&r<i;r++)t(o[r],r)}function Ch(n,t,e){var o={},i=Ah(t);if(!i||!n)return o;var a,r,s=[],l=[],h=t.ecModel,u=bh(h).datasetMap,c=i.uid+"_"+e.seriesLayoutBy;D(n=n.slice(),function(t,e){B(t)||(n[e]={name:t}),"ordinal"===t.type&&null==a&&(r=p(n[a=e])),o[t.name]=[]});var d=u.get(c)||u.set(c,{categoryWayDim:r,valueWayDim:0});function f(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function p(t){var e=t.dimsDef;return e?e.length:1}return D(n,function(t,e){var n,i=t.name,r=p(t);null==a?(n=d.valueWayDim,f(o[i],n,r),f(l,n,r),d.valueWayDim+=r):a===e?(f(o[i],0,r),f(s,0,r)):(n=d.categoryWayDim,f(o[i],n,r),f(l,n,r),d.categoryWayDim+=r)}),s.length&&(o.itemName=s),l.length&&(o.seriesName=l),o}function Th(t,l,h){var e={};if(!Ah(t))return e;var u,c=l.sourceFormat,d=l.dimensionsDefine;c!==ph&&c!==gh||D(d,function(t,e){"name"===(B(t)?t.name:t)&&(u=e)});var n,i=function(){for(var t={},e={},n=[],i=0,r=Math.min(5,h);i<r;i++){var o=Dh(l.data,c,l.seriesLayoutBy,d,l.startIndex,i);n.push(o);var a=o===wh.Not;if(a&&null==t.v&&i!==u&&(t.v=i),null!=t.n&&t.n!==t.v&&(a||n[t.n]!==wh.Not)||(t.n=i),s(t)&&n[t.n]!==wh.Not)return t;a||(o===wh.Might&&null==e.v&&i!==u&&(e.v=i),null!=e.n&&e.n!==e.v||(e.n=i))}function s(t){return null!=t.v&&null!=t.n}return s(t)?t:s(e)?e:null}();return i&&(e.value=i.v,n=null!=u?u:i.n,e.itemName=[n],e.seriesName=[n]),e}function Ah(t){var e=t.option;if(!e.data)return t.ecModel.getComponent("dataset",e.datasetIndex||0)}function Dh(t,e,n,i,r,o){var a,s,l,h;if(F(t))return wh.Not;if(i&&(B(h=i[o])?(s=h.name,l=h.type):R(h)&&(s=h)),null!=l)return"ordinal"===l?wh.Must:wh.Not;if(e===fh)if(n===_h){for(var u=t[o],c=0;c<(u||[]).length&&c<5;c++)if(null!=(a=g(u[r+c])))return a}else for(c=0;c<t.length&&c<5;c++){var d=t[r+c];if(d&&null!=(a=g(d[o])))return a}else if(e===ph){if(!s)return wh.Not;for(c=0;c<t.length&&c<5;c++){if((f=t[c])&&null!=(a=g(f[s])))return a}}else if(e===gh){if(!s)return wh.Not;if(!(u=t[s])||F(u))return wh.Not;for(c=0;c<u.length&&c<5;c++)if(null!=(a=g(u[c])))return a}else if(e===dh)for(c=0;c<t.length&&c<5;c++){var f,p=Dr(f=t[c]);if(!z(p))return wh.Not;if(null!=(a=g(p[o])))return a}function g(t){var e=R(t);return null!=t&&isFinite(t)&&""!==t?e?wh.Might:wh.Not:e&&"-"!==t?wh.Must:void 0}return wh.Not}var kh="\0_ec_inner",Ph=ll.extend({init:function(t,e,n,i){n=n||{},this.option=null,this._theme=new ll(n),this._optionManager=i},setOption:function(t,e){j(!(kh in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e,n,i,r=!1,o=this._optionManager;return t&&"recreate"!==t||(e=o.mountOption("recreate"===t),this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(e)):function(t){this.option={},this.option[kh]=1,this._componentsMap=tt({series:[]}),this._seriesIndices,this._seriesIndicesMap,function(n,t){var i=n.color&&!n.colorLayer;D(t,function(t,e){"colorLayer"===e&&i||ah.hasClass(e)||("object"===FS(t)?n[e]=n[e]?m(n[e],t,!1):T(t):null==n[e]&&(n[e]=t))})}(t,this._theme.option),m(t,hh,!1),this.mergeOption(t)}.call(this,e),r=!0),"timeline"!==t&&"media"!==t||this.restoreData(),t&&"recreate"!==t&&"timeline"!==t||(n=o.getTimelineOption(this))&&(this.mergeOption(n),r=!0),t&&"recreate"!==t&&"media"!==t||(i=o.getMediaOption(this,this._api)).length&&D(i,function(t){this.mergeOption(t,r=!0)},this),r},mergeOption:function(i){var l=this.option,h=this._componentsMap,n=[];bh(this).datasetMap=tt(),D(i,function(t,e){null!=t&&(ah.hasClass(e)?e&&n.push(e):l[e]=null==l[e]?T(t):m(l[e],t,!0))}),ah.topologicalTravel(n,ah.getAllClassMainTypes(),function(a,t){var e=Cr(i[a]),n=kr(h.get(a),e);Pr(n),D(n,function(t,e){var n,i,r,o=t.option;B(o)&&(t.keyInfo.mainType=a,t.keyInfo.subType=(n=a,i=o,r=t.exist,i.type?i.type:r?r.subType:ah.determineSubType(n,i)))});var s=function(e,t){z(t)||(t=t?[t]:[]);var n={};return D(t,function(t){n[t]=(e.get(t)||[]).slice()}),n}(h,t);l[a]=[],h.set(a,[]),D(n,function(t,e){var n,i,r=t.exist,o=t.option;j(B(o)||r,"Empty component definition"),o?(n=ah.getClass(a,t.keyInfo.subType,!0),r&&r.constructor===n?(r.name=t.keyInfo.name,r.mergeOption(o,this),r.optionUpdated(o,!1)):(i=k({dependentModels:s,componentIndex:e},t.keyInfo),k(r=new n(o,this,this,i),i),r.init(o,this,this,i),r.optionUpdated(null,!0))):(r.mergeOption({},this),r.optionUpdated({},!1)),h.get(a)[e]=r,l[a][e]=r.option},this),"series"===a&&Lh(this,h.get("series"))},this),this._seriesIndicesMap=tt(this._seriesIndices=this._seriesIndices||[])},getOption:function(){var i=T(this.option);return D(i,function(t,e){if(ah.hasClass(e)){for(var n=(t=Cr(t)).length-1;0<=n;n--)Or(t[n])&&t.splice(n,1);i[e]=t}}),delete i[kh],i},getTheme:function(){return this._theme},getComponent:function(t,e){var n=this._componentsMap.get(t);if(n)return n[e||0]},queryComponents:function(t){var e=t.mainType;if(!e)return[];var n,i,r=t.index,o=t.id,a=t.name,s=this._componentsMap.get(e);return s&&s.length?Oh(null!=r?(z(r)||(r=[r]),I(P(r,function(t){return s[t]}),function(t){return!!t})):null!=o?(n=z(o),I(s,function(t){return n&&0<=w(o,t.id)||!n&&t.id===o})):null!=a?(i=z(a),I(s,function(t){return i&&0<=w(a,t.name)||!i&&t.name===a})):s.slice(),t):[]},findComponents:function(t){var e,n,i,r,o,a=t.query,s=t.mainType,l=(n=s+"Index",i=s+"Id",r=s+"Name",!(e=a)||null==e[n]&&null==e[i]&&null==e[r]?null:{mainType:s,index:e[n],id:e[i],name:e[r]}),h=l?this.queryComponents(l):this._componentsMap.get(s);return o=Oh(h,t),t.filter?I(o,t.filter):o},eachComponent:function(t,i,r){var e=this._componentsMap;"function"==typeof t?(r=i,i=t,e.each(function(t,n){D(t,function(t,e){i.call(r,n,t,e)})})):R(t)?D(e.get(t),i,r):B(t)&&D(this.findComponents(t),i,r)},getSeriesByName:function(e){return I(this._componentsMap.get("series"),function(t){return t.name===e})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(e){return I(this._componentsMap.get("series"),function(t){return t.subType===e})},getSeries:function(){return this._componentsMap.get("series").slice()},getSeriesCount:function(){return this._componentsMap.get("series").length},eachSeries:function(n,i){Eh(this),D(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];n.call(i,e,t)},this)},eachRawSeries:function(t,e){D(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(n,i,r){Eh(this),D(this._seriesIndices,function(t){var e=this._componentsMap.get("series")[t];e.subType===n&&i.call(r,e,t)},this)},eachRawSeriesByType:function(t,e,n){return D(this.getSeriesByType(t),e,n)},isSeriesFiltered:function(t){return Eh(this),null==this._seriesIndicesMap.get(t.componentIndex)},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){Eh(this);var n=I(this._componentsMap.get("series"),t,e);Lh(this,n)},restoreData:function(n){var i=this._componentsMap;Lh(this,i.get("series"));var r=[];i.each(function(t,e){r.push(e)}),ah.topologicalTravel(r,ah.getAllClassMainTypes(),function(e,t){D(i.get(e),function(t){"series"===e&&function(t,e){if(e){var n=e.seiresIndex,i=e.seriesId,r=e.seriesName;return null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=r&&t.name!==r}}(t,n)||t.restoreData()})})}});function Lh(t,e){t._seriesIndicesMap=tt(t._seriesIndices=P(e,function(t){return t.componentIndex})||[])}function Oh(t,e){return e.hasOwnProperty("subType")?I(t,function(t){return t.subType===e.subType}):t}function Eh(t){if(C&&!t._seriesIndices)throw new Error("Option should contains series.")}S(Ph,ch);var zh=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"];function Nh(e){D(zh,function(t){this[t]=L(e[t],e)},this)}var Rh={};function Bh(){this._coordinateSystems=[]}Bh.prototype={constructor:Bh,create:function(i,r){var o=[];D(Rh,function(t,e){var n=t.create(i,r);o=o.concat(n||[])}),this._coordinateSystems=o},update:function(e,n){D(this._coordinateSystems,function(t){t.update&&t.update(e,n)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},Bh.register=function(t,e){Rh[t]=e},Bh.get=function(t){return Rh[t]};var Vh=D,Fh=T,Hh=P,Wh=m,Gh=/^(min|max)?(.+)$/;function Zh(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}Zh.prototype={constructor:Zh,setOption:function(t,e){t&&D(Cr(t.series),function(t){t&&t.data&&F(t.data)&&K(t.data)}),t=Fh(t);var r,n,i=this._optionBackup,o=function(t,n,i){var e,r,o=[],a=[],s=t.timeline;t.baseOption&&(r=t.baseOption);(s||t.options)&&(r=r||{},o=(t.options||[]).slice());{var l;t.media&&(r=r||{},l=t.media,Vh(l,function(t){t&&t.option&&(t.query?a.push(t):e=e||t)}))}r=r||t;r.timeline||(r.timeline=s);return Vh([r].concat(o).concat(P(a,function(t){return t.option})),function(e){Vh(n,function(t){t(e,i)})}),{baseOption:r,timelineOptions:o,mediaDefault:e,mediaList:a}}.call(this,t,e,!i);this._newBaseOption=o.baseOption,i?(r=i.baseOption,n=o.baseOption,Vh(n=n||{},function(t,e){var n,i;null!=t&&(n=r[e],ah.hasClass(e)?(t=Cr(t),i=kr(n=Cr(n),t),r[e]=Hh(i,function(t){return t.option&&t.exist?Wh(t.exist,t.option,!0):t.exist||t.option})):r[e]=Wh(n,t,!0))}),o.timelineOptions.length&&(i.timelineOptions=o.timelineOptions),o.mediaList.length&&(i.mediaList=o.mediaList),o.mediaDefault&&(i.mediaDefault=o.mediaDefault)):this._optionBackup=o},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=Hh(e.timelineOptions,Fh),this._mediaList=Hh(e.mediaList,Fh),this._mediaDefault=Fh(e.mediaDefault),this._currentMediaIndices=[],Fh(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,n,i=this._timelineOptions;return!i.length||(n=t.getComponent("timeline"))&&(e=Fh(i[n.getCurrentIndex()],!0)),e},getMediaOption:function(){var t=this._api.getWidth(),e=this._api.getHeight(),n=this._mediaList,i=this._mediaDefault,r=[],o=[];if(!n.length&&!i)return o;for(var a,s,l=0,h=n.length;l<h;l++)!function(t,e,n){var l={width:e,height:n,aspectratio:e/n},h=!0;return D(t,function(t,e){var n,i,r,o,a,s=e.match(Gh);s&&s[1]&&s[2]&&(n=s[1],i=s[2].toLowerCase(),r=l[i],o=t,("min"===(a=n)?o<=r:"max"===a?r<=o:r===o)||(h=!1))}),h}(n[l].query,t,e)||r.push(l);return!r.length&&i&&(r=[-1]),r.length&&(a=r,s=this._currentMediaIndices,a.join(",")!==s.join(","))&&(o=Hh(r,function(t){return Fh(-1===t?i.option:n[t].option)})),this._currentMediaIndices=r,o}};var Uh=D,Xh=B,Yh=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function jh(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=Yh.length;n<i;n++){var r=Yh[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?m(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?m(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function qh(t,e,n){var i,r;t&&t[e]&&(t[e].normal||t[e].emphasis)&&(i=t[e].normal,r=t[e].emphasis,i&&(n?(t[e].normal=t[e].emphasis=null,A(t[e],i)):t[e]=i),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r))}function $h(t){qh(t,"itemStyle"),qh(t,"lineStyle"),qh(t,"areaStyle"),qh(t,"label"),qh(t,"labelLine"),qh(t,"upperLabel"),qh(t,"edgeLabel")}function Kh(t,e){var n=Xh(t)&&t[e],i=Xh(n)&&n.textStyle;if(i)for(var r=0,o=Ar.length;r<o;r++){e=Ar[r];i.hasOwnProperty(e)&&(n[e]=i[e])}}function Qh(t){t&&($h(t),Kh(t,"label"),t.emphasis&&Kh(t.emphasis,"label"))}function Jh(t){return z(t)?t:t?[t]:[]}function tu(t){return(z(t)?t[0]:t)||{}}function eu(e,t){Uh(Jh(e.series),function(t){Xh(t)&&function(t){if(Xh(t)){jh(t),$h(t),Kh(t,"label"),Kh(t,"upperLabel"),Kh(t,"edgeLabel"),t.emphasis&&(Kh(t.emphasis,"label"),Kh(t.emphasis,"upperLabel"),Kh(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(jh(e),Qh(e));var n=t.markLine;n&&(jh(n),Qh(n));var i=t.markArea;i&&Qh(i);var r=t.data;if("graph"===t.type){r=r||t.nodes;var o=t.links||t.edges;if(o&&!F(o))for(var a=0;a<o.length;a++)Qh(o[a]);D(t.categories,function(t){$h(t)})}if(r&&!F(r))for(a=0;a<r.length;a++)Qh(r[a]);if((e=t.markPoint)&&e.data)for(var s=e.data,a=0;a<s.length;a++)Qh(s[a]);if((n=t.markLine)&&n.data)for(var l=n.data,a=0;a<l.length;a++)z(l[a])?(Qh(l[a][0]),Qh(l[a][1])):Qh(l[a]);"gauge"===t.type?(Kh(t,"axisLabel"),Kh(t,"title"),Kh(t,"detail")):"treemap"===t.type?(qh(t.breadcrumb,"itemStyle"),D(t.levels,function(t){$h(t)})):"tree"===t.type&&$h(t.leaves)}}(t)});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];t&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),Uh(n,function(t){Uh(Jh(e[t]),function(t){t&&(Kh(t,"axisLabel"),Kh(t.axisPointer,"label"))})}),Uh(Jh(e.parallel),function(t){var e=t&&t.parallelAxisDefault;Kh(e,"axisLabel"),Kh(e&&e.axisPointer,"label")}),Uh(Jh(e.calendar),function(t){qh(t,"itemStyle"),Kh(t,"dayLabel"),Kh(t,"monthLabel"),Kh(t,"yearLabel")}),Uh(Jh(e.radar),function(t){Kh(t,"name")}),Uh(Jh(e.geo),function(t){Xh(t)&&(Qh(t),Uh(Jh(t.regions),function(t){Qh(t)}))}),Uh(Jh(e.timeline),function(t){Qh(t),qh(t,"label"),qh(t,"itemStyle"),qh(t,"controlStyle",!0);var e=t.data;z(e)&&D(e,function(t){B(t)&&(qh(t,"label"),qh(t,"itemStyle"))})}),Uh(Jh(e.toolbox),function(t){qh(t,"iconStyle"),Uh(t.feature,function(t){qh(t,"iconStyle")})}),Kh(tu(e.axisPointer),"label"),Kh(tu(e.tooltip).axisPointer,"label")}function nu(e){D(iu,function(t){t[0]in e&&!(t[1]in e)&&(e[t[1]]=e[t[0]])})}var iu=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],ru=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],ou=function(n,t){eu(n,t),n.series=Cr(n.series),D(n.series,function(t){var e,n;B(t)&&("line"===(e=t.type)?null!=t.clipOverflow&&(t.clip=t.clipOverflow):"pie"===e||"gauge"===e?null!=t.clockWise&&(t.clockwise=t.clockWise):"gauge"===e&&null!=(n=function(t,e){e=e.split(",");for(var n=t,i=0;i<e.length&&null!=(n=n&&n[e[i]]);i++);return n}(t,"pointer.color"))&&function(t,e,n,i){e=e.split(",");for(var r,o=t,a=0;a<e.length-1;a++)null==o[r=e[a]]&&(o[r]={}),o=o[r];!i&&null!=o[e[a]]||(o[e[a]]=n)}(t,"itemStyle.color",n),nu(t))}),n.dataRange&&(n.visualMap=n.dataRange),D(ru,function(t){var e=n[t];e&&(z(e)||(e=[e]),D(e,function(t){nu(t)}))})};function au(m){D(m,function(u,c){var d=[],f=[NaN,NaN],t=[u.stackResultDimension,u.stackedOverDimension],p=u.data,g=u.isStackedByIndex,e=p.map(t,function(t,e,n){var i,r,o=p.get(u.stackedDimension,n);if(isNaN(o))return f;g?r=p.getRawIndex(n):i=p.get(u.stackedByDimension,n);for(var a=NaN,s=c-1;0<=s;s--){var l=m[s];if(g||(r=l.data.rawIndexOf(l.stackedByDimension,i)),0<=r){var h=l.data.getByRawIndex(l.stackResultDimension,r);if(0<=o&&0<h||o<=0&&h<0){o+=h,a=h;break}}}return d[0]=o,d[1]=a,d});p.hostModel.setData(e),u.data=e})}function su(t,e){xh.isInstance(t)||(t=xh.seriesDataToSource(t)),this._source=t;var n=this._data=t.data,i=t.sourceFormat;if(i===vh){if(C&&null==e)throw new Error("Typed array data must specify dimension size");this._offset=0,this._dimSize=e,this._data=n}var r=hu[i===fh?i+"_"+t.seriesLayoutBy:i];C&&j(r,"Invalide sourceFormat: "+i),k(this,r)}var lu=su.prototype;lu.pure=!1;var hu={arrayRows_column:{pure:lu.persistent=!0,count:function(){return Math.max(0,this._data.length-this._source.startIndex)},getItem:function(t){return this._data[t+this._source.startIndex]},appendData:du},arrayRows_row:{pure:!0,count:function(){var t=this._data[0];return t?Math.max(0,t.length-this._source.startIndex):0},getItem:function(t){t+=this._source.startIndex;for(var e=[],n=this._data,i=0;i<n.length;i++){var r=n[i];e.push(r?r[t]:null)}return e},appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},objectRows:{pure:!0,count:uu,getItem:cu,appendData:du},keyedColumns:{pure:!0,count:function(){var t=this._source.dimensionsDefine[0].name,e=this._data[t];return e?e.length:0},getItem:function(t){for(var e=[],n=this._source.dimensionsDefine,i=0;i<n.length;i++){var r=this._data[n[i].name];e.push(r?r[t]:null)}return e},appendData:function(t){var r=this._data;D(t,function(t,e){for(var n=r[e]||(r[e]=[]),i=0;i<(t||[]).length;i++)n.push(t[i])})}},original:{count:uu,getItem:cu,appendData:du},typedArray:{persistent:!(lu.getSource=function(){return this._source}),pure:!0,count:function(){return this._data?this._data.length/this._dimSize:0},getItem:function(t,e){t-=this._offset,e=e||[];for(var n=this._dimSize*t,i=0;i<this._dimSize;i++)e[i]=this._data[n+i];return e},appendData:function(t){C&&j(F(t),"Added data must be TypedArray if data in initialization is TypedArray"),this._data=t},clean:function(){this._offset+=this.count(),this._data=null}}};function uu(){return this._data.length}function cu(t){return this._data[t]}function du(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}var fu={arrayRows:pu,objectRows:function(t,e,n,i){return null!=n?t[i]:t},keyedColumns:pu,original:function(t,e,n){var i=Dr(t);return null!=n&&i instanceof Array?i[n]:i},typedArray:pu};function pu(t,e,n,i){return null!=n?t[n]:t}var gu={arrayRows:mu,objectRows:function(t,e){return vu(t[e],this._dimensionInfos[e])},keyedColumns:mu,original:function(t,e,n,i){var r,o=t&&(null==t.value?t:t.value);return this._rawData.pure||(!Sr(r=t)||r instanceof Array)||(this.hasItemOption=!0),vu(o instanceof Array?o[i]:o,this._dimensionInfos[e])},typedArray:function(t,e,n,i){return t[i]}};function mu(t,e,n,i){return vu(t[i],this._dimensionInfos[e])}function vu(t,e){var n=e&&e.type;if("ordinal"!==n)return"time"===n&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+Il(t)),null==t||""===t?NaN:+t;var i=e&&e.ordinalMeta;return i?i.parseAndCollect(t):t}function yu(t,e,n){if(t){var i=t.getRawDataItem(e);if(null!=i){var r,o,a=t.getProvider().getSource().sourceFormat,s=t.getDimensionInfo(n);return s&&(r=s.name,o=s.index),fu[a](i,e,o,r)}}}var _u=/\{@(.+?)\}/g,xu={getDataParams:function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"color"),l=n.getItemVisual(t,"borderColor"),h=this.ecModel.getComponent("tooltip"),u=Fr(h&&h.get("renderMode")),c=this.mainType,d="series"===c,f=n.userOutput;return{componentType:c,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:d?this.subType:null,seriesIndex:this.seriesIndex,seriesId:d?this.id:null,seriesName:d?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:s,borderColor:l,dimensionNames:f?f.dimensionNames:null,encode:f?f.encode:null,marker:Vl({color:s,renderMode:u}),$vars:["seriesName","name","value"]}},getFormattedLabel:function(i,t,e,n,r){t=t||"normal";var o=this.getData(e),a=o.getItemModel(i),s=this.getDataParams(i,e);null!=n&&s.value instanceof Array&&(s.value=s.value[n]);var l=a.get("normal"===t?[r||"label","formatter"]:[t,r||"label","formatter"]);return"function"==typeof l?(s.status=t,s.dimensionIndex=n,l(s)):"string"==typeof l?Bl(l,s).replace(_u,function(t,e){var n=e.length;return"["===e.charAt(0)&&"]"===e.charAt(n-1)&&(e=+e.slice(1,n-1)),yu(o,i,e)}):void 0},getRawValue:function(t,e){return yu(this.getData(e),t)},formatTooltip:function(){}};function wu(t){return new bu(t)}function bu(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0,this.context}var Su=bu.prototype;Su.perform=function(t){var e,n,i=this._upstream,r=t&&t.skip;this._dirty&&i&&((e=this.context).data=e.outputData=i.context.outputData),this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!r&&(n=this._plan(this.context));var o,a=u(this._modBy),s=this._modDataCount||0,l=u(t&&t.modBy),h=t&&t.modDataCount||0;function u(t){return 1<=t||(t=1),t}a===l&&s===h||(n="reset"),!this._dirty&&"reset"!==n||(this._dirty=!1,o=function(t,e){var n,i;t._dueIndex=t._outputDueEnd=t._dueEnd=0,t._settedOutputEnd=null,!e&&t._reset&&((n=t._reset(t.context))&&n.progress&&(i=n.forceFirstProgress,n=n.progress),z(n)&&!n.length&&(n=null));t._progress=n,t._modBy=t._modDataCount=null;var r=t._downstream;return r&&r.dirty(),i}(this,r)),this._modBy=l,this._modDataCount=h;var c=t&&t.step;if(i?(C&&j(null!=i._outputDueEnd),this._dueEnd=i._outputDueEnd):(C&&j(!this._progress||this._count),this._dueEnd=this._count?this._count(this.context):1/0),this._progress){var d=this._dueIndex,f=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!r&&(o||d<f)){var p=this._progress;if(z(p))for(var g=0;g<p.length;g++)Ou(this,p[g],d,f,l,h);else Ou(this,p,d,f,l,h)}this._dueIndex=f;var m=null!=this._settedOutputEnd?this._settedOutputEnd:f;C&&j(m>=this._outputDueEnd),this._outputDueEnd=m}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()};var Mu,Iu,Cu,Tu,Au,Du,ku=Du={reset:function(t,e,n,i){Iu=t,Mu=e,Cu=n,Tu=i,Au=Math.ceil(Tu/Cu),Du.next=1<Cu&&0<Tu?Lu:Pu}};function Pu(){return Iu<Mu?Iu++:null}function Lu(){var t=Iu%Au*Cu+Math.ceil(Iu/Au),e=Mu<=Iu?null:t<Tu?t:Iu;return Iu++,e}function Ou(t,e,n,i,r,o){ku.reset(n,i,r,o),t._callingProgress=e,t._callingProgress({start:n,end:i,count:i-n,next:ku.next},t.context)}Su.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},Su.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},Su.pipe=function(t){C&&j(t&&!t._disposed&&t!==this),this._downstream===t&&!this._dirty||((this._downstream=t)._upstream=this,t.dirty())},Su.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},Su.getUpstream=function(){return this._upstream},Su.getDownstream=function(){return this._downstream},Su.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t};var Eu=zr(),zu=ah.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendVisualProvider:null,visualColorAccessPath:"itemStyle.color",visualBorderColorAccessPath:"itemStyle.borderColor",layoutMode:null,init:function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=wu({count:Ru,reset:Bu}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),Sh(this);var i=this.getInitialData(t,n);Fu(i,this),this.dataTask.context.data=i,C&&j(i,"getInitialData returned invalid data."),Eu(this).dataBeforeProcessed=i,Nu(this)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?th(t):{},r=this.subType;ah.hasClass(r),m(t,e.getTheme().get(this.subType)),m(t,this.getDefaultOption()),Tr(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&Jl(t,i,n)},mergeOption:function(t,e){t=m(this.option,t,!0),this.fillDataTextStyle(t.data);var n=this.layoutMode;n&&Jl(this.option,t,n),Sh(this);var i=this.getInitialData(t,e);Fu(i,this),this.dataTask.dirty(),this.dataTask.context.data=i,Eu(this).dataBeforeProcessed=i,Nu(this)},fillDataTextStyle:function(t){if(t&&!F(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&Tr(t[n],"label",e)},getInitialData:function(){},appendData:function(t){this.getRawData().appendData(t.data)},getData:function(t){var e=Wu(this);if(e){var n=e.context.data;return null==t?n:n.getLinkedData(t)}return Eu(this).data},setData:function(t){var e,n=Wu(this);n&&((e=n.context).data!==t&&n.modifyOutputEnd&&n.setOutputEnd(t.count()),e.outputData=t,n!==this.dataTask&&(e.data=t)),Eu(this).data=t},getSource:function(){return bh(this).source},getRawData:function(){return Eu(this).dataBeforeProcessed},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(r,u,t,c){var d=this,e="html"===(c=c||"html")?"<br/>":"\n",f="richText"===c,p={},g=0;function n(t){return{renderMode:c,content:zl(kl(t)),style:p}}var m=this.getData(),o=m.mapDimension("defaultedTooltip",!0),i=o.length,a=this.getRawValue(r),s=z(a),v=m.getItemVisual(r,"color");B(v)&&v.colorStops&&(v=(v.colorStops[0]||{}).color),v=v||"transparent";var l=(1<i||s&&!i?function(t){var l=M(t,function(t,e,n){var i=m.getDimensionInfo(n);return t|(i&&!1!==i.tooltip&&null!=i.displayName)},0),h=[];function e(t,e){var n,i,r,o,a,s=m.getDimensionInfo(e);s&&!1!==s.otherDims.tooltip&&(n=s.type,i="sub"+d.seriesIndex+"at"+g,o="string"==typeof(r=Vl({color:v,type:"subItem",renderMode:c,markerId:i}))?r:r.content,(a=(l?o+zl(s.displayName||"-")+": ":"")+zl("ordinal"===n?t+"":"time"===n?u?"":Hl("yyyy/MM/dd hh:mm:ss",t):kl(t)))&&h.push(a),f&&(p[i]=v,++g))}o.length?D(o,function(t){e(yu(m,r,t),t)}):D(t,e);var n=l?f?"\n":"<br/>":"",i=n+h.join(n||", ");return{renderMode:c,content:i,style:p}}(a):n(i?yu(m,r,o[0]):s?a[0]:a)).content,h=d.seriesIndex+"at"+g,y=Vl({color:v,type:"item",renderMode:c,markerId:h});p[h]=v,++g;var _=m.getName(r),x=this.name;Lr(this)||(x=""),x=x?zl(x)+(u?": ":e):"";var w="string"==typeof y?y:y.content;return{html:u?w+x+l:x+w+(_?zl(_)+": "+l:l),markers:p}},isAnimationEnabled:function(){if(v.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){this.dataTask.dirty()},getColorFromPalette:function(t,e,n){var i=this.ecModel;return ch.getColorFromPalette.call(this,t,e,n)||i.getColorFromPalette(t,e,n)},coordDimToDataDim:function(t){return this.getRawData().mapDimension(t,!0)},getProgressive:function(){return this.get("progressive")},getProgressiveThreshold:function(){return this.get("progressiveThreshold")},getAxisTooltipData:null,getTooltipPosition:null,pipeTask:null,preventIncremental:null,pipelineContext:null});function Nu(t){var n,e,i,r=t.name;Lr(t)||(t.name=(n=t.getRawData(),e=n.mapDimension("seriesName",!0),i=[],D(e,function(t){var e=n.getDimensionInfo(t);e.displayName&&i.push(e.displayName)}),i.join(" ")||r))}function Ru(t){return t.model.getRawData().count()}function Bu(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),Vu}function Vu(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function Fu(e,n){D(e.CHANGABLE_METHODS,function(t){e.wrapMethod(t,O(Hu,n))})}function Hu(t){var e=Wu(t);e&&e.setOutputEnd(this.count())}function Wu(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var i,r=n.currentTask;return!r||(i=r.agentStubMap)&&(r=i.get(t.uid)),r}}S(zu,xu),S(zu,ch);var Gu=function(){this.group=new _n,this.uid=dl("viewComponent")};Gu.prototype={constructor:Gu,init:function(){},render:function(){},dispose:function(){},filterForExposedEvent:null};var Zu=Gu.prototype;Zu.updateView=Zu.updateLayout=Zu.updateVisual=function(t,e,n,i){},Zr(Gu),qr(Gu,{registerWhenExtend:!0});function Uu(){var s=zr();return function(t){var e=s(t),n=t.pipelineContext,i=e.large,r=e.progressiveRender,o=e.large=n&&n.large,a=e.progressiveRender=n&&n.progressiveRender;return!!(i^o||r^a)&&"reset"}}var Xu=zr(),Yu=Uu();function ju(){this.group=new _n,this.uid=dl("viewChart"),this.renderTask=wu({plan:Qu,reset:Ju}),this.renderTask.context={view:this}}var qu=ju.prototype={type:"chart",init:function(){},render:function(){},highlight:function(t,e,n,i){Ku(t.getData(),i,"emphasis")},downplay:function(t,e,n,i){Ku(t.getData(),i,"normal")},remove:function(){this.group.removeAll()},dispose:function(){},incrementalPrepareRender:null,incrementalRender:null,updateTransform:null,filterForExposedEvent:null};function $u(t,e,n){if(t&&(t.trigger(e,n),t.isGroup&&!zs(t)))for(var i=0,r=t.childCount();i<r;i++)$u(t.childAt(i),e,n)}function Ku(e,t,n){var i=Er(e,t),r=t&&null!=t.highlightKey?Ns(t.highlightKey):null;null!=i?D(Cr(i),function(t){$u(e.getItemGraphicEl(t),n,r)}):e.eachItemGraphicEl(function(t){$u(t,n,r)})}function Qu(t){return Yu(t.model)}function Ju(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,a=t.view,s=r&&Xu(r).updateMethod,l=o?"incrementalPrepareRender":s&&a[s]?s:"render";return"render"!==l&&a[l](e,n,i,r),tc[l]}qu.updateView=qu.updateLayout=qu.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},Zr(ju,["dispose"]),qr(ju,{registerWhenExtend:!0}),ju.markUpdateMethod=function(t,e){Xu(t).updateMethod=e};var tc={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}},ec="\0__throttleOriginMethod",nc="\0__throttleRate",ic="\0__throttleType";function rc(t,n,i){var r,o,a,s,l,h=0,u=0,c=null;function d(){u=(new Date).getTime(),c=null,t.apply(a,s||[])}n=n||0;function e(){r=(new Date).getTime(),a=this,s=arguments;var t=l||n,e=l||i;l=null,o=r-(e?h:u)-t,clearTimeout(c),e?c=setTimeout(d,t):0<=o?d():c=setTimeout(d,-o),h=r}return e.clear=function(){c&&(clearTimeout(c),c=null)},e.debounceNextCall=function(t){l=t},e}function oc(t,e,n,i){var r=t[e];if(r){var o=r[ec]||r,a=r[ic];if(r[nc]!==n||a!==i){if(null==n||!i)return t[e]=o;(r=t[e]=rc(o,n,"debounce"===i))[ec]=o,r[ic]=i,r[nc]=n}return r}}function ac(t,e){var n=t[e];n&&n[ec]&&(t[e]=n[ec])}var sc={createOnAllSeries:!0,performRawSeries:!0,reset:function(e,t){var n=e.getData(),o=(e.visualColorAccessPath||"itemStyle.color").split("."),i=e.get(o),r=!N(i)||i instanceof ja?null:i;i&&!r||(i=e.getColorFromPalette(e.name,null,t.getSeriesCount())),n.setVisual("color",i);var a=(e.visualBorderColorAccessPath||"itemStyle.borderColor").split("."),s=e.get(a);if(n.setVisual("borderColor",s),!t.isSeriesFiltered(e)){r&&n.each(function(t){n.setItemVisual(t,"color",r(e.getDataParams(t)))});return{dataEach:n.hasItemOption?function(t,e){var n=t.getItemModel(e),i=n.get(o,!0),r=n.get(a,!0);null!=i&&t.setItemVisual(e,"color",i),null!=r&&t.setItemVisual(e,"borderColor",r)}:null}}}},lc={legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Guage",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:",",end:"."}}}},hc=function(t,e){var c,d,n,f,i,r,p,o=e.getModel("aria");function g(t,e){if("string"!=typeof t)return t;var n=t;return D(e,function(t,e){n=n.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)}),n}function m(t){var e=o.get(t);if(null!=e)return e;for(var n=t.split("."),i=lc.aria,r=0;r<n.length;++r)i=i[n[r]];return i}o.get("show")&&(o.get("description")?t.setAttribute("aria-label",o.get("description")):(c=0,e.eachSeries(function(t,e){++c},this),d=o.get("data.maxCount")||10,n=o.get("series.maxCount")||10,f=Math.min(c,n),c<1||(r=(i=function(){var t=e.getModel("title").option;t&&t.length&&(t=t[0]);return t&&t.text}())?g(m("general.withTitle"),{title:i}):m("general.withoutTitle"),p=[],r+=g(m(1<c?"series.multiple.prefix":"series.single.prefix"),{seriesCount:c}),e.eachSeries(function(t,e){if(e<f){var n=t.get("name"),i="series."+(1<c?"multiple":"single")+".",r=m(n?i+"withName":i+"withoutName");r=g(r,{seriesId:t.seriesIndex,seriesName:t.get("name"),seriesType:(u=t.subType,lc.series.typeNames[u]||"自定义图")});var o=t.getData();(window.data=o).count()>d?r+=g(m("data.partialData"),{displayCnt:d}):r+=m("data.allData");for(var a,s,l=[],h=0;h<o.count();h++){h<d&&(a=o.getName(h),s=yu(o,h),l.push(g(m(a?"data.withName":"data.withoutName"),{name:a,value:s})))}r+=l.join(m("data.separator.middle"))+m("data.separator.end"),p.push(r)}var u}),r+=p.join(m("series.multiple.separator.middle"))+m("series.multiple.separator.end"),t.setAttribute("aria-label",r))))},uc=Math.PI;function cc(t,e,n,i){this.ecInstance=t,this.api=e,this.unfinished;n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice();this._allHandlers=n.concat(i),this._stageTaskMap=tt()}var dc=cc.prototype;function fc(l,t,h,u,c){var d;function f(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}c=c||{},D(t,function(i,t){var e,n,r,o,a,s;c.visualType&&c.visualType!==i.visualType||(n=(e=l._stageTaskMap.get(i.uid)).seriesTaskMap,(r=e.overallTask)?((a=r.agentStubMap).each(function(t){f(c,t)&&(t.dirty(),o=!0)}),o&&r.dirty(),pc(r,u),s=l.getPerformArgs(r,c.block),a.each(function(t){t.perform(s)}),d|=r.perform(s)):n&&n.each(function(t,e){f(c,t)&&t.dirty();var n=l.getPerformArgs(t,c.block);n.skip=!i.performRawSeries&&h.isSeriesFiltered(t.context.model),pc(t,u),d|=t.perform(n)}))}),l.unfinished|=d}dc.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each(function(t){var e=t.overallTask;e&&e.dirty()})},dc.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,r=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,o=i&&i.modDataCount;return{step:r,modBy:null!=o?Math.ceil(o/r):null,modDataCount:o}}},dc.getPipeline=function(t){return this._pipelineMap.get(t)},dc.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),r=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,o=t.get("large")&&i>=t.get("largeThreshold"),a="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:r,modDataCount:a,large:o}},dc.restorePipelines=function(t){var i=this,r=i._pipelineMap=tt();t.eachSeries(function(t){var e=t.getProgressive(),n=t.uid;r.set(n,{id:n,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:e&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(e||700),count:0}),Mc(i,t,t.dataTask)})},dc.prepareStageTasks=function(){var n=this._stageTaskMap,i=this.ecInstance.getModel(),r=this.api;D(this._allHandlers,function(t){var e=n.get(t.uid)||n.set(t.uid,[]);t.reset&&function(i,r,t,o,a){var s=t.seriesTaskMap||(t.seriesTaskMap=tt()),e=r.seriesType,n=r.getTargetSeries;r.createOnAllSeries?o.eachRawSeries(l):e?o.eachRawSeriesByType(e,l):n&&n(o,a).each(l);function l(t){var e=t.uid,n=s.get(e)||s.set(e,wu({plan:_c,reset:xc,count:Sc}));n.context={model:t,ecModel:o,api:a,useClearVisual:r.isVisual&&!r.isLayout,plan:r.plan,reset:r.reset,scheduler:i},Mc(i,t,n)}var h=i._pipelineMap;s.each(function(t,e){h.get(e)||(t.dispose(),s.removeKey(e))})}(this,t,e,i,r),t.overallReset&&function(i,t,e,n,r){var o=e.overallTask=e.overallTask||wu({reset:gc});o.context={ecModel:n,api:r,overallReset:t.overallReset,scheduler:i};var a=o.agentStubMap=o.agentStubMap||tt(),s=t.seriesType,l=t.getTargetSeries,h=!0,u=t.modifyOutputEnd;s?n.eachRawSeriesByType(s,c):l?l(n,r).each(c):(h=!1,D(n.getSeries(),c));function c(t){var e=t.uid,n=a.get(e);n||(n=a.set(e,wu({reset:mc,onDirty:yc})),o.dirty()),n.context={model:t,overallProgress:h,modifyOutputEnd:u},n.agent=o,n.__block=h,Mc(i,t,n)}var d=i._pipelineMap;a.each(function(t,e){d.get(e)||(t.dispose(),o.dirty(),a.removeKey(e))})}(this,t,e,i,r)},this)},dc.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,Mc(this,e,r)},dc.performDataProcessorTasks=function(t,e){fc(this,this._dataProcessorHandlers,t,e,{block:!0})},dc.performVisualTasks=function(t,e,n){fc(this,this._visualHandlers,t,e,n)},dc.performSeriesTasks=function(t){var e;t.eachSeries(function(t){e|=t.dataTask.perform()}),this.unfinished|=e},dc.plan=function(){this._pipelineMap.each(function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)})};var pc=dc.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)};function gc(t){t.overallReset(t.ecModel,t.api,t.payload)}function mc(t,e){return t.overallProgress&&vc}function vc(){this.agent.dirty(),this.getDownstream().dirty()}function yc(){this.agent&&this.agent.dirty()}function _c(t){return t.plan&&t.plan(t.model,t.ecModel,t.api,t.payload)}function xc(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=Cr(t.reset(t.model,t.ecModel,t.api,t.payload));return 1<e.length?P(e,function(t,e){return bc(e)}):wc}var wc=bc(0);function bc(o){return function(t,e){var n=e.data,i=e.resetDefines[o];if(i&&i.dataEach)for(var r=t.start;r<t.end;r++)i.dataEach(n,r);else i&&i.progress&&i.progress(t,n)}}function Sc(t){return t.data.count()}function Mc(t,e,n){var i=e.uid,r=t._pipelineMap.get(i);r.head||(r.head=n),r.tail&&r.tail.pipe(n),(r.tail=n).__idxInPipeline=r.count++,n.__pipeline=r}cc.wrapStageHandler=function(t,e){return N(t)&&(t={overallReset:t,seriesType:function(t){Ic=null;try{t(Cc,Tc)}catch(t){}return Ic}(t)}),t.uid=dl("stageHandler"),e&&(t.visualType=e),t};var Ic,Cc={},Tc={};function Ac(t,e){for(var n in e.prototype)t[n]=et}Ac(Cc,Ph),Ac(Tc,Nh),Cc.eachSeriesByType=Cc.eachRawSeriesByType=function(t){Ic=t},Cc.eachComponent=function(t){"series"===t.mainType&&t.subType&&(Ic=t.subType)};function Dc(){return{axisLine:{lineStyle:{color:Lc}},axisTick:{lineStyle:{color:Lc}},axisLabel:{textStyle:{color:Lc}},splitLine:{lineStyle:{type:"dashed",color:"#aaa"}},splitArea:{areaStyle:{color:Lc}}}}var kc=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],Pc={color:kc,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],kc]},Lc="#eee",Oc=["#dd6b66","#759aa0","#e69d87","#8dc1a9","#ea7e53","#eedd78","#73a373","#73b9bc","#7289ab","#91ca8c","#f49f42"],Ec={color:Oc,backgroundColor:"#333",tooltip:{axisPointer:{lineStyle:{color:Lc},crossStyle:{color:Lc},label:{color:"#000"}}},legend:{textStyle:{color:Lc}},textStyle:{color:Lc},title:{textStyle:{color:Lc}},toolbox:{iconStyle:{normal:{borderColor:Lc}}},dataZoom:{textStyle:{color:Lc}},visualMap:{textStyle:{color:Lc}},timeline:{lineStyle:{color:Lc},itemStyle:{normal:{color:Oc[1]}},label:{normal:{textStyle:{color:Lc}}},controlStyle:{normal:{color:Lc,borderColor:Lc}}},timeAxis:Dc(),logAxis:Dc(),valueAxis:Dc(),categoryAxis:Dc(),line:{symbol:"circle"},graph:{color:Oc},gauge:{title:{textStyle:{color:Lc}}},candlestick:{itemStyle:{normal:{color:"#FD1050",color0:"#0CF49B",borderColor:"#FD1050",borderColor0:"#0CF49B"}}}};Ec.categoryAxis.splitLine.show=!1,ah.extend({type:"dataset",defaultOption:{seriesLayoutBy:yh,sourceHeader:null,dimensions:null,source:null},optionUpdated:function(){!function(t){var e=t.option.source,n=mh;if(F(e))n=vh;else if(z(e)){0===e.length&&(n=fh);for(var i=0,r=e.length;i<r;i++){var o=e[i];if(null!=o){if(z(o)){n=fh;break}if(B(o)){n=ph;break}}}}else if(B(e)){for(var a in e)if(e.hasOwnProperty(a)&&E(e[a])){n=gh;break}}else if(null!=e)throw new Error("Invalid data");bh(t).sourceFormat=n}(this)}}),Gu.extend({type:"dataset"});ua.extend({type:"ellipse",shape:{cx:0,cy:0,rx:0,ry:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.rx,o=e.ry,a=.5522848*r,s=.5522848*o;t.moveTo(n-r,i),t.bezierCurveTo(n-r,i-s,n-a,i-o,n,i-o),t.bezierCurveTo(n+a,i-o,n+r,i-s,n+r,i),t.bezierCurveTo(n+r,i+s,n+a,i+o,n,i+o),t.bezierCurveTo(n-a,i+o,n-r,i+s,n-r,i),t.closePath()}});function zc(t){for(R(t)&&(t=(new DOMParser).parseFromString(t,"text/xml")),9===t.nodeType&&(t=t.firstChild);"svg"!==t.nodeName.toLowerCase()||1!==t.nodeType;)t=t.nextSibling;return t}var Nc=tt(),Rc=function(t,e,n){var i=z(e)?e:e.svg?[{type:"svg",source:e.svg,specialAreas:e.specialAreas}]:(e.geoJson&&!e.features&&(n=e.specialAreas,e=e.geoJson),[{type:"geoJSON",source:e,specialAreas:n}]);return D(i,function(t){var e=t.type;"geoJson"===e&&(e=t.type="geoJSON");var n=Vc[e];C&&j(n,"Illegal map type: "+e),n(t)}),Nc.set(t,i)},Bc=function(t){return Nc.get(t)},Vc={geoJSON:function(t){var e=t.source;t.geoJSON=R(e)?"undefined"!=typeof JSON&&JSON.parse?JSON.parse(e):new Function("return ("+e+");")():e},svg:function(t){t.svgXML=zc(t.source)}},Fc=j,Hc=D,Wc=N,Gc=B,Zc=ah.parseClassType,Uc={zrender:"4.3.2"},Xc={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:2e3,CHART:3e3,POST_CHART_LAYOUT:3500,COMPONENT:4e3,BRUSH:5e3}},Yc="__flagInMainProcess",jc="__optionUpdated",qc=/^[a-zA-Z0-9_]+$/;function $c(i,r){return function(t,e,n){r||!this._disposed?(t=t&&t.toLowerCase(),It.prototype[i].call(this,t,e,n)):pd(this.id)}}function Kc(){It.call(this)}function Qc(t,e,n){n=n||{},"string"==typeof e&&(e=Sd[e]),this.id,this.group,this._dom=t;var i="canvas";C&&(i=("undefined"==typeof window?VS:window).__ECHARTS__DEFAULT__RENDERER__||i);var r=this._zr=yr(t,{renderer:n.renderer||i,devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height});this._throttledZrFlush=rc(L(r.flush,r),17),(e=T(e))&&ou(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new Bh;var o,a,s,l,h=this._api=(a=(o=this)._coordSysMgr,k(new Nh(o),{getCoordinateSystems:L(a.getCoordinateSystems,a),getComponentByElement:function(t){for(;t;){var e=t.__ecComponentInfo;if(null!=e)return o._model.getComponent(e.mainType,e.index);t=t.parent}}}));function u(t,e){return t.__prio-e.__prio}Tn(bd,u),Tn(_d,u),this._scheduler=new cc(this,h,_d,bd),It.call(this,this._ecEventProcessor=new md),this._messageCenter=new Kc,this._initEvents(),this.resize=L(this.resize,this),this._pendingActions=[],r.animation.on("frame",this._onframe,this),l=this,(s=r).on("rendered",function(){l.trigger("rendered"),!s.animation.isFinished()||l[jc]||l._scheduler.unfinished||l._pendingActions.length||l.trigger("finished")}),K(this)}Kc.prototype.on=$c("on",!0),Kc.prototype.off=$c("off",!0),Kc.prototype.one=$c("one",!0),S(Kc,It);var Jc=Qc.prototype;function td(t,e,n){if(this._disposed)pd(this.id);else{var i,r=this._model,o=this._coordSysMgr.getCoordinateSystems();e=Rr(r,e);for(var a=0;a<o.length;a++){var s=o[a];if(s[t]&&null!=(i=s[t](r,e,n)))return i}}}Jc._onframe=function(){if(!this._disposed){var t=this._scheduler;if(this[jc]){var e=this[jc].silent;this[Yc]=!0,nd(this),ed.update.call(this),this[Yc]=!1,this[jc]=!1,ad.call(this,e),sd.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),rd(this,i),t.performVisualTasks(i),cd(this,this._model,0,"remain"),n-=new Date-o}while(0<n&&t.unfinished);t.unfinished||this._zr.flush()}}},Jc.getDom=function(){return this._dom},Jc.getZr=function(){return this._zr},Jc.setOption=function(t,e,n){var i,r,o,a;C&&Fc(!this[Yc],"`setOption` should not be called during main process."),this._disposed?pd(this.id):(Gc(e)&&(n=e.lazyUpdate,i=e.silent,e=e.notMerge),this[Yc]=!0,this._model&&!e||(r=new Zh(this._api),o=this._theme,(a=this._model=new Ph).scheduler=this._scheduler,a.init(null,null,o,r)),this._model.setOption(t,xd),n?(this[jc]={silent:i},this[Yc]=!1):(nd(this),ed.update.call(this),this._zr.flush(),this[jc]=!1,this[Yc]=!1,ad.call(this,i),sd.call(this,i)))},Jc.setTheme=function(){},Jc.getModel=function(){return this._model},Jc.getOption=function(){return this._model&&this._model.getOption()},Jc.getWidth=function(){return this._zr.getWidth()},Jc.getHeight=function(){return this._zr.getHeight()},Jc.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},Jc.getRenderedCanvas=function(t){if(v.canvasSupported)return(t=t||{}).pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor"),this._zr.painter.getRenderedCanvas(t)},Jc.getSvgDataURL=function(){if(v.svgSupported){var t=this._zr;return D(t.storage.getDisplayList(),function(t){t.stopAnimation(!0)}),t.painter.toDataURL()}},Jc.getDataURL=function(t){if(!this._disposed){var e=(t=t||{}).excludeComponents,n=this._model,i=[],r=this;Hc(e,function(t){n.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)})});var o="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return Hc(i,function(t){t.group.ignore=!1}),o}pd(this.id)},Jc.getConnectedDataURL=function(r){if(this._disposed)pd(this.id);else if(v.canvasSupported){var o="svg"===r.type,a=this.group,s=Math.min,l=Math.max;if(Cd[a]){var h=1/0,u=1/0,c=-1/0,d=-1/0,f=[],n=r&&r.pixelRatio||1;D(Id,function(t,e){var n,i;t.group===a&&(n=o?t.getZr().painter.getSvgDom().innerHTML:t.getRenderedCanvas(T(r)),i=t.getDom().getBoundingClientRect(),h=s(i.left,h),u=s(i.top,u),c=l(i.right,c),d=l(i.bottom,d),f.push({dom:n,left:i.left,top:i.top}))});var t=(c*=n)-(h*=n),e=(d*=n)-(u*=n),i=y(),p=yr(i,{renderer:o?"svg":"canvas"});if(p.resize({width:t,height:e}),o){var g="";return Hc(f,function(t){var e=t.left-h,n=t.top-u;g+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"}),p.painter.getSvgRoot().innerHTML=g,r.connectedBackgroundColor&&p.painter.setBackgroundColor(r.connectedBackgroundColor),p.refreshImmediately(),p.painter.toDataURL()}return r.connectedBackgroundColor&&p.add(new Ga({shape:{x:0,y:0,width:t,height:e},style:{fill:r.connectedBackgroundColor}})),Hc(f,function(t){var e=new Fi({style:{x:t.left*n-h,y:t.top*n-u,image:t.dom}});p.add(e)}),p.refreshImmediately(),i.toDataURL("image/"+(r&&r.type||"png"))}return this.getDataURL(r)}},Jc.convertToPixel=O(td,"convertToPixel"),Jc.convertFromPixel=O(td,"convertFromPixel"),Jc.containPixel=function(t,r){var o;if(!this._disposed)return D(t=Rr(this._model,t),function(t,i){0<=i.indexOf("Models")&&D(t,function(t){var e,n=t.coordinateSystem;n&&n.containPoint?o|=!!n.containPoint(r):"seriesModels"!==i||(e=this._chartsMap[t.__viewId])&&e.containPoint&&(o|=e.containPoint(r,t))},this)},this),!!o;pd(this.id)},Jc.getVisual=function(t,e){var n=(t=Rr(this._model,t,{defaultMainType:"series"})).seriesModel,i=n.getData(),r=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?i.indexOfRawIndex(t.dataIndex):null;return null!=r?i.getItemVisual(r,e):i.getVisual(e)},Jc.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},Jc.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var ed={prepareAndUpdate:function(t){nd(this),ed.update.call(this,t)},update:function(t){var e,n,i=this._model,r=this._api,o=this._zr,a=this._coordSysMgr,s=this._scheduler;i&&(s.restoreData(i,t),s.performSeriesTasks(i),a.create(i,r),s.performDataProcessorTasks(i,t),rd(this,i),a.update(i,r),hd(i),s.performVisualTasks(i,t),ud(this,i,r,t),n=i.get("backgroundColor")||"transparent",v.canvasSupported?o.setBackgroundColor(n):(n=We(e=ze(n),"rgb"),0===e[3]&&(n="transparent")),dd(i,r))},updateTransform:function(r){var o,i,a=this._model,s=this,l=this._api;a&&(o=[],a.eachComponent(function(t,e){var n,i=s.getViewOfComponentModel(e);i&&i.__alive&&(!i.updateTransform||(n=i.updateTransform(e,a,l,r))&&n.update)&&o.push(i)}),i=tt(),a.eachSeries(function(t){var e,n=s._chartsMap[t.__viewId];(!n.updateTransform||(e=n.updateTransform(t,a,l,r))&&e.update)&&i.set(t.uid,1)}),hd(a),this._scheduler.performVisualTasks(a,r,{setDirty:!0,dirtyMap:i}),cd(s,a,0,r,i),dd(a,this._api))},updateView:function(t){var e=this._model;e&&(ju.markUpdateMethod(t,"updateView"),hd(e),this._scheduler.performVisualTasks(e,t,{setDirty:!0}),ud(this,this._model,this._api,t),dd(e,this._api))},updateVisual:function(t){ed.update.call(this,t)},updateLayout:function(t){ed.update.call(this,t)}};function nd(t){var e=t._model,n=t._scheduler;n.restorePipelines(e),n.prepareStageTasks(),ld(t,"component",e,n),ld(t,"chart",e,n),n.plan()}function id(e,n,i,r,t){var o,a,s,l=e._model;function h(t){t&&t.__alive&&t[n]&&t[n](t.__model,l,e._api,i)}r?((o={})[r+"Id"]=i[r+"Id"],o[r+"Index"]=i[r+"Index"],o[r+"Name"]=i[r+"Name"],a={mainType:r,query:o},t&&(a.subType=t),null!=(s=i.excludeSeriesId)&&(s=tt(Cr(s))),l&&l.eachComponent(a,function(t){s&&null!=s.get(t.id)||h(e["series"===r?"_chartsMap":"_componentsMap"][t.__viewId])},e)):Hc(e._componentsViews.concat(e._chartsViews),h)}function rd(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries(function(t){i.updateStreamModes(t,n[t.__viewId])})}function od(e,t){var n=e.type,i=e.escapeConnect,r=vd[n],o=r.actionInfo,a=(s=(o.update||"update").split(":")).pop(),s=null!=s[0]&&Zc(s[0]);this[Yc]=!0;var l=[e],h=!1;e.batch&&(h=!0,l=P(e.batch,function(t){return(t=A(k({},t),e)).batch=null,t}));var u,c=[],d="highlight"===n||"downplay"===n;Hc(l,function(t){(u=(u=r.action(t,this._model,this._api))||k({},t)).type=o.event||u.type,c.push(u),d?id(this,a,t,"series"):s&&id(this,a,t,s.main,s.sub)},this),"none"===a||d||s||(this[jc]?(nd(this),ed.update.call(this,e),this[jc]=!1):ed[a].call(this,e)),u=h?{type:o.event||n,escapeConnect:i,batch:c}:c[0],this[Yc]=!1,t||this._messageCenter.trigger(u.type,u)}function ad(t){for(var e=this._pendingActions;e.length;){var n=e.shift();od.call(this,n,t)}}function sd(t){t||this.trigger("updated")}function ld(t,e,o,a){for(var s="component"===e,l=s?t._componentsViews:t._chartsViews,h=s?t._componentsMap:t._chartsMap,u=t._zr,c=t._api,n=0;n<l.length;n++)l[n].__alive=!1;function i(t){var e,n,i="_ec_"+t.id+"_"+t.type,r=h[i];r||(e=Zc(t.type),n=s?Gu.getClass(e.main,e.sub):ju.getClass(e.sub),C&&Fc(n,e.sub+" does not exist."),(r=new n).init(o,c),h[i]=r,l.push(r),u.add(r.group)),t.__viewId=r.__id=i,r.__alive=!0,r.__model=t,r.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},s||a.prepareView(r,t,o,c)}s?o.eachComponent(function(t,e){"series"!==t&&i(e)}):o.eachSeries(i);for(n=0;n<l.length;){var r=l[n];r.__alive?n++:(s||r.renderTask.dispose(),u.remove(r.group),r.dispose(o,c),l.splice(n,1),delete h[r.__id],r.__id=r.group.__ecComponentInfo=null)}}function hd(t){t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()})}function ud(t,e,n,i){var r,o,a,s,l;r=t,o=e,a=n,s=i,Hc(l||r._componentsViews,function(t){var e=t.__model;t.render(e,o,a,s),gd(e,t)}),Hc(t._chartsViews,function(t){t.__alive=!1}),cd(t,e,0,i),Hc(t._chartsViews,function(t){t.__alive||t.remove(e,n)})}function cd(i,t,e,r,o){var a,n,s,l,h,u=i._scheduler;t.eachSeries(function(t){var e=i._chartsMap[t.__viewId];e.__alive=!0;var n=e.renderTask;u.updatePayload(n,r),o&&o.get(t.uid)&&n.dirty(),a|=n.perform(u.getPerformArgs(n)),e.group.silent=!!t.get("silent"),gd(t,e),function(t,e){var n=t.get("blendMode")||null;C&&v.canvasSupported;e.group.traverse(function(t){t.isGroup||t.style.blend!==n&&t.setStyle("blend",n),t.eachPendingDisplayable&&t.eachPendingDisplayable(function(t){t.setStyle("blend",n)})})}(t,e)}),u.unfinished|=a,s=t,l=(n=i)._zr.storage,h=0,l.traverse(function(t){h++}),h>s.get("hoverLayerThreshold")&&!v.node&&s.eachSeries(function(t){var e;t.preventUsingHoverLayer||(e=n._chartsMap[t.__viewId]).__alive&&e.group.traverse(function(t){t.useHoverLayer=!0})}),hc(i._zr.dom,t)}function dd(e,n){Hc(wd,function(t){t(e,n)})}Jc.resize=function(t){var e,n,i;C&&Fc(!this[Yc],"`resize` should not be called during main process."),this._disposed?pd(this.id):(this._zr.resize(t),e=this._model,this._loadingFX&&this._loadingFX.resize(),e&&(n=e.resetOption("media"),i=t&&t.silent,this[Yc]=!0,n&&nd(this),ed.update.call(this),this[Yc]=!1,ad.call(this,i),sd.call(this,i)))},Jc.showLoading=function(t,e){var n,i;this._disposed?pd(this.id):(Gc(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),Md[t]&&(n=Md[t](this._api,e),i=this._zr,this._loadingFX=n,i.add(n)))},Jc.hideLoading=function(){this._disposed?pd(this.id):(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},Jc.makeActionFromEvent=function(t){var e=k({},t);return e.type=yd[t.type],e},Jc.dispatchAction=function(t,e){this._disposed?pd(this.id):(Gc(e)||(e={silent:!!e}),vd[t.type]&&this._model&&(this[Yc]?this._pendingActions.push(t):(od.call(this,t,e.silent),e.flush?this._zr.flush(!0):!1!==e.flush&&v.browser.weChat&&this._throttledZrFlush(),ad.call(this,e.silent),sd.call(this,e.silent))))},Jc.appendData=function(t){var e,n;this._disposed?pd(this.id):(e=t.seriesIndex,n=this.getModel().getSeriesByIndex(e),C&&Fc(t.data&&n),n.appendData(t),this._scheduler.unfinished=!0)},Jc.on=$c("on",!1),Jc.off=$c("off",!1),Jc.one=$c("one",!1);var fd=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];function pd(){}function gd(t,e){var n=t.get("z"),i=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=i&&(t.zlevel=i))})}function md(){this.eventInfo}Jc._initEvents=function(){Hc(fd,function(u){function t(t){var e,n,i,r,o,a,s=this.getModel(),l=t.target,h="globalout"===u;h?n={}:l&&null!=l.dataIndex?n=(e=l.dataModel||s.getSeriesByIndex(l.seriesIndex))&&e.getDataParams(l.dataIndex,l.dataType,l)||{}:l&&l.eventData&&(n=k({},l.eventData)),n&&(i=n.componentType,r=n.componentIndex,"markLine"!==i&&"markPoint"!==i&&"markArea"!==i||(i="series",r=n.seriesIndex),a=(o=i&&null!=r&&s.getComponent(i,r))&&this["series"===o.mainType?"_chartsMap":"_componentsMap"][o.__viewId],n.event=t,n.type=u,this._ecEventProcessor.eventInfo={targetEl:l,packedEvent:n,model:o,view:a},this.trigger(u,n))}t.zrEventfulCallAtLast=!0,this._zr.on(u,t,this)},this),Hc(yd,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},Jc.isDisposed=function(){return this._disposed},Jc.clear=function(){this._disposed?pd(this.id):this.setOption({series:[]},!0)},Jc.dispose=function(){var e,n;this._disposed?pd(this.id):(this._disposed=!0,Vr(this.getDom(),Dd,""),e=this._api,n=this._model,Hc(this._componentsViews,function(t){t.dispose(n,e)}),Hc(this._chartsViews,function(t){t.dispose(n,e)}),this._zr.dispose(),delete Id[this.id])},S(Qc,It),md.prototype={constructor:md,normalizeQuery:function(t){var e,s,l,h={},u={},c={};return R(t)?(e=Zc(t),h.mainType=e.main||null,h.subType=e.sub||null):(s=["Index","Name","Id"],l={name:1,dataIndex:1,dataType:1},D(t,function(t,e){for(var n=!1,i=0;i<s.length;i++){var r,o=s[i],a=e.lastIndexOf(o);0<a&&a===e.length-o.length&&("data"!==(r=e.slice(0,a))&&(h.mainType=r,h[o.toLowerCase()]=t,n=!0))}l.hasOwnProperty(e)&&(u[e]=t,n=!0),n||(c[e]=t)})),{cptQuery:h,dataQuery:u,otherQuery:c}},filter:function(t,e){var n=this.eventInfo;if(!n)return!0;var i=n.targetEl,r=n.packedEvent,o=n.model,a=n.view;if(!o||!a)return!0;var s=e.cptQuery,l=e.dataQuery;return h(s,o,"mainType")&&h(s,o,"subType")&&h(s,o,"index","componentIndex")&&h(s,o,"name")&&h(s,o,"id")&&h(l,r,"name")&&h(l,r,"dataIndex")&&h(l,r,"dataType")&&(!a.filterForExposedEvent||a.filterForExposedEvent(t,e.otherQuery,i,r));function h(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},afterTrigger:function(){this.eventInfo=null}};var vd={},yd={},_d=[],xd=[],wd=[],bd=[],Sd={},Md={},Id={},Cd={},Td=+new Date,Ad=+new Date,Dd="_echarts_instance_";function kd(t){Cd[t]=!1}var Pd=kd;function Ld(t){return Id[n=Dd,(e=t).getAttribute?e.getAttribute(n):e[n]];var e,n}function Od(t,e){Sd[t]=e}function Ed(t){xd.push(t)}function zd(t,e){Vd(_d,t,e,1e3)}function Nd(t,e,n){"function"==typeof e&&(n=e,e="");var i=Gc(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,Fc(qc.test(i)&&qc.test(e)),vd[i]||(vd[i]={action:n,actionInfo:t}),yd[e]=i}function Rd(t,e){Vd(bd,t,e,1e3,"layout")}function Bd(t,e){Vd(bd,t,e,3e3,"visual")}function Vd(t,e,n,i,r){if((Wc(e)||Gc(e))&&(n=e,e=i),C){if(isNaN(e)||null==e)throw new Error("Illegal priority");Hc(t,function(t){Fc(t.__raw!==n)})}var o=cc.wrapStageHandler(n,r);return o.__prio=e,o.__raw=n,t.push(o),o}function Fd(t,e){Md[t]=e}function Hd(t){return ah.extend(t)}function Wd(t){return Gu.extend(t)}function Gd(t){return zu.extend(t)}function Zd(t){return ju.extend(t)}Bd(2e3,sc),Ed(ou),zd(900,function(t){var o=tt();t.eachSeries(function(t){var e=t.get("stack");if(e){var n=o.get(e)||o.set(e,[]),i=t.getData(),r={stackResultDimension:i.getCalculationInfo("stackResultDimension"),stackedOverDimension:i.getCalculationInfo("stackedOverDimension"),stackedDimension:i.getCalculationInfo("stackedDimension"),stackedByDimension:i.getCalculationInfo("stackedByDimension"),isStackedByIndex:i.getCalculationInfo("isStackedByIndex"),data:i,seriesModel:t};if(!r.stackedDimension||!r.isStackedByIndex&&!r.stackedByDimension)return;n.length&&i.setCalculationInfo("stackedOnSeries",n[n.length-1].seriesModel),n.push(r)}}),o.each(au)}),Fd("default",function(r,o){A(o=o||{},{text:"loading",textColor:"#000",fontSize:"12px",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#c23531",spinnerRadius:10,lineWidth:5,zlevel:0});var t=new _n,a=new Ga({style:{fill:o.maskColor},zlevel:o.zlevel,z:1e4});t.add(a);var s,l=o.fontSize+" sans-serif",h=new Ga({style:{fill:"none",text:o.text,font:l,textPosition:"right",textDistance:10,textFill:o.textColor},zlevel:o.zlevel,z:10001});return t.add(h),o.showSpinner&&((s=new $a({shape:{startAngle:-uc/2,endAngle:-uc/2+.1,r:o.spinnerRadius},style:{stroke:o.color,lineCap:"round",lineWidth:o.lineWidth},zlevel:o.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*uc/2}).start("circularInOut"),s.animateShape(!0).when(1e3,{startAngle:3*uc/2}).delay(300).start("circularInOut"),t.add(s)),t.resize=function(){var t=ni(o.text,l),e=o.showSpinner?o.spinnerRadius:0,n=(r.getWidth()-2*e-(o.showSpinner&&t?10:0)-t)/2-(o.showSpinner?0:t/2),i=r.getHeight()/2;o.showSpinner&&s.setShape({cx:n,cy:i}),h.setShape({x:n-e,y:i-e,width:2*e,height:2*e}),a.setShape({x:0,y:0,width:r.getWidth(),height:r.getHeight()})},t.resize(),t}),Nd({type:"highlight",event:"highlight",update:"highlight"},et),Nd({type:"downplay",event:"downplay",update:"downplay"},et),Od("light",Pc),Od("dark",Ec);function Ud(t){return t}function Xd(t,e,n,i,r){this._old=t,this._new=e,this._oldKeyGetter=n||Ud,this._newKeyGetter=i||Ud,this.context=r}function Yd(t,e,n,i,r){for(var o=0;o<t.length;o++){var a="_ec_"+r[i](t[o],o),s=e[a];null==s?(n.push(a),e[a]=o):(s.length||(e[a]=s=[s]),s.push(o))}}Xd.prototype={constructor:Xd,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t=this._old,e=this._new,n={},i=[],r=[];for(Yd(t,{},i,"_oldKeyGetter",this),Yd(e,n,r,"_newKeyGetter",this),o=0;o<t.length;o++){null!=(a=n[s=i[o]])?((h=a.length)?(1===h&&(n[s]=null),a=a.shift()):n[s]=null,this._update&&this._update(a,o)):this._remove&&this._remove(o)}for(var o=0;o<r.length;o++){var a,s=r[o];if(n.hasOwnProperty(s)){if(null==(a=n[s]))continue;if(a.length)for(var l=0,h=a.length;l<h;l++)this._add&&this._add(a[l]);else this._add&&this._add(a)}}}};var jd=tt(["tooltip","label","itemName","itemId","seriesName"]);function qd(o){var t={},a=t.encode={},s=tt(),l=[],h=[],u=t.userOutput={dimensionNames:o.dimensions.slice(),encode:{}};D(o.dimensions,function(t){var e,n,r=o.getDimensionInfo(t),i=r.coordDim;i&&(C&&j(null==jd.get(i)),e=r.coordDimIndex,$d(a,i)[e]=t,r.isExtraCoord||(s.set(i,1),"ordinal"===(n=r.type)||"time"===n||(l[0]=t),$d(u.encode,i)[e]=r.index),r.defaultTooltip&&h.push(t)),jd.each(function(t,e){var n=$d(a,e),i=r.otherDims[e];null!=i&&!1!==i&&(n[i]=r.name)})});var i=[],r={};s.each(function(t,e){var n=a[e];r[e]=n[0],i=i.concat(n)}),t.dataDimsOnCoord=i,t.encodeFirstDimNotExtra=r;var e=a.label;e&&e.length&&(l=e.slice());var n=a.tooltip;return n&&n.length?h=n.slice():h.length||(h=l.slice()),a.defaultedLabel=l,a.defaultedTooltip=h,t}function $d(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}function Kd(t){null!=t&&k(this,t),this.otherDims={}}var Qd=B,Jd="undefined",tf={float:("undefined"==typeof Float64Array?"undefined":FS(Float64Array))===Jd?Array:Float64Array,int:("undefined"==typeof Int32Array?"undefined":FS(Int32Array))===Jd?Array:Int32Array,ordinal:Array,number:Array,time:Array},ef=("undefined"==typeof Uint32Array?"undefined":FS(Uint32Array))===Jd?Array:Uint32Array,nf=("undefined"==typeof Int32Array?"undefined":FS(Int32Array))===Jd?Array:Int32Array,rf=("undefined"==typeof Uint16Array?"undefined":FS(Uint16Array))===Jd?Array:Uint16Array;function of(t){return 65535<t._rawCount?ef:rf}var af=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_rawData","_chunkSize","_chunkCount","_dimValueGetter","_count","_rawCount","_nameDimIdx","_idDimIdx"],sf=["_extent","_approximateExtent","_rawExtent"];function lf(e,n){D(af.concat(n.__wrappedMethods||[]),function(t){n.hasOwnProperty(t)&&(e[t]=n[t])}),e.__wrappedMethods=n.__wrappedMethods,D(sf,function(t){e[t]=T(n[t])}),e._calculationInfo=k(n._calculationInfo)}var hf=function(t,e){t=t||["x","y"];for(var n={},i=[],r={},o=0;o<t.length;o++){var a=t[o];R(a)?a=new Kd({name:a}):a instanceof Kd||(a=new Kd(a));var s=a.name;a.type=a.type||"float",a.coordDim||(a.coordDim=s,a.coordDimIndex=0),a.otherDims=a.otherDims||{},i.push(s),(n[s]=a).index=o,a.createInvertedIndices&&(r[s]=[])}this.dimensions=i,this._dimensionInfos=n,this.hostModel=e,this.dataType,this._indices=null,this._count=0,this._rawCount=0,this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this._visual={},this._layout={},this._itemVisuals=[],this.hasItemVisual={},this._itemLayouts=[],this._graphicEls=[],this._chunkSize=1e5,this._chunkCount=0,this._rawData,this._rawExtent={},this._extent={},this._approximateExtent={},this._dimensionsSummary=qd(this),this._invertedIndicesMap=r,this._calculationInfo={},this.userOutput=this._dimensionsSummary.userOutput},uf=hf.prototype;function cf(t,e,n,i,r){var o=tf[e.type],a=i-1,s=e.name,l=t[s][a];if(l&&l.length<n){for(var h=new o(Math.min(r-a*n,n)),u=0;u<l.length;u++)h[u]=l[u];t[s][a]=h}for(var c=i*n;c<r;c+=n)t[s].push(new o(Math.min(r-c,n)))}function df(r){var o=r._invertedIndicesMap;D(o,function(t,e){var n=r._dimensionInfos[e].ordinalMeta;if(n){t=o[e]=new nf(n.categories.length);for(var i=0;i<t.length;i++)t[i]=-1;for(i=0;i<r._count;i++)t[r.get(e,i)]=i}})}function ff(t,e,n){var i,r,o,a,s,l,h;return null!=e&&(r=t._chunkSize,o=Math.floor(n/r),a=n%r,s=t.dimensions[e],(l=t._storage[s][o])&&(i=l[a],(h=t._dimensionInfos[s].ordinalMeta)&&h.categories.length&&(i=h.categories[i]))),i}function pf(t){return t}function gf(t){return t<this._count&&0<=t?this._indices[t]:-1}function mf(t,e){var n=t._idList[e];return null==n&&(n=ff(t,t._idDimIdx,e)),null==n&&(n="e\0\0"+e),n}function vf(t){return z(t)||(t=[t]),t}function yf(t,e){for(var n=0;n<e.length;n++)t._dimensionInfos[e[n]]}function _f(t,e){var n=t.dimensions,i=new hf(P(n,t.getDimensionInfo,t),t.hostModel);lf(i,t);for(var r=i._storage={},o=t._storage,a=0;a<n.length;a++){var s=n[a];o[s]&&(0<=w(e,s)?(r[s]=function(t){for(var e=new Array(t.length),n=0;n<t.length;n++)e[n]=function(t){var e=t.constructor;return e===Array?t.slice():new e(t)}(t[n]);return e}(o[s]),i._rawExtent[s]=xf(),i._extent[s]=null):r[s]=o[s])}return i}function xf(){return[1/0,-1/0]}uf.type="list",uf.hasItemOption=!0,uf.getDimension=function(t){return"number"!=typeof t&&(isNaN(t)||this._dimensionInfos.hasOwnProperty(t))||(t=this.dimensions[t]),t},uf.getDimensionInfo=function(t){return this._dimensionInfos[this.getDimension(t)]},uf.getDimensionsOnCoord=function(){return this._dimensionsSummary.dataDimsOnCoord.slice()},uf.mapDimension=function(t,e){var n=this._dimensionsSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return!0===e?(i||[]).slice():i&&i[e]},uf.initData=function(t,e,n){var i=xh.isInstance(t)||E(t);if(i&&(t=new su(t,this.dimensions.length)),C&&!i&&("function"!=typeof t.getItem||"function"!=typeof t.count))throw new Error("Inavlid data provider.");this._rawData=t,this._storage={},this._indices=null,this._nameList=e||[],this._idList=[],this._nameRepeatCount={},n||(this.hasItemOption=!1),this.defaultDimValueGetter=gu[this._rawData.getSource().sourceFormat],this._dimValueGetter=n=n||this.defaultDimValueGetter,this._dimValueGetterArrayRows=gu.arrayRows,this._rawExtent={},this._initDataFromProvider(0,t.count()),t.pure&&(this.hasItemOption=!1)},uf.getProvider=function(){return this._rawData},uf.appendData=function(t){C&&j(!this._indices,"appendData can only be called on raw data.");var e=this._rawData,n=this.count();e.appendData(t);var i=e.count();e.persistent||(i+=n),this._initDataFromProvider(n,i)},uf.appendValues=function(t,e){for(var n=this._chunkSize,i=this._storage,r=this.dimensions,o=r.length,a=this._rawExtent,s=this.count(),l=s+Math.max(t.length,e?e.length:0),h=this._chunkCount,u=0;u<o;u++){a[v=r[u]]||(a[v]=xf()),i[v]||(i[v]=[]),cf(i,this._dimensionInfos[v],n,h,l),this._chunkCount=i[v].length}for(var c=new Array(o),d=s;d<l;d++){for(var f=d-s,p=Math.floor(d/n),g=d%n,m=0;m<o;m++){var v=r[m],y=this._dimValueGetterArrayRows(t[f]||c,v,f,m);i[v][p][g]=y;var _=a[v];y<_[0]&&(_[0]=y),y>_[1]&&(_[1]=y)}e&&(this._nameList[d]=e[f])}this._rawCount=this._count=l,this._extent={},df(this)},uf._initDataFromProvider=function(t,e){if(!(e<=t)){for(var n,i=this._chunkSize,r=this._rawData,o=this._storage,a=this.dimensions,s=a.length,l=this._dimensionInfos,h=this._nameList,u=this._idList,c=this._rawExtent,d=this._nameRepeatCount={},f=this._chunkCount,p=0;p<s;p++){c[C=a[p]]||(c[C]=xf());var g=l[C];0===g.otherDims.itemName&&(n=this._nameDimIdx=p),0===g.otherDims.itemId&&(this._idDimIdx=p),o[C]||(o[C]=[]),cf(o,g,i,f,e),this._chunkCount=o[C].length}for(var m=new Array(s),v=t;v<e;v++){m=r.getItem(v,m);for(var y,_,x,w,b,S=Math.floor(v/i),M=v%i,I=0;I<s;I++){var C,T=o[C=a[I]][S],A=this._dimValueGetter(m,C,v,I);T[M]=A;var D=c[C];A<D[0]&&(D[0]=A),A>D[1]&&(D[1]=A)}r.pure||(y=h[v],m&&null==y&&(null!=m.name?h[v]=y=m.name:null==n||(x=o[_=a[n]][S])&&(y=x[M],(w=l[_].ordinalMeta)&&w.categories.length&&(y=w.categories[y]))),null==(b=null==m?null:m.id)&&null!=y&&(d[y]=d[y]||0,0<d[b=y]&&(b+="__ec__"+d[y]),d[y]++),null!=b&&(u[v]=b))}!r.persistent&&r.clean&&r.clean(),this._rawCount=this._count=e,this._extent={},df(this)}},uf.count=function(){return this._count},uf.getIndices=function(){var t=this._indices;if(t){var e=t.constructor,n=this._count;if(e===Array){r=new e(n);for(var i=0;i<n;i++)r[i]=t[i]}else r=new e(t.buffer,0,n)}else for(var r=new(e=of(this))(this.count()),i=0;i<r.length;i++)r[i]=i;return r},uf.get=function(t,e){if(!(0<=e&&e<this._count))return NaN;var n=this._storage;if(!n[t])return NaN;e=this.getRawIndex(e);var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize;return n[t][i][r]},uf.getByRawIndex=function(t,e){if(!(0<=e&&e<this._rawCount))return NaN;var n=this._storage[t];if(!n)return NaN;var i=Math.floor(e/this._chunkSize),r=e%this._chunkSize;return n[i][r]},uf._getFast=function(t,e){var n=Math.floor(e/this._chunkSize),i=e%this._chunkSize;return this._storage[t][n][i]},uf.getValues=function(t,e){var n=[];z(t)||(e=t,t=this.dimensions);for(var i=0,r=t.length;i<r;i++)n.push(this.get(t[i],e));return n},uf.hasValue=function(t){for(var e=this._dimensionsSummary.dataDimsOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this.get(e[n],t)))return!1;return!0},uf.getDataExtent=function(t){t=this.getDimension(t);var e=this._storage[t],n=xf();if(!e)return n;var i,r=this.count();if(!this._indices)return this._rawExtent[t].slice();if(i=this._extent[t])return i.slice();for(var o=(i=n)[0],a=i[1],s=0;s<r;s++){var l=this._getFast(t,this.getRawIndex(s));l<o&&(o=l),a<l&&(a=l)}return i=[o,a],this._extent[t]=i},uf.getApproximateExtent=function(t){return t=this.getDimension(t),this._approximateExtent[t]||this.getDataExtent(t)},uf.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},uf.getCalculationInfo=function(t){return this._calculationInfo[t]},uf.setCalculationInfo=function(t,e){Qd(t)?k(this._calculationInfo,t):this._calculationInfo[t]=e},uf.getSum=function(t){var e=0;if(this._storage[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},uf.getMedian=function(t){var n=[];this.each(t,function(t,e){isNaN(t)||n.push(t)});var e=[].concat(n).sort(function(t,e){return t-e}),i=this.count();return 0===i?0:i%2==1?e[(i-1)/2]:(e[i/2]+e[i/2-1])/2},uf.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t];if(C&&!n)throw new Error("Do not supported yet");var i=n[e];return null==i||isNaN(i)?-1:i},uf.indexOfName=function(t){for(var e=0,n=this.count();e<n;e++)if(this.getName(e)===t)return e;return-1},uf.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=1+o;else{if(!(e[o]>t))return o;r=o-1}}return-1},uf.indicesOfNearest=function(t,e,n){var i=[];if(!this._storage[t])return i;null==n&&(n=1/0);for(var r=1/0,o=-1,a=0,s=0,l=this.count();s<l;s++){var h=e-this.get(t,s),u=Math.abs(h);u<=n&&((u<r||u===r&&0<=h&&o<0)&&(r=u,o=h,a=0),h===o&&(i[a++]=s))}return i.length=a,i},uf.getRawIndex=pf,uf.getRawDataItem=function(t){if(this._rawData.persistent)return this._rawData.getItem(this.getRawIndex(t));for(var e=[],n=0;n<this.dimensions.length;n++){var i=this.dimensions[n];e.push(this.get(i,t))}return e},uf.getName=function(t){var e=this.getRawIndex(t);return this._nameList[e]||ff(this,this._nameDimIdx,e)||""},uf.getId=function(t){return mf(this,this.getRawIndex(t))},uf.each=function(t,e,n,i){if(this._count){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=P(vf(t),this.getDimension,this),C&&yf(this,t);for(var r=t.length,o=0;o<this.count();o++)switch(r){case 0:e.call(n,o);break;case 1:e.call(n,this.get(t[0],o),o);break;case 2:e.call(n,this.get(t[0],o),this.get(t[1],o),o);break;default:for(var a=0,s=[];a<r;a++)s[a]=this.get(t[a],o);s[a]=o,e.apply(n,s)}}},uf.filterSelf=function(t,e,n,i){if(this._count){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this,t=P(vf(t),this.getDimension,this),C&&yf(this,t);for(var r=this.count(),o=new(of(this))(r),a=[],s=t.length,l=0,h=t[0],u=0;u<r;u++){var c=this.getRawIndex(u);if(0===s)f=e.call(n,u);else if(1===s)var d=this._getFast(h,c),f=e.call(n,d,u);else{for(var p=0;p<s;p++)a[p]=this._getFast(h,c);a[p]=u,f=e.apply(n,a)}f&&(o[l++]=c)}return l<r&&(this._indices=o),this._count=l,this._extent={},this.getRawIndex=this._indices?gf:pf,this}},uf.selectRange=function(t){if(this._count){var e,n=[];for(e in t)t.hasOwnProperty(e)&&n.push(e);C&&yf(this,n);var i=n.length;if(i){var r=this.count(),o=new(of(this))(r),a=0,s=n[0],l=t[s][0],h=t[s][1],u=!1;if(!this._indices){var c=0;if(1===i){for(var d=this._storage[n[0]],f=0;f<this._chunkCount;f++)for(var p=d[f],g=Math.min(this._count-f*this._chunkSize,this._chunkSize),m=0;m<g;m++){(l<=(w=p[m])&&w<=h||isNaN(w))&&(o[a++]=c),c++}u=!0}else if(2===i){for(var d=this._storage[s],v=this._storage[n[1]],y=t[n[1]][0],_=t[n[1]][1],f=0;f<this._chunkCount;f++)for(var p=d[f],x=v[f],g=Math.min(this._count-f*this._chunkSize,this._chunkSize),m=0;m<g;m++){var w=p[m],b=x[m];(l<=w&&w<=h||isNaN(w))&&(y<=b&&b<=_||isNaN(b))&&(o[a++]=c),c++}u=!0}}if(!u)if(1===i)for(m=0;m<r;m++){var S=this.getRawIndex(m);(l<=(w=this._getFast(s,S))&&w<=h||isNaN(w))&&(o[a++]=S)}else for(m=0;m<r;m++){for(var M=!0,S=this.getRawIndex(m),f=0;f<i;f++){var I=n[f];((w=this._getFast(e,S))<t[I][0]||w>t[I][1])&&(M=!1)}M&&(o[a++]=this.getRawIndex(m))}return a<r&&(this._indices=o),this._count=a,this._extent={},this.getRawIndex=this._indices?gf:pf,this}}},uf.mapArray=function(t,e,n,i){"function"==typeof t&&(i=n,n=e,e=t,t=[]),n=n||i||this;var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},n),r},uf.map=function(t,e,n,i){n=n||i||this,t=P(vf(t),this.getDimension,this),C&&yf(this,t);var r=_f(this,t);r._indices=this._indices,r.getRawIndex=r._indices?gf:pf;for(var o=r._storage,a=[],s=this._chunkSize,l=t.length,h=this.count(),u=[],c=r._rawExtent,d=0;d<h;d++){for(var f=0;f<l;f++)u[f]=this.get(t[f],d);u[l]=d;var p=e&&e.apply(n,u);if(null!=p){"object"!==FS(p)&&(a[0]=p,p=a);for(var g=this.getRawIndex(d),m=Math.floor(g/s),v=g%s,y=0;y<p.length;y++){var _=t[y],x=p[y],w=c[_],b=o[_];b&&(b[m][v]=x),x<w[0]&&(w[0]=x),x>w[1]&&(w[1]=x)}}}return r},uf.downSample=function(t,e,n,i){for(var r=_f(this,[t]),o=r._storage,a=[],s=Math.floor(1/e),l=o[t],h=this.count(),u=this._chunkSize,c=r._rawExtent[t],d=new(of(this))(h),f=0,p=0;p<h;p+=s){h-p<s&&(s=h-p,a.length=s);for(var g=0;g<s;g++){var m=this.getRawIndex(p+g),v=Math.floor(m/u),y=m%u;a[g]=l[v][y]}var _=n(a),x=this.getRawIndex(Math.min(p+i(a,_)||0,h-1)),w=x%u;(l[Math.floor(x/u)][w]=_)<c[0]&&(c[0]=_),_>c[1]&&(c[1]=_),d[f++]=x}return r._count=f,r._indices=d,r.getRawIndex=gf,r},uf.getItemModel=function(t){var e=this.hostModel;return new ll(this.getRawDataItem(t),e,e&&e.ecModel)},uf.diff=function(e){var n=this;return new Xd(e?e.getIndices():[],this.getIndices(),function(t){return mf(e,t)},function(t){return mf(n,t)})},uf.getVisual=function(t){var e=this._visual;return e&&e[t]},uf.setVisual=function(t,e){if(Qd(t))for(var n in t)t.hasOwnProperty(n)&&this.setVisual(n,t[n]);else this._visual=this._visual||{},this._visual[t]=e},uf.setLayout=function(t,e){if(Qd(t))for(var n in t)t.hasOwnProperty(n)&&this.setLayout(n,t[n]);else this._layout[t]=e},uf.getLayout=function(t){return this._layout[t]},uf.getItemLayout=function(t){return this._itemLayouts[t]},uf.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?k(this._itemLayouts[t]||{},e):e},uf.clearItemLayouts=function(){this._itemLayouts.length=0},uf.getItemVisual=function(t,e,n){var i=this._itemVisuals[t],r=i&&i[e];return null!=r||n?r:this.getVisual(e)},uf.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{},r=this.hasItemVisual;if(this._itemVisuals[t]=i,Qd(e))for(var o in e)e.hasOwnProperty(o)&&(i[o]=e[o],r[o]=!0);else i[e]=n,r[e]=!0},uf.clearAllVisual=function(){this._visual={},this._itemVisuals=[],this.hasItemVisual={}};function wf(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType}function bf(t,e,n){xh.isInstance(e)||(e=xh.seriesDataToSource(e)),n=n||{},t=(t||[]).slice();for(var i,r,o,a,s,l=(n.dimsDef||[]).slice(),h=tt(),u=tt(),c=[],d=(i=e,r=t,o=l,a=n.dimCount,s=Math.max(i.dimensionsDetectCount||1,r.length,o.length,a||0),D(r,function(t){var e=t.dimsDef;e&&(s=Math.max(s,e.length))}),s),f=0;f<d;f++){var p=l[f]=k({},B(l[f])?l[f]:{name:l[f]}),g=p.name,m=c[f]=new Kd;null!=g&&null==h.get(g)&&(m.name=m.displayName=g,h.set(g,f)),null!=p.type&&(m.type=p.type),null!=p.displayName&&(m.displayName=p.displayName)}var v=n.encodeDef;!v&&n.encodeDefaulter&&(v=n.encodeDefaulter(e,d)),(v=tt(v)).each(function(t,n){var i;1===(t=Cr(t).slice()).length&&!R(t[0])&&t[0]<0?v.set(n,!1):(i=v.set(n,[]),D(t,function(t,e){R(t)&&(t=h.get(t)),null!=t&&t<d&&(i[e]=t,_(c[t],n,e))}))});var y=0;function _(t,e,n){null!=jd.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,u.set(e,!0))}D(t,function(r,t){var o,a,s,e;R(r)?(o=r,r={}):(o=r.name,e=r.ordinalMeta,r.ordinalMeta=null,(r=T(r)).ordinalMeta=e,a=r.dimsDef,s=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null);var n=v.get(o);if(!1!==n){if(!(n=Cr(n)).length)for(var i=0;i<(a&&a.length||1);i++){for(;y<c.length&&null!=c[y].coordDim;)y++;y<c.length&&n.push(y++)}D(n,function(t,e){var n,i=c[t];_(A(i,r),o,e),null==i.name&&a&&(B(n=a[e])||(n={name:n}),i.name=i.displayName=n.name,i.defaultTooltip=n.defaultTooltip),s&&A(i.otherDims,s)})}});for(var x,w,b=n.generateCoord,S=null!=(M=n.generateCoordCount),M=b?M||1:0,I=b||"value",C=0;C<d;C++){null==(m=c[C]=c[C]||new Kd).coordDim&&(m.coordDim=Sf(I,u,S),m.coordDimIndex=0,(!b||M<=0)&&(m.isExtraCoord=!0),M--),null==m.name&&(m.name=Sf(m.coordDim,h)),null==m.type&&(x=e,w=C,m.name,Dh(x.data,x.sourceFormat,x.seriesLayoutBy,x.dimensionsDefine,x.startIndex,w)===wh.Must||m.isExtraCoord&&(null!=m.otherDims.itemName||null!=m.otherDims.seriesName))&&(m.type="ordinal")}return c}function Sf(t,e,n){if(n||null!=e.get(t)){for(var i=0;null!=e.get(t+i);)i++;t+=i}return e.set(t,!0),t}uf.setItemGraphicEl=function(t,e){var n=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=n&&n.seriesIndex,"group"===e.type&&e.traverse(wf,e)),this._graphicEls[t]=e},uf.getItemGraphicEl=function(t){return this._graphicEls[t]},uf.eachItemGraphicEl=function(n,i){D(this._graphicEls,function(t,e){t&&n&&n.call(i,t,e)})},uf.cloneShallow=function(t){var e,n;return t||(e=P(this.dimensions,this.getDimensionInfo,this),t=new hf(e,this.hostModel)),t._storage=this._storage,lf(t,this),this._indices?(n=this._indices.constructor,t._indices=new n(this._indices)):t._indices=null,t.getRawIndex=t._indices?gf:pf,t},uf.wrapMethod=function(t,e){var n=this[t];"function"==typeof n&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(X(arguments)))})},uf.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],uf.CHANGABLE_METHODS=["filterSelf","selectRange"];var Mf=function(t,e){return bf((e=e||{}).coordDimensions||[],t,{dimsDef:e.dimensionsDefine||t.dimensionsDefine,encodeDef:e.encodeDefine||t.encodeDefine,dimCount:e.dimensionsCount,encodeDefaulter:e.encodeDefaulter,generateCoord:e.generateCoord,generateCoordCount:e.generateCoordCount})};function If(t){this.coordSysName=t,this.coordSysDims=[],this.axisMap=tt(),this.categoryAxisMap=tt(),this.firstCategoryDimIndex=null}var Cf={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis")[0],o=t.getReferringComponents("yAxis")[0];if(C){if(!r)throw new Error('xAxis "'+G(t.get("xAxisIndex"),t.get("xAxisId"),0)+'" not found');if(!o)throw new Error('yAxis "'+G(t.get("xAxisIndex"),t.get("yAxisId"),0)+'" not found')}e.coordSysDims=["x","y"],n.set("x",r),n.set("y",o),Tf(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),Tf(o)&&(i.set("y",o),e.firstCategoryDimIndex,e.firstCategoryDimIndex=1)},singleAxis:function(t,e,n,i){var r=t.getReferringComponents("singleAxis")[0];if(C&&!r)throw new Error("singleAxis should be specified.");e.coordSysDims=["single"],n.set("single",r),Tf(r)&&(i.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var r=t.getReferringComponents("polar")[0],o=r.findAxisModel("radiusAxis"),a=r.findAxisModel("angleAxis");if(C){if(!a)throw new Error("angleAxis option not found");if(!o)throw new Error("radiusAxis option not found")}e.coordSysDims=["radius","angle"],n.set("radius",o),n.set("angle",a),Tf(o)&&(i.set("radius",o),e.firstCategoryDimIndex=0),Tf(a)&&(i.set("angle",a),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e){e.coordSysDims=["lng","lat"]},parallel:function(t,r,o,a){var s=t.ecModel,e=s.getComponent("parallel",t.get("parallelIndex")),l=r.coordSysDims=e.dimensions.slice();D(e.parallelAxisIndex,function(t,e){var n=s.getComponent("parallelAxis",t),i=l[e];o.set(i,n),Tf(n)&&null==r.firstCategoryDimIndex&&(a.set(i,n),r.firstCategoryDimIndex=e)})}};function Tf(t){return"category"===t.get("type")}function Af(t,n,e){var i,r,o,a,s,l,h,u=(e=e||{}).byIndex,c=e.stackedCoordDimension,d=!(!t||!t.get("stack"));return D(n,function(t,e){R(t)&&(n[e]=t={name:t}),d&&!t.isExtraCoord&&(u||i||!t.ordinalMeta||(i=t),r||"ordinal"===t.type||"time"===t.type||c&&c!==t.coordDim||(r=t))}),!r||u||i||(u=!0),r&&(o="__\0ecstackresult",a="__\0ecstackedover",i&&(i.createInvertedIndices=!0),s=r.coordDim,l=r.type,h=0,D(n,function(t){t.coordDim===s&&h++}),n.push({name:o,coordDim:s,coordDimIndex:h,type:l,isExtraCoord:!0,isCalculationCoord:!0}),h++,n.push({name:a,coordDim:a,coordDimIndex:h,type:l,isExtraCoord:!0,isCalculationCoord:!0})),{stackedDimension:r&&r.name,stackedByDimension:i&&i.name,isStackedByIndex:u,stackedOverDimension:a,stackResultDimension:o}}function Df(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function kf(t,e){return Df(t,e)?t.getCalculationInfo("stackResultDimension"):e}function Pf(t,e,n){n=n||{},xh.isInstance(t)||(t=xh.seriesDataToSource(t));var i,r=e.get("coordinateSystem"),o=Bh.get(r),a=function(t){var e=t.get("coordinateSystem"),n=new If(e),i=Cf[e];if(i)return i(t,n,n.axisMap,n.categoryAxisMap),n}(e);a&&(i=P(a.coordSysDims,function(t){var e,n,i={name:t},r=a.axisMap.get(t);return r&&(e=r.get("type"),i.type="category"===(n=e)?"ordinal":"time"===n?"time":"float"),i})),i=i||(o&&(o.getDimensionsInfo?o.getDimensionsInfo():o.dimensions.slice())||["x","y"]);var s,l,h=Mf(t,{coordDimensions:i,generateCoord:n.generateCoord,encodeDefaulter:n.useEncodeDefaulter?O(Ch,i,e):null});a&&D(h,function(t,e){var n=t.coordDim,i=a.categoryAxisMap.get(n);i&&(null==s&&(s=e),t.ordinalMeta=i.getOrdinalMeta()),null!=t.otherDims.itemName&&(l=!0)}),l||null==s||(h[s].otherDims.itemName=0);var u=Af(e,h),c=new hf(h,e);c.setCalculationInfo(u);var d=null!=s&&function(t){if(t.sourceFormat===dh){var e=function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(t.data||[]);return null!=e&&!z(Dr(e))}}(t)?function(t,e,n,i){return i===s?n:this.defaultDimValueGetter(t,e,n,i)}:null;return c.hasItemOption=!1,c.initData(t,null,d),c}function Lf(t){this._setting=t||{},this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}function Of(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this._map}Lf.prototype.parse=function(t){return t},Lf.prototype.getSetting=function(t){return this._setting[t]},Lf.prototype.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},Lf.prototype.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},Lf.prototype.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},Lf.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},Lf.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},Lf.prototype.getExtent=function(){return this._extent.slice()},Lf.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},Lf.prototype.isBlank=function(){return this._isBlank},Lf.prototype.setBlank=function(t){this._isBlank=t},Lf.prototype.getLabel=null,Zr(Lf),qr(Lf,{registerWhenExtend:!0}),Of.createByAxisModel=function(t){var e=t.option,n=e.data,i=n&&P(n,Nf);return new Of({categories:i,needCollect:!i,deduplication:!1!==e.dedplication})};var Ef=Of.prototype;function zf(t){return t._map||(t._map=tt(t.categories))}function Nf(t){return B(t)&&null!=t.value?t.value:t+""}Ef.getOrdinal=function(t){return zf(this).get(t)},Ef.parseAndCollect=function(t){var e=this._needCollect;if("string"!=typeof t&&!e)return t;if(e&&!this._deduplication)return n=this.categories.length,this.categories[n]=t,n;var n,i=zf(this);return null==(n=i.get(t))&&(e?(n=this.categories.length,this.categories[n]=t,i.set(t,n)):n=NaN),n};var Rf=Lf.prototype,Bf=Lf.extend({type:"ordinal",init:function(t,e){t&&!z(t)||(t=new Of({categories:t})),this._ordinalMeta=t,this._extent=e||[0,t.categories.length-1]},parse:function(t){return"string"==typeof t?this._ordinalMeta.getOrdinal(t):Math.round(t)},contain:function(t){return t=this.parse(t),Rf.contain.call(this,t)&&null!=this._ordinalMeta.categories[t]},normalize:function(t){return Rf.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(Rf.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push(n),n++;return t},getLabel:function(t){if(!this.isBlank())return this._ordinalMeta.categories[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},getOrdinalMeta:function(){return this._ordinalMeta},niceTicks:et,niceExtent:et});Bf.create=function(){return new Bf};var Vf=ml;function Ff(t){return _l(t)+2}function Hf(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function Wf(t,e){isFinite(t[0])||(t[0]=e[0]),isFinite(t[1])||(t[1]=e[1]),Hf(t,0,e),Hf(t,1,e),t[0]>t[1]&&(t[0]=t[1])}var Gf=ml,Zf=Lf.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),Zf.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=Ff(t)},getTicks:function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(!e)return o;n[0]<i[0]&&(t?o.push(Gf(i[0]-e,r)):o.push(n[0]));for(var a=i[0];a<=i[1]&&(o.push(a),(a=Gf(a+e,r))!==o[o.length-1]);)if(1e4<o.length)return[];var s=o.length?o[o.length-1]:i[1];return n[1]>s&&(t?o.push(Gf(s+e,r)):o.push(n[1])),o},getMinorTicks:function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],h=(o-a)/t;s<t-1;){var u=ml(a+(s+1)*h);u>i[0]&&u<i[1]&&l.push(u),s++}n.push(l)}return n},getLabel:function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=_l(t)||0:"auto"===n&&(n=this._intervalPrecision),kl(t=Gf(t,n,!0))},niceTicks:function(t,e,n){t=t||5;var i,r=this._extent,o=r[1]-r[0];isFinite(o)&&(o<0&&r.reverse(),i=function(t,e,n,i){var r={},o=t[1]-t[0],a=r.interval=Al(o/e,!0);null!=n&&a<n&&(a=r.interval=n),null!=i&&i<a&&(a=r.interval=i);var s=r.intervalPrecision=Ff(a);return Wf(r.niceTickExtent=[Vf(Math.ceil(t[0]/a)*a,s),Vf(Math.floor(t[1]/a)*a,s)],t),r}(r,t,e,n),this._intervalPrecision=i.intervalPrecision,this._interval=i.interval,this._niceExtent=i.niceTickExtent)},niceExtent:function(t){var e,n=this._extent;n[0]===n[1]&&(0!==n[0]?(e=n[0],t.fixMax||(n[1]+=e/2),n[0]-=e/2):n[1]=1);var i=n[1]-n[0];isFinite(i)||(n[0]=0,n[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(n[0]=Gf(Math.floor(n[0]/r)*r)),t.fixMax||(n[1]=Gf(Math.ceil(n[1]/r)*r))}});Zf.create=function(){return new Zf};var Uf="__ec_stack_",Xf="undefined"!=typeof Float32Array?Float32Array:Array;function Yf(t){return t.get("stack")||Uf+t.seriesIndex}function jf(t){return t.dim+t.index}function qf(t,e){var n=[];return e.eachSeriesByType(t,function(t){Jf(t)&&!tp(t)&&n.push(t)}),n}function $f(t){var g=function(t){var l={};D(t,function(t){var e=t.coordinateSystem.getBaseAxis();if("time"===e.type||"value"===e.type)for(var n=t.getData(),i=e.dim+"_"+e.index,r=n.mapDimension(e.dim),o=0,a=n.count();o<a;++o){var s=n.get(r,o);l[i]?l[i].push(s):l[i]=[s]}});var e,n=[];for(e in l)if(l.hasOwnProperty(e)){var i=l[e];if(i){i.sort(function(t,e){return t-e});for(var r=null,o=1;o<i.length;++o){var a=i[o]-i[o-1];0<a&&(r=null===r?a:Math.min(r,a))}n[e]=r}}return n}(t),m=[];return D(t,function(t){var e,n,i,r,o,a,s,l=t.coordinateSystem.getBaseAxis(),h=l.getExtent();a="category"===l.type?l.getBandWidth():"value"===l.type||"time"===l.type?(e=l.dim+"_"+l.index,n=g[e],i=Math.abs(h[1]-h[0]),r=l.scale.getExtent(),o=Math.abs(r[1]-r[0]),n?i/o*n:i):(s=t.getData(),Math.abs(h[1]-h[0])/s.count());var u=gl(t.get("barWidth"),a),c=gl(t.get("barMaxWidth"),a),d=gl(t.get("barMinWidth")||1,a),f=t.get("barGap"),p=t.get("barCategoryGap");m.push({bandWidth:a,barWidth:u,barMaxWidth:c,barMinWidth:d,barGap:f,barCategoryGap:p,axisKey:jf(l),stackId:Yf(t)})}),function(t){var d={};D(t,function(t,e){var n=t.axisKey,i=t.bandWidth,r=d[n]||{bandWidth:i,remainedWidth:i,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},o=r.stacks;d[n]=r;var a=t.stackId;o[a]||r.autoWidthCount++,o[a]=o[a]||{width:0,maxWidth:0};var s=t.barWidth;s&&!o[a].width&&(o[a].width=s,s=Math.min(r.remainedWidth,s),r.remainedWidth-=s);var l=t.barMaxWidth;l&&(o[a].maxWidth=l);var h=t.barMinWidth;h&&(o[a].minWidth=h);var u=t.barGap;null!=u&&(r.gap=u);var c=t.barCategoryGap;null!=c&&(r.categoryGap=c)});var f={};return D(d,function(t,n){f[n]={};var e=t.stacks,i=t.bandWidth,r=gl(t.categoryGap,i),o=gl(t.gap,1),a=t.remainedWidth,s=t.autoWidthCount,l=(a-r)/(s+(s-1)*o),l=Math.max(l,0);D(e,function(t){var e,n=t.maxWidth,i=t.minWidth;t.width?(e=t.width,n&&(e=Math.min(e,n)),i&&(e=Math.max(e,i)),t.width=e,a-=e+o*e,s--):(e=l,n&&n<e&&(e=Math.min(n,a)),i&&e<i&&(e=i),e!==l&&(t.width=e,a-=e+o*e,s--))}),l=(a-r)/(s+(s-1)*o),l=Math.max(l,0);var h,u=0;D(e,function(t,e){t.width||(t.width=l),u+=(h=t).width*(1+o)}),h&&(u-=h.width*o);var c=-u/2;D(e,function(t,e){f[n][e]=f[n][e]||{bandWidth:i,offset:c,width:t.width},c+=t.width*(1+o)})}),f}(m)}function Kf(t,e,n){if(t&&e){var i=t[jf(e)];return null!=i&&null!=n&&(i=i[Yf(n)]),i}}var Qf={seriesType:"bar",plan:Uu(),reset:function(t){if(Jf(t)&&tp(t)){var e=t.getData(),c=t.coordinateSystem,d=c.grid.getRect(),n=c.getBaseAxis(),f=c.getOtherAxis(n),p=e.mapDimension(f.dim),g=e.mapDimension(n.dim),m=f.isHorizontal(),v=m?0:1,y=Kf($f([t]),n,t).width;return.5<y||(y=.5),{progress:function(t,e){var n,i=t.count,r=new Xf(2*i),o=new Xf(2*i),a=new Xf(i),s=[],l=[],h=0,u=0;for(;null!=(n=t.next());)l[v]=e.get(p,n),l[1-v]=e.get(g,n),s=c.dataToPoint(l,null,s),o[h]=m?d.x+d.width:s[0],r[h++]=s[0],o[h]=m?s[1]:d.y+d.height,r[h++]=s[1],a[u++]=n;e.setLayout({largePoints:r,largeDataIndices:a,largeBackgroundPoints:o,barWidth:y,valueAxisStart:ep(0,f),backgroundStart:m?d.x:d.y,valueAxisHorizontal:m})}}}}};function Jf(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function tp(t){return t.pipelineContext&&t.pipelineContext.large}function ep(t,e){return e.toGlobalCoord(e.dataToCoord("log"===e.type?1:0))}var np=Zf.prototype,ip=Math.ceil,rp=Math.floor,op=36e5,ap=864e5,sp=Zf.extend({type:"time",getLabel:function(t){var e=this._stepLvl,n=new Date(t);return Hl(e[0],n,this.getSetting("useUTC"))},niceExtent:function(t){var e,n=this._extent;n[0]===n[1]&&(n[0]-=ap,n[1]+=ap),n[1]===-1/0&&n[0]===1/0&&(e=new Date,n[1]=+new Date(e.getFullYear(),e.getMonth(),e.getDate()),n[0]=n[1]-ap),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var i=this._interval;t.fixMin||(n[0]=ml(rp(n[0]/i)*i)),t.fixMax||(n[1]=ml(ip(n[1]/i)*i))},niceTicks:function(t,e,n){t=t||10;var i=this._extent,r=i[1]-i[0],o=r/t;null!=e&&o<e&&(o=e),null!=n&&n<o&&(o=n);var a=lp.length,s=function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=1+r:i=r}return n}(lp,o,0,a),l=lp[Math.min(s,a-1)],h=l[1];"year"===l[0]&&(h*=Al(r/h/t,!0));var u=this.getSetting("useUTC")?0:60*new Date(+i[0]||+i[1]).getTimezoneOffset()*1e3,c=[Math.round(ip((i[0]-u)/h)*h+u),Math.round(rp((i[1]-u)/h)*h+u)];Wf(c,i),this._stepLvl=l,this._interval=h,this._niceExtent=c},parse:function(t){return+Il(t)}});D(["contain","normalize"],function(e){sp.prototype[e]=function(t){return np[e].call(this,this.parse(t))}});var lp=[["hh:mm:ss",1e3],["hh:mm:ss",5e3],["hh:mm:ss",1e4],["hh:mm:ss",15e3],["hh:mm:ss",3e4],["hh:mm\nMM-dd",6e4],["hh:mm\nMM-dd",3e5],["hh:mm\nMM-dd",6e5],["hh:mm\nMM-dd",9e5],["hh:mm\nMM-dd",18e5],["hh:mm\nMM-dd",op],["hh:mm\nMM-dd",72e5],["hh:mm\nMM-dd",6*op],["hh:mm\nMM-dd",432e5],["MM-dd\nyyyy",ap],["MM-dd\nyyyy",2*ap],["MM-dd\nyyyy",3*ap],["MM-dd\nyyyy",4*ap],["MM-dd\nyyyy",5*ap],["MM-dd\nyyyy",6*ap],["week",7*ap],["MM-dd\nyyyy",864e6],["week",14*ap],["week",21*ap],["month",31*ap],["week",42*ap],["month",62*ap],["week",70*ap],["quarter",95*ap],["month",31*ap*4],["month",13392e6],["half-year",16416e6],["month",31*ap*8],["month",26784e6],["year",380*ap]];sp.create=function(t){return new sp({useUTC:t.ecModel.get("useUTC")})};var hp=Lf.prototype,up=Zf.prototype,cp=_l,dp=ml,fp=Math.floor,pp=Math.ceil,gp=Math.pow,mp=Math.log,vp=Lf.extend({type:"log",base:10,$constructor:function(){Lf.apply(this,arguments),this._originalScale=new Zf},getTicks:function(t){var n=this._originalScale,i=this._extent,r=n.getExtent();return P(up.getTicks.call(this,t),function(t){var e=ml(gp(this.base,t)),e=t===i[0]&&n.__fixMin?yp(e,r[0]):e;return e=t===i[1]&&n.__fixMax?yp(e,r[1]):e},this)},getMinorTicks:up.getMinorTicks,getLabel:up.getLabel,scale:function(t){return t=hp.scale.call(this,t),gp(this.base,t)},setExtent:function(t,e){var n=this.base;t=mp(t)/mp(n),e=mp(e)/mp(n),up.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=hp.getExtent.call(this);e[0]=gp(t,e[0]),e[1]=gp(t,e[1]);var n=this._originalScale,i=n.getExtent();return n.__fixMin&&(e[0]=yp(e[0],i[0])),n.__fixMax&&(e[1]=yp(e[1],i[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=mp(t[0])/mp(e),t[1]=mp(t[1])/mp(e),hp.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getApproximateExtent(e))},niceTicks:function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n==1/0||n<=0)){var i=Cl(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&0<Math.abs(i);)i*=10;var r=[ml(pp(e[0]/i)*i),ml(fp(e[1]/i)*i)];this._interval=i,this._niceExtent=r}},niceExtent:function(t){up.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});function yp(t,e){return dp(t,cp(e))}function _p(t,e){var n,i,r,o=t.type,a=e.getMin(),s=e.getMax(),l=t.getExtent();"ordinal"===o?n=e.getCategories().length:(z(i=e.get("boundaryGap"))||(i=[i||0,i||0]),"boolean"==typeof i[0]&&(i=[0,0]),i[0]=gl(i[0],1),i[1]=gl(i[1],1),r=l[1]-l[0]||Math.abs(l[0])),"dataMin"===a?a=l[0]:"function"==typeof a&&(a=a({min:l[0],max:l[1]})),"dataMax"===s?s=l[1]:"function"==typeof s&&(s=s({min:l[0],max:l[1]}));var h=null!=a,u=null!=s;null==a&&(a="ordinal"===o?n?0:NaN:l[0]-i[0]*r),null==s&&(s="ordinal"===o?n?n-1:NaN:l[1]+i[1]*r),null!=a&&isFinite(a)||(a=NaN),null!=s&&isFinite(s)||(s=NaN),t.setBlank(W(a)||W(s)||"ordinal"===o&&!t.getOrdinalMeta().categories.length),e.getNeedCrossZero()&&(0<a&&0<s&&!h&&(a=0),a<0&&s<0&&!u&&(s=0));var c,d,f,p,g=e.ecModel;return g&&"time"===o&&(D(c=qf("bar",g),function(t){d|=t.getBaseAxis()===e.axis}),d&&(f=$f(c),a=(p=function(t,e,n,i){var r=n.axis.getExtent(),o=r[1]-r[0],a=Kf(i,n.axis);if(void 0===a)return{min:t,max:e};var s=1/0;D(a,function(t){s=Math.min(t.offset,s)});var l=-1/0;D(a,function(t){l=Math.max(t.offset+t.width,l)}),s=Math.abs(s),l=Math.abs(l);var h=s+l,u=e-t,c=u/(1-(s+l)/o)-u;return{min:t-=s/h*c,max:e+=l/h*c}}(a,s,e,f)).min,s=p.max)),{extent:[a,s],fixMin:h,fixMax:u}}function xp(t,e){var n=_p(t,e),i=n.extent,r=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var o=t.type;t.setExtent(i[0],i[1]),t.niceExtent({splitNumber:r,fixMin:n.fixMin,fixMax:n.fixMax,minInterval:"interval"===o||"time"===o?e.get("minInterval"):null,maxInterval:"interval"===o||"time"===o?e.get("maxInterval"):null});var a=e.get("interval");null!=a&&t.setInterval&&t.setInterval(a)}function wp(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Bf(t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),[1/0,-1/0]);case"value":return new Zf;default:return(Lf.getClass(e)||Zf).create(t)}}function bp(n){var e,i=n.getLabelModel().get("formatter"),r="category"===n.type?n.scale.getExtent()[0]:null;return"string"==typeof i?(e=i,i=function(t){return t=n.scale.getLabel(t),e.replace("{value}",null!=t?t:"")}):"function"==typeof i?function(t,e){return null!=r&&(e=t-r),i(Sp(n,t),e)}:function(t){return n.scale.getLabel(t)}}function Sp(t,e){return"category"===t.type?t.scale.getLabel(e):e}function Mp(t){var e=t.model,n=t.scale;if(e.get("axisLabel.show")&&!n.isBlank()){var i,r,o="category"===t.type,a=n.getExtent(),s=o?n.count():(i=n.getTicks()).length,l=t.getLabelModel(),h=bp(t),u=1;40<s&&(u=Math.ceil(s/40));for(var c,d,f,p,g,m,v,y,_=0;_<s;_+=u){var x=h(i?i[_]:a[0]+_),w=l.getTextRect(x),b=(c=w,d=l.get("rotate")||0,y=v=m=g=p=f=void 0,f=d*Math.PI/180,p=c.plain(),g=p.width,m=p.height,v=g*Math.abs(Math.cos(f))+Math.abs(m*Math.sin(f)),y=g*Math.abs(Math.sin(f))+Math.abs(m*Math.cos(f)),new yn(p.x,p.y,v,y));r?r.union(b):r=b}return r}}function Ip(t){var e=t.get("interval");return null==e?"auto":e}function Cp(t){return"category"===t.type&&0===Ip(t.getLabelModel())}D(["contain","normalize"],function(e){vp.prototype[e]=function(t){return t=mp(t)/mp(this.base),hp[e].call(this,t)}}),vp.create=function(){return new vp};var Tp={getMin:function(t){var e=this.option,n=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=n&&"dataMin"!==n&&"function"!=typeof n&&!W(n)&&(n=this.axis.scale.parse(n)),n},getMax:function(t){var e=this.option,n=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=n&&"dataMax"!==n&&"function"!=typeof n&&!W(n)&&(n=this.axis.scale.parse(n)),n},getNeedCrossZero:function(){var t=this.option;return null==t.rangeStart&&null==t.rangeEnd&&!t.scale},getCoordSysModel:et,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}},Ap=cs({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i+o),t.lineTo(n-r,i+o),t.closePath()}}),Dp=cs({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i),t.lineTo(n,i+o),t.lineTo(n-r,i),t.closePath()}}),kp=cs({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,o=Math.max(r,e.height),a=r/2,s=a*a/(o-a),l=i-o+a+s,h=Math.asin(s/a),u=Math.cos(h)*a,c=Math.sin(h),d=Math.cos(h),f=.6*a,p=.7*a;t.moveTo(n-u,l+s),t.arc(n,l,a,Math.PI-h,2*Math.PI+h),t.bezierCurveTo(n+u-c*f,l+s+d*f,n,i-p,n,i),t.bezierCurveTo(n,i-p,n-u+c*f,l+s+d*f,n-u,l+s),t.closePath()}}),Pp=cs({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,o=e.y,a=i/3*2;t.moveTo(r,o),t.lineTo(r+a,o+n),t.lineTo(r,o+n/4*3),t.lineTo(r-a,o+n),t.lineTo(r,o),t.closePath()}}),Lp={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var o=Math.min(n,i);r.x=t,r.y=e,r.width=o,r.height=o},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},Op={};D({line:Ua,rect:Ga,roundRect:Ga,square:Ga,circle:ka,diamond:Dp,pin:kp,arrow:Pp,triangle:Ap},function(t,e){Op[e]=new t});var Ep=cs({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var i=ai(t,e,n),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.textPosition&&(i.y=n.y+.4*n.height),i},buildPath:function(t,e,n){var i,r=e.symbolType;"none"!==r&&(i=(i=Op[r])||Op[r="rect"],Lp[r](e.x,e.y,e.width,e.height,i.shape),i.buildPath(t,i.shape,n))}});function zp(t,e){var n,i;"image"!==this.type&&(n=this.style,(i=this.shape)&&"line"===i.symbolType?n.stroke=t:this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff"):(n.fill&&(n.fill=t),n.stroke&&(n.stroke=t)),this.dirty(!1))}function Np(t,e,n,i,r,o,a){var s,l=0===t.indexOf("empty");return l&&(t=t.substr(5,1).toLowerCase()+t.substr(6)),(s=0===t.indexOf("image://")?gs(t.slice(8),new yn(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?ps(t.slice(7),{},new yn(e,n,i,r),a?"center":"cover"):new Ep({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=l,s.setColor=zp,s.setColor(o),s}var Rp={isDimensionStacked:Df,enableDataStack:Af,getStackedDimension:kf};var Bp=(Object.freeze||Object)({createList:function(t){return Pf(t.getSource(),t)},getLayoutRect:Kl,dataStack:Rp,createScale:function(t,e){var n=e;ll.isInstance(e)||S(n=new ll(e),Tp);var i=wp(n);return i.setExtent(t[0],t[1]),xp(i,n),i},mixinAxisModelCommonMethods:function(t){S(t,Tp)},completeDimensions:bf,createDimensions:Mf,createSymbol:Np}),Vp=1e-8;function Fp(t,e){return Math.abs(t-e)<Vp}function Hp(t,e,n){var i=0,r=t[0];if(r){for(var o=1;o<t.length;o++){var a=t[o];i+=Qo(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return Fp(r[0],s[0])&&Fp(r[1],s[1])||(i+=Qo(r[0],r[1],s[0],s[1],e,n)),0!==i}}function Wp(t,e,n){var i;this.name=t,this.geometries=e,n=n?[n[0],n[1]]:[(i=this.getBoundingRect()).x+i.width/2,i.y+i.height/2],this.center=n}function Gp(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=(s=t.charCodeAt(a)-64)>>1^-(1&s),l=(l=t.charCodeAt(a+1)-64)>>1^-(1&l),r=s+=r,o=l+=o;i.push([s/n,l/n])}return i}Wp.prototype={constructor:Wp,properties:null,getBoundingRect:function(){var t=this._rect;if(t)return t;for(var e=Number.MAX_VALUE,n=[e,e],i=[-e,-e],r=[],o=[],a=this.geometries,s=0;s<a.length;s++){"polygon"===a[s].type&&(Do(a[s].exterior,r,o),_t(n,n,r),xt(i,i,o))}return 0===s&&(n[0]=n[1]=i[0]=i[1]=0),this._rect=new yn(n[0],n[1],i[0]-n[0],i[1]-n[1])},contain:function(t){var e=this.getBoundingRect(),n=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var i=0,r=n.length;i<r;i++)if("polygon"===n[i].type){var o=n[i].exterior,a=n[i].interiors;if(Hp(o,t[0],t[1])){for(var s=0;s<(a?a.length:0);s++)if(Hp(a[s]))continue t;return!0}}return!1},transformTo:function(t,e,n,i){var r=this.getBoundingRect(),o=r.width/r.height;n?i=i||n/o:n=o*i;for(var a=new yn(t,e,n,i),s=r.calculateTransform(a),l=this.geometries,h=0;h<l.length;h++)if("polygon"===l[h].type){for(var u=l[h].exterior,c=l[h].interiors,d=0;d<u.length;d++)yt(u[d],u[d],s);for(var f=0;f<(c?c.length:0);f++)for(d=0;d<c[f].length;d++)yt(c[f][d],c[f][d],s)}(r=this._rect).copy(a),this.center=[r.x+r.width/2,r.y+r.height/2]},cloneShallow:function(t){null==t&&(t=this.name);var e=new Wp(t,this.geometries,this.center);return e._rect=this._rect,e.transformTo=null,e}};function Zp(t,a){return function(t){if(!t.UTF8Encoding)return;var e=t.UTF8Scale;null==e&&(e=1024);for(var n=t.features,i=0;i<n.length;i++)for(var r=n[i].geometry,o=r.coordinates,a=r.encodeOffsets,s=0;s<o.length;s++){var l=o[s];if("Polygon"===r.type)o[s]=Gp(l,a[s],e);else if("MultiPolygon"===r.type)for(var h=0;h<l.length;h++){var u=l[h];l[h]=Gp(u,a[s][h],e)}}t.UTF8Encoding=!1}(t),P(I(t.features,function(t){return t.geometry&&t.properties&&0<t.geometry.coordinates.length}),function(t){var e=t.properties,n=t.geometry,i=n.coordinates,r=[];"Polygon"===n.type&&r.push({type:"polygon",exterior:i[0],interiors:i.slice(1)}),"MultiPolygon"===n.type&&D(i,function(t){t[0]&&r.push({type:"polygon",exterior:t[0],interiors:t.slice(1)})});var o=new Wp(e[a||"name"],r,e.cp);return o.properties=e,o})}var Up=zr();function Xp(t){return"category"===t.type?(o=(r=t).getLabelModel(),a=jp(r,o),!o.get("show")||r.scale.isBlank()?{labels:[],labelCategoryInterval:a.labelCategoryInterval}:a):(e=(n=t).scale.getTicks(),i=bp(n),{labels:P(e,function(t,e){return{formattedLabel:i(t,e),rawLabel:n.scale.getLabel(t),tickValue:t}})});var n,e,i,r,o,a}function Yp(t,e){return"category"===t.type?function(t,e){var n,i,r=qp(t,"ticks"),o=Ip(e),a=$p(r,o);if(a)return a;e.get("show")&&!t.scale.isBlank()||(n=[]);{var s;n=N(o)?Jp(t,o,!0):"auto"===o?(s=jp(t,t.getLabelModel()),i=s.labelCategoryInterval,P(s.labels,function(t){return t.tickValue})):Qp(t,i=o,!0)}return Kp(r,o,{ticks:n,tickCategoryInterval:i})}(t,e):{ticks:t.scale.getTicks()}}function jp(t,e){var n,i,r,o=qp(t,"labels"),a=Ip(e),s=$p(o,a);return s||Kp(o,a,{labels:N(a)?Jp(t,a):Qp(t,n="auto"===a?null!=(r=Up(i=t).autoInterval)?r:Up(i).autoInterval=i.calculateCategoryInterval():a),labelCategoryInterval:n})}function qp(t,e){return Up(t)[e]||(Up(t)[e]=[])}function $p(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function Kp(t,e,n){return t.push({key:e,value:n}),n}function Qp(t,e,n){var i=bp(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),h=o[0],u=r.count();0!==h&&1<l&&2<u/l&&(h=Math.round(Math.ceil(h/l)*l));var c=Cp(t),d=a.get("showMinLabel")||c,f=a.get("showMaxLabel")||c;d&&h!==o[0]&&g(o[0]);for(var p=h;p<=o[1];p+=l)g(p);function g(t){s.push(n?t:{formattedLabel:i(t),rawLabel:r.getLabel(t),tickValue:t})}return f&&p-l!==o[1]&&g(o[1]),s}function Jp(t,n,i){var r=t.scale,o=bp(t),a=[];return D(r.getTicks(),function(t){var e=r.getLabel(t);n(t,e)&&a.push(i?t:{formattedLabel:o(t),rawLabel:e,tickValue:t})}),a}function tg(t,e,n){this.dim=t,this.scale=e,this._extent=n||[0,0],this.inverse=!1,this.onBand=!1}var eg=[0,1];function ng(t,e){var n=(t[1]-t[0])/e/2;t[0]+=n,t[1]-=n}tg.prototype={constructor:tg,contain:function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return n<=t&&t<=i},containData:function(t){return this.scale.contain(t)},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return xl(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var n=this._extent;n[0]=t,n[1]=e},dataToCoord:function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&ng(n=n.slice(),i.count()),pl(t,eg,n,e)},coordToData:function(t,e){var n=this._extent,i=this.scale;this.onBand&&"ordinal"===i.type&&ng(n=n.slice(),i.count());var r=pl(t,n,eg,e);return this.scale.scale(r)},pointToData:function(){},getTicksCoords:function(t){var e=(t=t||{}).tickModel||this.getTickModel(),n=P(Yp(this,e).ticks,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this);return function(t,e,n,i){var r=e.length;if(!t.onBand||n||!r)return;var o,a,s=t.getExtent();{var l,h,u;1===r?(e[0].coord=s[0],o=e[1]={coord:s[0]}):(l=e[r-1].tickValue-e[0].tickValue,h=(e[r-1].coord-e[0].coord)/l,D(e,function(t){t.coord-=h/2}),u=t.scale.getExtent(),a=1+u[1]-e[r-1].tickValue,o={coord:e[r-1].coord+h*a},e.push(o))}var c=s[0]>s[1];d(e[0].coord,s[0])&&(i?e[0].coord=s[0]:e.shift());i&&d(s[0],e[0].coord)&&e.unshift({coord:s[0]});d(s[1],o.coord)&&(i?o.coord=s[1]:e.pop());i&&d(o.coord,s[1])&&e.push({coord:s[1]});function d(t,e){return t=ml(t),e=ml(e),c?e<t:t<e}}(this,n,e.get("alignWithLabel"),t.clamp),n},getMinorTicksCoords:function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick").get("splitNumber");return 0<t&&t<100||(t=5),P(this.scale.getMinorTicks(t),function(t){return P(t,function(t){return{coord:this.dataToCoord(t),tickValue:t}},this)},this)},getViewLabels:function(){return Xp(this).labels},getLabelModel:function(){return this.model.getModel("axisLabel")},getTickModel:function(){return this.model.getModel("axisTick")},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},isHorizontal:null,getRotate:null,calculateCategoryInterval:function(){return function(t){var e,n,i=(n=(e=t).getLabelModel(),{axisRotate:e.getRotate?e.getRotate():e.isHorizontal&&!e.isHorizontal()?90:0,labelRotate:n.get("rotate")||0,font:n.getFont()}),r=bp(t),o=(i.axisRotate-i.labelRotate)/180*Math.PI,a=t.scale,s=a.getExtent(),l=a.count();if(s[1]-s[0]<1)return 0;var h=1;40<l&&(h=Math.max(1,Math.floor(l/40)));for(var u=s[0],c=t.dataToCoord(u+1)-t.dataToCoord(u),d=Math.abs(c*Math.cos(o)),f=Math.abs(c*Math.sin(o)),p=0,g=0;u<=s[1];u+=h)var m=ii(r(u),i.font,"center","top"),v=1.3*m.width,y=1.3*m.height,p=Math.max(p,v,7),g=Math.max(g,y,7);var _=p/d,x=g/f;isNaN(_)&&(_=1/0),isNaN(x)&&(x=1/0);var w=Math.max(0,Math.floor(Math.min(_,x))),b=Up(t.model),S=t.getExtent(),M=b.lastAutoInterval,I=b.lastTickCount;return null!=M&&null!=I&&Math.abs(M-w)<=1&&Math.abs(I-l)<=1&&w<M&&b.axisExtend0===S[0]&&b.axisExtend1===S[1]?w=M:(b.lastTickCount=l,b.lastAutoInterval=w,b.axisExtend0=S[0],b.axisExtend1=S[1]),w}(this)}};var ig=Zp,rg={};D(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],function(t){rg[t]=nt[t]});var og={};function ag(t,e){var n=t.mapDimension("defaultedLabel",!0),i=n.length;if(1===i)return yu(t,e,n[0]);if(i){for(var r=[],o=0;o<n.length;o++){var a=yu(t,e,n[o]);r.push(a)}return r.join(" ")}}function sg(t,e,n){_n.call(this),this.updateData(t,e,n)}D(["extendShape","extendPath","makePath","makeImage","mergePath","resizePath","createIcon","setHoverStyle","setLabelStyle","setTextStyle","setText","getFont","updateProps","initProps","getTransform","clipPointsByRect","clipRectByRect","registerShape","getShapeClass","Group","Image","Text","Circle","Sector","Ring","Polygon","Polyline","Rect","Line","BezierCurve","Arc","IncrementalDisplayable","CompoundPath","LinearGradient","RadialGradient","BoundingRect"],function(t){og[t]=el[t]}),zu.extend({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t){if(C){var e=t.coordinateSystem;if("polar"!==e&&"cartesian2d"!==e)throw new Error("Line not support coordinateSystem besides cartesian and polar")}return Pf(this.getSource(),this,{useEncodeDefaulter:!0})},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clip:!0,label:{position:"top"},lineStyle:{width:2,type:"solid"},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}});var lg=sg.prototype,hg=sg.getSymbolSize=function(t,e){var n=t.getItemVisual(e,"symbolSize");return n instanceof Array?n.slice():[+n,+n]};function ug(t){return[t[0]/2,t[1]/2]}function cg(t,e){this.parent.drift(t,e)}lg._createSymbol=function(t,e,n,i,r){this.removeAll();var o=Np(t,-1,-1,2,2,e.getItemVisual(n,"color"),r);o.attr({z2:100,culling:!0,scale:ug(i)}),o.drift=cg,this._symbolType=t,this.add(o)},lg.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},lg.getSymbolPath=function(){return this.childAt(0)},lg.getScale=function(){return this.childAt(0).scale},lg.highlight=function(){this.childAt(0).trigger("emphasis")},lg.downplay=function(){this.childAt(0).trigger("normal")},lg.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},lg.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":e.cursor},lg.updateData=function(t,e,n){this.silent=!1;var i,r,o,a,s=t.getItemVisual(e,"symbol")||"circle",l=t.hostModel,h=hg(t,e),u=s!==this._symbolType;u?(i=t.getItemVisual(e,"symbolKeepAspect"),this._createSymbol(s,t,e,h,i)):((r=this.childAt(0)).silent=!1,Xs(r,{scale:ug(h)},l,e)),this._updateCommon(t,e,h,n),u&&(r=this.childAt(0),o=n&&n.fadeIn,a={scale:r.scale.slice()},o&&(a.style={opacity:r.style.opacity}),r.scale=[0,0],o&&(r.style.opacity=0),Ys(r,a,l,e)),this._seriesModel=l};var dg=["itemStyle"],fg=["emphasis","itemStyle"],pg=["label"],gg=["emphasis","label"];function mg(t,e){var n,i,r;this.incremental||this.useHoverLayer||("emphasis"===e?(i=(n=this.__symbolOriginalScale)[1]/n[0],r={scale:[Math.max(1.1*n[0],n[0]+3),Math.max(1.1*n[1],n[1]+3*i)]},this.animateTo(r,400,"elasticOut")):"normal"===e&&this.animateTo({scale:this.__symbolOriginalScale},400,"elasticOut"))}function vg(t){this.group=new _n,this._symbolCtor=t||sg}lg._updateCommon=function(n,t,e,i){var r=this.childAt(0),o=n.hostModel,a=n.getItemVisual(t,"color");"image"!==r.type?r.useStyle({strokeNoScale:!0}):r.setStyle({opacity:1,shadowBlur:null,shadowOffsetX:null,shadowOffsetY:null,shadowColor:null});var s,l=i&&i.itemStyle,h=i&&i.hoverItemStyle,u=i&&i.symbolOffset,c=i&&i.labelModel,d=i&&i.hoverLabelModel,f=i&&i.hoverAnimation,p=i&&i.cursorStyle;!i||n.hasItemOption?(l=(s=i&&i.itemModel?i.itemModel:n.getItemModel(t)).getModel(dg).getItemStyle(["color"]),h=s.getModel(fg).getItemStyle(),u=s.getShallow("symbolOffset"),c=s.getModel(pg),d=s.getModel(gg),f=s.getShallow("hoverAnimation"),p=s.getShallow("cursor")):h=k({},h);var g=r.style,m=n.getItemVisual(t,"symbolRotate");r.attr("rotation",(m||0)*Math.PI/180||0),u&&r.attr("position",[gl(u[0],e[0]),gl(u[1],e[1])]),p&&r.attr("cursor",p),r.setColor(a,i&&i.symbolInnerColor),r.setStyle(l);var v=n.getItemVisual(t,"opacity");null!=v&&(g.opacity=v);var y=n.getItemVisual(t,"liftZ"),_=r.__z2Origin;null!=y?null==_&&(r.__z2Origin=r.z2,r.z2+=y):null!=_&&(r.z2=_,r.__z2Origin=null);var x=i&&i.useNameLabel;Rs(g,h,c,d,{labelFetcher:o,labelDataIndex:t,defaultText:function(t,e){return x?n.getName(t):ag(n,t)},isRectText:!0,autoColor:a}),r.__symbolOriginalScale=ug(e),r.hoverStyle=h,r.highDownOnUpdate=f&&o.isAnimationEnabled()?mg:null,Os(r)},lg.fadeOut=function(t,e){var n=this.childAt(0);this.silent=n.silent=!0,e&&e.keepLabel||(n.style.text=null),Xs(n,{style:{opacity:0},scale:[0,0]},this._seriesModel,this.dataIndex,t)},b(sg,_n);var yg=vg.prototype;function _g(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&(!i.isIgnore||!i.isIgnore(n))&&(!i.clipShape||i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function xg(t){return null==t||B(t)||(t={isIgnore:t}),t||{}}function wg(t){var e=t.hostModel;return{itemStyle:e.getModel("itemStyle").getItemStyle(["color"]),hoverItemStyle:e.getModel("emphasis.itemStyle").getItemStyle(),symbolRotate:e.get("symbolRotate"),symbolOffset:e.get("symbolOffset"),hoverAnimation:e.get("hoverAnimation"),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label"),cursorStyle:e.get("cursor")}}function bg(t,e,n){var i,r=t.getBaseAxis(),o=t.getOtherAxis(r),a=function(t,e){var n=0,i=t.scale.getExtent();"start"===e?n=i[0]:"end"===e?n=i[1]:0<i[0]?n=i[0]:i[1]<0&&(n=i[1]);return n}(o,n),s=r.dim,l=o.dim,h=e.mapDimension(l),u=e.mapDimension(s),c="x"===l||"radius"===l?1:0,d=P(t.dimensions,function(t){return e.mapDimension(t)}),f=e.getCalculationInfo("stackResultDimension");return(i|=Df(e,d[0]))&&(d[0]=f),(i|=Df(e,d[1]))&&(d[1]=f),{dataDimsForPoint:d,valueStart:a,valueAxisDim:l,baseAxisDim:s,stacked:!!i,valueDim:h,baseDim:u,baseDataOffset:c,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function Sg(t,e,n,i){var r=NaN;t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart);var o=t.baseDataOffset,a=[];return a[o]=n.get(t.baseDim,i),a[1-o]=r,e.dataToPoint(a)}yg.updateData=function(r,o){o=xg(o);var a=this.group,s=r.hostModel,l=this._data,h=this._symbolCtor,u=wg(r);l||a.removeAll(),r.diff(l).add(function(t){var e,n=r.getItemLayout(t);_g(r,n,t,o)&&((e=new h(r,t,u)).attr("position",n),r.setItemGraphicEl(t,e),a.add(e))}).update(function(t,e){var n=l.getItemGraphicEl(e),i=r.getItemLayout(t);_g(r,i,t,o)?(n?(n.updateData(r,t,u),Xs(n,{position:i},s)):(n=new h(r,t)).attr("position",i),a.add(n),r.setItemGraphicEl(t,n)):a.remove(n)}).remove(function(t){var e=l.getItemGraphicEl(t);e&&e.fadeOut(function(){a.remove(e)})}).execute(),this._data=r},yg.isPersistent=function(){return!0},yg.updateLayout=function(){var i=this._data;i&&i.eachItemGraphicEl(function(t,e){var n=i.getItemLayout(e);t.attr("position",n)})},yg.incrementalPrepareUpdate=function(t){this._seriesScope=wg(t),this._data=null,this.group.removeAll()},yg.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=t.useHoverLayer=!0)}n=xg(n);for(var r=t.start;r<t.end;r++){var o,a=e.getItemLayout(r);_g(e,a,r,n)&&((o=new this._symbolCtor(e,r,this._seriesScope)).traverse(i),o.attr("position",a),this.group.add(o),e.setItemGraphicEl(r,o))}},yg.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)})}):e.removeAll()};var Mg=_t,Ig=xt,Cg=lt,Tg=ot,Ag=[],Dg=[],kg=[];function Pg(t){return isNaN(t[0])||isNaN(t[1])}function Lg(t,e,n,i,r,o,a,s,l,h){return"none"!==h&&h?function(t,e,n,i,r,o,a,s,l,h,u){for(var c=0,d=n,f=0;f<i;f++){var p,g,m,v=e[d];if(r<=d||d<0)break;if(Pg(v)){if(u){d+=o;continue}break}d===n?t[0<o?"moveTo":"lineTo"](v[0],v[1]):0<l?(p=e[c],m=(v[g="y"===h?1:0]-p[g])*l,Tg(Dg,p),Dg[g]=p[g]+m,Tg(kg,v),kg[g]=v[g]-m,t.bezierCurveTo(Dg[0],Dg[1],kg[0],kg[1],v[0],v[1])):t.lineTo(v[0],v[1]),c=d,d+=o}return f}.apply(this,arguments):function(t,e,n,i,r,o,a,s,l,h,u){for(var c,d,f=0,p=n,g=0;g<i;g++){var m=e[p];if(r<=p||p<0)break;if(Pg(m)){if(u){p+=o;continue}break}if(p===n)t[0<o?"moveTo":"lineTo"](m[0],m[1]),Tg(Dg,m);else if(0<l){var v=p+o,y=e[v];if(u)for(;y&&Pg(e[v]);)y=e[v+=o];var _,x=.5,w=e[f];!(y=e[v])||Pg(y)?Tg(kg,m):(Pg(y)&&!u&&(y=m),ht(Ag,y,w),d="x"===h||"y"===h?(_="x"===h?0:1,c=Math.abs(m[_]-w[_]),Math.abs(m[_]-y[_])):(c=gt(m,w),gt(m,y)),Cg(kg,m,Ag,-l*(1-(x=d/(d+c))))),Mg(Dg,Dg,s),Ig(Dg,Dg,a),Mg(kg,kg,s),Ig(kg,kg,a),t.bezierCurveTo(Dg[0],Dg[1],kg[0],kg[1],m[0],m[1]),Cg(Dg,m,Ag,l*x)}else t.lineTo(m[0],m[1]);f=p,p+=o}return g}.apply(this,arguments)}function Og(t,e){var n=[1/0,1/0],i=[-1/0,-1/0];if(e)for(var r=0;r<t.length;r++){var o=t[r];o[0]<n[0]&&(n[0]=o[0]),o[1]<n[1]&&(n[1]=o[1]),o[0]>i[0]&&(i[0]=o[0]),o[1]>i[1]&&(i[1]=o[1])}return{min:e?n:i,max:e?i:n}}var Eg=ua.extend({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},brush:Da(ua.prototype.brush),buildPath:function(t,e){var n=e.points,i=0,r=n.length,o=Og(n,e.smoothConstraint);if(e.connectNulls){for(;0<r&&Pg(n[r-1]);r--);for(;i<r&&Pg(n[i]);i++);}for(;i<r;)i+=Lg(t,n,i,r,r,1,o.min,o.max,e.smooth,e.smoothMonotone,e.connectNulls)+1}}),zg=ua.extend({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},brush:Da(ua.prototype.brush),buildPath:function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,o=n.length,a=e.smoothMonotone,s=Og(n,e.smoothConstraint),l=Og(i,e.smoothConstraint);if(e.connectNulls){for(;0<o&&Pg(n[o-1]);o--);for(;r<o&&Pg(n[r]);r++);}for(;r<o;){var h=Lg(t,n,r,o,o,1,s.min,s.max,e.smooth,a,e.connectNulls);Lg(t,i,r+h-1,h,o,-1,l.min,l.max,e.stackedOnSmooth,a,e.connectNulls),r+=h+1,t.closePath()}}});function Ng(t,e,n){var i=t.getArea(),r=t.getBaseAxis().isHorizontal(),o=i.x,a=i.y,s=i.width,l=i.height,h=n.get("lineStyle.width")||2;o-=h/2,a-=h/2,s+=h,l+=h,o=Math.floor(o),s=Math.round(s);var u=new Ga({shape:{x:o,y:a,width:s,height:l}});return e&&(u.shape[r?"width":"height"]=0,Ys(u,{shape:{width:s,height:l}},n)),u}function Rg(t,e,n){var i=t.getArea(),r=new La({shape:{cx:ml(t.cx,1),cy:ml(t.cy,1),r0:ml(i.r0,1),r:ml(i.r,1),startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});return e&&(r.shape.endAngle=i.startAngle,Ys(r,{shape:{endAngle:i.endAngle}},n)),r}function Bg(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++){var i=t[n],r=e[n];if(i[0]!==r[0]||i[1]!==r[1])return}return 1}}function Vg(t,e){var n=[],i=[],r=[],o=[];return Do(t,n,i),Do(e,r,o),Math.max(Math.abs(n[0]-r[0]),Math.abs(n[1]-r[1]),Math.abs(i[0]-o[0]),Math.abs(i[1]-o[1]))}function Fg(t){return"number"==typeof t?t:t?.5:0}function Hg(t,e,n){for(var i=e.getBaseAxis(),r="x"===i.dim||"radius"===i.dim?0:1,o=[],a=0;a<t.length-1;a++){var s=t[a+1],l=t[a];o.push(l);var h=[];switch(n){case"end":h[r]=s[r],h[1-r]=l[1-r],o.push(h);break;case"middle":var u=(l[r]+s[r])/2,c=[];h[r]=c[r]=u,h[1-r]=l[1-r],c[1-r]=s[1-r],o.push(h),o.push(c);break;default:h[r]=l[r],h[1-r]=s[1-r],o.push(h)}}return t[a]&&o.push(t[a]),o}function Wg(t,e,n){var i=t.get("showAllSymbol"),r="auto"===i;if(!i||r){var o=n.getAxesByScale("ordinal")[0];if(o&&(!r||!function(t,e){var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count();isNaN(i)&&(i=0);for(var r=e.count(),o=Math.max(1,Math.round(r/5)),a=0;a<r;a+=o)if(1.5*sg.getSymbolSize(e,a)[t.isHorizontal()?1:0]>i)return!1;return!0}(o,e))){var a=e.mapDimension(o.dim),s={};return D(o.getViewLabels(),function(t){s[t.tickValue]=1}),function(t){return!s.hasOwnProperty(e.get(a,t))}}}}function Gg(t,e,n){if("cartesian2d"!==t.type)return Rg(t,e,n);var i,r,o=t.getBaseAxis().isHorizontal(),a=Ng(t,e,n);return n.get("clip",!0)||(i=a.shape,r=Math.max(i.width,i.height),o?(i.y-=r,i.height+=2*r):(i.x-=r,i.width+=2*r)),a}ju.extend({type:"line",init:function(){var t=new _n,e=new vg;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,e,n){var i=t.coordinateSystem,r=this.group,o=t.getData(),a=t.getModel("lineStyle"),s=t.getModel("areaStyle"),l=o.mapArray(o.getItemLayout),h="polar"===i.type,u=this._coordSys,c=this._symbolDraw,d=this._polyline,f=this._polygon,p=this._lineGroup,g=t.get("animation"),m=!s.isEmpty(),v=s.get("origin"),y=function(t,e,n){if(!n.valueDim)return[];for(var i=[],r=0,o=e.count();r<o;r++)i.push(Sg(n,t,e,r));return i}(i,o,bg(i,o,v)),_=t.get("showSymbol"),x=_&&!h&&Wg(t,o,i),w=this._data;w&&w.eachItemGraphicEl(function(t,e){t.__temp&&(r.remove(t),w.setItemGraphicEl(e,null))}),_||c.remove(),r.add(p);var b,S=!h&&t.get("step");i&&i.getArea&&t.get("clip",!0)&&(null!=(b=i.getArea()).width?(b.x-=.1,b.y-=.1,b.width+=.2,b.height+=.2):b.r0&&(b.r0-=.5,b.r1+=.5)),this._clipShapeForSymbol=b,d&&u.type===i.type&&S===this._step?(m&&!f?f=this._newPolygon(l,y,i,g):f&&!m&&(p.remove(f),f=this._polygon=null),p.setClipPath(Gg(i,!1,t)),_&&c.updateData(o,{isIgnore:x,clipShape:b}),o.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),Bg(this._stackedOnPoints,y)&&Bg(this._points,l)||(g?this._updateAnimation(o,y,i,n,S,v):(S&&(l=Hg(l,i,S),y=Hg(y,i,S)),d.setShape({points:l}),f&&f.setShape({points:l,stackedOnPoints:y})))):(_&&c.updateData(o,{isIgnore:x,clipShape:b}),S&&(l=Hg(l,i,S),y=Hg(y,i,S)),d=this._newPolyline(l,i,g),m&&(f=this._newPolygon(l,y,i,g)),p.setClipPath(Gg(i,!0,t)));var M=function(t,e){var n=t.getVisual("visualMeta");if(n&&n.length&&t.count()&&"cartesian2d"===e.type){for(var i,r=n.length-1;0<=r;r--){var o,a=n[r].dimension,s=t.dimensions[a],l=t.getDimensionInfo(s);if("x"===(o=l&&l.coordDim)||"y"===o){i=n[r];break}}if(i){var h=e.getAxis(o),u=P(i.stops,function(t){return{coord:h.toGlobalCoord(h.dataToCoord(t.value)),color:t.color}}),c=u.length,d=i.outerColors.slice();c&&u[0].coord>u[c-1].coord&&(u.reverse(),d.reverse());var f=u[0].coord-10,p=u[c-1].coord+10,g=p-f;if(g<.001)return"transparent";D(u,function(t){t.offset=(t.coord-f)/g}),u.push({offset:c?u[c-1].offset:.5,color:d[1]||"transparent"}),u.unshift({offset:c?u[0].offset:.5,color:d[0]||"transparent"});var m=new Qa(0,0,0,0,u,!0);return m[o]=f,m[o+"2"]=p,m}}}(o,i)||o.getVisual("color");d.useStyle(A(a.getLineStyle(),{fill:"none",stroke:M,lineJoin:"bevel"}));var I,C,T=t.get("smooth"),T=Fg(t.get("smooth"));d.setShape({smooth:T,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),f&&(I=o.getCalculationInfo("stackedOnSeries"),C=0,f.useStyle(A(s.getAreaStyle(),{fill:M,opacity:.7,lineJoin:"bevel"})),I&&(C=Fg(I.get("smooth"))),f.setShape({smooth:T,stackedOnSmooth:C,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})),this._data=o,this._coordSys=i,this._stackedOnPoints=y,this._points=l,this._step=S,this._valueOrigin=v},dispose:function(){},highlight:function(t,e,n,i){var r=t.getData(),o=Er(r,i);if(!(o instanceof Array)&&null!=o&&0<=o){var a=r.getItemGraphicEl(o);if(!a){var s=r.getItemLayout(o);if(!s)return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(s[0],s[1]))return;(a=new sg(r,o)).position=s,a.setZ(t.get("zlevel"),t.get("z")),a.ignore=isNaN(s[0])||isNaN(s[1]),a.__temp=!0,r.setItemGraphicEl(o,a),a.stopSymbolAnimation(!0),this.group.add(a)}a.highlight()}else ju.prototype.highlight.call(this,t,e,n,i)},downplay:function(t,e,n,i){var r,o=t.getData(),a=Er(o,i);null!=a&&0<=a?(r=o.getItemGraphicEl(a))&&(r.__temp?(o.setItemGraphicEl(a,null),this.group.remove(r)):r.downplay()):ju.prototype.downplay.call(this,t,e,n,i)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new Eg({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e},_newPolygon:function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new zg({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(n),this._polygon=n},_updateAnimation:function(t,e,n,i,r,o){var a=this._polyline,s=this._polygon,l=t.hostModel,h=function(t,e,n,i,r,o,a,s){for(var l,h,u=(l=t,h=[],e.diff(l).add(function(t){h.push({cmd:"+",idx:t})}).update(function(t,e){h.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){h.push({cmd:"-",idx:t})}).execute(),h),c=[],d=[],f=[],p=[],g=[],m=[],v=[],y=bg(r,e,a),_=bg(o,t,s),x=0;x<u.length;x++){var w=u[x],b=!0;switch(w.cmd){case"=":var S=t.getItemLayout(w.idx),M=e.getItemLayout(w.idx1);(isNaN(S[0])||isNaN(S[1]))&&(S=M.slice()),c.push(S),d.push(M),f.push(n[w.idx]),p.push(i[w.idx1]),v.push(e.getRawIndex(w.idx1));break;case"+":var I=w.idx;c.push(r.dataToPoint([e.get(y.dataDimsForPoint[0],I),e.get(y.dataDimsForPoint[1],I)])),d.push(e.getItemLayout(I).slice()),f.push(Sg(y,r,e,I)),p.push(i[I]),v.push(e.getRawIndex(I));break;case"-":var I=w.idx,C=t.getRawIndex(I);C!==I?(c.push(t.getItemLayout(I)),d.push(o.dataToPoint([t.get(_.dataDimsForPoint[0],I),t.get(_.dataDimsForPoint[1],I)])),f.push(n[I]),p.push(Sg(_,o,t,I)),v.push(C)):b=!1}b&&(g.push(w),m.push(m.length))}m.sort(function(t,e){return v[t]-v[e]});for(var T=[],A=[],D=[],k=[],P=[],x=0;x<m.length;x++){I=m[x];T[x]=c[I],A[x]=d[I],D[x]=f[I],k[x]=p[I],P[x]=g[I]}return{current:T,next:A,stackedOnCurrent:D,stackedOnNext:k,status:P}}(this._data,t,this._stackedOnPoints,e,this._coordSys,n,this._valueOrigin,o),u=h.current,c=h.stackedOnCurrent,d=h.next,f=h.stackedOnNext;if(r&&(u=Hg(h.current,n,r),c=Hg(h.stackedOnCurrent,n,r),d=Hg(h.next,n,r),f=Hg(h.stackedOnNext,n,r)),3e3<Vg(u,d)||s&&3e3<Vg(c,f))return a.setShape({points:d}),void(s&&s.setShape({points:d,stackedOnPoints:f}));a.shape.__points=h.current,a.shape.points=u,Xs(a,{shape:{points:d}},l),s&&(s.setShape({points:u,stackedOnPoints:c}),Xs(s,{shape:{points:d,stackedOnPoints:f}},l));for(var p,g=[],m=h.status,v=0;v<m.length;v++){"="!==m[v].cmd||(p=t.getItemGraphicEl(m[v].idx1))&&g.push({el:p,ptIdx:v})}a.animators&&a.animators.length&&a.animators[0].during(function(){for(var t=0;t<g.length;t++){g[t].el.attr("position",a.shape.__points[g[t].ptIdx])}})},remove:function(){var n=this.group,i=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),i&&i.eachItemGraphicEl(function(t,e){t.__temp&&(n.remove(t),i.setItemGraphicEl(e,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}});function Zg(t,o,a){return{seriesType:t,performRawSeries:!0,reset:function(h,t){var e=h.getData(),u=h.get("symbol"),c=h.get("symbolSize"),n=h.get("symbolKeepAspect"),d=h.get("symbolRotate"),f=N(u),p=N(c),g=N(d),m=f||p||g,i=!f&&u?u:o,r=p?null:c;if(e.setVisual({legendSymbol:a||i,symbol:i,symbolSize:r,symbolKeepAspect:n,symbolRotate:d}),!t.isSeriesFiltered(h))return{dataEach:e.hasItemOption||m?function(t,e){var n,i,r,o,a,s,l;m&&(n=h.getRawValue(e),i=h.getDataParams(e),f&&t.setItemVisual(e,"symbol",u(n,i)),p&&t.setItemVisual(e,"symbolSize",c(n,i)),g&&t.setItemVisual(e,"symbolRotate",d(n,i))),t.hasItemOption&&(o=(r=t.getItemModel(e)).getShallow("symbol",!0),a=r.getShallow("symbolSize",!0),s=r.getShallow("symbolRotate",!0),l=r.getShallow("symbolKeepAspect",!0),null!=o&&t.setItemVisual(e,"symbol",o),null!=a&&t.setItemVisual(e,"symbolSize",a),null!=s&&t.setItemVisual(e,"symbolRotate",s),null!=l&&t.setItemVisual(e,"symbolKeepAspect",l))}:null}}}}function Ug(t){return{seriesType:t,plan:Uu(),reset:function(t){var e=t.getData(),c=t.coordinateSystem,d=t.pipelineContext.large;if(c){var f=P(c.dimensions,function(t){return e.mapDimension(t)}).slice(0,2),p=f.length,n=e.getCalculationInfo("stackResultDimension");return Df(e,f[0])&&(f[0]=n),Df(e,f[1])&&(f[1]=n),p&&{progress:function(t,e){for(var n,i,r,o=t.end-t.start,a=d&&new Float32Array(o*p),s=t.start,l=0,h=[],u=[];s<t.end;s++){n=1===p?(i=e.get(f[0],s),!isNaN(i)&&c.dataToPoint(i,null,u)):(i=h[0]=e.get(f[0],s),r=h[1]=e.get(f[1],s),!isNaN(i)&&!isNaN(r)&&c.dataToPoint(h,null,u)),d?(a[l++]=n?n[0]:NaN,a[l++]=n?n[1]:NaN):e.setItemLayout(s,n&&n.slice()||[NaN,NaN])}d&&e.setLayout("symbolPoints",a)}}}}}}function Xg(t){return Math.round(t.length/2)}var Yg={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}};function jg(t){return this._axes[t]}function qg(t){this._axes={},this._dimList=[],this.name=t||""}function $g(t){qg.call(this,t)}qg.prototype={constructor:qg,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return P(this._dimList,jg,this)},getAxesByScale:function(e){return e=e.toLowerCase(),I(this.getAxes(),function(t){return t.scale.type===e})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var n=this._dimList,i=t instanceof Array?[]:{},r=0;r<n.length;r++){var o=n[r],a=this._axes[o];i[o]=a[e](t[o])}return i}},$g.prototype={constructor:$g,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e,n){var i=this.getAxis("x"),r=this.getAxis("y");return(n=n||[])[0]=i.toGlobalCoord(i.dataToCoord(t[0])),n[1]=r.toGlobalCoord(r.dataToCoord(t[1])),n},clampData:function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),o=i.getExtent(),a=n.parse(t[0]),s=i.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(r[0],r[1]),a),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),s),Math.max(o[0],o[1])),e},pointToData:function(t,e){var n=this.getAxis("x"),i=this.getAxis("y");return(e=e||[])[0]=n.coordToData(n.toLocalCoord(t[0])),e[1]=i.coordToData(i.toLocalCoord(t[1])),e},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")},getArea:function(){var t=this.getAxis("x").getGlobalExtent(),e=this.getAxis("y").getGlobalExtent(),n=Math.min(t[0],t[1]),i=Math.min(e[0],e[1]);return new yn(n,i,Math.max(t[0],t[1])-n,Math.max(e[0],e[1])-i)}},b($g,qg);function Kg(t,e,n,i,r){tg.call(this,t,e,n),this.type=i||"value",this.position=r||"bottom"}Kg.prototype={constructor:Kg,index:0,getAxesOnZeroOf:null,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},b(Kg,tg);var Qg={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},Jg={};Jg.categoryAxis=m({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Qg),Jg.valueAxis=m({boundaryGap:[0,0],splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#eee",width:1}}},Qg),Jg.timeAxis=A({scale:!0,min:"dataMin",max:"dataMax"},Jg.valueAxis),Jg.logAxis=A({scale:!0,logBase:10},Jg.valueAxis);function tm(o,t,a,e){D(em,function(r){t.extend({type:o+"Axis."+r,mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?th(t):{};m(t,e.getTheme().get(r+"Axis")),m(t,this.getDefaultOption()),t.type=a(o,t),n&&Jl(t,i,n)},optionUpdated:function(){"category"===this.option.type&&(this.__ordinalMeta=Of.createByAxisModel(this))},getCategories:function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},getOrdinalMeta:function(){return this.__ordinalMeta},defaultOption:g([{},Jg[r+"Axis"],e],!0)})}),ah.registerSubTypeDefaulter(o+"Axis",O(a,o))}var em=["value","category","time","log"],nm=ah.extend({type:"cartesian2dAxis",axis:null,init:function(){nm.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){nm.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){nm.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});function im(t,e){return e.type||(e.data?"category":"value")}m(nm.prototype,Tp);var rm={offset:0};function om(t,e){return t.getCoordSysModel()===e}function am(t,e,n){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,n),this.model=t}tm("x",nm,im,rm),tm("y",nm,im,rm),ah.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}});var sm=am.prototype;function lm(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,o=t[e],a=n.model,s=a.get("axisLine.onZero"),l=a.get("axisLine.onZeroAxisIndex");if(s){if(null!=l)hm(o[l])&&(r=o[l]);else for(var h in o)if(o.hasOwnProperty(h)&&hm(o[h])&&!i[u(o[h])]){r=o[h];break}r&&(i[u(r)]=!0)}function u(t){return t.dim+"_"+t.index}}function hm(t){return t&&"category"!==t.type&&"time"!==t.type&&(e=t.scale.getExtent(),n=e[0],i=e[1],!(0<n&&0<i||n<0&&i<0));var e,n,i}sm.type="grid",sm.axisPointerEnabled=!0,sm.getRect=function(){return this._rect},sm.update=function(t,e){var n=this._axesMap;this._updateScale(t,this.model),D(n.x,function(t){xp(t.scale,t.model)}),D(n.y,function(t){xp(t.scale,t.model)});var i={};D(n.x,function(t){lm(n,"y",t,i)}),D(n.y,function(t){lm(n,"x",t,i)}),this.resize(this.model,e)},sm.resize=function(t,e,n){var l=Kl(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=l;var i=this._axesList;function r(){D(i,function(t){var e,n,i,r,o=t.isHorizontal(),a=o?[0,l.width]:[0,l.height],s=t.inverse?1:0;t.setExtent(a[s],a[1-s]),e=t,n=o?l.x:l.y,i=e.getExtent(),r=i[0]+i[1],e.toGlobalCoord="x"===e.dim?function(t){return t+n}:function(t){return r-t+n},e.toLocalCoord="x"===e.dim?function(t){return t-n}:function(t){return r-t+n}})}r(),!n&&t.get("containLabel")&&(D(i,function(t){var e,n,i;t.model.get("axisLabel.inside")||(e=Mp(t))&&(n=t.isHorizontal()?"height":"width",i=t.model.get("axisLabel.margin"),l[n]-=e[n]+i,"top"===t.position?l.y+=e.height+i:"left"===t.position&&(l.x+=e.width+i))}),r())},sm.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n){if(null==e)for(var i in n)if(n.hasOwnProperty(i))return n[i];return n[e]}},sm.getAxes=function(){return this._axesList.slice()},sm.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}B(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},sm.getCartesians=function(){return this._coordsList.slice()},sm.convertToPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},sm.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},sm._findConvertTarget=function(t,e){var n,i,r=e.seriesModel,o=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],a=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,l=this._coordsList;return r?w(l,n=r.coordinateSystem)<0&&(n=null):o&&a?n=this.getCartesian(o.componentIndex,a.componentIndex):o?i=this.getAxis("x",o.componentIndex):a?i=this.getAxis("y",a.componentIndex):s&&s.coordinateSystem===this&&(n=this._coordsList[0]),{cartesian:n,axis:i}},sm.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},sm._initCartesian=function(a,t,e){var s={left:!1,right:!1,top:!1,bottom:!1},l={x:{},y:{}},h={x:0,y:0};if(t.eachComponent("xAxis",n("x"),this),t.eachComponent("yAxis",n("y"),this),!h.x||!h.y)return this._axesMap={},void(this._axesList=[]);function n(o){return function(t,e){var n,i,r;om(t,a)&&(n=t.get("position"),"x"===o?"top"!==n&&"bottom"!==n&&(n=s.bottom?"top":"bottom"):"left"!==n&&"right"!==n&&(n=s.left?"right":"left"),s[n]=!0,r="category"===(i=new Kg(o,wp(t),[0,0],t.get("type"),n)).type,i.onBand=r&&t.get("boundaryGap"),i.inverse=t.get("inverse"),(t.axis=i).model=t,i.grid=this,i.index=e,this._axesList.push(i),l[o][e]=i,h[o]++)}}D((this._axesMap=l).x,function(r,o){D(l.y,function(t,e){var n="x"+o+"y"+e,i=new $g(n);i.grid=this,i.model=a,this._coordsMap[n]=i,this._coordsList.push(i),i.addAxis(r),i.addAxis(t)},this)},this)},sm._updateScale=function(t,l){function h(e,n){D(e.mapDimension(n.dim,!0),function(t){n.scale.unionExtentFromData(e,kf(e,t))})}D(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t.eachSeries(function(t){if(dm(t)){var e=cm(t),n=e[0],i=e[1];if(!om(n,l)||!om(i,l))return;var r=this.getCartesian(n.componentIndex,i.componentIndex),o=t.getData(),a=r.getAxis("x"),s=r.getAxis("y");"list"===o.type&&(h(o,a),h(o,s))}},this)},sm.getTooltipAxes=function(i){var r=[],o=[];return D(this.getCartesians(),function(t){var e=null!=i&&"auto"!==i?t.getAxis(i):t.getBaseAxis(),n=t.getOtherAxis(e);w(r,e)<0&&r.push(e),w(o,n)<0&&o.push(n)}),{baseAxes:r,otherAxes:o}};var um=["xAxis","yAxis"];function cm(n){return P(um,function(t){var e=n.getReferringComponents(t)[0];if(C&&!e)throw new Error(t+' "'+G(n.get(t+"Index"),n.get(t+"Id"),0)+'" not found');return e})}function dm(t){return"cartesian2d"===t.get("coordinateSystem")}am.create=function(i,r){var o=[];return i.eachComponent("grid",function(t,e){var n=new am(t,i,r);n.name="grid_"+e,n.resize(t,r,!0),t.coordinateSystem=n,o.push(n)}),i.eachSeries(function(t){if(dm(t)){var e=cm(t),n=e[0],i=e[1],r=n.getCoordSysModel();if(C){if(!r)throw new Error('Grid "'+G(n.get("gridIndex"),n.get("gridId"),0)+'" not found');if(n.getCoordSysModel()!==i.getCoordSysModel())throw new Error("xAxis and yAxis must use the same grid")}var o=r.coordinateSystem;t.coordinateSystem=o.getCartesian(n.componentIndex,i.componentIndex)}}),o},am.dimensions=am.prototype.dimensions=$g.prototype.dimensions,Bh.register("cartesian2d",am);function fm(t,e){this.opt=e,this.axisModel=t,A(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new _n;var n=new _n({position:e.position.slice(),rotation:e.rotation});n.updateTransform(),this._transform=n.transform,this._dumbGroup=n}var pm=Math.PI;fm.prototype={constructor:fm,hasBuilder:function(t){return!!gm[t]},add:function(t){gm[t].call(this)},getGroup:function(){return this.group}};var gm={axisLine:function(){var t,e,o,n,a,s,i,r,l,h,u=this.opt,c=this.axisModel;c.get("axisLine.show")&&(t=this.axisModel.axis.getExtent(),e=this._transform,o=[t[0],0],n=[t[1],0],e&&(yt(o,o,e),yt(n,n,e)),a=k({lineCap:"round"},c.getModel("axisLine.lineStyle").getLineStyle()),this.group.add(new Ua({anid:"line",subPixelOptimize:!0,shape:{x1:o[0],y1:o[1],x2:n[0],y2:n[1]},style:a,strokeContainThreshold:u.strokeContainThreshold||5,silent:!0,z2:1})),s=c.get("axisLine.symbol"),i=c.get("axisLine.symbolSize"),"number"==typeof(r=c.get("axisLine.symbolOffset")||0)&&(r=[r,r]),null!=s&&("string"==typeof s&&(s=[s,s]),"string"!=typeof i&&"number"!=typeof i||(i=[i,i]),l=i[0],h=i[1],D([{rotate:u.rotation+Math.PI/2,offset:r[0],r:0},{rotate:u.rotation-Math.PI/2,offset:r[1],r:Math.sqrt((o[0]-n[0])*(o[0]-n[0])+(o[1]-n[1])*(o[1]-n[1]))}],function(t,e){var n,i,r;"none"!==s[e]&&null!=s[e]&&(n=Np(s[e],-l/2,-h/2,l,h,a.stroke,!0),i=t.r+t.offset,r=[o[0]+i*Math.cos(u.rotation),o[1]-i*Math.sin(u.rotation)],n.attr({rotation:t.rotate,position:r,silent:!0,z2:11}),this.group.add(n))},this)))},axisTickLabel:function(){var t=this.axisModel,e=this.opt,n=function(t,e,n){var i=e.axis,r=e.getModel("axisTick");if(!r.get("show")||i.scale.isBlank())return;for(var o=r.getModel("lineStyle"),a=n.tickDirection*r.get("length"),s=bm(i.getTicksCoords(),t._transform,a,A(o.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),"ticks"),l=0;l<s.length;l++)t.group.add(s[l]);return s}(this,t,e);!function(t,e,n){if(Cp(t.axis))return;var i=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");n=n||[];var o=(e=e||[])[0],a=e[1],s=e[e.length-1],l=e[e.length-2],h=n[0],u=n[1],c=n[n.length-1],d=n[n.length-2];!1===i?(_m(o),_m(h)):xm(o,a)&&(i?(_m(a),_m(u)):(_m(o),_m(h)));!1===r?(_m(s),_m(c)):xm(l,s)&&(r?(_m(l),_m(d)):(_m(s),_m(c)))}(t,function(h,u,c){var d=u.axis;if(!G(c.axisLabelShow,u.get("axisLabel.show"))||d.scale.isBlank())return;var f=u.getModel("axisLabel"),p=f.get("margin"),t=d.getViewLabels(),e=(G(c.labelRotate,f.get("rotate"))||0)*pm/180,g=vm(c.rotation,e,c.labelDirection),m=u.getCategories&&u.getCategories(!0),v=[],y=ym(u),_=u.get("triggerEvent");return D(t,function(t,e){var n=t.tickValue,i=t.formattedLabel,r=t.rawLabel,o=f;m&&m[n]&&m[n].textStyle&&(o=new ll(m[n].textStyle,f,u.ecModel));var a=o.getTextColor()||u.get("axisLine.lineStyle.color"),s=[d.dataToCoord(n),c.labelOffset+c.labelDirection*p],l=new Aa({anid:"label_"+n,position:s,rotation:g.rotation,silent:y,z2:10});Bs(l.style,o,{text:i,textAlign:o.getShallow("align",!0)||g.textAlign,textVerticalAlign:o.getShallow("verticalAlign",!0)||o.getShallow("baseline",!0)||g.textVerticalAlign,textFill:"function"==typeof a?a("category"===d.type?r:"value"===d.type?n+"":n,e):a}),_&&(l.eventData=mm(u),l.eventData.targetType="axisLabel",l.eventData.value=r),h._dumbGroup.add(l),l.updateTransform(),v.push(l),h.group.add(l),l.decomposeTransform()}),v}(this,t,e),n),function(t,e,n){var i=e.axis,r=e.getModel("minorTick");if(!r.get("show")||i.scale.isBlank())return;var o=i.getMinorTicksCoords();if(!o.length)return;for(var a=r.getModel("lineStyle"),s=n.tickDirection*r.get("length"),l=A(a.getLineStyle(),A(e.getModel("axisTick").getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")})),h=0;h<o.length;h++)for(var u=bm(o[h],t._transform,s,l,"minorticks_"+h),c=0;c<u.length;c++)t.group.add(u[c])}(this,t,e)},axisName:function(){var t,e,n,i,r,o,a,s,l,h,u,c,d,f,p,g,m,v,y,_=this.opt,x=this.axisModel,w=G(_.axisName,x.get("name"));w&&(t=x.get("nameLocation"),e=_.nameDirection,n=x.getModel("nameTextStyle"),i=x.get("nameGap")||0,o=(r=this.axisModel.axis.getExtent())[0]>r[1]?-1:1,a=["start"===t?r[0]-o*i:"end"===t?r[1]+o*i:(r[0]+r[1])/2,wm(t)?_.labelOffset+e*i:0],null!=(l=x.get("nameRotate"))&&(l=l*pm/180),wm(t)?s=vm(_.rotation,null!=l?l:_.rotation,e):(s=function(t,e,n,i){var r,o,a=bl(n-t.rotation),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;r=Sl(a-pm/2)?(o=l?"bottom":"top","center"):Sl(a-1.5*pm)?(o=l?"top":"bottom","center"):(o="middle",a<1.5*pm&&pm/2<a?l?"left":"right":l?"right":"left");return{rotation:a,textAlign:r,textVerticalAlign:o}}(_,t,l||0,r),null!=(h=_.axisNameAvailableWidth)&&(h=Math.abs(h/Math.sin(s.rotation)),isFinite(h)||(h=null))),u=n.getFont(),d=(c=x.get("nameTruncate",!0)||{}).ellipsis,f=G(_.nameTruncateMaxWidth,c.maxWidth,h),p=null!=d&&null!=f?Gl(w,f,u,d,{minChar:2,placeholder:c.placeholder}):w,g=x.get("tooltip",!0),(v={componentType:m=x.mainType,name:w,$vars:["name"]})[m+"Index"]=x.componentIndex,Bs((y=new Aa({anid:"name",__fullText:w,__truncatedText:p,position:a,rotation:s.rotation,silent:ym(x),z2:1,tooltip:g&&g.show?k({content:w,formatter:function(){return w},formatterParams:v},g):null})).style,n,{text:p,textFont:u,textFill:n.getTextColor()||x.get("axisLine.lineStyle.color"),textAlign:n.get("align")||s.textAlign,textVerticalAlign:n.get("verticalAlign")||s.textVerticalAlign}),x.get("triggerEvent")&&(y.eventData=mm(x),y.eventData.targetType="axisName",y.eventData.name=w),this._dumbGroup.add(y),y.updateTransform(),this.group.add(y),y.decomposeTransform())}},mm=fm.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},vm=fm.innerTextLayout=function(t,e,n){var i,r=bl(e-t),o=Sl(r)?(i=0<n?"top":"bottom","center"):Sl(r-pm)?(i=0<n?"bottom":"top","center"):(i="middle",0<r&&r<pm?0<n?"right":"left":0<n?"left":"right");return{rotation:r,textAlign:o,textVerticalAlign:i}};var ym=fm.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)};function _m(t){t&&(t.ignore=!0)}function xm(t,e){var n=t&&t.getBoundingRect().clone(),i=e&&e.getBoundingRect().clone();if(n&&i){var r=ee([]);return oe(r,r,-t.rotation),n.applyTransform(ie([],r,t.getLocalTransform())),i.applyTransform(ie([],r,e.getLocalTransform())),n.intersect(i)}}function wm(t){return"middle"===t||"center"===t}function bm(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var h=t[l].coord;a[0]=h,s[a[1]=0]=h,s[1]=n,e&&(yt(a,a,e),yt(s,s,e));var u=new Ua({anid:r+"_"+t[l].tickValue,subPixelOptimize:!0,shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,silent:!0});o.push(u)}return o}var Sm=D,Mm=O;function Im(t,e){var p,g,n,o,m,v,y,i={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return p=i,n=e,o=(g=t).getComponent("tooltip"),m=g.getComponent("axisPointer"),v=m.get("link",!0)||[],y=[],Sm(n.getCoordinateSystems(),function(c){var t,d,f,e,n,i;function r(t,e,n){var i,r,o,a,s,l,h=n.model.getModel("axisPointer",m),u=h.get("show");u&&("auto"!==u||t||Am(h))&&(null==e&&(e=h.get("triggerTooltip")),i=(h=t?function(t,e,n,i,r,o){var a=e.getModel("axisPointer"),s={};Sm(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){s[t]=T(a.get(t))}),s.snap="category"!==t.type&&!!o,"cross"===a.get("type")&&(s.type="line");var l=s.label||(s.label={});{var h,u;null==l.show&&(l.show=!1),"cross"===r&&(h=a.get("label.show"),l.show=null==h||h,o||(u=s.lineStyle=a.get("crossStyle"))&&A(l,u.textStyle))}return t.model.getModel("axisPointer",new ll(s,n,i))}(n,f,m,g,t,e):h).get("snap"),r=Dm(n.model),o=e||i||"category"===n.type,a=p.axesInfo[r]={key:r,axis:n,coordSys:c,axisPointerModel:h,triggerTooltip:e,involveSeries:o,snap:i,useHandle:Am(h),seriesModels:[]},d[r]=a,p.seriesInvolved|=o,null!=(s=function(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var o=t[r]||{};if(Cm(o[i+"AxisId"],n.id)||Cm(o[i+"AxisIndex"],n.componentIndex)||Cm(o[i+"AxisName"],n.name))return r}}(v,n))&&((l=y[s]||(y[s]={axesInfo:{}})).axesInfo[r]=a,l.mapper=v[s].mapper,a.linkGroup=l))}c.axisPointerEnabled&&(t=Dm(c.model),d=p.coordSysAxesInfo[t]={},f=(p.coordSysMap[t]=c).model.getModel("tooltip",o),Sm(c.getAxes(),Mm(r,!1,null)),c.getTooltipAxes&&o&&f.get("show")&&(e="axis"===f.get("trigger"),n="cross"===f.get("axisPointer.type"),i=c.getTooltipAxes(f.get("axisPointer.axis")),(e||n)&&Sm(i.baseAxes,Mm(r,!n||"cross",e)),n&&Sm(i.otherAxes,Mm(r,"cross",!1))))}),i.seriesInvolved&&function(r,t){t.eachSeries(function(n){var i=n.coordinateSystem,t=n.get("tooltip.trigger",!0),e=n.get("tooltip.show",!0);i&&"none"!==t&&!1!==t&&"item"!==t&&!1!==e&&!1!==n.get("axisPointer.show",!0)&&Sm(r.coordSysAxesInfo[Dm(i.model)],function(t){var e=t.axis;i.getAxis(e.dim)===e&&(t.seriesModels.push(n),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=n.getData().count())})},this)}(i,t),i}function Cm(t,e){return"all"===t||z(t)&&0<=w(t,e)||t===e}function Tm(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[Dm(t)]}function Am(t){return!!t.get("handle.show")}function Dm(t){return t.type+"||"+t.id}var km=Wd({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,n,i){var r,o,a,s,l,h,u,c;!this.axisPointerClass||(c=Tm(t))&&(r=c.axisPointerModel,o=c.axis.scale,a=r.option,s=r.get("status"),null!=(l=r.get("value"))&&(l=o.parse(l)),h=Am(r),null==s&&(a.status=h?"show":"hide"),(u=o.getExtent().slice())[0]>u[1]&&u.reverse(),(null==l||l>u[1])&&(l=u[1]),l<u[0]&&(l=u[0]),a.value=l,h&&(a.status=c.axis.scale.isBlank()?"hide":"show")),km.superApply(this,"render",arguments),Pm(this,t,0,n,0,!0)},updateAxisPointer:function(t,e,n,i){Pm(this,t,0,n,0,!1)},remove:function(t,e){var n=this._axisPointer;n&&n.remove(e),km.superApply(this,"remove",arguments)},dispose:function(t,e){Lm(this,e),km.superApply(this,"dispose",arguments)}});function Pm(t,e,n,i,r,o){var a,s,l=km.getAxisPointerClass(t.axisPointerClass);l&&((a=(s=Tm(e))&&s.axisPointerModel)?(t._axisPointer||(t._axisPointer=new l)).render(e,a,i,o):Lm(t,i))}function Lm(t,e,n){var i=t._axisPointer;i&&i.dispose(e,n),t._axisPointer=null}var Om=[];function Em(t,e,n){n=n||{};var i,r=t.coordinateSystem,o=e.axis,a={},s=o.getAxesOnZeroOf()[0],l=o.position,h=s?"onZero":l,u=o.dim,c=r.getRect(),d=[c.x,c.x+c.width,c.y,c.y+c.height],f={left:0,right:1,top:0,bottom:1,onZero:2},p=e.get("offset")||0,g="x"===u?[d[2]-p,d[3]+p]:[d[0]-p,d[1]+p];s&&(i=s.toGlobalCoord(s.dataToCoord(0)),g[f.onZero]=Math.max(Math.min(i,g[1]),g[0])),a.position=["y"===u?g[f[h]]:d[0],"x"===u?g[f[h]]:d[3]],a.rotation=Math.PI/2*("x"===u?0:1);a.labelDirection=a.tickDirection=a.nameDirection={top:-1,bottom:1,left:-1,right:1}[l],a.labelOffset=s?g[f[l]]-g[f.onZero]:0,e.get("axisTick.inside")&&(a.tickDirection=-a.tickDirection),G(n.labelInside,e.get("axisLabel.inside"))&&(a.labelDirection=-a.labelDirection);var m=e.get("axisLabel.rotate");return a.labelRotate="top"===h?-m:m,a.z2=1,a}km.registerAxisPointerClass=function(t,e){if(C&&Om[t])throw new Error("axisPointer "+t+" exists");Om[t]=e},km.getAxisPointerClass=function(t){return t&&Om[t]};var zm=["axisLine","axisTickLabel","axisName"],Nm=["splitArea","splitLine","minorSplitLine"],Rm=km.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(e,t,n,i){this.group.removeAll();var r,o,a,s=this._axisGroup;this._axisGroup=new _n,this.group.add(this._axisGroup),e.get("show")&&(o=Em(r=e.getCoordSysModel(),e),a=new fm(e,o),D(zm,a.add,a),this._axisGroup.add(a.getGroup()),D(Nm,function(t){e.get(t+".show")&&this["_"+t](e,r)},this),Ks(s,this._axisGroup,e),Rm.superCall(this,"render",e,t,n,i))},remove:function(){this.__splitAreaColors=null},_splitLine:function(t,e){var n=t.axis;if(!n.scale.isBlank())for(var i=t.getModel("splitLine"),r=i.getModel("lineStyle"),o=z(o=r.get("color"))?o:[o],a=e.coordinateSystem.getRect(),s=n.isHorizontal(),l=0,h=n.getTicksCoords({tickModel:i}),u=[],c=[],d=r.getLineStyle(),f=0;f<h.length;f++){var p=n.toGlobalCoord(h[f].coord);s?(u[0]=p,u[1]=a.y,c[0]=p,c[1]=a.y+a.height):(u[0]=a.x,u[1]=p,c[0]=a.x+a.width,c[1]=p);var g=l++%o.length,m=h[f].tickValue;this._axisGroup.add(new Ua({anid:null!=m?"line_"+h[f].tickValue:null,subPixelOptimize:!0,shape:{x1:u[0],y1:u[1],x2:c[0],y2:c[1]},style:A({stroke:o[g]},d),silent:!0}))}},_minorSplitLine:function(t,e){var n=t.axis,i=t.getModel("minorSplitLine").getModel("lineStyle"),r=e.coordinateSystem.getRect(),o=n.isHorizontal(),a=n.getMinorTicksCoords();if(a.length)for(var s=[],l=[],h=i.getLineStyle(),u=0;u<a.length;u++)for(var c=0;c<a[u].length;c++){var d=n.toGlobalCoord(a[u][c].coord);o?(s[0]=d,s[1]=r.y,l[0]=d,l[1]=r.y+r.height):(s[0]=r.x,s[1]=d,l[0]=r.x+r.width,l[1]=d),this._axisGroup.add(new Ua({anid:"minor_line_"+a[u][c].tickValue,subPixelOptimize:!0,shape:{x1:s[0],y1:s[1],x2:l[0],y2:l[1]},style:h,silent:!0}))}},_splitArea:function(t,e){!function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank()){var o=n.getModel("splitArea"),a=o.getModel("areaStyle"),s=a.get("color"),l=i.coordinateSystem.getRect(),h=r.getTicksCoords({tickModel:o,clamp:!0});if(h.length){var u=s.length,c=t.__splitAreaColors,d=tt(),f=0;if(c)for(var p=0;p<h.length;p++){var g=c.get(h[p].tickValue);if(null!=g){f=(g+(u-1)*p)%u;break}}for(var m=r.toGlobalCoord(h[0].coord),v=a.getAreaStyle(),s=z(s)?s:[s],p=1;p<h.length;p++){var y,_,x,w,b=r.toGlobalCoord(h[p].coord),m=r.isHorizontal()?(y=m,_=l.y,x=b-y,w=l.height,y+x):(y=l.x,_=m,x=l.width,_+(w=b-_)),S=h[p-1].tickValue;null!=S&&d.set(S,f),e.add(new Ga({anid:null!=S?"area_"+S:null,shape:{x:y,y:_,width:x,height:w},style:A({fill:s[f]},v),silent:!0})),f=(f+1)%u}t.__splitAreaColors=d}}}(this,this._axisGroup,t,e)}});function Bm(t,e){"outside"===t.textPosition&&(t.textPosition=e)}Rm.extend({type:"xAxis"}),Rm.extend({type:"yAxis"}),Wd({type:"grid",render:function(t){this.group.removeAll(),t.get("show")&&this.group.add(new Ga({shape:t.coordinateSystem.getRect(),style:A({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),Ed(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}),Bd(Zg("line","circle","line")),Rd(Ug("line")),zd(Xc.PROCESSOR.STATISTIC,{seriesType:"line",modifyOutputEnd:!0,reset:function(t){var e,n,i,r,o,a,s=t.getData(),l=t.get("sampling"),h=t.coordinateSystem;"cartesian2d"===h.type&&l&&(e=h.getBaseAxis(),n=h.getOtherAxis(e),i=e.getExtent(),r=Math.abs(i[1]-i[0]),1<(o=Math.round(s.count()/r))&&("string"==typeof l?a=Yg[l]:"function"==typeof l&&(a=l),a&&t.setData(s.downSample(s.mapDimension(n.dim),1/o,a,Xg))))}}),zu.extend({type:"series.__base_bar__",getInitialData:function(){return Pf(this.getSource(),this,{useEncodeDefaulter:!0})},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var n=e.dataToPoint(e.clampData(t)),i=this.getData(),r=i.getLayout("offset"),o=i.getLayout("size");return n[e.getBaseAxis().isHorizontal()?0:1]+=r+o/2,n}return[NaN,NaN]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod",itemStyle:{},emphasis:{}}}).extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect",getProgressive:function(){return!!this.get("large")&&this.get("progressive")},getProgressiveThreshold:function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return t<e&&(t=e),t},defaultOption:{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1}}});var Vm=$r([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),Fm={getBarItemStyle:function(t){var e,n=Vm(this,t);return!this.getBorderLineDash||(e=this.getBorderLineDash())&&(n.lineDash=e),n}},Hm=cs({type:"sausage",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=.5*(o-r),s=r+a,l=e.startAngle,h=e.endAngle,u=e.clockwise,c=Math.cos(l),d=Math.sin(l),f=Math.cos(h),p=Math.sin(h);(u?h-l<2*Math.PI:l-h<2*Math.PI)&&(t.moveTo(c*r+n,d*r+i),t.arc(c*s+n,d*s+i,a,-Math.PI+l,l,!u)),t.arc(n,i,o,l,h,!u),t.moveTo(f*o+n,p*o+i),t.arc(f*s+n,p*s+i,a,h-2*Math.PI,h-Math.PI,!u),0!==r&&(t.arc(n,i,r,h,l,u),t.moveTo(c*r+n,p*r+i)),t.closePath()}}),Wm=["itemStyle","barBorderWidth"],Gm=[0,0];k(ll.prototype,Fm),Zd({type:"bar",render:function(t,e,n){this._updateDrawMode(t);var i=t.get("coordinateSystem");return"cartesian2d"!==i&&"polar"!==i||(this._isLargeDraw?this._renderLarge(t,e,n):this._renderNormal(t,e,n)),this.group},incrementalPrepareRender:function(t){this._clear(),this._updateDrawMode(t)},incrementalRender:function(t,e){this._incrementalRenderLarge(t,e)},_updateDrawMode:function(t){var e=t.pipelineContext.large;(null==this._isLargeDraw||e^this._isLargeDraw)&&(this._isLargeDraw=e,this._clear())},_renderNormal:function(s){var l,h=this.group,u=s.getData(),c=this._data,d=s.coordinateSystem,t=d.getBaseAxis();"cartesian2d"===d.type?l=t.isHorizontal():"polar"===d.type&&(l="angle"===t.dim);var e,n,i,r,o,f=s.isAnimationEnabled()?s:null,p=s.get("clip",!0),g=(n=u,o=(e=d).getArea&&e.getArea(),"cartesian2d"===e.type&&("category"===(i=e.getBaseAxis()).type&&i.onBand||(r=n.getLayout("bandWidth"),i.isHorizontal()?(o.x-=r,o.width+=2*r):(o.y-=r,o.height+=2*r))),o);h.removeClipPath();function m(t){var e,n,i,r=$m[d.type](u,t),o=(n=l,i=r,new("polar"===(e=d).type?La:Ga)({shape:nv(n,i,e),silent:!0,z2:0}));return o.useStyle(_.getBarItemStyle()),"cartesian2d"===d.type&&o.setShape("r",x),w[t]=o}var v=s.get("roundCap",!0),y=s.get("showBackground",!0),_=s.getModel("backgroundStyle"),x=_.get("barBorderRadius")||0,w=[],b=this._backgroundEls||[];u.diff(c).add(function(t){var e=u.getItemModel(t),n=$m[d.type](u,t,e);if(y&&m(t),u.hasValue(t)){if(p)if(Xm[d.type](g,n))return void h.remove(i);var i=Ym[d.type](t,n,l,f,!1,v);u.setItemGraphicEl(t,i),h.add(i),Qm(i,u,t,e,n,s,l,"polar"===d.type)}}).update(function(t,e){var n,i,r=u.getItemModel(t),o=$m[d.type](u,t,r);y&&(0===b.length?n=m(e):((n=b[e]).useStyle(_.getBarItemStyle()),"cartesian2d"===d.type&&n.setShape("r",x),w[t]=n),i=$m[d.type](u,t),Xs(n,{shape:nv(l,i,d)},f,t));var a=c.getItemGraphicEl(e);if(u.hasValue(t)){if(p)if(Xm[d.type](g,o))return void h.remove(a);a?Xs(a,{shape:o},f,t):a=Ym[d.type](t,o,l,f,!0,v),u.setItemGraphicEl(t,a),h.add(a),Qm(a,u,t,r,o,s,l,"polar"===d.type)}else h.remove(a)}).remove(function(t){var e=c.getItemGraphicEl(t);"cartesian2d"===d.type?e&&jm(t,f,e):e&&qm(t,f,e)}).execute();var a=this._backgroundGroup||(this._backgroundGroup=new _n);a.removeAll();for(var S=0;S<w.length;++S)a.add(w[S]);h.add(a),this._backgroundEls=w,this._data=u},_renderLarge:function(t){this._clear(),tv(t,this.group);var e,n,i,r=t.get("clip",!0)?(e=t.coordinateSystem,n=!1,i=t,e?"polar"===e.type?Rg(e,n,i):"cartesian2d"===e.type?Ng(e,n,i):null:null):null;r?this.group.setClipPath(r):this.group.removeClipPath()},_incrementalRenderLarge:function(t,e){this._removeBackground(),tv(e,this.group,!0)},dispose:et,remove:function(t){this._clear(t)},_clear:function(e){var t=this.group,n=this._data;e&&e.get("animation")&&n&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],n.eachItemGraphicEl(function(t){("sector"===t.type?qm:jm)(t.dataIndex,e,t)})):t.removeAll(),this._data=null},_removeBackground:function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null}});var Zm=Math.max,Um=Math.min,Xm={cartesian2d:function(t,e){var n=e.width<0?-1:1,i=e.height<0?-1:1;n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height);var r=Zm(e.x,t.x),o=Um(e.x+e.width,t.x+t.width),a=Zm(e.y,t.y),s=Um(e.y+e.height,t.y+t.height);e.x=r,e.y=a,e.width=o-r,e.height=s-a;var l=e.width<0||e.height<0;return n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height),l},polar:function(t,e){var n=e.r0<=e.r?1:-1;n<0&&(i=e.r,e.r=e.r0,e.r0=i);var i=Um(e.r,t.r),r=Zm(e.r0,t.r0),o=(e.r=i)-(e.r0=r)<0;return n<0&&(i=e.r,e.r=e.r0,e.r0=i),o}},Ym={cartesian2d:function(t,e,n,i,r){var o,a,s=new Ga({shape:k({},e),z2:1});return s.name="item",i&&(o=n?"height":"width",a={},s.shape[o]=0,a[o]=e[o],el[r?"updateProps":"initProps"](s,{shape:a},i,t)),s},polar:function(t,e,n,i,r,o){var a,s,l=e.startAngle<e.endAngle,h=new(!n&&o?Hm:La)({shape:A({clockwise:l},e),z2:1});return h.name="item",i&&(a=n?"r":"endAngle",s={},h.shape[a]=n?0:e.startAngle,s[a]=e[a],el[r?"updateProps":"initProps"](h,{shape:s},i,t)),h}};function jm(t,e,n){n.style.text=null,Xs(n,{shape:{width:0}},e,t,function(){n.parent&&n.parent.remove(n)})}function qm(t,e,n){n.style.text=null,Xs(n,{shape:{r:n.shape.r0}},e,t,function(){n.parent&&n.parent.remove(n)})}var $m={cartesian2d:function(t,e,n){var i,r,o,a,s=t.getItemLayout(e),l=n?(i=s,r=n.get(Wm)||0,o=isNaN(i.width)?Number.MAX_VALUE:Math.abs(i.width),a=isNaN(i.height)?Number.MAX_VALUE:Math.abs(i.height),Math.min(r,o,a)):0,h=0<s.width?1:-1,u=0<s.height?1:-1;return{x:s.x+h*l/2,y:s.y+u*l/2,width:s.width-h*l,height:s.height-u*l}},polar:function(t,e){var n=t.getItemLayout(e);return{cx:n.cx,cy:n.cy,r0:n.r0,r:n.r,startAngle:n.startAngle,endAngle:n.endAngle}}};function Km(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}function Qm(t,e,n,i,r,o,a,s){var l=e.getItemVisual(n,"color"),h=e.getItemVisual(n,"opacity"),u=e.getVisual("borderColor"),c=i.getModel("itemStyle"),d=i.getModel("emphasis.itemStyle").getBarItemStyle();s||t.setShape("r",c.get("barBorderRadius")||0),t.useStyle(A({stroke:Km(r)?"none":u,fill:Km(r)?"none":l,opacity:h},c.getBarItemStyle()));var f=i.getShallow("cursor");f&&t.attr("cursor",f);var p,g,m,v,y,_;a?r.height:r.width;s||(p=t.style,v=l,y=o,_=n,Rs(p,g=d,(m=i).getModel("label"),m.getModel("emphasis.label"),{labelFetcher:y,labelDataIndex:_,defaultText:ag(y.getData(),_),isRectText:!0,autoColor:v}),Bm(p),Bm(g)),Km(r)&&(d.fill=d.stroke="none"),Os(t,d)}var Jm=ua.extend({type:"largeBar",shape:{points:[]},buildPath:function(t,e){for(var n=e.points,i=this.__startPoint,r=this.__baseDimIdx,o=0;o<n.length;o+=2)i[r]=n[o+r],t.moveTo(i[0],i[1]),t.lineTo(n[o],n[o+1])}});function tv(t,e,n){var i=t.getData(),r=[],o=i.getLayout("valueAxisHorizontal")?1:0;r[1-o]=i.getLayout("valueAxisStart");var a,s,l,h,u,c,d,f,p=i.getLayout("largeDataIndices"),g=i.getLayout("barWidth"),m=t.getModel("backgroundStyle");t.get("showBackground",!0)&&(a=i.getLayout("largeBackgroundPoints"),(s=[])[1-o]=i.getLayout("backgroundStart"),l=new Jm({shape:{points:a},incremental:!!n,__startPoint:s,__baseDimIdx:o,__largeDataIndices:p,__barWidth:g,silent:!0,z2:0}),h=l,c=i,d=(u=m).get("borderColor")||u.get("color"),f=u.getItemStyle(["color","borderColor"]),h.useStyle(f),h.style.fill=null,h.style.stroke=d,h.style.lineWidth=c.getLayout("barWidth"),e.add(l));var v,y,_,x,w,b=new Jm({shape:{points:i.getLayout("largePoints")},incremental:!!n,__startPoint:r,__baseDimIdx:o,__largeDataIndices:p,__barWidth:g});e.add(b),v=b,y=t,x=(_=i).getVisual("borderColor")||_.getVisual("color"),w=y.getModel("itemStyle").getItemStyle(["color","borderColor"]),v.useStyle(w),v.style.fill=null,v.style.stroke=x,v.style.lineWidth=_.getLayout("barWidth"),b.seriesIndex=t.seriesIndex,t.get("silent")||(b.on("mousedown",ev),b.on("mousemove",ev))}var ev=rc(function(t){var e=function(t,e,n){var i=t.__baseDimIdx,r=1-i,o=t.shape.points,a=t.__largeDataIndices,s=Math.abs(t.__barWidth/2),l=t.__startPoint[r];Gm[0]=e,Gm[1]=n;for(var h=Gm[i],u=Gm[1-i],c=h-s,d=h+s,f=0,p=o.length/2;f<p;f++){var g=2*f,m=o[g+i],v=o[g+r];if(c<=m&&m<=d&&(l<=v?l<=u&&u<=v:v<=u&&u<=l))return a[f]}return-1}(this,t.offsetX,t.offsetY);this.dataIndex=0<=e?e:null},30,!1);function nv(t,e,n){var i="polar"===n.type,r=i?n.getArea():n.grid.getRect();return i?{cx:r.cx,cy:r.cy,r0:t?r.r0:e.r0,r:t?r.r:e.r,startAngle:t?e.startAngle:0,endAngle:t?e.endAngle:2*Math.PI}:{x:t?e.x:r.x,y:t?r.y:e.y,width:t?e.width:r.width,height:t?r.height:e.height}}Rd(Xc.VISUAL.LAYOUT,O(function(t,e){var n=qf(t,e),C=$f(n),T={};D(n,function(t){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),r=Yf(t),o=C[jf(i)][r],a=o.offset,s=o.width,l=n.getOtherAxis(i),h=t.get("barMinHeight")||0;T[r]=T[r]||[],e.setLayout({bandWidth:o.bandWidth,offset:a,size:s});for(var u=e.mapDimension(l.dim),c=e.mapDimension(i.dim),d=Df(e,u),f=l.isHorizontal(),p=ep(0,l),g=0,m=e.count();g<m;g++){var v,y,_,x,w,b=e.get(u,g),S=e.get(c,g),M=0<=b?"p":"n",I=p;d&&(T[r][S]||(T[r][S]={p:p,n:p}),I=T[r][S][M]),f?(v=I,y=(w=n.dataToPoint([b,S]))[1]+a,_=w[0]-p,x=s,Math.abs(_)<h&&(_=(_<0?-1:1)*h),isNaN(_)||d&&(T[r][S][M]+=_)):(v=(w=n.dataToPoint([S,b]))[0]+a,y=I,_=s,x=w[1]-p,Math.abs(x)<h&&(x=(x<=0?-1:1)*h),isNaN(x)||d&&(T[r][S][M]+=x)),e.setItemLayout(g,{x:v,y:y,width:_,height:x})}},this)},"bar")),Rd(Xc.VISUAL.PROGRESSIVE_LAYOUT,Qf),Bd({seriesType:"bar",reset:function(t){t.getData().setVisual("legendSymbol","roundRect")}});var iv={updateSelectedMap:function(t){this._targetList=z(t)?t.slice():[],this._selectTargetMap=M(t||[],function(t,e){return t.set(e.name,e),t},tt())},select:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);"single"===this.get("selectedMode")&&this._selectTargetMap.each(function(t){t.selected=!1}),n&&(n.selected=!0)},unSelect:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);n&&(n.selected=!1)},toggleSelected:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);if(null!=n)return this[n.selected?"unSelect":"select"](t,e),n.selected},isSelected:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return n&&n.selected}};function rv(n,e){this.getAllNames=function(){var t=e();return t.mapArray(t.getName)},this.containName=function(t){return 0<=e().indexOfName(t)},this.indexOfName=function(t){return n().indexOfName(t)},this.getItemVisual=function(t,e){return n().getItemVisual(t,e)}}var ov=Gd({type:"series.pie",init:function(t){ov.superApply(this,"init",arguments),this.legendVisualProvider=new rv(L(this.getData,this),L(this.getRawData,this)),this.updateSelectedMap(this._createSelectableList()),this._defaultLabelLine(t)},mergeOption:function(t){ov.superCall(this,"mergeOption",t),this.updateSelectedMap(this._createSelectableList())},getInitialData:function(){return function(t,e,n){e=z(e)?{coordDimensions:e}:k({},e);var i=t.getSource(),r=Mf(i,e),o=new hf(r,t);return o.initData(i,n),o}(this,{coordDimensions:["value"],encodeDefaulter:O(Th,this)})},_createSelectableList:function(){for(var t=this.getRawData(),e=t.mapDimension("value"),n=[],i=0,r=t.count();i<r;i++)n.push({name:t.getName(i),value:t.get(e,i),selected:function(t,e,n){if(t){var i=t.getProvider().getSource().sourceFormat;if(i===dh||i===ph){var r=t.getRawDataItem(e);return i!==dh||B(r)||(r=null),r?r[n]:void 0}}}(t,i,"selected")});return n},getDataParams:function(t){var e=this.getData(),n=ov.superCall(this,"getDataParams",t),i=[];return e.each(e.mapDimension("value"),function(t){i.push(t)}),n.percent=wl(i,t,e.hostModel.get("percentPrecision")),n.$vars.push("percent"),n},_defaultLabelLine:function(t){Tr(t,"labelLine",["show"]);var e=t.labelLine,n=t.emphasis.labelLine;e.show=e.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,minShowLabelAngle:0,selectedOffset:10,hoverOffset:10,avoidLabelOverlap:!0,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:!1,show:!0,position:"outer",alignTo:"none",margin:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1},animationType:"expansion",animationTypeUpdate:"transition",animationEasing:"cubicOut"}});function av(t,e,n,i){var r=e.getData(),o=this.dataIndex,a=r.getName(o),s=e.get("selectedOffset");i.dispatchAction({type:"pieToggleSelect",from:t,name:a,seriesId:e.id}),r.each(function(t){sv(r.getItemGraphicEl(t),r.getItemLayout(t),e.isSelected(r.getName(t)),s,n)})}function sv(t,e,n,i,r){var o=(e.startAngle+e.endAngle)/2,a=n?i:0,s=[Math.cos(o)*a,Math.sin(o)*a];r?t.animate().when(200,{position:s}).start("bounceOut"):t.attr("position",s)}function lv(t,e){_n.call(this);var n=new La({z2:2}),i=new Ra,r=new Aa;this.add(n),this.add(i),this.add(r),this.updateData(t,e,!0)}S(ov,iv);var hv=lv.prototype;hv.updateData=function(t,e,n){var i=this.childAt(0),r=this.childAt(1),o=this.childAt(2),a=t.hostModel,s=t.getItemModel(e),l=t.getItemLayout(e),h=k({},l);h.label=null;var u=a.getShallow("animationTypeUpdate");n?(i.setShape(h),"scale"===a.getShallow("animationType")?(i.shape.r=l.r0,Ys(i,{shape:{r:l.r}},a,e)):(i.shape.endAngle=l.startAngle,Xs(i,{shape:{endAngle:l.endAngle}},a,e))):"expansion"===u?i.setShape(h):Xs(i,{shape:h},a,e);var c=t.getItemVisual(e,"color");i.useStyle(A({lineJoin:"bevel",fill:c},s.getModel("itemStyle").getItemStyle())),i.hoverStyle=s.getModel("emphasis.itemStyle").getItemStyle();var d=s.getShallow("cursor");d&&i.attr("cursor",d),sv(this,t.getItemLayout(e),a.isSelected(t.getName(e)),a.get("selectedOffset"),a.get("animation"));var f=!n&&"transition"===u;this._updateLabel(t,e,f),this.highDownOnUpdate=a.get("silent")?null:function(t,e){var n=a.isAnimationEnabled()&&s.get("hoverAnimation");"emphasis"===e?(r.ignore=r.hoverIgnore,o.ignore=o.hoverIgnore,n&&(i.stopAnimation(!0),i.animateTo({shape:{r:l.r+a.get("hoverOffset")}},300,"elasticOut"))):(r.ignore=r.normalIgnore,o.ignore=o.normalIgnore,n&&(i.stopAnimation(!0),i.animateTo({shape:{r:l.r}},300,"elasticOut")))},Os(this)},hv._updateLabel=function(t,e,n){var i,r,o,a,s,l,h,u=this.childAt(1),c=this.childAt(2),d=t.hostModel,f=t.getItemModel(e),p=t.getItemLayout(e).label,g=t.getItemVisual(e,"color");!p||isNaN(p.x)||isNaN(p.y)?c.ignore=c.normalIgnore=c.hoverIgnore=u.ignore=u.normalIgnore=u.hoverIgnore=!0:(i={points:p.linePoints||[[p.x,p.y],[p.x,p.y],[p.x,p.y]]},r={x:p.x,y:p.y},n?(Xs(u,{shape:i},d,e),Xs(c,{style:r},d,e)):(u.attr({shape:i}),c.attr({style:r})),c.attr({rotation:p.rotation,origin:[p.x,p.y],z2:10}),o=f.getModel("label"),a=f.getModel("emphasis.label"),s=f.getModel("labelLine"),l=f.getModel("emphasis.labelLine"),g=t.getItemVisual(e,"color"),Rs(c.style,c.hoverStyle={},o,a,{labelFetcher:t.hostModel,labelDataIndex:e,defaultText:p.text,autoColor:g,useInsideStyle:!!p.inside},{textAlign:p.textAlign,textVerticalAlign:p.verticalAlign,opacity:t.getItemVisual(e,"opacity")}),c.ignore=c.normalIgnore=!o.get("show"),c.hoverIgnore=!a.get("show"),u.ignore=u.normalIgnore=!s.get("show"),u.hoverIgnore=!l.get("show"),u.setStyle({stroke:g,opacity:t.getItemVisual(e,"opacity")}),u.setStyle(s.getModel("lineStyle").getLineStyle()),u.hoverStyle=l.getModel("lineStyle").getLineStyle(),(h=s.get("smooth"))&&!0===h&&(h=.4),u.setShape({smooth:h}))},b(lv,_n);ju.extend({type:"pie",init:function(){var t=new _n;this._sectorGroup=t},render:function(t,e,n,i){if(!i||i.from!==this.uid){var r=t.getData(),o=this._data,a=this.group,s=e.get("animation"),l=!o,h=t.get("animationType"),u=t.get("animationTypeUpdate"),c=O(av,this.uid,t,s,n),d=t.get("selectedMode");if(r.diff(o).add(function(t){var e=new lv(r,t);l&&"scale"!==h&&e.eachChild(function(t){t.stopAnimation(!0)}),d&&e.on("click",c),r.setItemGraphicEl(t,e),a.add(e)}).update(function(t,e){var n=o.getItemGraphicEl(e);l||"transition"===u||n.eachChild(function(t){t.stopAnimation(!0)}),n.updateData(r,t),n.off("click"),d&&n.on("click",c),a.add(n),r.setItemGraphicEl(t,n)}).remove(function(t){var e=o.getItemGraphicEl(t);a.remove(e)}).execute(),s&&0<r.count()&&(l?"scale"!==h:"transition"!==u)){for(var f=r.getItemLayout(0),p=1;isNaN(f.startAngle)&&p<r.count();++p)f=r.getItemLayout(p);var g=Math.max(n.getWidth(),n.getHeight())/2,m=L(a.removeClipPath,a);a.setClipPath(this._createClipPath(f.cx,f.cy,g,f.startAngle,f.clockwise,m,t,l))}else a.removeClipPath();this._data=r}},dispose:function(){},_createClipPath:function(t,e,n,i,r,o,a,s){var l=new La({shape:{cx:t,cy:e,r0:0,r:n,startAngle:i,endAngle:i,clockwise:r}});return(s?Ys:Xs)(l,{shape:{endAngle:i+(r?1:-1)*Math.PI*2}},a,o),l},containPoint:function(t,e){var n=e.getData().getItemLayout(0);if(n){var i=t[0]-n.cx,r=t[1]-n.cy,o=Math.sqrt(i*i+r*r);return o<=n.r&&o>=n.r0}}});var uv=Math.PI/180;function cv(r,t,e,n,i,o,a,s,l,h){function u(t,e){for(var n=t;0<=n&&!(r[n].y-e<l)&&(r[n].y-=e,!(0<n&&r[n].y>r[n-1].y+r[n-1].height));n--);}function c(t,e,n,i,r,o){for(var a,s,l,h,u=e?Number.MAX_VALUE:0,c=0,d=t.length;c<d;c++){"none"===t[c].labelAlignTo&&(a=Math.abs(t[c].y-i),s=t[c].len,l=t[c].len2,h=a<r+s?Math.sqrt((r+s+l)*(r+s+l)-a*a):Math.abs(t[c].x-n),e&&u<=h&&(h=u-10),!e&&h<=u&&(h=u+10),t[c].x=n+h*o,u=h)}}r.sort(function(t,e){return t.y-e.y});for(var d,f,p=0,g=r.length,m=[],v=[],y=0;y<g;y++){"outer"===r[y].position&&"labelLine"===r[y].labelAlignTo&&(f=r[y].x-h,r[y].linePoints[1][0]+=f,r[y].x=h),(d=r[y].y-p)<0&&function(t,e,n){for(var i=t;i<e&&!(r[i].y+n>l+a);i++)if(r[i].y+=n,t<i&&i+1<e&&r[i+1].y>r[i].y+r[i].height)return u(i,n/2);u(e-1,n/2)}(y,g,-d),p=r[y].y+r[y].height}a-p<0&&u(g-1,p-a);for(y=0;y<g;y++)r[y].y>=e?v.push(r[y]):m.push(r[y]);c(m,!1,t,e,n,i),c(v,!0,t,e,n,i)}function dv(t){return"center"===t.position}function fv(k,P,L,t,O,e){var E,z,N=k.getData(),R=[],B=!1,V=(k.get("minShowLabelAngle")||0)*uv;N.each(function(t){var e,n,i,r,o,a,s,l,h,u,c,d,f,p,g,m,v,y=N.getItemLayout(t),_=N.getItemModel(t),x=_.getModel("label"),w=x.get("position")||_.get("emphasis.label.position"),b=x.get("distanceToLabelLine"),S=x.get("alignTo"),M=gl(x.get("margin"),L),I=x.get("bleedMargin"),C=x.getFont(),T=_.getModel("labelLine"),A=gl(A=T.get("length"),L),D=gl(D=T.get("length2"),L);y.angle<V||(e=(y.startAngle+y.endAngle)/2,n=Math.cos(e),i=Math.sin(e),E=y.cx,z=y.cy,o=ii(r=k.getFormattedLabel(t,"normal")||N.getName(t),C,g,"top"),a="inside"===w||"inner"===w,g="center"===w?(d=y.cx,f=y.cy,"center"):(d=(s=(a?(y.r+y.r0)/2*n:y.r*n)+E)+3*n,f=(l=(a?(y.r+y.r0)/2*i:y.r*i)+z)+3*i,a||(h=s+n*(A+P-y.r),u=l+i*(A+P-y.r),c=h+(n<0?-1:1)*D,d="edge"===S?n<0?O+M:O+L-M:c+(n<0?-b:b),p=[[s,l],[h,f=u],[c,u]]),a?"center":"edge"===S?0<n?"right":"left":0<n?"left":"right"),v="number"==typeof(m=x.get("rotate"))?m*(Math.PI/180):m?n<0?-e+Math.PI:-e:0,B=!!v,y.label={x:d,y:f,position:w,height:o.height,len:A,len2:D,linePoints:p,textAlign:g,verticalAlign:"middle",rotation:v,inside:a,labelDistance:b,labelAlignTo:S,labelMargin:M,bleedMargin:I,textRect:o,text:r,font:C},a||R.push(y.label))}),!B&&k.get("avoidLabelOverlap")&&function(t,e,n,i,r,o,a,s){for(var l=[],h=[],u=Number.MAX_VALUE,c=-Number.MAX_VALUE,d=0;d<t.length;d++)dv(t[d])||(t[d].x<e?(u=Math.min(u,t[d].x),l.push(t[d])):(c=Math.max(c,t[d].x),h.push(t[d])));for(cv(h,e,n,i,1,0,o,0,s,c),cv(l,e,n,i,-1,0,o,0,s,u),d=0;d<t.length;d++){var f,p,g,m,v,y=t[d];dv(y)||(f=y.linePoints)&&(p="edge"===y.labelAlignTo,g=y.textRect.width,(m=p?y.x<e?f[2][0]-y.labelDistance-a-y.labelMargin:a+r-y.labelMargin-f[2][0]-y.labelDistance:y.x<e?y.x-a-y.bleedMargin:a+r-y.x-y.bleedMargin)<y.textRect.width&&(y.text=si(y.text,m,y.font),"edge"===y.labelAlignTo&&(g=ni(y.text,y.font))),v=f[1][0]-f[2][0],p?y.x<e?f[2][0]=a+y.labelMargin+g+y.labelDistance:f[2][0]=a+r-y.labelMargin-g-y.labelDistance:(y.x<e?f[2][0]=y.x+y.labelDistance:f[2][0]=y.x-y.labelDistance,f[1][0]=f[2][0]+v),f[1][1]=f[2][1]=y.y)}}(R,E,z,P,L,t,O,e)}var pv=2*Math.PI,gv=Math.PI/180;var mv,vv;mv="pie",D([{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}],function(o){o.update="updateView",Nd(o,function(t,e){var r={};return e.eachComponent({mainType:"series",subType:mv,query:t},function(n){n[o.method]&&n[o.method](t.name,t.dataIndex);var i=n.getData();i.each(function(t){var e=i.getName(t);r[e]=n.isSelected(e)||!1})}),{name:t.name,selected:r,seriesId:t.seriesId}})}),Bd((vv="pie",{getTargetSeries:function(t){var e={},n=tt();return t.eachSeriesByType(vv,function(t){t.__paletteScope=e,n.set(t.uid,t)}),n},reset:function(s){var l=s.getRawData(),h={},u=s.getData();u.each(function(t){var e=u.getRawIndex(t);h[e]=t}),l.each(function(t){var e,n,i,r=h[t],o=null!=r&&u.getItemVisual(r,"color",!0),a=null!=r&&u.getItemVisual(r,"borderColor",!0);o&&a||(e=l.getItemModel(t)),o||(n=e.get("itemStyle.color")||s.getColorFromPalette(l.getName(t)||t+"",s.__paletteScope,l.count()),null!=r&&u.setItemVisual(r,"color",n)),a||(i=e.get("itemStyle.borderColor"),null!=r&&u.setItemVisual(r,"borderColor",i))})}})),Rd(O(function(t,e,A){e.eachSeriesByType(t,function(t){var e,r=t.getData(),n=r.mapDimension("value"),o=(e=A,Kl(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})),i=t.get("center"),a=t.get("radius");z(a)||(a=[0,a]),z(i)||(i=[i,i]);var s=gl(o.width,A.getWidth()),l=gl(o.height,A.getHeight()),h=Math.min(s,l),u=gl(i[0],s)+o.x,c=gl(i[1],l)+o.y,d=gl(a[0],h/2),f=gl(a[1],h/2),p=-t.get("startAngle")*gv,g=t.get("minAngle")*gv,m=0;r.each(n,function(t){isNaN(t)||m++});var v=r.getSum(n),y=Math.PI/(v||m)*2,_=t.get("clockwise"),x=t.get("roseType"),w=t.get("stillShowZeroSum"),b=r.getDataExtent(n);b[0]=0;var S,M=pv,I=0,C=p,T=_?1:-1;r.each(n,function(t,e){var n,i;isNaN(t)?r.setItemLayout(e,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:_,cx:u,cy:c,r0:d,r:x?NaN:f,viewRect:o}):((n="area"!==x?0===v&&w?y:t*y:pv/m)<g?M-=n=g:I+=t,i=C+T*n,r.setItemLayout(e,{angle:n,startAngle:C,endAngle:i,clockwise:_,cx:u,cy:c,r0:d,r:x?pl(t,b,[d,f]):f,viewRect:o}),C=i)}),M<pv&&m&&(M<=.001?(S=pv/m,r.each(n,function(t,e){var n;isNaN(t)||((n=r.getItemLayout(e)).angle=S,n.startAngle=p+T*e*S,n.endAngle=p+T*(e+1)*S)})):(y=M/I,C=p,r.each(n,function(t,e){var n,i;isNaN(t)||(i=(n=r.getItemLayout(e)).angle===g?g:t*y,n.startAngle=C,n.endAngle=C+T*i,C+=T*i)}))),fv(t,f,o.width,o.height,o.x,o.y)})},"pie")),zd({seriesType:"pie",reset:function(t,e){var i,r=e.findComponents({mainType:"legend"});r&&r.length&&(i=t.getData()).filterSelf(function(t){for(var e=i.getName(t),n=0;n<r.length;n++)if(!r[n].isSelected(e))return!1;return!0})}}),zu.extend({type:"series.scatter",dependencies:["grid","polar","geo","singleAxis","calendar"],getInitialData:function(){return Pf(this.getSource(),this,{useEncodeDefaulter:!0})},brushSelector:"point",getProgressive:function(){var t=this.option.progressive;return null==t?this.option.large?5e3:this.get("progressive"):t},getProgressiveThreshold:function(){var t=this.option.progressiveThreshold;return null==t?this.option.large?1e4:this.get("progressiveThreshold"):t},defaultOption:{coordinateSystem:"cartesian2d",zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},clip:!0}});var yv=cs({shape:{points:null},symbolProxy:null,softClipShape:null,buildPath:function(t,e){var n=e.points,i=e.size,r=this.symbolProxy,o=r.shape;if(!((t.getContext?t.getContext():t)&&i[0]<4))for(var a=0;a<n.length;){var s=n[a++],l=n[a++];isNaN(s)||isNaN(l)||this.softClipShape&&!this.softClipShape.contain(s,l)||(o.x=s-i[0]/2,o.y=l-i[1]/2,o.width=i[0],o.height=i[1],r.buildPath(t,o,!0))}},afterBrush:function(t){var e=this.shape,n=e.points,i=e.size;if(i[0]<4){this.setTransform(t);for(var r=0;r<n.length;){var o=n[r++],a=n[r++];isNaN(o)||isNaN(a)||this.softClipShape&&!this.softClipShape.contain(o,a)||t.fillRect(o-i[0]/2,a-i[1]/2,i[0],i[1])}this.restoreTransform(t)}},findDataIndex:function(t,e){for(var n=this.shape,i=n.points,r=n.size,o=Math.max(r[0],4),a=Math.max(r[1],4),s=i.length/2-1;0<=s;s--){var l=2*s,h=i[l]-o/2,u=i[1+l]-a/2;if(h<=t&&u<=e&&t<=h+o&&e<=u+a)return s}return-1}});function _v(){this.group=new _n}var xv=_v.prototype;xv.isPersistent=function(){return!this._incremental},xv.updateData=function(t,e){this.group.removeAll();var n=new yv({rectHover:!0,cursor:"default"});n.setShape({points:t.getLayout("symbolPoints")}),this._setCommon(n,t,!1,e),this.group.add(n),this._incremental=null},xv.updateLayout=function(t){var i;this._incremental||(i=t.getLayout("symbolPoints"),this.group.eachChild(function(t){var e,n;null!=t.startIndex&&(e=2*(t.endIndex-t.startIndex),n=4*t.startIndex*2,i=new Float32Array(i.buffer,n,e)),t.setShape("points",i)}))},xv.incrementalPrepareUpdate=function(t){this.group.removeAll(),this._clearIncremental(),2e6<t.count()?(this._incremental||(this._incremental=new ts({silent:!0})),this.group.add(this._incremental)):this._incremental=null},xv.incrementalUpdate=function(t,e,n){var i;this._incremental?(i=new yv,this._incremental.addDisplayable(i,!0)):((i=new yv({rectHover:!0,cursor:"default",startIndex:t.start,endIndex:t.end})).incremental=!0,this.group.add(i)),i.setShape({points:e.getLayout("symbolPoints")}),this._setCommon(i,e,!!this._incremental,n)},xv._setCommon=function(n,t,e,i){var r=t.hostModel;i=i||{};var o=t.getVisual("symbolSize");n.setShape("size",o instanceof Array?o:[o,o]),n.softClipShape=i.clipShape||null,n.symbolProxy=Np(t.getVisual("symbol"),0,0,0,0),n.setColor=n.symbolProxy.setColor;var a=n.shape.size[0]<4;n.useStyle(r.getModel("itemStyle").getItemStyle(a?["color","shadowBlur","shadowColor"]:["color"]));var s=t.getVisual("color");s&&n.setColor(s),e||(n.seriesIndex=r.seriesIndex,n.on("mousemove",function(t){n.dataIndex=null;var e=n.findDataIndex(t.offsetX,t.offsetY);0<=e&&(n.dataIndex=e+(n.startIndex||0))}))},xv.remove=function(){this._clearIncremental(),this._incremental=null,this.group.removeAll()},xv._clearIncremental=function(){var t=this._incremental;t&&t.clearDisplaybles()},Zd({type:"scatter",render:function(t){var e=t.getData();this._updateSymbolDraw(e,t).updateData(e,{clipShape:this._getClipShape(t)}),this._finished=!0},incrementalPrepareRender:function(t){var e=t.getData();this._updateSymbolDraw(e,t).incrementalPrepareUpdate(e),this._finished=!1},incrementalRender:function(t,e){this._symbolDraw.incrementalUpdate(t,e.getData(),{clipShape:this._getClipShape(e)}),this._finished=t.end===e.getData().count()},updateTransform:function(t){var e=t.getData();if(this.group.dirty(),!this._finished||1e4<e.count()||!this._symbolDraw.isPersistent())return{update:!0};var n=Ug().reset(t);n.progress&&n.progress({start:0,end:e.count()},e),this._symbolDraw.updateLayout(e)},_getClipShape:function(t){var e=t.coordinateSystem,n=e&&e.getArea&&e.getArea();return t.get("clip",!0)?n:null},_updateSymbolDraw:function(t,e){var n=this._symbolDraw,i=e.pipelineContext.large;return n&&i===this._isLargeDraw||(n&&n.remove(),n=this._symbolDraw=new(i?_v:vg),this._isLargeDraw=i,this.group.removeAll()),this.group.add(n.group),n},remove:function(){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},dispose:function(){}}),Bd(Zg("scatter","circle")),Rd(Ug("scatter"));var wv={path:null,compoundPath:null,group:_n,image:Fi,text:Aa};Ed(function(t){var e=t.graphic;z(e)?e[0]&&e[0].elements?t.graphic=[t.graphic[0]]:t.graphic=[{elements:e}]:e&&!e.elements&&(t.graphic=[{elements:[e]}])});var bv=Hd({type:"graphic",defaultOption:{elements:[],parentId:null},_elOptionsToUpdate:null,mergeOption:function(t){var e=this.option.elements;this.option.elements=null,bv.superApply(this,"mergeOption",arguments),this.option.elements=e},optionUpdated:function(t,e){var n=this.option,i=(e?n:t).elements,r=n.elements=e?[]:n.elements,o=[];this._flatten(i,o);var a=kr(r,o);Pr(a);var s=this._elOptionsToUpdate=[];D(a,function(t,e){var n=t.option;C&&j(B(n)||t.exist,"Empty graphic option definition"),n&&(s.push(n),function(t,e){var n=t.exist;{var i;e.id=t.keyInfo.id,!e.type&&n&&(e.type=n.type),null==e.parentId&&((i=e.parentOption)?e.parentId=i.id:n&&(e.parentId=n.parentId))}e.parentOption=null}(t,n),function(t,e,n){var i=k({},n),r=t[e],o=n.$action||"merge";{var a;"merge"===o?r?(C&&j(!(a=n.type)||r.type===a,'Please set $action: "replace" to change `type`'),m(r,i,!0),Jl(r,i,{ignoreSize:!0}),eh(n,r)):t[e]=i:"replace"===o?t[e]=i:"remove"===o&&r&&(t[e]=null)}}(r,e,n),function(t,e){if(!t)return;t.hv=e.hv=[Iv(e,["left","right"]),Iv(e,["top","bottom"])],"group"===t.type&&(null==t.width&&(t.width=e.width=0),null==t.height&&(t.height=e.height=0))}(r[e],n))},this);for(var l=r.length-1;0<=l;l--)null==r[l]?r.splice(l,1):delete r[l].$action},_flatten:function(t,n,i){D(t,function(t){var e;t&&(i&&(t.parentOption=i),n.push(t),e=t.children,"group"===t.type&&e&&this._flatten(e,n,t),delete t.children)},this)},useElOptionsToUpdate:function(){var t=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,t}});function Sv(t,e,n,i){var r=n.type;C&&j(r,"graphic type MUST be set");var o=wv.hasOwnProperty(r)?wv[r]:fs(r);C&&j(o,"graphic type can not be found");var a=new o(n);e.add(a),i.set(t,a),a.__ecGraphicId=t}function Mv(t,e){var n=t&&t.parent;n&&("group"===t.type&&t.traverse(function(t){Mv(t,e)}),e.removeKey(t.__ecGraphicId),n.remove(t))}function Iv(e,t){var n;return D(t,function(t){null!=e[t]&&"auto"!==e[t]&&(n=!0)}),n}Wd({type:"graphic",init:function(){this._elMap=tt(),this._lastGraphicModel},render:function(t,e,n){t!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=t,this._updateElements(t),this._relocate(t,n)},_updateElements:function(u){var c,d,t=u.useElOptionsToUpdate();t&&(c=this._elMap,d=this.group,D(t,function(t){var e=t.$action,n=t.id,i=c.get(n),r=t.parentId,o=null!=r?c.get(r):d,a=t.style;"text"===t.type&&a&&(t.hv&&t.hv[1]&&(a.textVerticalAlign=a.textBaseline=null),!a.hasOwnProperty("textFill")&&a.fill&&(a.textFill=a.fill),!a.hasOwnProperty("textStroke")&&a.stroke&&(a.textStroke=a.stroke));var s,l=(s=k({},s=t),D(["id","parentId","$action","hv","bounding"].concat(Yl),function(t){delete s[t]}),s);C&&i&&j(o===i.parent,"Changing parent is not supported."),e&&"merge"!==e?"replace"===e?(Mv(i,c),Sv(n,o,l,c)):"remove"===e&&Mv(i,c):i?i.attr(l):Sv(n,o,l,c);var h=c.get(n);h&&(h.__ecGraphicWidthOption=t.width,h.__ecGraphicHeightOption=t.height,function(t,e){var n=t.eventData;t.silent||t.ignore||n||(n=t.eventData={componentType:"graphic",componentIndex:e.componentIndex,name:t.name});n&&(n.info=t.info)}(h,u))}))},_relocate:function(t,e){for(var n=t.option.elements,i=this.group,r=this._elMap,o=e.getWidth(),a=e.getHeight(),s=0;s<n.length;s++){var l,h=n[s];(u=r.get(h.id))&&u.isGroup&&(l=(c=u.parent)===i,u.__ecGraphicWidth=gl(u.__ecGraphicWidthOption,l?o:c.__ecGraphicWidth)||0,u.__ecGraphicHeight=gl(u.__ecGraphicHeightOption,l?a:c.__ecGraphicHeight)||0)}for(s=n.length-1;0<=s;s--){var u,c,h=n[s];(u=r.get(h.id))&&Ql(u,h,(c=u.parent)===i?{width:o,height:a}:{width:c.__ecGraphicWidth,height:c.__ecGraphicHeight},null,{hv:h.hv,boundingMode:h.bounding})}},_clear:function(){var e=this._elMap;e.each(function(t){Mv(t,e)}),this._elMap=tt()},dispose:function(){this._clear()}});function Cv(t,e){var n,i=[],r=t.seriesIndex;if(null==r||!(n=e.getSeriesByIndex(r)))return{point:[]};var o=n.getData(),a=Er(o,t);if(null==a||a<0||z(a))return{point:[]};var s,l=o.getItemGraphicEl(a),h=n.coordinateSystem;return n.getTooltipPosition?i=n.getTooltipPosition(a)||[]:h&&h.dataToPoint?i=h.dataToPoint(o.getValues(P(h.dimensions,function(t){return o.mapDimension(t)}),a,!0))||[]:l&&((s=l.getBoundingRect().clone()).applyTransform(l.transform),i=[s.x+s.width/2,s.y+s.height/2]),{point:i,el:l}}var Tv=D,Av=O,Dv=zr();function kv(t,e,n,i,r){var o,l,a,h,u,c,d,f,p,s,g,m=t.axis;!m.scale.isBlank()&&m.containData(e)&&(t.involveSeries?(g=(o=(l=e,h=(a=t).axis,u=h.dim,c=l,d=[],f=Number.MAX_VALUE,p=-1,Tv(a.seriesModels,function(e,t){var n,i,r=e.getData().mapDimension(u,!0);if(e.getAxisTooltipData)var o=e.getAxisTooltipData(r,l,h),a=o.dataIndices,s=o.nestestValue;else{if(!(a=e.getData().indicesOfNearest(r[0],l,"category"===h.type?.5:null)).length)return;s=e.getData().get(r[0],a[0])}null!=s&&isFinite(s)&&(n=l-s,(i=Math.abs(n))<=f&&((i<f||0<=n&&p<0)&&(f=i,p=n,c=s,d.length=0),Tv(a,function(t){d.push({seriesIndex:e.seriesIndex,dataIndexInside:t,dataIndex:e.getData().getRawIndex(t)})})))}),{payloadBatch:d,snapToValue:c})).snapToValue,(s=o.payloadBatch)[0]&&null==r.seriesIndex&&k(r,s[0]),!i&&t.snap&&m.containData(g)&&null!=g&&(e=g),n.showPointer(t,e,s,r),n.showTooltip(t,o,g)):n.showPointer(t,e))}function Pv(t,e,n,i){t[e.key]={value:n,payloadBatch:i}}function Lv(t,e,n,i){var r,o,a,s=n.payloadBatch,l=e.axis,h=l.model,u=e.axisPointerModel;e.triggerTooltip&&s.length&&(o=Dm(r=e.coordSys.model),(a=t.map[o])||(a=t.map[o]={coordSysId:r.id,coordSysIndex:r.componentIndex,coordSysType:r.type,coordSysMainType:r.mainType,dataByAxis:[]},t.list.push(a)),a.dataByAxis.push({axisDim:l.dim,axisIndex:h.componentIndex,axisType:h.type,axisId:h.id,value:i,valueLabelOpt:{precision:u.get("label.precision"),formatter:u.get("label.formatter")},seriesDataIndices:s.slice()}))}function Ov(t){var e=t.axis.model,n={},i=n.axisDim=t.axis.dim;return n.axisIndex=n[i+"AxisIndex"]=e.componentIndex,n.axisName=n[i+"AxisName"]=e.name,n.axisId=n[i+"AxisId"]=e.id,n}function Ev(t){return!t||null==t[0]||isNaN(t[0])||null==t[1]||isNaN(t[1])}Hd({type:"axisPointer",coordSysAxesInfo:null,defaultOption:{show:"auto",triggerOn:null,zlevel:0,z:50,type:"line",snap:!1,triggerTooltip:!0,value:null,status:null,link:[],animation:null,animationDurationUpdate:200,lineStyle:{color:"#aaa",width:1,type:"solid"},shadowStyle:{color:"rgba(150,150,150,0.3)"},label:{show:!0,formatter:null,precision:"auto",margin:3,color:"#fff",padding:[5,7,5,7],backgroundColor:"auto",borderColor:null,borderWidth:0,shadowBlur:3,shadowColor:"#aaa"},handle:{show:!1,icon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z",size:45,margin:50,color:"#333",shadowBlur:3,shadowColor:"#aaa",shadowOffsetX:0,shadowOffsetY:2,throttle:40}}});var zv=zr(),Nv=D;function Rv(t,e,n){var i;v.node||(i=e.getZr(),zv(i).records||(zv(i).records={}),function(a,s){if(zv(a).initialized)return;function t(t,o){a.on(t,function(e){var i,r,n=(i=s,{dispatchAction:function t(e){var n=r[e.type];n?n.push(e):(e.dispatchAction=t,i.dispatchAction(e))},pendings:r={showTip:[],hideTip:[]}});Nv(zv(a).records,function(t){t&&o(t,e,n.dispatchAction)}),function(t,e){var n,i=t.showTip.length,r=t.hideTip.length;i?n=t.showTip[i-1]:r&&(n=t.hideTip[r-1]);n&&(n.dispatchAction=null,e.dispatchAction(n))}(n.pendings,s)})}zv(a).initialized=!0,t("click",O(Vv,"click")),t("mousemove",O(Vv,"mousemove")),t("globalout",Bv)}(i,e),(zv(i).records[t]||(zv(i).records[t]={})).handler=n)}function Bv(t,e,n){t.handler("leave",null,n)}function Vv(t,e,n,i){e.handler(t,n,i)}function Fv(t,e){var n;v.node||(n=e.getZr(),(zv(n).records||{})[t]&&(zv(n).records[t]=null))}var Hv=Wd({type:"axisPointer",render:function(t,e,n){var i=e.getComponent("tooltip"),r=t.get("triggerOn")||i&&i.get("triggerOn")||"mousemove|click";Rv("axisPointer",n,function(t,e,n){"none"!==r&&("leave"===t||0<=r.indexOf(t))&&n({type:"updateAxisPointer",currTrigger:t,x:e&&e.offsetX,y:e&&e.offsetY})})},remove:function(t,e){Fv(e.getZr(),"axisPointer"),Hv.superApply(this._model,"remove",arguments)},dispose:function(t,e){Fv("axisPointer",e),Hv.superApply(this._model,"dispose",arguments)}}),Wv=zr(),Gv=T,Zv=L;function Uv(){}function Xv(t,e,n,i){!function n(i,t){{if(B(i)&&B(t)){var r=!0;return D(t,function(t,e){r=r&&n(i[e],t)}),!!r}return i===t}}(Wv(n).lastProp,i)&&(Wv(n).lastProp=i,e?Xs(n,i,t):(n.stopAnimation(),n.attr(i)))}function Yv(t,e){t[e.get("label.show")?"show":"hide"]()}function jv(t){return{position:t.position.slice(),rotation:t.rotation||0}}function qv(t,e,n){var i=e.get("z"),r=e.get("zlevel");t&&t.traverse(function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=r&&(t.zlevel=r),t.silent=n)})}function $v(t,e,n,i,r){var o=Kv(n.get("value"),e.axis,e.ecModel,n.get("seriesDataIndices"),{precision:n.get("label.precision"),formatter:n.get("label.formatter")}),a=n.getModel("label"),s=Ll(a.get("padding")||0),l=a.getFont(),h=ii(o,l),u=r.position,c=h.width+s[1]+s[3],d=h.height+s[0]+s[2],f=r.align;"right"===f&&(u[0]-=c),"center"===f&&(u[0]-=c/2);var p,g,m,v,y,_,x=r.verticalAlign;"bottom"===x&&(u[1]-=d),"middle"===x&&(u[1]-=d/2),p=u,g=c,m=d,y=(v=i).getWidth(),_=v.getHeight(),p[0]=Math.min(p[0]+g,y)-g,p[1]=Math.min(p[1]+m,_)-m,p[0]=Math.max(p[0],0),p[1]=Math.max(p[1],0);var w=a.get("backgroundColor");w&&"auto"!==w||(w=e.get("axisLine.lineStyle.color")),t.label={shape:{x:0,y:0,width:c,height:d,r:a.get("borderRadius")},position:u.slice(),style:{text:o,textFont:l,textFill:a.getTextColor(),textPosition:"inside",textPadding:s,fill:w,stroke:a.get("borderColor")||"transparent",lineWidth:a.get("borderWidth")||0,shadowBlur:a.get("shadowBlur"),shadowColor:a.get("shadowColor"),shadowOffsetX:a.get("shadowOffsetX"),shadowOffsetY:a.get("shadowOffsetY")},z2:10}}function Kv(t,e,r,n,i){t=e.scale.parse(t);var o,a=e.scale.getLabel(t,{precision:i.precision}),s=i.formatter;return s&&(o={value:Sp(e,t),axisDimension:e.dim,axisIndex:e.index,seriesData:[]},D(n,function(t){var e=r.getSeriesByIndex(t.seriesIndex),n=t.dataIndexInside,i=e&&e.getDataParams(n);i&&o.seriesData.push(i)}),R(s)?a=s.replace("{value}",a):N(s)&&(a=s(o))),a}function Qv(t,e,n){var i=te();return oe(i,i,n.rotation),re(i,i,n.position),qs([t.dataToCoord(e),(n.labelOffset||0)+(n.labelDirection||1)*(n.labelMargin||0)],i)}Zr((Uv.prototype={_group:null,_lastGraphicKey:null,_handle:null,_dragging:!1,_lastValue:null,_lastStatus:null,_payloadInfo:null,animationThreshold:15,render:function(t,e,n,i){var r=e.get("value"),o=e.get("status");if(this._axisModel=t,this._axisPointerModel=e,this._api=n,i||this._lastValue!==r||this._lastStatus!==o){this._lastValue=r,this._lastStatus=o;var a=this._group,s=this._handle;if(!o||"hide"===o)return a&&a.hide(),void(s&&s.hide());a&&a.show(),s&&s.show();var l={};this.makeElOption(l,r,t,e,n);var h=l.graphicKey;h!==this._lastGraphicKey&&this.clear(n),this._lastGraphicKey=h;var u,c=this._moveAnimation=this.determineAnimation(t,e);a?(u=O(Xv,e,c),this.updatePointerEl(a,l,u,e),this.updateLabelEl(a,l,u,e)):(a=this._group=new _n,this.createPointerEl(a,l,t,e),this.createLabelEl(a,l,t,e),n.getZr().add(a)),qv(a,e,!0),this._renderHandle(r)}},remove:function(t){this.clear(t)},dispose:function(t){this.clear(t)},determineAnimation:function(t,e){var n=e.get("animation"),i=t.axis,r="category"===i.type,o=e.get("snap");if(!o&&!r)return!1;if("auto"!==n&&null!=n)return!0===n;var a=this.animationThreshold;if(r&&i.getBandWidth()>a)return!0;if(o){var s=Tm(t).seriesDataCount,l=i.getExtent();return Math.abs(l[0]-l[1])/s>a}return!1},makeElOption:function(){},createPointerEl:function(t,e){var n,i=e.pointer;i&&(n=Wv(t).pointerEl=new el[i.type](Gv(e.pointer)),t.add(n))},createLabelEl:function(t,e,n,i){var r;e.label&&(r=Wv(t).labelEl=new Ga(Gv(e.label)),t.add(r),Yv(r,i))},updatePointerEl:function(t,e,n){var i=Wv(t).pointerEl;i&&e.pointer&&(i.setStyle(e.pointer.style),n(i,{shape:e.pointer.shape}))},updateLabelEl:function(t,e,n,i){var r=Wv(t).labelEl;r&&(r.setStyle(e.label.style),n(r,{shape:e.label.shape,position:e.label.position}),Yv(r,i))},_renderHandle:function(t){if(!this._dragging&&this.updateHandleTransform){var e,n=this._axisPointerModel,i=this._api.getZr(),r=this._handle,o=n.getModel("handle"),a=n.get("status");if(!o.get("show")||!a||"hide"===a)return r&&i.remove(r),void(this._handle=null);this._handle||(e=!0,r=this._handle=Js(o.get("icon"),{cursor:"move",draggable:!0,onmousemove:function(t){Wt(t.event)},onmousedown:Zv(this._onHandleDragMove,this,0,0),drift:Zv(this._onHandleDragMove,this),ondragend:Zv(this._onHandleDragEnd,this)}),i.add(r)),qv(r,n,!1);r.setStyle(o.getItemStyle(null,["color","borderColor","borderWidth","opacity","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"]));var s=o.get("size");z(s)||(s=[s,s]),r.attr("scale",[s[0]/2,s[1]/2]),oc(this,"_doDispatchAxisPointer",o.get("throttle")||0,"fixRate"),this._moveHandleToValue(t,e)}},_moveHandleToValue:function(t,e){Xv(this._axisPointerModel,!e&&this._moveAnimation,this._handle,jv(this.getHandleTransform(t,this._axisModel,this._axisPointerModel)))},_onHandleDragMove:function(t,e){var n,i=this._handle;i&&(this._dragging=!0,n=this.updateHandleTransform(jv(i),[t,e],this._axisModel,this._axisPointerModel),this._payloadInfo=n,i.stopAnimation(),i.attr(jv(n)),Wv(i).lastProp=null,this._doDispatchAxisPointer())},_doDispatchAxisPointer:function(){var t,e;this._handle&&(t=this._payloadInfo,e=this._axisModel,this._api.dispatchAction({type:"updateAxisPointer",x:t.cursorPoint[0],y:t.cursorPoint[1],tooltipOption:t.tooltipOption,axesInfo:[{axisDim:e.axis.dim,axisIndex:e.componentIndex}]}))},_onHandleDragEnd:function(){var t;this._dragging=!1,this._handle&&(t=this._axisPointerModel.get("value"),this._moveHandleToValue(t),this._api.dispatchAction({type:"hideTip"}))},getHandleTransform:null,updateHandleTransform:null,clear:function(t){this._lastValue=null,this._lastStatus=null;var e=t.getZr(),n=this._group,i=this._handle;e&&n&&(this._lastGraphicKey=null,n&&e.remove(n),i&&e.remove(i),this._group=null,this._handle=null,this._payloadInfo=null)},doClear:function(){},buildLabel:function(t,e,n){return{x:t[n=n||0],y:t[1-n],width:e[n],height:e[1-n]}}}).constructor=Uv);var Jv=Uv.extend({makeElOption:function(t,e,n,i,r){var o,a,s,l,h,u,c=n.axis,d=c.grid,f=i.get("type"),p=ty(d,c).getOtherAxis(c).getGlobalExtent(),g=c.toGlobalCoord(c.dataToCoord(e,!0));f&&"none"!==f&&(h=(s=i).get("type"),u=s.getModel(h+"Style"),"line"===h?(l=u.getLineStyle()).fill=null:"shadow"===h&&((l=u.getAreaStyle()).stroke=null),o=l,(a=ey[f](c,g,p)).style=o,t.graphicKey=a.type,t.pointer=a);var m,v,y,_,x,w,b,S=Em(d.model,n);m=e,v=t,_=n,x=i,w=r,b=fm.innerTextLayout((y=S).rotation,0,y.labelDirection),y.labelMargin=x.get("label.margin"),$v(v,_,x,w,{position:Qv(_.axis,m,y),align:b.textAlign,verticalAlign:b.textVerticalAlign})},getHandleTransform:function(t,e,n){var i=Em(e.axis.grid.model,e,{labelInside:!1});return i.labelMargin=n.get("handle.margin"),{position:Qv(e.axis,t,i),rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},updateHandleTransform:function(t,e,n){var i=n.axis,r=i.grid,o=i.getGlobalExtent(!0),a=ty(r,i).getOtherAxis(i).getGlobalExtent(),s="x"===i.dim?0:1,l=t.position;l[s]+=e[s],l[s]=Math.min(o[1],l[s]),l[s]=Math.max(o[0],l[s]);var h=(a[1]+a[0])/2,u=[h,h];u[s]=l[s];return{position:l,rotation:t.rotation,cursorPoint:u,tooltipOption:[{verticalAlign:"middle"},{align:"center"}][s]}}});function ty(t,e){var n={};return n[e.dim+"AxisIndex"]=e.index,t.getCartesian(n)}var ey={line:function(t,e,n){var i,r,o;return{type:"Line",subPixelOptimize:!0,shape:(i=[e,n[0]],r=[e,n[1]],o=ny(t),{x1:i[o=o||0],y1:i[1-o],x2:r[o],y2:r[1-o]})}},shadow:function(t,e,n){var i,r,o,a=Math.max(1,t.getBandWidth()),s=n[1]-n[0];return{type:"Rect",shape:(i=[e-a/2,n[0]],r=[a,s],o=ny(t),{x:i[o=o||0],y:i[1-o],width:r[o],height:r[1-o]})}}};function ny(t){return"x"===t.dim?0:1}km.registerAxisPointerClass("CartesianAxisPointer",Jv),Ed(function(t){var e;t&&(t.axisPointer&&0!==t.axisPointer.length||(t.axisPointer={}),(e=t.axisPointer.link)&&!z(e)&&(t.axisPointer.link=[e]))}),zd(Xc.PROCESSOR.STATISTIC,function(t,e){t.getComponent("axisPointer").coordSysAxesInfo=Im(t,e)}),Nd({type:"updateAxisPointer",event:"updateAxisPointer",update:":updateAxisPointer"},function(t,e,n){var i=t.currTrigger,a=[t.x,t.y],r=t,o=t.dispatchAction||L(n.dispatchAction,n),s=e.getComponent("axisPointer").coordSysAxesInfo;if(s){Ev(a)&&(a=Cv({seriesIndex:r.seriesIndex,dataIndex:r.dataIndex},e).point);var l=Ev(a),h=r.axesInfo,u=s.axesInfo,c="leave"===i||Ev(a),d={},f={},p={list:[],map:{}},g={showPointer:Av(Pv,f),showTooltip:Av(Lv,p)};Tv(s.coordSysMap,function(t,e){var o=l||t.containPoint(a);Tv(s.coordSysAxesInfo[e],function(t,e){var n,i=t.axis,r=function(t,e){for(var n=0;n<(t||[]).length;n++){var i=t[n];if(e.axis.dim===i.axisDim&&e.axis.model.componentIndex===i.axisIndex)return i}}(h,t);c||!o||h&&!r||(null!=(n=r&&r.value)||l||(n=i.pointToData(a)),null!=n&&kv(t,n,g,!1,d))})});var m,v,y,_={};return Tv(u,function(r,t){var o=r.linkGroup;o&&!f[t]&&Tv(o.axesInfo,function(t,e){var n,i=f[e];t!==r&&i&&(n=i.value,o.mapper&&(n=r.axis.scale.parse(o.mapper(n,Ov(t),Ov(r)))),_[r.key]=n)})}),Tv(_,function(t,e){kv(u[e],t,g,!0,d)}),m=f,v=u,y=d.axesInfo=[],Tv(v,function(t,e){var n=t.axisPointerModel.option,i=m[e];i?(t.useHandle||(n.status="show"),n.value=i.value,n.seriesDataIndices=(i.payloadBatch||[]).slice()):t.useHandle||(n.status="hide"),"show"===n.status&&y.push({axisDim:t.axis.dim,axisIndex:t.axis.model.componentIndex,value:n.value})}),function(t,e,n,i){if(Ev(e)||!t.list.length)return i({type:"hideTip"});var r=((t.list[0].dataByAxis[0]||{}).seriesDataIndices||[])[0]||{};i({type:"showTip",escapeConnect:!0,x:e[0],y:e[1],tooltipOption:n.tooltipOption,position:n.position,dataIndexInside:r.dataIndexInside,dataIndex:r.dataIndex,seriesIndex:r.seriesIndex,dataByCoordSys:t.list})}(p,a,t,o),function(t,e){var n=e.getZr(),i="axisPointerLastHighlights",r=Dv(n)[i]||{},o=Dv(n)[i]={};Tv(t,function(t,e){var n=t.axisPointerModel.option;"show"===n.status&&Tv(n.seriesDataIndices,function(t){var e=t.seriesIndex+" | "+t.dataIndex;o[e]=t})});var a=[],s=[];D(r,function(t,e){o[e]||s.push(t)}),D(o,function(t,e){r[e]||a.push(t)}),s.length&&e.dispatchAction({type:"downplay",escapeConnect:!0,batch:s}),a.length&&e.dispatchAction({type:"highlight",escapeConnect:!0,batch:a})}(u,n),d}}),Hd({type:"tooltip",dependencies:["axisPointer"],defaultOption:{zlevel:0,z:60,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove|click",alwaysShowContent:!1,displayMode:"single",renderMode:"auto",confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:"auto",animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",crossStyle:{color:"#999",width:1,type:"dashed",textStyle:{}}},textStyle:{color:"#fff",fontSize:14}}});var iy=D,ry=Pl,oy=["","-webkit-","-moz-","-o-"];function ay(r){var t,e,n,o=[],i=r.get("transitionDuration"),a=r.get("backgroundColor"),s=r.getModel("textStyle"),l=r.get("padding");return i&&o.push((n="left "+(t=i)+"s "+(e="cubic-bezier(0.23, 1, 0.32, 1)")+",top "+t+"s "+e,P(oy,function(t){return t+"transition:"+n}).join(";"))),a&&(v.canvasSupported?o.push("background-Color:"+a):(o.push("background-Color:#"+Be(a)),o.push("filter:alpha(opacity=70)"))),iy(["width","color","radius"],function(t){var e="border-"+t,n=ry(e),i=r.get(n);null!=i&&o.push(e+":"+i+("color"===t?"":"px"))}),o.push(function(n){var i=[],t=n.get("fontSize"),e=n.getTextColor();e&&i.push("color:"+e),i.push("font:"+n.getFont());var r=n.get("lineHeight");null==r&&(r=Math.round(3*t/2)),t&&i.push("line-height:"+r+"px");var o=n.get("textShadowColor"),a=n.get("textShadowBlur")||0,s=n.get("textShadowOffsetX")||0,l=n.get("textShadowOffsetY")||0;return a&&i.push("text-shadow:"+s+"px "+l+"px "+a+"px "+o),iy(["decoration","align"],function(t){var e=n.get(t);e&&i.push("text-"+t+":"+e)}),i.join(";")}(s)),null!=l&&o.push("padding:"+Ll(l).join("px ")+"px"),o.join(";")+";"}function sy(t,e,n,i,r){var o,a,s,l,h,u=e&&e.painter;n?(o=u&&u.getViewportRoot())&&(s=t,l=o,h=document.body,Lt(Pt,l,i,r,!0)&&Lt(s,h,Pt[0],Pt[1])):(t[0]=i,t[1]=r,(a=u&&u.getViewportRootOffset())&&(t[0]+=a.offsetLeft,t[1]+=a.offsetTop)),t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}function ly(t,e,n){if(v.wxa)return null;var i=document.createElement("div");i.domBelongToZr=!0,this.el=i;var r=this._zr=e.getZr(),o=this._appendToBody=n&&n.appendToBody;this._styleCoord=[0,0,0,0],sy(this._styleCoord,r,o,e.getWidth()/2,e.getHeight()/2),o?document.body.appendChild(i):t.appendChild(i),this._container=t,this._show=!1,this._hideTimeout;var a=this;i.onmouseenter=function(){a._enterable&&(clearTimeout(a._hideTimeout),a._show=!0),a._inContent=!0},i.onmousemove=function(t){var e;t=t||window.event,a._enterable||(e=r.handler,Ft(r.painter.getViewportRoot(),t,!0),e.dispatch("mousemove",t))},i.onmouseleave=function(){a._enterable&&a._show&&a.hideLater(a._hideDelay),a._inContent=!1}}function hy(t,e,n,i){t[0]=n,t[1]=i,t[2]=t[0]/e.getWidth(),t[3]=t[1]/e.getHeight()}function uy(t){var e=this._zr=t.getZr();this._styleCoord=[0,0,0,0],hy(this._styleCoord,e,t.getWidth()/2,t.getHeight()/2),this._show=!1,this._hideTimeout}ly.prototype={constructor:ly,_enterable:!0,update:function(t){var e=this._container,n=e.currentStyle||document.defaultView.getComputedStyle(e),i=e.style;"absolute"!==i.position&&"absolute"!==n.position&&(i.position="relative"),t.get("alwaysShowContent")&&this._moveTooltipIfResized()},_moveTooltipIfResized:function(){var t=this._styleCoord[2],e=this._styleCoord[3],n=t*this._zr.getWidth(),i=e*this._zr.getHeight();this.moveTo(n,i)},show:function(t){clearTimeout(this._hideTimeout);var e=this.el,n=this._styleCoord;e.style.cssText="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;"+ay(t)+";left:"+n[0]+"px;top:"+n[1]+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",e.style.pointerEvents=this._enterable?"auto":"none",this._show=!0},setContent:function(t){this.el.innerHTML=null==t?"":t},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el;return[t.clientWidth,t.clientHeight]},moveTo:function(t,e){var n=this._styleCoord;sy(n,this._zr,this._appendToBody,t,e);var i=this.el.style;i.left=n[0]+"px",i.top=n[1]+"px"},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(L(this.hide,this),t)):this.hide())},isShow:function(){return this._show},dispose:function(){this.el.parentNode.removeChild(this.el)},getOuterSize:function(){var t,e=this.el.clientWidth,n=this.el.clientHeight;return document.defaultView&&document.defaultView.getComputedStyle&&((t=document.defaultView.getComputedStyle(this.el))&&(e+=parseInt(t.borderLeftWidth,10)+parseInt(t.borderRightWidth,10),n+=parseInt(t.borderTopWidth,10)+parseInt(t.borderBottomWidth,10))),{width:e,height:n}}},uy.prototype={constructor:uy,_enterable:!0,update:function(t){t.get("alwaysShowContent")&&this._moveTooltipIfResized()},_moveTooltipIfResized:function(){var t=this._styleCoord[2],e=this._styleCoord[3],n=t*this._zr.getWidth(),i=e*this._zr.getHeight();this.moveTo(n,i)},show:function(){this._hideTimeout&&clearTimeout(this._hideTimeout),this.el.attr("show",!0),this._show=!0},setContent:function(t,e,n){this.el&&this._zr.remove(this.el);for(var i={},r=t,o="{marker",a=r.indexOf(o);0<=a;){var s=r.indexOf("|}"),l=r.substr(a+o.length,s-a-o.length);-1<l.indexOf("sub")?i["marker"+l]={textWidth:4,textHeight:4,textBorderRadius:2,textBackgroundColor:e[l],textOffset:[3,0]}:i["marker"+l]={textWidth:10,textHeight:10,textBorderRadius:5,textBackgroundColor:e[l]},a=(r=r.substr(s+1)).indexOf("{marker")}var h=n.getModel("textStyle"),u=h.get("fontSize"),c=n.get("textLineHeight");null==c&&(c=Math.round(3*u/2)),this.el=new Aa({style:Bs({},h,{rich:i,text:t,textBackgroundColor:n.get("backgroundColor"),textBorderRadius:n.get("borderRadius"),textFill:n.get("textStyle.color"),textPadding:n.get("padding"),textLineHeight:c}),z:n.get("z")}),this._zr.add(this.el);var d=this;this.el.on("mouseover",function(){d._enterable&&(clearTimeout(d._hideTimeout),d._show=!0),d._inContent=!0}),this.el.on("mouseout",function(){d._enterable&&d._show&&d.hideLater(d._hideDelay),d._inContent=!1})},setEnterable:function(t){this._enterable=t},getSize:function(){var t=this.el.getBoundingRect();return[t.width,t.height]},moveTo:function(t,e){var n;this.el&&(hy(n=this._styleCoord,this._zr,t,e),this.el.attr("position",[n[0],n[1]]))},hide:function(){this.el&&this.el.hide(),this._show=!1},hideLater:function(t){!this._show||this._inContent&&this._enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(L(this.hide,this),t)):this.hide())},isShow:function(){return this._show},dispose:function(){clearTimeout(this._hideTimeout),this.el&&this._zr.remove(this.el)},getOuterSize:function(){var t=this.getSize();return{width:t[0],height:t[1]}}};var cy=L,dy=D,fy=gl,py=new Ga({shape:{x:-1,y:-1,width:2,height:2}});function gy(t){for(var e=t.pop();t.length;){var n=t.pop();n&&(ll.isInstance(n)&&(n=n.get("tooltip",!0)),"string"==typeof n&&(n={formatter:n}),e=new ll(n,e,e.ecModel))}return e}function my(t,e){return t.dispatchAction||L(e.dispatchAction,e)}function vy(t){return"center"===t||"middle"===t}Wd({type:"tooltip",init:function(t,e){var n,i,r;v.node||(i=(n=t.getComponent("tooltip")).get("renderMode"),this._renderMode=Fr(i),"html"===this._renderMode?(r=new ly(e.getDom(),e,{appendToBody:n.get("appendToBody",!0)}),this._newLine="<br/>"):(r=new uy(e),this._newLine="\n"),this._tooltipContent=r)},render:function(t,e,n){var i;v.node||(this.group.removeAll(),this._tooltipModel=t,this._ecModel=e,this._api=n,this._lastDataByCoordSys=null,this._alwaysShowContent=t.get("alwaysShowContent"),(i=this._tooltipContent).update(t),i.setEnterable(t.get("enterable")),this._initGlobalListener(),this._keepShow())},_initGlobalListener:function(){var i=this._tooltipModel.get("triggerOn");Rv("itemTooltip",this._api,cy(function(t,e,n){"none"!==i&&(0<=i.indexOf(t)?this._tryShow(e,n):"leave"===t&&this._hide(n))},this))},_keepShow:function(){var t,e=this._tooltipModel,n=this._ecModel,i=this._api;null!=this._lastX&&null!=this._lastY&&"none"!==e.get("triggerOn")&&(t=this,clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){i.isDisposed()||t.manuallyShowTip(e,n,i,{x:t._lastX,y:t._lastY})}))},manuallyShowTip:function(t,e,n,i){if(i.from!==this.uid&&!v.node){var r=my(i,n);this._ticket="";var o=i.dataByCoordSys;if(i.tooltip&&null!=i.x&&null!=i.y){var a=py;a.position=[i.x,i.y],a.update(),a.tooltip=i.tooltip,this._tryShow({offsetX:i.x,offsetY:i.y,target:a},r)}else if(o)this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,dataByCoordSys:i.dataByCoordSys,tooltipOption:i.tooltipOption},r);else if(null!=i.seriesIndex){if(this._manuallyAxisShowTip(t,e,n,i))return;var s=Cv(i,e),l=s.point[0],h=s.point[1];null!=l&&null!=h&&this._tryShow({offsetX:l,offsetY:h,position:i.position,target:s.el},r)}else null!=i.x&&null!=i.y&&(n.dispatchAction({type:"updateAxisPointer",x:i.x,y:i.y}),this._tryShow({offsetX:i.x,offsetY:i.y,position:i.position,target:n.getZr().findHover(i.x,i.y).target},r))}},manuallyHideTip:function(t,e,n,i){var r=this._tooltipContent;!this._alwaysShowContent&&this._tooltipModel&&r.hideLater(this._tooltipModel.get("hideDelay")),this._lastX=this._lastY=null,i.from!==this.uid&&this._hide(my(i,n))},_manuallyAxisShowTip:function(t,e,n,i){var r=i.seriesIndex,o=i.dataIndex,a=e.getComponent("axisPointer").coordSysAxesInfo;if(null!=r&&null!=o&&null!=a){var s=e.getSeriesByIndex(r);if(s)if("axis"===(t=gy([s.getData().getItemModel(o),s,(s.coordinateSystem||{}).model,t])).get("trigger"))return n.dispatchAction({type:"updateAxisPointer",seriesIndex:r,dataIndex:o,position:i.position}),!0}},_tryShow:function(t,e){var n,i=t.target;this._tooltipModel&&(this._lastX=t.offsetX,this._lastY=t.offsetY,(n=t.dataByCoordSys)&&n.length?this._showAxisTooltip(n,t):i&&null!=i.dataIndex?(this._lastDataByCoordSys=null,this._showSeriesItemTooltip(t,i,e)):i&&i.tooltip?(this._lastDataByCoordSys=null,this._showComponentItemTooltip(t,i,e)):(this._lastDataByCoordSys=null,this._hide(e)))},_showOrMove:function(t,e){var n=t.get("showDelay");e=L(e,this),clearTimeout(this._showTimout),0<n?this._showTimout=setTimeout(e,n):e()},_showAxisTooltip:function(t,e){var d=this._ecModel,n=this._tooltipModel,i=[e.offsetX,e.offsetY],r=[],f=[],o=gy([e.tooltipOption,n]),p=this._renderMode,a=this._newLine,g={};dy(t,function(t){dy(t.dataByAxis,function(s){var l,t,h=d.getComponent(s.axisDim+"Axis",s.axisIndex),u=s.value,c=[];h&&null!=u&&(l=Kv(u,h.axis,d,s.seriesDataIndices,s.valueLabelOpt),D(s.seriesDataIndices,function(t){var e,n,i,r=d.getSeriesByIndex(t.seriesIndex),o=t.dataIndexInside,a=r&&r.getDataParams(o);a.axisDim=s.axisDim,a.axisIndex=s.axisIndex,a.axisType=s.axisType,a.axisId=s.axisId,a.axisValue=Sp(h.axis,u),a.axisValueLabel=l,a&&(f.push(a),B(e=r.formatTooltip(o,!0,null,p))?(n=e.html,i=e.markers,m(g,i)):n=e,c.push(n))}),t=l,"html"!==p?r.push(c.join(a)):r.push((t?zl(t)+a:"")+c.join(a)))})},this),r.reverse(),r=r.join(this._newLine+this._newLine);var s=e.position;this._showOrMove(o,function(){this._updateContentNotChangedOnAxis(t)?this._updatePosition(o,s,i[0],i[1],this._tooltipContent,f):this._showTooltipContent(o,r,f,Math.random(),i[0],i[1],s,void 0,g)})},_showSeriesItemTooltip:function(t,e,n){var i,r,o,a,s,l=this._ecModel,h=e.seriesIndex,u=l.getSeriesByIndex(h),c=e.dataModel||u,d=e.dataIndex,f=e.dataType,p=c.getData(f),g=gy([p.getItemModel(d),c,u&&(u.coordinateSystem||{}).model,this._tooltipModel]),m=g.get("trigger");null!=m&&"item"!==m||(i=c.getDataParams(d,f),r=c.formatTooltip(d,!1,f,this._renderMode),a=B(r)?(o=r.html,r.markers):(o=r,null),s="item_"+c.name+"_"+d,this._showOrMove(g,function(){this._showTooltipContent(g,o,i,s,t.offsetX,t.offsetY,t.position,t.target,a)}),n({type:"showTip",dataIndexInside:d,dataIndex:p.getRawIndex(d),seriesIndex:h,from:this.uid}))},_showComponentItemTooltip:function(t,e,n){var i=e.tooltip;"string"==typeof i&&(i={content:i,formatter:i});var r=new ll(i,this._tooltipModel,this._ecModel),o=r.get("content"),a=Math.random();this._showOrMove(r,function(){this._showTooltipContent(r,o,r.get("formatterParams")||{},a,t.offsetX,t.offsetY,t.position,e)}),n({type:"showTip",from:this.uid})},_showTooltipContent:function(n,t,i,e,r,o,a,s,l){var h,u,c,d;this._ticket="",n.get("showContent")&&n.get("show")&&(h=this._tooltipContent,u=n.get("formatter"),a=a||n.get("position"),c=t,u&&"string"==typeof u?c=Bl(u,i,!0):"function"==typeof u&&(d=cy(function(t,e){t===this._ticket&&(h.setContent(e,l,n),this._updatePosition(n,a,r,o,h,i,s))},this),this._ticket=e,c=u(i,e,d)),h.setContent(c,l,n),h.show(n),this._updatePosition(n,a,r,o,h,i,s))},_updatePosition:function(t,e,n,i,r,o,a){var s=this._api.getWidth(),l=this._api.getHeight();e=e||t.get("position");var h,u,c,d,f,p,g,m,v,y=r.getSize(),_=t.get("align"),x=t.get("verticalAlign"),w=a&&a.getBoundingRect().clone();a&&w.applyTransform(a.transform),"function"==typeof e&&(e=e([n,i],o,r.el,w,{viewSize:[s,l],contentSize:y.slice()})),z(e)?(n=fy(e[0],s),i=fy(e[1],l)):B(e)?(e.width=y[0],e.height=y[1],n=(h=Kl(e,{width:s,height:l})).x,i=h.y,x=_=null):i=(n="string"==typeof e&&a?(u=function(t,e,n){var i=n[0],r=n[1],o=0,a=0,s=e.width,l=e.height;switch(t){case"inside":o=e.x+s/2-i/2,a=e.y+l/2-r/2;break;case"top":o=e.x+s/2-i/2,a=e.y-r-5;break;case"bottom":o=e.x+s/2-i/2,a=e.y+l+5;break;case"left":o=e.x-i-5,a=e.y+l/2-r/2;break;case"right":o=e.x+s+5,a=e.y+l/2-r/2}return[o,a]}(e,w,y))[0]:(u=function(t,e,n,i,r,o,a){var s=n.getOuterSize(),l=s.width,h=s.height;null!=o&&(i<t+l+o?t-=l+o:t+=o);null!=a&&(r<e+h+a?e-=h+a:e+=a);return[t,e]}(n,i,r,s,l,_?null:20,x?null:20))[0],u[1]),_&&(n-=vy(_)?y[0]/2:"right"===_?y[0]:0),x&&(i-=vy(x)?y[1]/2:"bottom"===x?y[1]:0),t.get("confine")&&(n=(u=(c=n,d=i,f=s,p=l,g=r.getOuterSize(),m=g.width,v=g.height,c=Math.min(c+m,f)-m,d=Math.min(d+v,p)-v,c=Math.max(c,0),d=Math.max(d,0),[c,d]))[0],i=u[1]),r.moveTo(n,i)},_updateContentNotChangedOnAxis:function(i){var t=this._lastDataByCoordSys,a=!!t&&t.length===i.length;return a&&dy(t,function(t,e){var n=t.dataByAxis||{},o=(i[e]||{}).dataByAxis||[];(a&=n.length===o.length)&&dy(n,function(t,e){var n=o[e]||{},i=t.seriesDataIndices||[],r=n.seriesDataIndices||[];(a&=t.value===n.value&&t.axisType===n.axisType&&t.axisId===n.axisId&&i.length===r.length)&&dy(i,function(t,e){var n=r[e];a&=t.seriesIndex===n.seriesIndex&&t.dataIndex===n.dataIndex})})}),this._lastDataByCoordSys=i,!!a},_hide:function(t){this._lastDataByCoordSys=null,t({type:"hideTip",from:this.uid})},dispose:function(t,e){v.node||(this._tooltipContent.dispose(),Fv("itemTooltip",e))}}),Nd({type:"showTip",event:"showTip",update:"tooltip:manuallyShowTip"},function(){}),Nd({type:"hideTip",event:"hideTip",update:"tooltip:manuallyHideTip"},function(){});var yy=lc.legend.selector,_y={all:{type:"all",title:T(yy.all)},inverse:{type:"inverse",title:T(yy.inverse)}},xy=Hd({type:"legend.plain",dependencies:["series"],layoutMode:{type:"box",ignoreSize:!0},init:function(t,e,n){this.mergeDefaultAndTheme(t,n),t.selected=t.selected||{},this._updateSelector(t)},mergeOption:function(t){xy.superCall(this,"mergeOption",t),this._updateSelector(t)},_updateSelector:function(t){var n=t.selector;!0===n&&(n=t.selector=["all","inverse"]),z(n)&&D(n,function(t,e){R(t)&&(t={type:t}),n[e]=m(t,_y[t.type])})},optionUpdated:function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&"single"===this.get("selectedMode")){for(var e=!1,n=0;n<t.length;n++){var i=t[n].get("name");if(this.isSelected(i)){this.select(i),e=!0;break}}e||this.select(t[0].get("name"))}},_updateData:function(r){var o=[],a=[];r.eachRawSeries(function(t){var e,n,i=t.name;a.push(i),t.legendVisualProvider?(n=t.legendVisualProvider.getAllNames(),r.isSeriesFiltered(t)||(a=a.concat(n)),n.length?o=o.concat(n):e=!0):e=!0,e&&Lr(t)&&o.push(t.name)}),this._availableNames=a;var t=P(this.get("data")||o,function(t){return"string"!=typeof t&&"number"!=typeof t||(t={name:t}),new ll(t,this,this.ecModel)},this);this._data=t},getData:function(){return this._data},select:function(t){var e=this.option.selected;"single"===this.get("selectedMode")&&D(this._data,function(t){e[t.get("name")]=!1}),e[t]=!0},unSelect:function(t){"single"!==this.get("selectedMode")&&(this.option.selected[t]=!1)},toggleSelected:function(t){var e=this.option.selected;e.hasOwnProperty(t)||(e[t]=!0),this[e[t]?"unSelect":"select"](t)},allSelect:function(){var t=this._data,e=this.option.selected;D(t,function(t){e[t.get("name",!0)]=!0})},inverseSelect:function(){var t=this._data,n=this.option.selected;D(t,function(t){var e=t.get("name",!0);n.hasOwnProperty(e)||(n[e]=!0),n[e]=!n[e]})},isSelected:function(t){var e=this.option.selected;return!(e.hasOwnProperty(t)&&!e[t])&&0<=w(this._availableNames,t)},getOrient:function(){return"vertical"===this.get("orient")?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},defaultOption:{zlevel:0,z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",itemStyle:{borderWidth:0},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:" sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}}});function wy(t,e,n){var r,o={},a="toggleSelected"===t;return n.eachComponent("legend",function(i){a&&null!=r?i[r?"select":"unSelect"](e.name):"allSelect"===t||"inverseSelect"===t?i[t]():(i[t](e.name),r=i.isSelected(e.name)),D(i.getData(),function(t){var e,n=t.get("name");"\n"!==n&&""!==n&&(e=i.isSelected(n),o.hasOwnProperty(n)?o[n]=o[n]&&e:o[n]=e)})}),"allSelect"===t||"inverseSelect"===t?{selected:o}:{name:e.name,selected:o}}function by(t,e){var n=Ll(e.get("padding")),i=e.getItemStyle(["color","opacity"]);return i.fill=e.get("backgroundColor"),t=new Ga({shape:{x:t.x-n[3],y:t.y-n[0],width:t.width+n[1]+n[3],height:t.height+n[0]+n[2],r:e.get("borderRadius")},style:i,silent:!0,z2:-1})}Nd("legendToggleSelect","legendselectchanged",O(wy,"toggleSelected")),Nd("legendAllSelect","legendselectall",O(wy,"allSelect")),Nd("legendInverseSelect","legendinverseselect",O(wy,"inverseSelect")),Nd("legendSelect","legendselected",O(wy,"select")),Nd("legendUnSelect","legendunselected",O(wy,"unSelect"));var Sy=O,My=D,Iy=_n,Cy=Wd({type:"legend.plain",newlineDisabled:!1,init:function(){this.group.add(this._contentGroup=new Iy),this._backgroundEl,this.group.add(this._selectorGroup=new Iy),this._isFirstRender=!0},getContentGroup:function(){return this._contentGroup},getSelectorGroup:function(){return this._selectorGroup},render:function(t,e,n){var i,r,o,a,s,l,h,u,c,d,f=this._isFirstRender;this._isFirstRender=!1,this.resetInner(),t.get("show",!0)&&(i=t.get("align"),r=t.get("orient"),i&&"auto"!==i||(i="right"===t.get("left")&&"vertical"===r?"right":"left"),o=t.get("selector",!0),a=t.get("selectorPosition",!0),!o||a&&"auto"!==a||(a="horizontal"===r?"end":"start"),this.renderInner(i,t,e,n,o,r,a),u=Kl(s=t.getBoxLayoutParams(),l={width:n.getWidth(),height:n.getHeight()},h=t.get("padding")),d=Kl(A({width:(c=this.layoutInner(t,i,u,f,o,a)).width,height:c.height},s),l,h),this.group.attr("position",[d.x-c.x,d.y-c.y]),this.group.add(this._backgroundEl=by(c,t)))},resetInner:function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},renderInner:function(h,u,c,d,t,e,n){var f=this.getContentGroup(),p=tt(),g=u.get("selectedMode"),m=[];c.eachRawSeries(function(t){t.get("legendHoverLink")||m.push(t.id)}),My(u.getData(),function(o,a){var t,e,n,i,r,s,l=o.get("name");this.newlineDisabled||""!==l&&"\n"!==l?(t=c.getSeriesByName(l)[0],p.get(l)||(t?(n=(e=t.getData()).getVisual("color"),i=e.getVisual("borderColor"),"function"==typeof n&&(n=n(t.getDataParams(0))),"function"==typeof i&&(i=i(t.getDataParams(0))),r=e.getVisual("legendSymbol")||"roundRect",s=e.getVisual("symbol"),this._createItem(l,a,o,u,r,s,h,n,i,g).on("click",Sy(Ay,l,null,d,m)).on("mouseover",Sy(Dy,t.name,null,d,m)).on("mouseout",Sy(ky,t.name,null,d,m)),p.set(l,!0)):c.eachRawSeries(function(t){if(!p.get(l)&&t.legendVisualProvider){var e=t.legendVisualProvider;if(!e.containName(l))return;var n=e.indexOfName(l),i=e.getItemVisual(n,"color"),r=e.getItemVisual(n,"borderColor");this._createItem(l,a,o,u,"roundRect",null,h,i,r,g).on("click",Sy(Ay,null,l,d,m)).on("mouseover",Sy(Dy,null,l,d,m)).on("mouseout",Sy(ky,null,l,d,m)),p.set(l,!0)}},this),C&&p.get(l))):f.add(new Iy({newline:!0}))},this),t&&this._createSelector(t,u,d,e,n)},_createSelector:function(t,o,a){var s=this.getSelectorGroup();My(t,function(t){!function(t){var e=t.type,n=new Aa({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){a.dispatchAction({type:"all"===e?"legendAllSelect":"legendInverseSelect"})}});s.add(n);var i=o.getModel("selectorLabel"),r=o.getModel("emphasis.selectorLabel");Rs(n.style,n.hoverStyle={},i,r,{defaultText:t.title,isRectText:!1}),Os(n)}(t)})},_createItem:function(t,e,n,i,r,o,a,s,l,h){var u,c,d=i.get("itemWidth"),f=i.get("itemHeight"),p=i.get("inactiveColor"),g=i.get("inactiveBorderColor"),m=i.get("symbolKeepAspect"),v=i.getModel("itemStyle"),y=i.isSelected(t),_=new Iy,x=n.getModel("textStyle"),w=n.get("icon"),b=n.getModel("tooltip"),S=b.parentModel,M=Np(r=w||r,0,0,d,f,y?s:p,null==m||m);_.add(Ty(M,r,v,l,g,y)),w||!o||o===r&&"none"!==o||("none"===o&&(o="circle"),c=Np(o,(d-(u=.8*f))/2,(f-u)/2,u,u,y?s:p,null==m||m),_.add(Ty(c,o,v,l,g,y)));var I="left"===a?d+5:-5,C=a,T=i.get("formatter"),A=t;"string"==typeof T&&T?A=T.replace("{name}",null!=t?t:""):"function"==typeof T&&(A=T(t)),_.add(new Aa({style:Bs({},x,{text:A,x:I,y:f/2,textFill:y?x.getTextColor():p,textAlign:C,textVerticalAlign:"middle"})}));var D=new Ga({shape:_.getBoundingRect(),invisible:!0,tooltip:b.get("show")?k({content:t,formatter:S.get("formatter",!0)||function(){return t},formatterParams:{componentType:"legend",legendIndex:i.componentIndex,name:t,$vars:["name"]}},b.option):null});return _.add(D),_.eachChild(function(t){t.silent=!0}),D.silent=!h,this.getContentGroup().add(_),Os(_),_.__legendDataIndex=e,_},layoutInner:function(t,e,n,i,r,o){var a=this.getContentGroup(),s=this.getSelectorGroup();$l(t.get("orient"),a,t.get("itemGap"),n.width,n.height);var l=a.getBoundingRect(),h=[-l.x,-l.y];if(r){$l("horizontal",s,t.get("selectorItemGap",!0));var u=s.getBoundingRect(),c=[-u.x,-u.y],d=t.get("selectorButtonGap",!0),f=t.getOrient().index,p=0===f?"width":"height",g=0===f?"height":"width",m=0===f?"y":"x";"end"===o?c[f]+=l[p]+d:h[f]+=u[p]+d,c[1-f]+=l[g]/2-u[g]/2,s.attr("position",c),a.attr("position",h);var v={x:0,y:0};return v[p]=l[p]+d+u[p],v[g]=Math.max(l[g],u[g]),v[m]=Math.min(0,u[m]+c[1-f]),v}return a.attr("position",h),this.group.getBoundingRect()},remove:function(){this.getContentGroup().removeAll(),this._isFirstRender=!0}});function Ty(t,e,n,i,r,o){var a;return"line"!==e&&e.indexOf("empty")<0?(a=n.getItemStyle(),t.style.stroke=i,o||(a.stroke=r)):a=n.getItemStyle(["borderWidth","borderColor"]),t.setStyle(a)}function Ay(t,e,n,i){ky(t,e,n,i),n.dispatchAction({type:"legendToggleSelect",name:null!=t?t:e}),Dy(t,e,n,i)}function Dy(t,e,n,i){var r=n.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||n.dispatchAction({type:"highlight",seriesName:t,name:e,excludeSeriesId:i})}function ky(t,e,n,i){var r=n.getZr().storage.getDisplayList()[0];r&&r.useHoverLayer||n.dispatchAction({type:"downplay",seriesName:t,name:e,excludeSeriesId:i})}zd(Xc.PROCESSOR.SERIES_FILTER,function(t){var n=t.findComponents({mainType:"legend"});n&&n.length&&t.filterSeries(function(t){for(var e=0;e<n.length;e++)if(!n[e].isSelected(t.name))return!1;return!0})}),ah.registerSubTypeDefaulter("legend",function(){return"plain"});var Py=xy.extend({type:"legend.scroll",setScrollDataIndex:function(t){this.option.scrollDataIndex=t},defaultOption:{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800},init:function(t,e,n,i){var r=th(t);Py.superCall(this,"init",t,e,n,i),Ly(this,t,r)},mergeOption:function(t,e){Py.superCall(this,"mergeOption",t,e),Ly(this,this.option,t)}});function Ly(t,e,n){var i=[1,1];i[t.getOrient().index]=0,Jl(e,n,{type:"box",ignoreSize:i})}var Oy=_n,Ey=["width","height"],zy=["x","y"],Ny=Cy.extend({type:"legend.scroll",newlineDisabled:!0,init:function(){Ny.superCall(this,"init"),this._currentIndex=0,this.group.add(this._containerGroup=new Oy),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new Oy),this._showController},resetInner:function(){Ny.superCall(this,"resetInner"),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},renderInner:function(t,r,e,o,n,i,a){var s=this;Ny.superCall(this,"renderInner",t,r,e,o,n,i,a);var l=this._controllerGroup,h=r.get("pageIconSize",!0);z(h)||(h=[h,h]),c("pagePrev",0);var u=r.getModel("pageTextStyle");function c(t,e){var n=t+"DataIndex",i=Js(r.get("pageIcons",!0)[r.getOrient().name][e],{onclick:L(s._pageGo,s,n,r,o)},{x:-h[0]/2,y:-h[1]/2,width:h[0],height:h[1]});i.name=t,l.add(i)}l.add(new Aa({name:"pageText",style:{textFill:u.getTextColor(),font:u.getFont(),textVerticalAlign:"middle",textAlign:"center"},silent:!0})),c("pageNext",1)},layoutInner:function(t,e,n,i,r,o){var a=this.getSelectorGroup(),s=t.getOrient().index,l=Ey[s],h=zy[s],u=Ey[1-s],c=zy[1-s];r&&$l("horizontal",a,t.get("selectorItemGap",!0));var d=t.get("selectorButtonGap",!0),f=a.getBoundingRect(),p=[-f.x,-f.y],g=T(n);r&&(g[l]=n[l]-f[l]-d);var m,v=this._layoutContentAndController(t,i,g,s,l,u,c);return r&&("end"===o?p[s]+=v[l]+d:(m=f[l]+d,p[s]-=m,v[h]-=m),v[l]+=f[l]+d,p[1-s]+=v[c]+v[u]/2-f[u]/2,v[u]=Math.max(v[u],f[u]),v[c]=Math.min(v[c],f[c]+p[1-s]),a.attr("position",p)),v},_layoutContentAndController:function(t,e,n,i,r,o,a){var s=this.getContentGroup(),l=this._containerGroup,h=this._controllerGroup;$l(t.get("orient"),s,t.get("itemGap"),i?n.width:null,i?null:n.height),$l("horizontal",h,t.get("pageButtonItemGap",!0));var u=s.getBoundingRect(),c=h.getBoundingRect(),d=this._showController=u[r]>n[r],f=[-u.x,-u.y];e||(f[i]=s.position[i]);var p=[0,0],g=[-c.x,-c.y],m=Z(t.get("pageButtonGap",!0),t.get("itemGap",!0));d&&("end"===t.get("pageButtonPosition",!0)?g[i]+=n[r]-c[r]:p[i]+=c[r]+m),g[1-i]+=u[o]/2-c[o]/2,s.attr("position",f),l.attr("position",p),h.attr("position",g);var v,y={x:0,y:0};y[r]=d?n[r]:u[r],y[o]=Math.max(u[o],c[o]),y[a]=Math.min(0,c[a]+g[1-i]),l.__rectSize=n[r],d?((v={x:0,y:0})[r]=Math.max(n[r]-c[r]-m,0),v[o]=y[o],l.setClipPath(new Ga({shape:v})),l.__rectSize=v[r]):h.eachChild(function(t){t.attr({invisible:!0,silent:!0})});var _=this._getPageInfo(t);return null!=_.pageIndex&&Xs(s,{position:_.contentPosition},d&&t),this._updatePageInfoView(t,_),y},_pageGo:function(t,e,n){var i=this._getPageInfo(e)[t];null!=i&&n.dispatchAction({type:"legendScroll",scrollDataIndex:i,legendId:e.id})},_updatePageInfoView:function(i,r){var o=this._controllerGroup;D(["pagePrev","pageNext"],function(t){var e=null!=r[t+"DataIndex"],n=o.childOfName(t);n&&(n.setStyle("fill",e?i.get("pageIconColor",!0):i.get("pageIconInactiveColor",!0)),n.cursor=e?"pointer":"default")});var t=o.childOfName("pageText"),e=i.get("pageFormatter"),n=r.pageIndex,a=null!=n?n+1:0,s=r.pageCount;t&&e&&t.setStyle("text",R(e)?e.replace("{current}",a).replace("{total}",s):e({current:a,total:s}))},_getPageInfo:function(t){var e=t.get("scrollDataIndex",!0),n=this.getContentGroup(),i=this._containerGroup.__rectSize,r=t.getOrient().index,o=Ey[r],a=zy[r],s=this._findTargetItemIndex(e),l=n.children(),h=l[s],u=l.length,c=u?1:0,d={contentPosition:n.position.slice(),pageCount:c,pageIndex:c-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!h)return d;var f=y(h);d.contentPosition[r]=-f.s;for(var p=s+1,g=f,m=f,v=null;p<=u;++p)(!(v=y(l[p]))&&m.e>g.s+i||v&&!_(v,g.s))&&(g=m.i>g.i?m:v)&&(null==d.pageNextDataIndex&&(d.pageNextDataIndex=g.i),++d.pageCount),m=v;for(p=s-1,g=f,m=f,v=null;-1<=p;--p)(v=y(l[p]))&&_(m,v.s)||!(g.i<m.i)||(m=g,null==d.pagePrevDataIndex&&(d.pagePrevDataIndex=g.i),++d.pageCount,++d.pageIndex),g=v;return d;function y(t){if(t){var e=t.getBoundingRect(),n=e[a]+t.position[r];return{s:n,e:n+e[o],i:t.__legendDataIndex}}}function _(t,e){return t.e>=e&&t.s<=e+i}},_findTargetItemIndex:function(i){return this._showController?(this.getContentGroup().eachChild(function(t,e){var n=t.__legendDataIndex;null==o&&null!=n&&(o=e),n===i&&(r=e)}),null!=r?r:o):0;var r,o}});Nd("legendScroll","legendscroll",function(t,e){var n=t.scrollDataIndex;null!=n&&e.eachComponent({mainType:"legend",subType:"scroll",query:t},function(t){t.setScrollDataIndex(n)})}),Hd({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),Wd({type:"title",render:function(t,e,n){var i,r,o,a,s,l,h,u,c,d,f,p,g,m,v,y,_,x,w;this.group.removeAll(),t.get("show")&&(i=this.group,r=t.getModel("textStyle"),o=t.getModel("subtextStyle"),a=t.get("textAlign"),s=Z(t.get("textBaseline"),t.get("textVerticalAlign")),h=(l=new Aa({style:Bs({},r,{text:t.get("text"),textFill:r.getTextColor()},{disableBox:!0}),z2:10})).getBoundingRect(),u=t.get("subtext"),c=new Aa({style:Bs({},o,{text:u,textFill:o.getTextColor(),y:h.height+t.get("itemGap"),textVerticalAlign:"top"},{disableBox:!0}),z2:10}),d=t.get("link"),f=t.get("sublink"),p=t.get("triggerEvent",!0),l.silent=!d&&!p,c.silent=!f&&!p,d&&l.on("click",function(){Zl(d,"_"+t.get("target"))}),f&&c.on("click",function(){Zl(f,"_"+t.get("subtarget"))}),l.eventData=c.eventData=p?{componentType:"title",componentIndex:t.componentIndex}:null,i.add(l),u&&i.add(c),g=i.getBoundingRect(),(m=t.getBoxLayoutParams()).width=g.width,m.height=g.height,v=Kl(m,{width:n.getWidth(),height:n.getHeight()},t.get("padding")),a||("middle"===(a=t.get("left")||t.get("right"))&&(a="center"),"right"===a?v.x+=v.width:"center"===a&&(v.x+=v.width/2)),s||("center"===(s=t.get("top")||t.get("bottom"))&&(s="middle"),"bottom"===s?v.y+=v.height:"middle"===s&&(v.y+=v.height/2),s=s||"top"),i.attr("position",[v.x,v.y]),y={textAlign:a,textVerticalAlign:s},l.setStyle(y),c.setStyle(y),g=i.getBoundingRect(),_=v.margin,(x=t.getItemStyle(["color","opacity"])).fill=t.get("backgroundColor"),w=new Ga({shape:{x:g.x-_[3],y:g.y-_[0],width:g.width+_[1]+_[3],height:g.height+_[0]+_[2],r:t.get("borderRadius")},style:x,subPixelOptimize:!0,silent:!0}),i.add(w))}});var Ry=kl,By=zl;function Vy(t){Tr(t,"label",["show"])}var Fy=Hd({type:"marker",dependencies:["series","grid","polar","geo"],init:function(t,e,n){if(C&&"marker"===this.type)throw new Error("Marker component is abstract component. Use markLine, markPoint, markArea instead.");this.mergeDefaultAndTheme(t,n),this._mergeOption(t,n,!1,!0)},isAnimationEnabled:function(){if(v.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},mergeOption:function(t,e){this._mergeOption(t,e,!1,!1)},_mergeOption:function(t,i,e,r){var o=this.constructor,a=this.mainType+"Model";e||i.eachSeries(function(t){var e=t.get(this.mainType,!0),n=t[a];e&&e.data?(n?n._mergeOption(e,i,!0):(r&&Vy(e),D(e.data,function(t){t instanceof Array?(Vy(t[0]),Vy(t[1])):Vy(t)}),k(n=new o(e,this,i),{mainType:this.mainType,seriesIndex:t.seriesIndex,name:t.name,createdBySelf:!0}),n.__hostSeries=t),t[a]=n):t[a]=null},this)},formatTooltip:function(t,e,n,i){var r=this.getData(),o=this.getRawValue(t),a=z(o)?P(o,Ry).join(", "):Ry(o),s=r.getName(t),l=By(this.name);return null==o&&!s||(l+="html"===i?"<br/>":"\n"),s&&(l+=By(s),null!=o&&(l+=" : ")),null!=o&&(l+=By(a)),l},getData:function(){return this._data},setData:function(t){this._data=t}});S(Fy,xu),Fy.extend({type:"markPoint",defaultOption:{zlevel:0,z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}}});var Hy=w;function Wy(t,e,n,i,r,o){var a=[],s=Df(e,i)?e.getCalculationInfo("stackResultDimension"):i,l=qy(e,s,t),h=e.indicesOfNearest(s,l)[0];a[r]=e.get(n,h),a[o]=e.get(s,h);var u=e.get(i,h),c=yl(e.get(i,h));return 0<=(c=Math.min(c,20))&&(a[o]=+a[o].toFixed(c)),[a,u]}var Gy=O,Zy={min:Gy(Wy,"min"),max:Gy(Wy,"max"),average:Gy(Wy,"average")};function Uy(t,e){var n,i=t.getData(),r=t.coordinateSystem;if(e&&(n=e,isNaN(parseFloat(n.x))||isNaN(parseFloat(n.y)))&&!z(e.coord)&&r){var o=r.dimensions,a=Xy(e,i,r,t);if((e=T(e)).type&&Zy[e.type]&&a.baseAxis&&a.valueAxis){var s=Hy(o,a.baseAxis.dim),l=Hy(o,a.valueAxis.dim),h=Zy[e.type](i,a.baseDataDim,a.valueDataDim,s,l);e.coord=h[0],e.value=h[1]}else{for(var u=[null!=e.xAxis?e.xAxis:e.radiusAxis,null!=e.yAxis?e.yAxis:e.angleAxis],c=0;c<2;c++)Zy[u[c]]&&(u[c]=qy(i,i.mapDimension(o[c]),u[c]));e.coord=u}}return e}function Xy(t,e,n,i){var r={};return null!=t.valueIndex||null!=t.valueDim?(r.valueDataDim=null!=t.valueIndex?e.getDimension(t.valueIndex):t.valueDim,r.valueAxis=n.getAxis(function(t,e){var n=t.getData(),i=n.dimensions;e=n.getDimension(e);for(var r=0;r<i.length;r++){var o=n.getDimensionInfo(i[r]);if(o.name===e)return o.coordDim}}(i,r.valueDataDim)),r.baseAxis=n.getOtherAxis(r.valueAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim)):(r.baseAxis=i.getBaseAxis(),r.valueAxis=n.getOtherAxis(r.baseAxis),r.baseDataDim=e.mapDimension(r.baseAxis.dim),r.valueDataDim=e.mapDimension(r.valueAxis.dim)),r}function Yy(t,e){return!(t&&t.containData&&e.coord&&(n=e,isNaN(parseFloat(n.x))&&isNaN(parseFloat(n.y))))||t.containData(e.coord);var n}function jy(t,e,n,i){return i<2?t.coord&&t.coord[i]:t.value}function qy(t,e,n){if("average"!==n)return"median"===n?t.getMedian(e):t.getDataExtent(e,!0)["max"===n?1:0];var i=0,r=0;return t.each(e,function(t,e){isNaN(t)||(i+=t,r++)}),i/r}var $y=Wd({type:"marker",init:function(){this.markerGroupMap=tt()},render:function(t,n,i){var e=this.markerGroupMap;e.each(function(t){t.__keep=!1});var r=this.type+"Model";n.eachSeries(function(t){var e=t[r];e&&this.renderSeries(t,e,n,i)},this),e.each(function(t){t.__keep||this.group.remove(t.group)},this)},renderSeries:function(){}});function Ky(s,l,h){var u=l.coordinateSystem;s.each(function(t){var e,n,i,r=s.getItemModel(t),o=gl(r.get("x"),h.getWidth()),a=gl(r.get("y"),h.getHeight());isNaN(o)||isNaN(a)?l.getMarkerPosition?i=l.getMarkerPosition(s.getValues(s.dimensions,t)):u&&(e=s.get(u.dimensions[0],t),n=s.get(u.dimensions[1],t),i=u.dataToPoint([e,n])):i=[o,a],isNaN(o)||(i[0]=o),isNaN(a)||(i[1]=a),s.setItemLayout(t,i)})}$y.extend({type:"markPoint",updateTransform:function(t,e,n){e.eachSeries(function(t){var e=t.markPointModel;e&&(Ky(e.getData(),t,n),this.markerGroupMap.get(t.id).updateLayout(e))},this)},renderSeries:function(t,u,e,n){var i=t.coordinateSystem,r=t.id,c=t.getData(),o=this.markerGroupMap,a=o.get(r)||o.set(r,new vg),d=function(t,e,n){var i;i=t?P(t&&t.dimensions,function(t){return A({name:t},e.getData().getDimensionInfo(e.getData().mapDimension(t))||{})}):[{name:"value",type:"float"}];var r=new hf(i,n),o=P(n.get("data"),O(Uy,e));t&&(o=I(o,O(Yy,t)));return r.initData(o,null,t?jy:function(t){return t.value}),r}(i,t,u);u.setData(d),Ky(u.getData(),t,n),d.each(function(t){var e,n,i=d.getItemModel(t),r=i.getShallow("symbol"),o=i.getShallow("symbolSize"),a=i.getShallow("symbolRotate"),s=N(r),l=N(o),h=N(a);(s||l||h)&&(e=u.getRawValue(t),n=u.getDataParams(t),s&&(r=r(e,n)),l&&(o=o(e,n)),h&&(a=a(e,n))),d.setItemVisual(t,{symbol:r,symbolSize:o,symbolRotate:a,color:i.get("itemStyle.color")||c.getVisual("color")})}),a.updateData(d),this.group.add(a.group),d.eachItemGraphicEl(function(t){t.traverse(function(t){t.dataModel=u})}),a.__keep=!0,a.group.silent=u.get("silent")||t.get("silent")}}),Ed(function(t){t.markPoint=t.markPoint||{}}),Fy.extend({type:"markLine",defaultOption:{zlevel:0,z:5,symbol:["circle","arrow"],symbolSize:[8,16],precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"}});var Qy=Ua.prototype,Jy=qa.prototype;function t_(t){return isNaN(+t.cpx1)||isNaN(+t.cpy1)}var e_=cs({type:"ec-line",style:{stroke:"#000",fill:null},shape:{x1:0,y1:0,x2:0,y2:0,percent:1,cpx1:null,cpy1:null},buildPath:function(t,e){this[t_(e)?"_buildPathLine":"_buildPathCurve"](t,e)},_buildPathLine:Qy.buildPath,_buildPathCurve:Jy.buildPath,pointAt:function(t){return this[t_(this.shape)?"_pointAtLine":"_pointAtCurve"](t)},_pointAtLine:Qy.pointAt,_pointAtCurve:Jy.pointAt,tangentAt:function(t){var e=this.shape,n=t_(e)?[e.x2-e.x1,e.y2-e.y1]:this._tangentAtCurve(t);return ft(n,n)},_tangentAtCurve:Jy.tangentAt}),n_=["fromSymbol","toSymbol"];function i_(t){return"_"+t+"Type"}function r_(t,e,n){var i=e.getItemVisual(n,t);if(i&&"none"!==i){var r=e.getItemVisual(n,"color"),o=e.getItemVisual(n,t+"Size"),a=e.getItemVisual(n,t+"Rotate");z(o)||(o=[o,o]);var s=Np(i,-o[0]/2,-o[1]/2,o[0],o[1],r);return s.__specifiedRotation=null==a||isNaN(a)?void 0:a*Math.PI/180||0,s.name=t,s}}function o_(t,e){t.x1=e[0][0],t.y1=e[0][1],t.x2=e[1][0],t.y2=e[1][1],t.percent=1;var n=e[2];n?(t.cpx1=n[0],t.cpy1=n[1]):(t.cpx1=NaN,t.cpy1=NaN)}function a_(t,e,n){_n.call(this),this._createLine(t,e,n)}var s_=a_.prototype;function l_(t){this._ctor=t||a_,this.group=new _n}s_.beforeUpdate=function(){var t=this.childOfName("fromSymbol"),e=this.childOfName("toSymbol"),n=this.childOfName("label");if(t||e||!n.ignore){for(var i=1,r=this.parent;r;)r.scale&&(i/=r.scale[0]),r=r.parent;var o=this.childOfName("line");if(this.__dirty||o.__dirty){var a,s,l,h,u,c=o.shape.percent,d=o.pointAt(0),f=o.pointAt(c),p=ht([],f,d);if(ft(p,p),t&&(t.attr("position",d),null==(a=t.__specifiedRotation)?(g=o.tangentAt(0),t.attr("rotation",Math.PI/2-Math.atan2(g[1],g[0]))):t.attr("rotation",a),t.attr("scale",[i*c,i*c])),e&&(e.attr("position",f),null==(a=e.__specifiedRotation)?(g=o.tangentAt(1),e.attr("rotation",-Math.PI/2-Math.atan2(g[1],g[0]))):e.attr("rotation",a),e.attr("scale",[i*c,i*c])),!n.ignore){n.attr("position",f);var g,m=n.__labelDistance,v=m[0]*i,y=m[1]*i,_=c/2,x=[(g=o.tangentAt(_))[1],-g[0]],w=o.pointAt(_);0<x[1]&&(x[0]=-x[0],x[1]=-x[1]);var b,S,M=g[0]<0?-1:1;switch("start"!==n.__position&&"end"!==n.__position&&(b=-Math.atan2(g[1],g[0]),f[0]<d[0]&&(b=Math.PI+b),n.attr("rotation",b)),n.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":S=-y,h="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":S=y,h="top";break;default:S=0,h="middle"}switch(n.__position){case"end":s=[p[0]*v+f[0],p[1]*y+f[1]],l=.8<p[0]?"left":p[0]<-.8?"right":"center",h=.8<p[1]?"top":p[1]<-.8?"bottom":"middle";break;case"start":s=[-p[0]*v+d[0],-p[1]*y+d[1]],l=.8<p[0]?"right":p[0]<-.8?"left":"center",h=.8<p[1]?"bottom":p[1]<-.8?"top":"middle";break;case"insideStartTop":case"insideStart":case"insideStartBottom":s=[v*M+d[0],d[1]+S],l=g[0]<0?"right":"left",u=[-v*M,-S];break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":s=[w[0],w[1]+S],l="center",u=[0,-S];break;case"insideEndTop":case"insideEnd":case"insideEndBottom":s=[-v*M+f[0],f[1]+S],l=0<=g[0]?"right":"left",u=[v*M,-S]}n.attr({style:{textVerticalAlign:n.__verticalAlign||h,textAlign:n.__textAlign||l},position:s,scale:[i,i],origin:u})}}}},s_._createLine=function(n,i,t){var e,r,o=n.hostModel,a=n.getItemLayout(i),s=(e=a,o_((r=new e_({name:"line",subPixelOptimize:!0})).shape,e),r);s.shape.percent=0,Ys(s,{shape:{percent:1}},o,i),this.add(s);var l=new Aa({name:"label",lineLabelOriginalOpacity:1});this.add(l),D(n_,function(t){var e=r_(t,n,i);this.add(e),this[i_(t)]=n.getItemVisual(i,t)},this),this._updateCommonStl(n,i,t)},s_.updateData=function(r,o,t){var e=r.hostModel,n=this.childOfName("line"),i=r.getItemLayout(o),a={shape:{}};o_(a.shape,i),Xs(n,a,e,o),D(n_,function(t){var e,n=r.getItemVisual(o,t),i=i_(t);this[i]!==n&&(this.remove(this.childOfName(t)),e=r_(t,r,o),this.add(e)),this[i]=n},this),this._updateCommonStl(r,o,t)},s_._updateCommonStl=function(t,e,n){var i,r=t.hostModel,o=this.childOfName("line"),a=n&&n.lineStyle,s=n&&n.hoverLineStyle,l=n&&n.labelModel,h=n&&n.hoverLabelModel;n&&!t.hasItemOption||(a=(i=t.getItemModel(e)).getModel("lineStyle").getLineStyle(),s=i.getModel("emphasis.lineStyle").getLineStyle(),l=i.getModel("label"),h=i.getModel("emphasis.label"));var u=t.getItemVisual(e,"color"),c=U(t.getItemVisual(e,"opacity"),a.opacity,1);o.useStyle(A({strokeNoScale:!0,fill:"none",stroke:u,opacity:c},a)),o.hoverStyle=s,D(n_,function(t){var e=this.childOfName(t);e&&(e.setColor(u),e.setStyle({opacity:c}))},this);var d,f,p,g=l.getShallow("show"),m=h.getShallow("show"),v=this.childOfName("label");(g||m)&&(p=u||"#000",null==(f=r.getFormattedLabel(e,"normal",t.dataType))&&(f=null==(d=r.getRawValue(e))?t.getName(e):isFinite(d)?ml(d):d));var y,_=g?f:null,x=m?Z(r.getFormattedLabel(e,"emphasis",t.dataType),f):null,w=v.style;null==_&&null==x||(Bs(v.style,l,{text:_},{autoColor:p}),v.__textAlign=w.textAlign,v.__verticalAlign=w.textVerticalAlign,v.__position=l.get("position")||"middle",z(y=l.get("distance"))||(y=[y,y]),v.__labelDistance=y),v.hoverStyle=null!=x?{text:x,textFill:h.getTextColor(!0),fontStyle:h.getShallow("fontStyle"),fontWeight:h.getShallow("fontWeight"),fontSize:h.getShallow("fontSize"),fontFamily:h.getShallow("fontFamily")}:{text:null},v.ignore=!g&&!m,Os(this)},s_.highlight=function(){this.trigger("emphasis")},s_.downplay=function(){this.trigger("normal")},s_.updateLayout=function(t,e){this.setLinePoints(t.getItemLayout(e))},s_.setLinePoints=function(t){var e=this.childOfName("line");o_(e.shape,t),e.dirty()},b(a_,_n);var h_=l_.prototype;function u_(t){var e=t.hostModel;return{lineStyle:e.getModel("lineStyle").getLineStyle(),hoverLineStyle:e.getModel("emphasis.lineStyle").getLineStyle(),labelModel:e.getModel("label"),hoverLabelModel:e.getModel("emphasis.label")}}function c_(t){return isNaN(t[0])||isNaN(t[1])}function d_(t){return!c_(t[0])&&!c_(t[1])}h_.isPersistent=function(){return!0},h_.updateData=function(n){var i=this,e=i.group,r=i._lineData;i._lineData=n,r||e.removeAll();var o=u_(n);n.diff(r).add(function(t){!function(t,e,n,i){if(!d_(e.getItemLayout(n)))return;var r=new t._ctor(e,n,i);e.setItemGraphicEl(n,r),t.group.add(r)}(i,n,t,o)}).update(function(t,e){!function(t,e,n,i,r,o){var a=e.getItemGraphicEl(i);if(!d_(n.getItemLayout(r)))return t.group.remove(a);a?a.updateData(n,r,o):a=new t._ctor(n,r,o);n.setItemGraphicEl(r,a),t.group.add(a)}(i,r,n,e,t,o)}).remove(function(t){e.remove(r.getItemGraphicEl(t))}).execute()},h_.updateLayout=function(){var n=this._lineData;n&&n.eachItemGraphicEl(function(t,e){t.updateLayout(n,e)},this)},h_.incrementalPrepareUpdate=function(t){this._seriesScope=u_(t),this._lineData=null,this.group.removeAll()},h_.incrementalUpdate=function(t,e){function n(t){var e;t.isGroup||(e=t).animators&&0<e.animators.length||(t.incremental=t.useHoverLayer=!0)}for(var i,r=t.start;r<t.end;r++){d_(e.getItemLayout(r))&&((i=new this._ctor(e,r,this._seriesScope)).traverse(n),this.group.add(i),e.setItemGraphicEl(r,i))}},h_.remove=function(){this._clearIncremental(),this._incremental=null,this.group.removeAll()},h_._clearIncremental=function(){var t=this._incremental;t&&t.clearDisplaybles()};function f_(t,e,n,i){var r,o,a,s,l,h,u,c,d=t.getData(),f=i.type;return z(i)||"min"!==f&&"max"!==f&&"average"!==f&&"median"!==f&&null==i.xAxis&&null==i.yAxis||(a=null!=i.yAxis||null!=i.xAxis?(o=e.getAxis(null!=i.yAxis?"y":"x"),G(i.yAxis,i.xAxis)):(o=(r=Xy(i,d,e,t)).valueAxis,qy(d,kf(d,r.valueDataDim),f)),l=1-(s="x"===o.dim?0:1),u={},(h=T(i)).type=null,h.coord=[],u.coord=[],h.coord[l]=-1/0,u.coord[l]=1/0,0<=(c=n.get("precision"))&&"number"==typeof a&&(a=+a.toFixed(Math.min(c,20))),h.coord[s]=u.coord[s]=a,i=[h,u,{type:f,valueIndex:i.valueIndex,value:a}]),(i=[Uy(t,i[0]),Uy(t,i[1]),k({},i[2])])[2].type=i[2].type||"",m(i[2],i[0]),m(i[2],i[1]),i}function p_(t){return!isNaN(t)&&!isFinite(t)}function g_(t,e,n,i){var r=1-t,o=i.dimensions[t];return p_(e[r])&&p_(n[r])&&e[t]===n[t]&&i.getAxis(o).containData(e[t])}function m_(t,e){if("cartesian2d"===t.type){var n=e[0].coord,i=e[1].coord;if(n&&i&&(g_(1,n,i,t)||g_(0,n,i,t)))return!0}return Yy(t,e[0])&&Yy(t,e[1])}function v_(t,e,n,i,r){var o,a,s,l,h,u,c=i.coordinateSystem,d=t.getItemModel(e),f=gl(d.get("x"),r.getWidth()),p=gl(d.get("y"),r.getHeight());isNaN(f)||isNaN(p)?(s=i.getMarkerPosition?i.getMarkerPosition(t.getValues(t.dimensions,e)):(u=c.dimensions,o=t.get(u[0],e),a=t.get(u[1],e),c.dataToPoint([o,a])),"cartesian2d"===c.type&&(l=c.getAxis("x"),h=c.getAxis("y"),u=c.dimensions,p_(t.get(u[0],e))?s[0]=l.toGlobalCoord(l.getExtent()[n?0:1]):p_(t.get(u[1],e))&&(s[1]=h.toGlobalCoord(h.getExtent()[n?0:1]))),isNaN(f)||(s[0]=f),isNaN(p)||(s[1]=p)):s=[f,p],t.setItemLayout(e,s)}$y.extend({type:"markLine",updateTransform:function(t,e,o){e.eachSeries(function(e){var n,i,r,t=e.markLineModel;t&&(n=t.getData(),i=t.__from,r=t.__to,i.each(function(t){v_(i,t,!0,e,o),v_(r,t,!1,e,o)}),n.each(function(t){n.setItemLayout(t,[i.getItemLayout(t),r.getItemLayout(t)])}),this.markerGroupMap.get(e.id).updateLayout())},this)},renderSeries:function(r,n,t,o){var e=r.coordinateSystem,i=r.id,a=r.getData(),s=this.markerGroupMap,l=s.get(i)||s.set(i,new l_);this.group.add(l.group);var h=function(t,e,n){var i;i=t?P(t&&t.dimensions,function(t){return A({name:t},e.getData().getDimensionInfo(e.getData().mapDimension(t))||{})}):[{name:"value",type:"float"}];var r=new hf(i,n),o=new hf(i,n),a=new hf([],n),s=P(n.get("data"),O(f_,e,t,n));t&&(s=I(s,O(m_,t)));var l=t?jy:function(t){return t.value};return r.initData(P(s,function(t){return t[0]}),null,l),o.initData(P(s,function(t){return t[1]}),null,l),a.initData(P(s,function(t){return t[2]})),a.hasItemOption=!0,{from:r,to:o,line:a}}(e,r,n),u=h.from,c=h.to,d=h.line;n.__from=u,n.__to=c,n.setData(d);var f=n.get("symbol"),p=n.get("symbolSize");function g(t,e,n){var i=t.getItemModel(e);v_(t,e,n,r,o),t.setItemVisual(e,{symbolRotate:i.get("symbolRotate"),symbolSize:i.get("symbolSize")||p[n?0:1],symbol:i.get("symbol",!0)||f[n?0:1],color:i.get("itemStyle.color")||a.getVisual("color")})}z(f)||(f=[f,f]),"number"==typeof p&&(p=[p,p]),h.from.each(function(t){g(u,t,!0),g(c,t,!1)}),d.each(function(t){var e=d.getItemModel(t).get("lineStyle.color");d.setItemVisual(t,{color:e||u.getItemVisual(t,"color")}),d.setItemLayout(t,[u.getItemLayout(t),c.getItemLayout(t)]),d.setItemVisual(t,{fromSymbolRotate:u.getItemVisual(t,"symbolRotate"),fromSymbolSize:u.getItemVisual(t,"symbolSize"),fromSymbol:u.getItemVisual(t,"symbol"),toSymbolRotate:c.getItemVisual(t,"symbolRotate"),toSymbolSize:c.getItemVisual(t,"symbolSize"),toSymbol:c.getItemVisual(t,"symbol")})}),l.updateData(d),h.line.eachItemGraphicEl(function(t,e){t.traverse(function(t){t.dataModel=n})}),l.__keep=!0,l.group.silent=n.get("silent")||r.get("silent")}}),Ed(function(t){t.markLine=t.markLine||{}}),Fy.extend({type:"markArea",defaultOption:{zlevel:0,z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}}});function y_(t,e,n,i){var r=Uy(t,i[0]),o=Uy(t,i[1]),a=G,s=r.coord,l=o.coord;s[0]=a(s[0],-1/0),s[1]=a(s[1],-1/0),l[0]=a(l[0],1/0),l[1]=a(l[1],1/0);var h=g([{},r,o]);return h.coord=[r.coord,o.coord],h.x0=r.x,h.y0=r.y,h.x1=o.x,h.y1=o.y,h}function __(t){return!isNaN(t)&&!isFinite(t)}function x_(t,e,n){var i=1-t;return __(e[i])&&__(n[i])}function w_(t,e){var n=e.coord[0],i=e.coord[1];return!("cartesian2d"!==t.type||!n||!i||!x_(1,n,i)&&!x_(0,n,i))||(Yy(t,{coord:n,x:e.x0,y:e.y0})||Yy(t,{coord:i,x:e.x1,y:e.y1}))}function b_(t,e,n,i,r){var o,a,s,l,h,u,c=i.coordinateSystem,d=t.getItemModel(e),f=gl(d.get(n[0]),r.getWidth()),p=gl(d.get(n[1]),r.getHeight());return isNaN(f)||isNaN(p)?(u=i.getMarkerPosition?i.getMarkerPosition(t.getValues(n,e)):(o=[l=t.get(n[0],e),h=t.get(n[1],e)],c.clampData&&c.clampData(o,o),c.dataToPoint(o,!0)),"cartesian2d"===c.type&&(a=c.getAxis("x"),s=c.getAxis("y"),l=t.get(n[0],e),h=t.get(n[1],e),__(l)?u[0]=a.toGlobalCoord(a.getExtent()["x0"===n[0]?0:1]):__(h)&&(u[1]=s.toGlobalCoord(s.getExtent()["y0"===n[1]?0:1]))),isNaN(f)||(u[0]=f),isNaN(p)||(u[1]=p)):u=[f,p],u}var S_=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]];$y.extend({type:"markArea",updateTransform:function(t,e,r){e.eachSeries(function(n){var i,t=n.markAreaModel;t&&(i=t.getData()).each(function(e){var t=P(S_,function(t){return b_(i,e,t,n,r)});i.setItemLayout(e,t),i.getItemGraphicEl(e).setShape("points",t)})},this)},renderSeries:function(e,a,t,n){var o=e.coordinateSystem,i=e.id,s=e.getData(),r=this.markerGroupMap,l=r.get(i)||r.set(i,{group:new _n});this.group.add(l.group),l.__keep=!0;var h=function(t,n,e){var i,r;r=t?(i=P(t&&t.dimensions,function(t){var e=n.getData();return A({name:t},e.getDimensionInfo(e.mapDimension(t))||{})}),new hf(P(["x0","y0","x1","y1"],function(t,e){return{name:t,type:i[e%2].type}}),e)):new hf(i=[{name:"value",type:"float"}],e);var o=P(e.get("data"),O(y_,n,t,e));t&&(o=I(o,O(w_,t)));var a=t?function(t,e,n,i){return t.coord[Math.floor(i/2)][i%2]}:function(t){return t.value};return r.initData(o,null,a),r.hasItemOption=!0,r}(o,e,a);a.setData(h),h.each(function(i){var t=P(S_,function(t){return b_(h,i,t,e,n)}),r=!0;D(S_,function(t){var e,n;r&&(e=h.get(t[0],i),n=h.get(t[1],i),(__(e)||o.getAxis("x").containData(e))&&(__(n)||o.getAxis("y").containData(n))&&(r=!1))}),h.setItemLayout(i,{points:t,allClipped:r}),h.setItemVisual(i,{color:s.getVisual("color")})}),h.diff(l.__data).add(function(t){var e,n=h.getItemLayout(t);n.allClipped||(e=new Na({shape:{points:n.points}}),h.setItemGraphicEl(t,e),l.group.add(e))}).update(function(t,e){var n=l.__data.getItemGraphicEl(e),i=h.getItemLayout(t);i.allClipped?n&&l.group.remove(n):(n?Xs(n,{shape:{points:i.points}},a,t):n=new Na({shape:{points:i.points}}),h.setItemGraphicEl(t,n),l.group.add(n))}).remove(function(t){var e=l.__data.getItemGraphicEl(t);l.group.remove(e)}).execute(),h.eachItemGraphicEl(function(t,e){var n=h.getItemModel(e),i=n.getModel("label"),r=n.getModel("emphasis.label"),o=h.getItemVisual(e,"color");t.useStyle(A(n.getModel("itemStyle").getItemStyle(),{fill:He(o,.4),stroke:o})),t.hoverStyle=n.getModel("emphasis.itemStyle").getItemStyle(),Rs(t.style,t.hoverStyle,i,r,{labelFetcher:a,labelDataIndex:e,defaultText:h.getName(e)||"",isRectText:!0,autoColor:o}),Os(t,{}),t.dataModel=a}),l.__data=h,l.group.silent=a.get("silent")||e.get("silent")}}),Ed(function(t){t.markArea=t.markArea||{}}),ah.registerSubTypeDefaulter("dataZoom",function(){return"slider"});var M_=["cartesian2d","polar","singleAxis"];var I_,C_,T_,A_,D_=(C_=["axisIndex","axis","index","id"],T_=P(I_=(I_=["x","y","z","radius","angle","single"]).slice(),Wl),A_=P(C_=(C_||[]).slice(),Wl),function(r,o){D(I_,function(t,e){for(var n={name:t,capital:T_[e]},i=0;i<C_.length;i++)n[C_[i]]=t+A_[i];r.call(o,n)})});function k_(n,s,l){return function(t){var o,a={nodes:[],records:{}};if(s(function(t){a.records[t.name]={}}),!t)return a;for(h(t,a);o=!1,n(e),o;);function e(t){var n,i,r,e;e=t,0<=w(a.nodes,e)||(n=t,i=a,r=!1,s(function(e){D(l(n,e)||[],function(t){i.records[e.name][t]&&(r=!0)})}),!r)||(h(t,a),o=!0)}return a};function h(t,n){n.nodes.push(t),s(function(e){D(l(t,e)||[],function(t){n.records[e.name][t]=!0})})}}function P_(t,e,n,i,r,o){t=t||0;var a,s=n[1]-n[0];null!=r&&(r=O_(r,[0,s])),null!=o&&(o=Math.max(o,null!=r?r:0)),"all"===i&&(a=O_(a=Math.abs(e[1]-e[0]),[0,s]),r=o=O_(a,[r,o]),i=0),e[0]=O_(e[0],n),e[1]=O_(e[1],n);var l=L_(e,i);e[i]+=t;var h=r||0,u=n.slice();l.sign<0?u[0]+=h:u[1]-=h,e[i]=O_(e[i],u);var c=L_(e,i);return null!=r&&(c.sign!==l.sign||c.span<r)&&(e[1-i]=e[i]+l.sign*r),c=L_(e,i),null!=o&&c.span>o&&(e[1-i]=e[i]+c.sign*o),e}function L_(t,e){var n=t[e]-t[1-e];return{span:Math.abs(n),sign:0<n||!(n<0)&&e?-1:1}}function O_(t,e){return Math.min(null!=e[1]?e[1]:1/0,Math.max(null!=e[0]?e[0]:-1/0,t))}function E_(t,e,n,i){this._dimName=t,this._axisIndex=e,this._valueWindow,this._percentWindow,this._dataExtent,this._minMaxSpan,this.ecModel=i,this._dataZoomModel=n}var z_=D,N_=vl;function R_(t,e){var n,i,r=t.getAxisModel(),o=t._percentWindow,a=t._valueWindow;o&&(n=xl(a,[0,500]),n=Math.min(n,20),i=e||0===o[0]&&100===o[1],r.setRange(i?null:+a[0].toFixed(n),i?null:+a[1].toFixed(n)))}E_.prototype={constructor:E_,hostedBy:function(t){return this._dataZoomModel===t},getDataValueWindow:function(){return this._valueWindow.slice()},getDataPercentWindow:function(){return this._percentWindow.slice()},getTargetSeriesModels:function(){var r=[],o=this.ecModel;return o.eachSeries(function(t){var e,n,i;i=t.get("coordinateSystem"),0<=w(M_,i)&&(e=this._dimName,n=o.queryComponents({mainType:e+"Axis",index:t.get(e+"AxisIndex"),id:t.get(e+"AxisId")})[0],this._axisIndex===(n&&n.componentIndex)&&r.push(t))},this),r},getAxisModel:function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},getOtherAxisModel:function(){var e,n,t=this._dimName,i=this.ecModel,r=this.getAxisModel(),o="x"===t||"y"===t?(e="gridIndex","x"===t?"y":"x"):(e="polarIndex","angle"===t?"radius":"angle");return i.eachComponent(o+"Axis",function(t){(t.get(e)||0)===(r.get(e)||0)&&(n=t)}),n},getMinMaxSpan:function(){return T(this._minMaxSpan)},calculateDataWindow:function(r){var o,a=this._dataExtent,s=this.getAxisModel().axis.scale,l=this._dataZoomModel.getRangePropMode(),h=[0,100],u=[],c=[];z_(["start","end"],function(t,e){var n=r[t],i=r[t+"Value"];"percent"===l[e]?(null==n&&(n=h[e]),i=s.parse(pl(n,h,a))):(o=!0,n=pl(i=null==i?a[e]:s.parse(i),a,h)),c[e]=i,u[e]=n}),N_(c),N_(u);var d=this._minMaxSpan;function t(t,e,n,i,r){var o=r?"Span":"ValueSpan";P_(0,t,n,"all",d["min"+o],d["max"+o]);for(var a=0;a<2;a++)e[a]=pl(t[a],n,i,!0),r&&(e[a]=s.parse(e[a]))}return o?t(c,u,a,h,!1):t(u,c,h,a,!0),{valueWindow:c,percentWindow:u}},reset:function(t){var e,i,r,o,a,n;t===this._dataZoomModel&&(e=this.getTargetSeriesModels(),this._dataExtent=function(t,e,n){var i=[1/0,-1/0];z_(n,function(t){var n=t.getData();n&&z_(n.mapDimension(e,!0),function(t){var e=n.getApproximateExtent(t);e[0]<i[0]&&(i[0]=e[0]),e[1]>i[1]&&(i[1]=e[1])})}),i[1]<i[0]&&(i=[NaN,NaN]);return function(t,e){var n=t.getAxisModel(),i=n.getMin(!0),r="category"===n.get("type"),o=r&&n.getCategories().length;null!=i&&"dataMin"!==i&&"function"!=typeof i?e[0]=i:r&&(e[0]=0<o?0:NaN);var a=n.getMax(!0);null!=a&&"dataMax"!==a&&"function"!=typeof a?e[1]=a:r&&(e[1]=0<o?o-1:NaN);n.get("scale",!0)||(0<e[0]&&(e[0]=0),e[1]<0&&(e[1]=0))}(t,i),i}(this,this._dimName,e),r=(i=this)._minMaxSpan={},o=i._dataZoomModel,a=i._dataExtent,z_(["min","max"],function(t){var e=o.get(t+"Span"),n=o.get(t+"ValueSpan");null!=n&&(n=i.getAxisModel().axis.scale.parse(n)),null!=n?e=pl(a[0]+n,a,[0,100],!0):null!=e&&(n=pl(e,[0,100],a,!0)-a[0]),r[t+"Span"]=e,r[t+"ValueSpan"]=n}),n=this.calculateDataWindow(t.settledOption),this._valueWindow=n.valueWindow,this._percentWindow=n.percentWindow,R_(this))},restore:function(t){t===this._dataZoomModel&&(this._valueWindow=this._percentWindow=null,R_(this,!0))},filterData:function(t){var e,n,i,c;t===this._dataZoomModel&&(e=this._dimName,n=this.getTargetSeriesModels(),i=t.get("filterMode"),c=this._valueWindow,"none"!==i&&z_(n,function(n){var h=n.getData(),u=h.mapDimension(e,!0);u.length&&("weakFilter"===i?h.filterSelf(function(t){for(var e,n,i,r=0;r<u.length;r++){var o=h.get(u[r],t),a=!isNaN(o),s=o<c[0],l=o>c[1];if(a&&!s&&!l)return!0;a&&(i=!0),s&&(e=!0),l&&(n=!0)}return i&&e&&n}):z_(u,function(t){var e;"empty"===i?n.setData(h=h.map(t,function(t){return(e=t)>=c[0]&&e<=c[1]?t:NaN;var e})):((e={})[t]=c,h.selectRange(e))}),z_(u,function(t){h.setApproximateExtent(c,t)}))}))}};var B_=D,V_=D_,F_=Hd({type:"dataZoom",dependencies:["xAxis","yAxis","zAxis","radiusAxis","angleAxis","singleAxis","series"],defaultOption:{zlevel:0,z:4,orient:null,xAxisIndex:null,yAxisIndex:null,filterMode:"filter",throttle:null,start:0,end:100,startValue:null,endValue:null,minSpan:null,maxSpan:null,minValueSpan:null,maxValueSpan:null,rangeMode:null},init:function(t,e,n){this._dataIntervalByAxis={},this._dataInfo={},this._axisProxies={},this.textStyleModel,this._autoThrottle=!0,this._rangePropMode=["percent","percent"];var i=H_(t);this.settledOption=i,this.mergeDefaultAndTheme(t,n),this.doInit(i)},mergeOption:function(t){var e=H_(t);m(this.option,t,!0),m(this.settledOption,e,!0),this.doInit(e)},doInit:function(t){var n=this.option;v.canvasSupported||(n.realtime=!1),this._setDefaultThrottle(t),W_(this,t);var i=this.settledOption;B_([["start","startValue"],["end","endValue"]],function(t,e){"value"===this._rangePropMode[e]&&(n[t[0]]=i[t[0]]=null)},this),this.textStyleModel=this.getModel("textStyle"),this._resetTarget(),this._giveAxisProxies()},_giveAxisProxies:function(){var a=this._axisProxies;this.eachTargetAxis(function(t,e,n,i){var r=this.dependentModels[t.axis][e],o=r.__dzAxisProxy||(r.__dzAxisProxy=new E_(t.name,e,this,i));a[t.name+"_"+e]=o},this)},_resetTarget:function(){var n=this.option,t=this._judgeAutoMode();V_(function(t){var e=t.axisIndex;n[e]=Cr(n[e])},this),"axisIndex"===t?this._autoSetAxisIndex():"orient"===t&&this._autoSetOrient()},_judgeAutoMode:function(){var e=this.option,n=!1;V_(function(t){null!=e[t.axisIndex]&&(n=!0)},this);var t=e.orient;return null==t&&n?"orient":n?void 0:(null==t&&(e.orient="horizontal"),"axisIndex")},_autoSetAxisIndex:function(){var t,o=!0,e=this.get("orient",!0),a=this.option,n=this.dependentModels;o&&(n[(t="vertical"===e?"y":"x")+"Axis"].length?(a[t+"AxisIndex"]=[0],o=!1):B_(n.singleAxis,function(t){o&&t.get("orient",!0)===e&&(a.singleAxisIndex=[t.componentIndex],o=!1)})),o&&V_(function(t){if(o){var e=[],n=this.dependentModels[t.axis];if(n.length&&!e.length)for(var i=0,r=n.length;i<r;i++)"category"===n[i].get("type")&&e.push(i);(a[t.axisIndex]=e).length&&(o=!1)}},this),o&&this.ecModel.eachSeries(function(o){this._isSeriesHasAllAxesTypeOf(o,"value")&&V_(function(t){var e=a[t.axisIndex],n=o.get(t.axisIndex),i=o.get(t.axisId),r=o.ecModel.queryComponents({mainType:t.axis,index:n,id:i})[0];if(C&&!r)throw new Error(t.axis+' "'+G(n,i,0)+'" not found');w(e,n=r.componentIndex)<0&&e.push(n)})},this)},_autoSetOrient:function(){var e;this.eachTargetAxis(function(t){e=e||t.name},this),this.option.orient="y"===e?"vertical":"horizontal"},_isSeriesHasAllAxesTypeOf:function(i,r){var o=!0;return V_(function(t){var e=i.get(t.axisIndex),n=this.dependentModels[t.axis][e];n&&n.get("type")===r||(o=!1)},this),o},_setDefaultThrottle:function(t){var e;t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle&&(e=this.ecModel.option,this.option.throttle=e.animation&&0<e.animationDurationUpdate?100:20)},getFirstTargetAxisModel:function(){var n;return V_(function(t){var e;null!=n||(e=this.get(t.axisIndex)).length&&(n=this.dependentModels[t.axis][e[0]])},this),n},eachTargetAxis:function(n,i){var r=this.ecModel;V_(function(e){B_(this.get(e.axisIndex),function(t){n.call(i,e,t,this,r)},this)},this)},getAxisProxy:function(t,e){return this._axisProxies[t+"_"+e]},getAxisModel:function(t,e){var n=this.getAxisProxy(t,e);return n&&n.getAxisModel()},setRawRange:function(e){var n=this.option,i=this.settledOption;B_([["start","startValue"],["end","endValue"]],function(t){null==e[t[0]]&&null==e[t[1]]||(n[t[0]]=i[t[0]]=e[t[0]],n[t[1]]=i[t[1]]=e[t[1]])},this),W_(this,e)},setCalculatedRange:function(e){var n=this.option;B_(["start","startValue","end","endValue"],function(t){n[t]=e[t]})},getPercentRange:function(){var t=this.findRepresentativeAxisProxy();if(t)return t.getDataPercentWindow()},getValueRange:function(t,e){if(null!=t||null!=e)return this.getAxisProxy(t,e).getDataValueWindow();var n=this.findRepresentativeAxisProxy();return n?n.getDataValueWindow():void 0},findRepresentativeAxisProxy:function(t){if(t)return t.__dzAxisProxy;var e,n=this._axisProxies;for(e in n)if(n.hasOwnProperty(e)&&n[e].hostedBy(this))return n[e];for(e in n)if(n.hasOwnProperty(e)&&!n[e].hostedBy(this))return n[e]},getRangePropMode:function(){return this._rangePropMode.slice()}});function H_(e){var n={};return B_(["start","end","startValue","endValue","throttle"],function(t){e.hasOwnProperty(t)&&(n[t]=e[t])}),n}function W_(t,r){var o=t._rangePropMode,a=t.get("rangeMode");B_([["start","startValue"],["end","endValue"]],function(t,e){var n=null!=r[t[0]],i=null!=r[t[1]];n&&!i?o[e]="percent":!n&&i?o[e]="value":a?o[e]=a[e]:n&&(o[e]="percent")})}var G_=Gu.extend({type:"dataZoom",render:function(t,e,n){this.dataZoomModel=t,this.ecModel=e,this.api=n},getTargetCoordInfo:function(){var t=this.dataZoomModel,r=this.ecModel,o={};return t.eachTargetAxis(function(t,e){var n,i=r.getComponent(t.axis,e);i&&(n=i.getCoordSysModel())&&function(t,e,n,i){for(var r,o=0;o<n.length;o++)if(n[o].model===t){r=n[o];break}r||n.push(r={model:t,axisModels:[],coordIndex:i});r.axisModels.push(e)}(n,i,o[n.mainType]||(o[n.mainType]=[]),n.componentIndex)},this),o}}),Z_=(F_.extend({type:"dataZoom.slider",layoutMode:"box",defaultOption:{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#2f4554",width:.5,opacity:.3},areaStyle:{color:"rgba(47,69,84,0.3)",opacity:.3}},borderColor:"#ddd",fillerColor:"rgba(167,183,204,0.4)",handleIcon:"M8.2,13.6V3.9H6.3v9.7H3.1v14.9h3.3v9.7h1.8v-9.7h3.3V13.6H8.2z M9.7,24.4H4.8v-1.4h4.9V24.4z M9.7,19.1H4.8v-1.4h4.9V19.1z",handleSize:"100%",handleStyle:{color:"#a7b7cc"},labelPrecision:null,labelFormatter:null,showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#333"}}}),Ga),U_=pl,X_=vl,Y_=L,j_=D,q_="horizontal",$_="vertical",K_=["line","bar","candlestick","scatter"],Q_=G_.extend({type:"dataZoom.slider",init:function(t,e){this._displayables={},this._orient,this._range,this._handleEnds,this._size,this._handleWidth,this._handleHeight,this._location,this._dragging,this._dataShadowInfo,this.api=e},render:function(t,e,n,i){Q_.superApply(this,"render",arguments),oc(this,"_dispatchZoomAction",this.dataZoomModel.get("throttle"),"fixRate"),this._orient=t.get("orient"),!1!==this.dataZoomModel.get("show")?(i&&"dataZoom"===i.type&&i.from===this.uid||this._buildView(),this._updateView()):this.group.removeAll()},remove:function(){Q_.superApply(this,"remove",arguments),ac(this,"_dispatchZoomAction")},dispose:function(){Q_.superApply(this,"dispose",arguments),ac(this,"_dispatchZoomAction")},_buildView:function(){var t=this.group;t.removeAll(),this._resetLocation(),this._resetInterval();var e=this._displayables.barGroup=new _n;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(e),this._positionGroup()},_resetLocation:function(){var t=this.dataZoomModel,e=this.api,n=this._findCoordRect(),i={width:e.getWidth(),height:e.getHeight()},r=this._orient===q_?{right:i.width-n.x-n.width,top:i.height-30-7,width:n.width,height:30}:{right:7,top:n.y,width:30,height:n.height},o=th(t.option);D(["right","top","width","height"],function(t){"ph"===o[t]&&(o[t]=r[t])});var a=Kl(o,i,t.padding);this._location={x:a.x,y:a.y},this._size=[a.width,a.height],this._orient===$_&&this._size.reverse()},_positionGroup:function(){var t=this.group,e=this._location,n=this._orient,i=this.dataZoomModel.getFirstTargetAxisModel(),r=i&&i.get("inverse"),o=this._displayables.barGroup,a=(this._dataShadowInfo||{}).otherAxisInverse;o.attr(n!==q_||r?n===q_&&r?{scale:a?[-1,1]:[-1,-1]}:n!==$_||r?{scale:a?[-1,-1]:[-1,1],rotation:Math.PI/2}:{scale:a?[1,-1]:[1,1],rotation:Math.PI/2}:{scale:a?[1,1]:[1,-1]});var s=t.getBoundingRect([o]);t.attr("position",[e.x-s.x,e.y-s.y])},_getViewExtent:function(){return[0,this._size[0]]},_renderBackground:function(){var t=this.dataZoomModel,e=this._size,n=this._displayables.barGroup;n.add(new Z_({silent:!0,shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:t.get("backgroundColor")},z2:-40})),n.add(new Z_({shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:"transparent"},z2:0,onclick:L(this._onClickPanelClick,this)}))},_renderDataShadow:function(){var t,e,n,i,r,o,a,s,l,h,u,c,d,f,p,g=this._dataShadowInfo=this._prepareDataShadowInfo();g&&(t=this._size,n=(e=g.series).getRawData(),null!=(i=e.getShadowDim?e.getShadowDim():g.otherDim)&&(r=.3*((o=n.getDataExtent(i))[1]-o[0]),o=[o[0]-r,o[1]+r],a=[0,t[1]],s=[0,t[0]],l=[[t[0],0],[0,0]],h=[],u=s[1]/(n.count()-1),c=0,d=Math.round(n.count()/t[0]),n.each([i],function(t,e){var n,i;0<d&&e%d?c+=u:(i=(n=null==t||isNaN(t)||""===t)?0:U_(t,o,a,!0),n&&!f&&e?(l.push([l[l.length-1][0],0]),h.push([h[h.length-1][0],0])):!n&&f&&(l.push([c,0]),h.push([c,0])),l.push([c,i]),h.push([c,i]),c+=u,f=n)}),p=this.dataZoomModel,this._displayables.barGroup.add(new Na({shape:{points:l},style:A({fill:p.get("dataBackgroundColor")},p.getModel("dataBackground.areaStyle").getAreaStyle()),silent:!0,z2:-20})),this._displayables.barGroup.add(new Ra({shape:{points:h},style:p.getModel("dataBackground.lineStyle").getLineStyle(),silent:!0,z2:-19}))))},_prepareDataShadowInfo:function(){var t=this.dataZoomModel,s=t.get("showDataShadow");if(!1!==s){var l,h=this.ecModel;return t.eachTargetAxis(function(o,a){D(t.getAxisProxy(o.name,a).getTargetSeriesModels(),function(t){var e,n,i,r;l||!0!==s&&w(K_,t.get("type"))<0||(e=h.getComponent(o.axis,a).axis,n={x:"y",y:"x",radius:"angle",angle:"radius"}[o.name],r=t.coordinateSystem,null!=n&&r.getOtherAxis&&(i=r.getOtherAxis(e).inverse),n=t.getData().mapDimension(n),l={thisAxis:e,series:t,thisDim:o.name,otherDim:n,otherAxisInverse:i})},this)},this),l}},_renderHandle:function(){var t=this._displayables,o=t.handles=[],a=t.handleLabels=[],s=this._displayables.barGroup,e=this._size,l=this.dataZoomModel;s.add(t.filler=new Z_({draggable:!0,cursor:J_(this._orient),drift:Y_(this._onDragMove,this,"all"),ondragstart:Y_(this._showDataInfo,this,!0),ondragend:Y_(this._onDragEnd,this),onmouseover:Y_(this._showDataInfo,this,!0),onmouseout:Y_(this._showDataInfo,this,!1),style:{fill:l.get("fillerColor"),textPosition:"inside"}})),s.add(new Z_({silent:!0,subPixelOptimize:!0,shape:{x:0,y:0,width:e[0],height:e[1]},style:{stroke:l.get("dataBackgroundColor")||l.get("borderColor"),lineWidth:1,fill:"rgba(0,0,0,0)"}})),j_([0,1],function(t){var e=Js(l.get("handleIcon"),{cursor:J_(this._orient),draggable:!0,drift:Y_(this._onDragMove,this,t),ondragend:Y_(this._onDragEnd,this),onmouseover:Y_(this._showDataInfo,this,!0),onmouseout:Y_(this._showDataInfo,this,!1)},{x:-1,y:0,width:2,height:2}),n=e.getBoundingRect();this._handleHeight=gl(l.get("handleSize"),this._size[1]),this._handleWidth=n.width/n.height*this._handleHeight,e.setStyle(l.getModel("handleStyle").getItemStyle());var i=l.get("handleColor");null!=i&&(e.style.fill=i),s.add(o[t]=e);var r=l.textStyleModel;this.group.add(a[t]=new Aa({silent:!0,invisible:!0,style:{x:0,y:0,text:"",textVerticalAlign:"middle",textAlign:"center",textFill:r.getTextColor(),textFont:r.getFont()},z2:10}))},this)},_resetInterval:function(){var t=this._range=this.dataZoomModel.getPercentRange(),e=this._getViewExtent();this._handleEnds=[U_(t[0],[0,100],e,!0),U_(t[1],[0,100],e,!0)]},_updateInterval:function(t,e){var n=this.dataZoomModel,i=this._handleEnds,r=this._getViewExtent(),o=n.findRepresentativeAxisProxy().getMinMaxSpan(),a=[0,100];P_(e,i,r,n.get("zoomLock")?"all":t,null!=o.minSpan?U_(o.minSpan,a,r,!0):null,null!=o.maxSpan?U_(o.maxSpan,a,r,!0):null);var s=this._range,l=this._range=X_([U_(i[0],r,a,!0),U_(i[1],r,a,!0)]);return!s||s[0]!==l[0]||s[1]!==l[1]},_updateView:function(t){var i=this._displayables,r=this._handleEnds,e=X_(r.slice()),o=this._size;j_([0,1],function(t){var e=i.handles[t],n=this._handleHeight;e.attr({scale:[n/2,n/2],position:[r[t],o[1]/2-n/2]})},this),i.filler.setShape({x:e[0],y:0,width:e[1]-e[0],height:o[1]}),this._updateDataInfo(t)},_updateDataInfo:function(t){var e,n,i,r,o=this.dataZoomModel,a=this._displayables,s=a.handleLabels,l=this._orient,h=["",""];!o.get("showDetail")||(e=o.findRepresentativeAxisProxy())&&(n=e.getAxisModel().axis,i=this._range,r=t?e.calculateDataWindow({start:i[0],end:i[1]}).valueWindow:e.getDataValueWindow(),h=[this._formatLabel(r[0],n),this._formatLabel(r[1],n)]);var u=X_(this._handleEnds.slice());function c(t){var e=js(a.handles[t].parent,this.group),n=$s(0===t?"right":"left",e),i=this._handleWidth/2+5,r=qs([u[t]+(0===t?-i:i),this._size[1]/2],e);s[t].setStyle({x:r[0],y:r[1],textVerticalAlign:l===q_?"middle":n,textAlign:l===q_?n:"center",text:h[t]})}c.call(this,0),c.call(this,1)},_formatLabel:function(t,e){var n=this.dataZoomModel,i=n.get("labelFormatter"),r=n.get("labelPrecision");null!=r&&"auto"!==r||(r=e.getPixelPrecision());var o=null==t||isNaN(t)?"":"category"===e.type||"time"===e.type?e.scale.getLabel(Math.round(t)):t.toFixed(Math.min(r,20));return N(i)?i(t,o):R(i)?i.replace("{value}",o):o},_showDataInfo:function(t){t=this._dragging||t;var e=this._displayables.handleLabels;e[0].attr("invisible",!t),e[1].attr("invisible",!t)},_onDragMove:function(t,e,n,i){this._dragging=!0,Wt(i.event);var r=qs([e,n],this._displayables.barGroup.getLocalTransform(),!0),o=this._updateInterval(t,r[0]),a=this.dataZoomModel.get("realtime");this._updateView(!a),o&&a&&this._dispatchZoomAction()},_onDragEnd:function(){this._dragging=!1,this._showDataInfo(!1),this.dataZoomModel.get("realtime")||this._dispatchZoomAction()},_onClickPanelClick:function(t){var e,n,i,r=this._size,o=this._displayables.barGroup.transformCoordToLocal(t.offsetX,t.offsetY);o[0]<0||o[0]>r[0]||o[1]<0||o[1]>r[1]||(n=((e=this._handleEnds)[0]+e[1])/2,i=this._updateInterval("all",o[0]-n),this._updateView(),i&&this._dispatchZoomAction())},_dispatchZoomAction:function(){var t=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,start:t[0],end:t[1]})},_findCoordRect:function(){var t,e,n;return j_(this.getTargetCoordInfo(),function(t){var e;!n&&t.length&&(e=t[0].model.coordinateSystem,n=e.getRect&&e.getRect())}),n||(t=this.api.getWidth(),e=this.api.getHeight(),n={x:.2*t,y:.2*e,width:.6*t,height:.6*e}),n}});function J_(t){return"vertical"===t?"ns-resize":"ew-resize"}zd({getTargetSeries:function(t){var i=tt();return t.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(t,e,n){D(n.getAxisProxy(t.name,e).getTargetSeriesModels(),function(t){i.set(t.uid,t)})})}),i},modifyOutputEnd:!0,overallReset:function(t,i){t.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(t,e,n){n.getAxisProxy(t.name,e).reset(n,i)}),t.eachTargetAxis(function(t,e,n){n.getAxisProxy(t.name,e).filterData(n,i)})}),t.eachComponent("dataZoom",function(t){var e=t.findRepresentativeAxisProxy(),n=e.getDataPercentWindow(),i=e.getDataValueWindow();t.setCalculatedRange({start:n[0],end:n[1],startValue:i[0],endValue:i[1]})})}}),Nd("dataZoom",function(n,t){var i=k_(L(t.eachComponent,t,"dataZoom"),D_,function(t,e){return t.get(e.axisIndex)}),r=[];t.eachComponent({mainType:"dataZoom",query:n},function(t,e){r.push.apply(r,i(t).nodes)}),D(r,function(t,e){t.setRawRange({start:n.start,end:n.end,startValue:n.startValue,endValue:n.endValue})})}),F_.extend({type:"dataZoom.inside",defaultOption:{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}});var tx="\0_ec_interaction_mutex";function ex(t,e){return nx(t)[e]}function nx(t){return t[tx]||(t[tx]={})}function ix(n){this.pointerChecker,this._zr=n,this._opt={};var t=L,i=t(rx,this),r=t(ox,this),o=t(ax,this),a=t(sx,this),s=t(lx,this);It.call(this),this.setPointerChecker=function(t){this.pointerChecker=t},this.enable=function(t,e){this.disable(),this._opt=A(T(e)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),null==t&&(t=!0),!0!==t&&"move"!==t&&"pan"!==t||(n.on("mousedown",i),n.on("mousemove",r),n.on("mouseup",o)),!0!==t&&"scale"!==t&&"zoom"!==t||(n.on("mousewheel",a),n.on("pinch",s))},this.disable=function(){n.off("mousedown",i),n.off("mousemove",r),n.off("mouseup",o),n.off("mousewheel",a),n.off("pinch",s)},this.dispose=this.disable,this.isDragging=function(){return this._dragging},this.isPinching=function(){return this._pinching}}function rx(t){var e,n;Gt(t)||t.target&&t.target.draggable||(e=t.offsetX,n=t.offsetY,this.pointerChecker&&this.pointerChecker(t,e,n)&&(this._x=e,this._y=n,this._dragging=!0))}function ox(t){var e,n,i,r,o,a;this._dragging&&cx("moveOnMouseMove",t,this._opt)&&"pinch"!==t.gestureEvent&&!ex(this._zr,"globalPan")&&(e=t.offsetX,n=t.offsetY,o=e-(i=this._x),a=n-(r=this._y),this._x=e,this._y=n,this._opt.preventDefaultMouseMove&&Wt(t.event),ux(this,"pan","moveOnMouseMove",t,{dx:o,dy:a,oldX:i,oldY:r,newX:e,newY:n}))}function ax(t){Gt(t)||(this._dragging=!1)}function sx(t){var e,n,i=cx("zoomOnMouseWheel",t,this._opt),r=cx("moveOnMouseWheel",t,this._opt),o=t.wheelDelta,a=Math.abs(o),s=t.offsetX,l=t.offsetY;0!==o&&(i||r)&&(i&&(e=3<a?1.4:1<a?1.2:1.1,hx(this,"zoom","zoomOnMouseWheel",t,{scale:0<o?e:1/e,originX:s,originY:l})),r&&hx(this,"scrollMove","moveOnMouseWheel",t,{scrollDelta:(0<o?1:-1)*(3<(n=Math.abs(o))?.4:1<n?.15:.05),originX:s,originY:l}))}function lx(t){ex(this._zr,"globalPan")||hx(this,"zoom",null,t,{scale:1<t.pinchScale?1.1:1/1.1,originX:t.pinchX,originY:t.pinchY})}function hx(t,e,n,i,r){t.pointerChecker&&t.pointerChecker(i,r.originX,r.originY)&&(Wt(i.event),ux(t,e,n,i,r))}function ux(t,e,n,i,r){r.isAvailableBehavior=L(cx,null,n,i),t.trigger(e,r)}function cx(t,e,n){var i=n[t];return!t||i&&(!R(i)||e.event[i+"Key"])}Nd({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},function(){}),S(ix,It);var dx="\0_ec_dataZoom_roams";function fx(t,i){var e=gx(t),r=i.dataZoomId,o=i.coordId;D(e,function(t,e){var n=t.dataZoomInfos;n[r]&&w(i.allCoordIds,o)<0&&(delete n[r],t.count--)}),mx(e);var a,n,s=e[o];s||((s=e[o]={coordId:o,dataZoomInfos:{},count:0}).controller=(a=s,n=new ix(t.getZr()),D(["pan","zoom","scrollMove"],function(o){n.on(o,function(i){var r=[];D(a.dataZoomInfos,function(t){var e,n;i.isAvailableBehavior(t.dataZoomModel.option)&&(n=(e=(t.getRange||{})[o])&&e(a.controller,i),!t.dataZoomModel.get("disabled",!0)&&n&&r.push({dataZoomId:t.dataZoomId,start:n[0],end:n[1]}))}),r.length&&a.dispatchAction(r)})}),n),s.dispatchAction=O(vx,t)),s.dataZoomInfos[r]||s.count++,s.dataZoomInfos[r]=i;var l,h,u,c,d=(l=s.dataZoomInfos,u={type_true:2,type_move:1,type_false:0,type_undefined:-1},c=!0,D(l,function(t){var e=t.dataZoomModel,n=!e.get("disabled",!0)&&(!e.get("zoomLock",!0)||"move");u["type_"+h]<u["type_"+n]&&(h=n),c&=e.get("preventDefaultMouseMove",!0)}),{controlType:h,opt:{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!0,preventDefaultMouseMove:!!c}});s.controller.enable(d.controlType,d.opt),s.controller.setPointerChecker(i.containsPoint),oc(s,"dispatchAction",i.dataZoomModel.get("throttle",!0),"fixRate")}function px(t){return t.type+"\0_"+t.id}function gx(t){var e=t.getZr();return e[dx]||(e[dx]={})}function mx(n){D(n,function(t,e){t.count||(t.controller.dispose(),delete n[e])})}function vx(t,e){t.dispatchAction({type:"dataZoom",batch:e})}var yx=L,_x=G_.extend({type:"dataZoom.inside",init:function(){this._range},render:function(a,t,s,e){_x.superApply(this,"render",arguments),this._range=a.getPercentRange(),D(this.getTargetCoordInfo(),function(t,r){var o=P(t,function(t){return px(t.model)});D(t,function(e){var i=e.model,n={};D(["pan","zoom","scrollMove"],function(t){n[t]=yx(xx[t],this,e,r)},this),fx(s,{coordId:px(i),allCoordIds:o,containsPoint:function(t,e,n){return i.coordinateSystem.containPoint([e,n])},dataZoomId:a.id,dataZoomModel:a,getRange:n})},this)},this)},dispose:function(){var t,n,e;t=this.api,n=this.dataZoomModel.id,D(e=gx(t),function(t){t.controller.dispose();var e=t.dataZoomInfos;e[n]&&(delete e[n],t.count--)}),mx(e),_x.superApply(this,"dispose",arguments),this._range=null}}),xx={zoom:function(t,e,n,i){var r=this._range,o=r.slice(),a=t.axisModels[0];if(a){var s=Sx[e](null,[i.originX,i.originY],a,n,t),l=(0<s.signal?s.pixelStart+s.pixelLength-s.pixel:s.pixel-s.pixelStart)/s.pixelLength*(o[1]-o[0])+o[0],h=Math.max(1/i.scale,0);o[0]=(o[0]-l)*h+l,o[1]=(o[1]-l)*h+l;var u=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();return P_(0,o,[0,100],0,u.minSpan,u.maxSpan),this._range=o,r[0]!==o[0]||r[1]!==o[1]?o:void 0}},pan:bx(function(t,e,n,i,r,o){var a=Sx[i]([o.oldX,o.oldY],[o.newX,o.newY],e,r,n);return a.signal*(t[1]-t[0])*a.pixel/a.pixelLength}),scrollMove:bx(function(t,e,n,i,r,o){return Sx[i]([0,0],[o.scrollDelta,o.scrollDelta],e,r,n).signal*(t[1]-t[0])*o.scrollDelta})};function bx(l){return function(t,e,n,i){var r=this._range,o=r.slice(),a=t.axisModels[0];if(a){var s=l(o,a,t,e,n,i);return P_(s,o,[0,100],"all"),this._range=o,r[0]!==o[0]||r[1]!==o[1]?o:void 0}}}var Sx={grid:function(t,e,n,i,r){var o=n.axis,a={},s=r.model.coordinateSystem.getRect();return t=t||[0,0],"x"===o.dim?(a.pixel=e[0]-t[0],a.pixelLength=s.width,a.pixelStart=s.x,a.signal=o.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=s.height,a.pixelStart=s.y,a.signal=o.inverse?-1:1),a},polar:function(t,e,n,i,r){var o=n.axis,a={},s=r.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),h=s.getAngleAxis().getExtent();return t=t?s.pointToCoord(t):[0,0],e=s.pointToCoord(e),"radiusAxis"===n.mainType?(a.pixel=e[0]-t[0],a.pixelLength=l[1]-l[0],a.pixelStart=l[0],a.signal=o.inverse?1:-1):(a.pixel=e[1]-t[1],a.pixelLength=h[1]-h[0],a.pixelStart=h[0],a.signal=o.inverse?-1:1),a},singleAxis:function(t,e,n,i,r){var o=n.axis,a=r.model.coordinateSystem.getRect(),s={};return t=t||[0,0],"horizontal"===o.orient?(s.pixel=e[0]-t[0],s.pixelLength=a.width,s.pixelStart=a.x,s.signal=o.inverse?1:-1):(s.pixel=e[1]-t[1],s.pixelLength=a.height,s.pixelStart=a.y,s.signal=o.inverse?-1:1),s}},Mx={};function Ix(t,e){Mx[t]=e}function Cx(t){return Mx[t]}var Tx=Hd({type:"toolbox",layoutMode:{type:"box",ignoreSize:!0},optionUpdated:function(){Tx.superApply(this,"optionUpdated",arguments),D(this.option.feature,function(t,e){var n=Cx(e);n&&m(t,n.defaultOption)})},defaultOption:{show:!0,z:6,zlevel:0,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{borderColor:"#666",color:"none"},emphasis:{iconStyle:{borderColor:"#3E98C5"}},tooltip:{show:!1}}});Wd({type:"toolbox",render:function(u,c,d,l){var f,h,p,g,t,e,n,i,r,o,a,m=this.group;function s(t,e){var n,i=g[t],r=g[e],o=h[i],a=new ll(o,u,u.ecModel);if(l&&null!=l.newTitle&&l.featureName===i&&(o.title=l.newTitle),i&&!r){if(0===i.indexOf("my"))n={model:a,onclick:a.option.onclick,featureName:i};else{var s=Cx(i);if(!s)return;n=new s(a,c,d)}p[i]=n}else{if(!(n=p[r]))return;n.model=a,n.ecModel=c,n.api=d}i||!r?a.get("show")&&!n.unusable?(function(r,o,t){var a=r.getModel("iconStyle"),s=r.getModel("emphasis.iconStyle"),e=o.getIcons?o.getIcons():r.get("icon"),l=r.get("title")||{};{var n,i;"string"==typeof e&&(n=e,i=l,l={},(e={})[t]=n,l[t]=i)}var h=r.iconPaths={};D(e,function(t,e){var n=Js(t,{},{x:-f/2,y:-f/2,width:f,height:f});n.setStyle(a.getItemStyle()),n.hoverStyle=s.getItemStyle(),n.setStyle({text:l[e],textAlign:s.get("textAlign"),textBorderRadius:s.get("textBorderRadius"),textPadding:s.get("textPadding"),textFill:null});var i=u.getModel("tooltip");i&&i.get("show")&&n.attr("tooltip",k({content:l[e],formatter:i.get("formatter",!0)||function(){return l[e]},formatterParams:{componentType:"toolbox",name:e,title:l[e],$vars:["name","title"]},position:i.get("position",!0)||"bottom"},i.option)),Os(n),u.get("showTitle")&&(n.__title=l[e],n.on("mouseover",function(){var t=s.getItemStyle(),e="vertical"===u.get("orient")?null==u.get("right")?"right":"left":null==u.get("bottom")?"bottom":"top";n.setStyle({textFill:s.get("textFill")||t.fill||t.stroke||"#000",textBackgroundColor:s.get("textBackgroundColor"),textPosition:s.get("textPosition")||e})}).on("mouseout",function(){n.setStyle({textFill:null,textBackgroundColor:null})})),n.trigger(r.get("iconStatus."+e)||"normal"),m.add(n),n.on("click",L(o.onclick,o,c,d,e)),h[e]=n})}(a,n,i),a.setIconStatus=function(t,e){var n=this.option,i=this.iconPaths;n.iconStatus=n.iconStatus||{},n.iconStatus[t]=e,i[t]&&i[t].trigger(e)},n.render&&n.render(a,c,d,l)):n.remove&&n.remove(c,d):n.dispose&&n.dispose(c,d)}m.removeAll(),u.get("show")&&(f=+u.get("itemSize"),h=u.get("feature")||{},p=this._features||(this._features={}),g=[],D(h,function(t,e){g.push(e)}),new Xd(this._featureNames||[],g).add(s).update(s).remove(O(s,null)).execute(),this._featureNames=g,t=m,n=d,i=(e=u).getBoxLayoutParams(),r=e.get("padding"),o={width:n.getWidth(),height:n.getHeight()},a=Kl(i,o,r),$l(e.get("orient"),t,e.get("itemGap"),a.width,a.height),Ql(t,i,o,r),m.add(by(m.getBoundingRect(),u)),m.eachChild(function(t){var e,n,i,r,o=t.__title,a=t.hoverStyle;a&&o&&(e=ii(o,pi(a)),n=t.position[0]+m.position[0],i=!1,t.position[1]+m.position[1]+f+e.height>d.getHeight()&&(a.textPosition="top",i=!0),r=i?-5-e.height:f+8,n+e.width/2>d.getWidth()?(a.textPosition=["100%",r],a.textAlign="right"):n-e.width/2<0&&(a.textPosition=[0,r],a.textAlign="left"))}))},updateView:function(t,e,n,i){D(this._features,function(t){t.updateView&&t.updateView(t.model,e,n,i)})},remove:function(e,n){D(this._features,function(t){t.remove&&t.remove(e,n)}),this.group.removeAll()},dispose:function(e,n){D(this._features,function(t){t.dispose&&t.dispose(e,n)})}});var Ax=lc.toolbox.saveAsImage;function Dx(t){this.model=t}Dx.defaultOption={show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:Ax.title,type:"png",connectedBackgroundColor:"#fff",name:"",excludeComponents:["toolbox"],pixelRatio:1,lang:Ax.lang.slice()},Dx.prototype.unusable=!v.canvasSupported,Dx.prototype.onclick=function(t,e){var n=this.model,i=n.get("name")||t.get("title.0.text")||"echarts",r="svg"===e.getZr().painter.getType()?"svg":n.get("type",!0)||"png",o=e.getConnectedDataURL({type:r,backgroundColor:n.get("backgroundColor",!0)||t.get("backgroundColor")||"#fff",connectedBackgroundColor:n.get("connectedBackgroundColor"),excludeComponents:n.get("excludeComponents"),pixelRatio:n.get("pixelRatio")});if("function"!=typeof MouseEvent||v.browser.ie||v.browser.edge)if(window.navigator.msSaveOrOpenBlob){for(var a=atob(o.split(",")[1]),s=a.length,l=new Uint8Array(s);s--;)l[s]=a.charCodeAt(s);var h=new Blob([l]);window.navigator.msSaveOrOpenBlob(h,i+"."+r)}else{var u=n.get("lang"),c='<body style="margin:0;"><img src="'+o+'" style="max-width:100%;" title="'+(u&&u[0]||"")+'" /></body>';window.open().document.write(c)}else{var d=document.createElement("a");d.download=i+"."+r,d.target="_blank",d.href=o;var f=new MouseEvent("click",{view:document.defaultView,bubbles:!0,cancelable:!1});d.dispatchEvent(f)}},Ix("saveAsImage",Dx);var kx=lc.toolbox.magicType,Px="__ec_magicType_stack__";function Lx(t){this.model=t}Lx.defaultOption={show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z"},title:T(kx.title),option:{},seriesIndex:{}};var Ox=Lx.prototype;Ox.getIcons=function(){var t=this.model,e=t.get("icon"),n={};return D(t.get("type"),function(t){e[t]&&(n[t]=e[t])}),n};var Ex={line:function(t,e,n,i){if("bar"===t)return m({id:e,type:"line",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},i.get("option.line")||{},!0)},bar:function(t,e,n,i){if("line"===t)return m({id:e,type:"bar",data:n.get("data"),stack:n.get("stack"),markPoint:n.get("markPoint"),markLine:n.get("markLine")},i.get("option.bar")||{},!0)},stack:function(t,e,n,i){var r=n.get("stack")===Px;if("line"===t||"bar"===t)return i.setIconStatus("stack",r?"normal":"emphasis"),m({id:e,stack:r?"":Px},i.get("option.stack")||{},!0)}},zx=[["line","bar"],["stack"]];Ox.onclick=function(h,t,u){var c,e,d=this.model,n=d.get("seriesIndex."+u);Ex[u]&&(c={series:[]},D(zx,function(t){0<=w(t,u)&&D(t,function(t){d.setIconStatus(t,"normal")})}),d.setIconStatus(u,"emphasis"),h.eachComponent({mainType:"series",query:null==n?null:{seriesIndex:n}},function(t){var e=t.subType,n=t.id,i=Ex[u](e,n,t,d);i&&(A(i,t.option),c.series.push(i));var r=t.coordinateSystem;if(r&&"cartesian2d"===r.type&&("line"===u||"bar"===u)){var o=r.getAxesByScale("ordinal")[0];if(o){var a=o.dim+"Axis",s=h.queryComponents({mainType:a,index:t.get(name+"Index"),id:t.get(name+"Id")})[0].componentIndex;c[a]=c[a]||[];for(var l=0;l<=s;l++)c[a][s]=c[a][s]||{};c[a][s].boundaryGap="bar"===u}}}),"stack"===u&&(e=c.series&&c.series[0]&&c.series[0].stack===Px?m({stack:kx.title.tiled},kx.title):T(kx.title)),t.dispatchAction({type:"changeMagicType",currentType:u,newOption:c,newTitle:e,featureName:"magicType"}))},Nd({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},function(t,e){e.mergeOption(t.newOption)}),Ix("magicType",Lx);var Nx=lc.toolbox.dataView,Rx=new Array(60).join("-"),Bx="\t";function Vx(t){var r,o,a,u,e=(r={},o=[],a=[],t.eachRawSeries(function(t){var e,n,i=t.coordinateSystem;i&&("cartesian2d"===i.type||"polar"===i.type)&&"category"===(e=i.getBaseAxis()).type?(n=e.dim+"_"+e.index,r[n]||(r[n]={categoryAxis:e,valueAxis:i.getOtherAxis(e),series:[]},a.push({axisDim:e.dim,axisIndex:e.index})),r[n].series.push(t)):o.push(t)}),{seriesGroupByCategoryAxis:r,other:o,meta:a});return{value:I([(u=[],D(e.seriesGroupByCategoryAxis,function(t,e){var n=t.categoryAxis,i=t.valueAxis.dim,r=[" "].concat(P(t.series,function(t){return t.name})),o=[n.model.getCategories()];D(t.series,function(t){var e=t.getRawData();o.push(t.getRawData().mapArray(e.mapDimension(i),function(t){return t}))});for(var a=[r.join(Bx)],s=0;s<o[0].length;s++){for(var l=[],h=0;h<o.length;h++)l.push(o[h][s]);a.push(l.join(Bx))}u.push(a.join("\n"))}),u.join("\n\n"+Rx+"\n\n")),P(e.other,function(t){var r=t.getRawData(),o=[t.name],a=[];return r.each(r.dimensions,function(){for(var t=arguments.length,e=arguments[t-1],n=r.getName(e),i=0;i<t-1;i++)a[i]=arguments[i];o.push((n?n+Bx:"")+a.join(Bx))}),o.join("\n")}).join("\n\n"+Rx+"\n\n")],function(t){return t.replace(/[\n\t\s]/g,"")}).join("\n\n"+Rx+"\n\n"),meta:e.meta}}function Fx(t){return t.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}var Hx=new RegExp("["+Bx+"]+","g");function Wx(t,a){var e=t.split(new RegExp("\n*"+Rx+"\n*","g")),s={series:[]};return D(e,function(t,e){var n,i,r,o;0<=(o=t).slice(0,o.indexOf("\n")).indexOf(Bx)?(r=function(t){for(var e=t.split(/\n+/g),n=[],i=P(Fx(e.shift()).split(Hx),function(t){return{name:t,data:[]}}),r=0;r<e.length;r++){var o=Fx(e[r]).split(Hx);n.push(o.shift());for(var a=0;a<o.length;a++)i[a]&&(i[a].data[r]=o[a])}return{series:i,categories:n}}(t),i=(n=a[e]).axisDim+"Axis",n&&(s[i]=s[i]||[],s[i][n.axisIndex]={data:r.categories},s.series=s.series.concat(r.series))):(r=function(t){for(var e=t.split(/\n+/g),n=Fx(e.shift()),i=[],r=0;r<e.length;r++){var o=Fx(e[r]);if(o){for(var a,s=o.split(Hx),l=!1,h=isNaN(s[0])?(l=!0,a=s[0],s=s.slice(1),i[r]={name:a,value:[]},i[r].value):i[r]=[],u=0;u<s.length;u++)h.push(+s[u]);1===h.length&&(l?i[r].value=h[0]:i[r]=h[0])}}return{name:n,data:i}}(t),s.series.push(r))}),s}function Gx(t){this._dom=null,this.model=t}Gx.defaultOption={show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:T(Nx.title),lang:T(Nx.lang),backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"},Gx.prototype.onclick=function(t,e){var n=e.getDom(),i=this.model;this._dom&&n.removeChild(this._dom);var r=document.createElement("div");r.style.cssText="position:absolute;left:5px;top:5px;bottom:5px;right:5px;",r.style.backgroundColor=i.get("backgroundColor")||"#fff";var o=document.createElement("h4"),a=i.get("lang")||[];o.innerHTML=a[0]||i.get("title"),o.style.cssText="margin: 10px 20px;",o.style.color=i.get("textColor");var s=document.createElement("div"),l=document.createElement("textarea");s.style.cssText="display:block;width:100%;overflow:auto;";var h,u=i.get("optionToContent"),c=i.get("contentToOption"),d=Vx(t);"function"==typeof u?"string"==typeof(h=u(e.getOption()))?s.innerHTML=h:H(h)&&s.appendChild(h):(s.appendChild(l),l.readOnly=i.get("readOnly"),l.style.cssText="width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;",l.style.color=i.get("textColor"),l.style.borderColor=i.get("textareaBorderColor"),l.style.backgroundColor=i.get("textareaColor"),l.value=d.value);var f=d.meta,p=document.createElement("div");p.style.cssText="position:absolute;bottom:0;left:0;right:0;";var g="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",m=document.createElement("div"),v=document.createElement("div");g+=";background-color:"+i.get("buttonColor"),g+=";color:"+i.get("buttonTextColor");var y=this;function _(){n.removeChild(r),y._dom=null}Ht(m,"click",_),Ht(v,"click",function(){var t;try{t="function"==typeof c?c(s,e.getOption()):Wx(l.value,f)}catch(t){throw _(),new Error("Data view format error "+t)}t&&e.dispatchAction({type:"changeDataView",newOption:t}),_()}),m.innerHTML=a[1],v.innerHTML=a[2],v.style.cssText=g,m.style.cssText=g,i.get("readOnly")||p.appendChild(v),p.appendChild(m),r.appendChild(o),r.appendChild(s),r.appendChild(p),s.style.height=n.clientHeight-80+"px",n.appendChild(r),this._dom=r},Gx.prototype.remove=function(t,e){this._dom&&e.getDom().removeChild(this._dom)},Gx.prototype.dispose=function(t,e){this.remove(t,e)},Ix("dataView",Gx),Nd({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},function(t,o){var a=[];D(t.newOption.series,function(t){var e,n,r,i=o.getSeriesByName(t.name)[0];i?(e=i.get("data"),a.push({name:t.name,data:(n=t.data,r=e,P(n,function(t,e){var n=r&&r[e];if(!B(n)||z(n))return t;B(t)&&!z(t)||(t={value:t});var i=null!=n.name&&null==t.name;return t=A(t,n),i&&delete t.name,t}))})):a.push(k({type:"scatter"},t))}),o.mergeOption(A({series:a},t.newOption))});var Zx=O,Ux=D,Xx=P,Yx=Math.min,jx=Math.max,qx=Math.pow,$x=1e4,Kx=6,Qx=6,Jx="globalPan",tw={w:[0,0],e:[0,1],n:[1,0],s:[1,1]},ew={w:"ew",e:"ew",n:"ns",s:"ns",ne:"nesw",sw:"nesw",nw:"nwse",se:"nwse"},nw={brushStyle:{lineWidth:2,stroke:"rgba(0,0,0,0.3)",fill:"rgba(0,0,0,0.1)"},transformable:!0,brushMode:"single",removeOnClick:!1},iw=0;function rw(t){C&&j(t),It.call(this),this._zr=t,this.group=new _n,this._brushType,this._brushOption,this._panels,this._track=[],this._dragging,this._covers=[],this._creatingCover,this._creatingPanel,this._enableGlobalPan,C&&this._mounted,this._uid="brushController_"+iw++,this._handlers={},Ux(kw,function(t,e){this._handlers[e]=L(t,this)},this)}function ow(t,e){var n=Lw[e.brushType].createCover(t,e);return n.__brushOption=e,lw(n,e),t.group.add(n),n}function aw(t,e){var n=uw(e);return n.endCreating&&(n.endCreating(t,e),lw(e,e.__brushOption)),e}function sw(t,e){var n=e.__brushOption;uw(e).updateCoverShape(t,e,n.range,n)}function lw(t,e){var n=e.z;null==n&&(n=$x),t.traverse(function(t){t.z=n,t.z2=n})}function hw(t,e){uw(e).updateCommon(t,e),sw(t,e)}function uw(t){return Lw[t.__brushOption.brushType]}function cw(t,e,n){var i,r=t._panels;if(!r)return!0;var o=t._transform;return Ux(r,function(t){t.isTargetByCursor(e,n,o)&&(i=t)}),i}function dw(t,e){var n=t._panels;if(!n)return!0;var i=e.__brushOption.panelId;return null==i||n[i]}function fw(e){var t=e._covers,n=t.length;return Ux(t,function(t){e.group.remove(t)},e),t.length=0,n}function pw(t,e){var n=Xx(t._covers,function(t){var e=t.__brushOption,n=T(e.range);return{brushType:e.brushType,panelId:e.panelId,range:n}});t.trigger("brush",n,{isEnd:!!e.isEnd,removeOnClick:!!e.removeOnClick})}function gw(t){var e=t.length-1;return e<0&&(e=0),[t[0],t[e]]}function mw(e,n,t,i){var r=new _n;return r.add(new Ga({name:"main",style:xw(t),silent:!0,draggable:!0,cursor:"move",drift:Zx(e,n,r,"nswe"),ondragend:Zx(pw,n,{isEnd:!0})})),Ux(i,function(t){r.add(new Ga({name:t,style:{opacity:0},draggable:!0,silent:!0,invisible:!0,drift:Zx(e,n,r,t),ondragend:Zx(pw,n,{isEnd:!0})}))}),r}function vw(t,e,n,i){var r=i.brushStyle.lineWidth||0,o=jx(r,Qx),a=n[0][0],s=n[1][0],l=a-r/2,h=s-r/2,u=n[0][1],c=n[1][1],d=u-o+r/2,f=c-o+r/2,p=u-a,g=c-s,m=p+r,v=g+r;_w(t,e,"main",a,s,p,g),i.transformable&&(_w(t,e,"w",l,h,o,v),_w(t,e,"e",d,h,o,v),_w(t,e,"n",l,h,m,o),_w(t,e,"s",l,f,m,o),_w(t,e,"nw",l,h,o,o),_w(t,e,"ne",d,h,o,o),_w(t,e,"sw",l,f,o,o),_w(t,e,"se",d,f,o,o))}function yw(i,r){var t=r.__brushOption,o=t.transformable,e=r.childAt(0);e.useStyle(xw(t)),e.attr({silent:!o,cursor:o?"move":"default"}),Ux(["w","e","n","s","se","sw","ne","nw"],function(t){var e=r.childOfName(t),n=function t(e,n){{if(1<n.length){n=n.split("");var i=[t(e,n[0]),t(e,n[1])];return"e"!==i[0]&&"w"!==i[0]||i.reverse(),i.join("")}var r={w:"left",e:"right",n:"top",s:"bottom"},o={left:"w",right:"e",top:"n",bottom:"s"},i=$s(r[n],js(e.group));return o[i]}}(i,t);e&&e.attr({silent:!o,invisible:!o,cursor:o?ew[n]+"-resize":null})})}function _w(t,e,n,i,r,o,a){var s,l,h,u,c,d=e.childOfName(n);d&&d.setShape((s=Iw(t,e,[[i,r],[i+o,r+a]]),l=Yx(s[0][0],s[1][0]),h=Yx(s[0][1],s[1][1]),u=jx(s[0][0],s[1][0]),c=jx(s[0][1],s[1][1]),{x:l,y:h,width:u-l,height:c-h}))}function xw(t){return A({strokeNoScale:!0},t.brushStyle)}function ww(t,e,n,i){var r=[Yx(t,n),Yx(e,i)],o=[jx(t,n),jx(e,i)];return[[r[0],o[0]],[r[1],o[1]]]}function bw(t,e,n,i,r,o,a,s){var l=i.__brushOption,h=t(l.range),u=Mw(n,o,a);Ux(r.split(""),function(t){var e=tw[t];h[e[0]][e[1]]+=u[e[0]]}),l.range=e(ww(h[0][0],h[1][0],h[0][1],h[1][1])),hw(n,i),pw(n,{isEnd:!1})}function Sw(t,e,n,i,r){var o=e.__brushOption.range,a=Mw(t,n,i);Ux(o,function(t){t[0]+=a[0],t[1]+=a[1]}),hw(t,e),pw(t,{isEnd:!1})}function Mw(t,e,n){var i=t.group,r=i.transformCoordToLocal(e,n),o=i.transformCoordToLocal(0,0);return[r[0]-o[0],r[1]-o[1]]}function Iw(t,e,n){var i=dw(t,e);return i&&!0!==i?i.clipPath(n,t._transform):T(n)}function Cw(t){var e=t.event;e.preventDefault&&e.preventDefault()}function Tw(t,e,n){return t.childOfName("main").contain(e,n)}function Aw(t,e,n,i){var r,o,a,s=t._creatingCover,l=t._creatingPanel,h=t._brushOption;return t._track.push(n.slice()),function(t){var e=t._track;if(e.length){var n=e[e.length-1],i=e[0],r=n[0]-i[0],o=n[1]-i[1],a=qx(r*r+o*o,.5);return Kx<a}}(t)||s?(l&&!s&&("single"===h.brushMode&&fw(t),(o=T(h)).brushType=Dw(o.brushType,l),o.panelId=!0===l?null:l.panelId,s=t._creatingCover=ow(t,o),t._covers.push(s)),s&&(a=Lw[Dw(t._brushType,l)],s.__brushOption.range=a.getCreatingRange(Iw(t,s,t._track)),i&&(aw(t,s),a.updateCommon(t,s)),sw(t,s),r={isEnd:i})):i&&"single"===h.brushMode&&h.removeOnClick&&cw(t,e,n)&&fw(t)&&(r={isEnd:i,removeOnClick:!0}),r}function Dw(t,e){return"auto"===t?(C&&j(e&&e.defaultBrushType,'MUST have defaultBrushType when brushType is "atuo"'),e.defaultBrushType):t}rw.prototype={constructor:rw,enableBrush:function(t){var e,n;return C&&j(this._mounted),this._brushType&&(function(t,e,n){var i=nx(t);i[e]===n&&(i[e]=null)}(n=(e=this)._zr,Jx,e._uid),function(n,t){Ux(t,function(t,e){n.off(e,t)})}(n,e._handlers),e._brushType=e._brushOption=null),t.brushType&&function(t,e){var n=t._zr;t._enableGlobalPan||function(t,e,n){nx(t)[e]=n}(n,Jx,t._uid);(function(n,t){Ux(t,function(t,e){n.on(e,t)})})(n,t._handlers),t._brushType=e.brushType,t._brushOption=m(T(nw),e,!0)}(this,t),this},setPanels:function(t){var e;return t&&t.length?(e=this._panels={},D(t,function(t){e[t.panelId]=T(t)})):this._panels=null,this},mount:function(t){t=t||{},C&&(this._mounted=!0),this._enableGlobalPan=t.enableGlobalPan;var e=this.group;return this._zr.add(e),e.attr({position:t.position||[0,0],rotation:t.rotation||0,scale:t.scale||[1,1]}),this._transform=e.getLocalTransform(),this},eachCover:function(t,e){Ux(this._covers,t,e)},updateCovers:function(r){C&&j(this._mounted),r=P(r,function(t){return m(T(nw),t,!0)});var n="\0-brush-index-",o=this._covers,a=this._covers=[],s=this,l=this._creatingCover;return new Xd(o,r,function(t,e){return i(t.__brushOption,e)},i).add(t).update(t).remove(function(t){o[t]!==l&&s.group.remove(o[t])}).execute(),this;function i(t,e){return(null!=t.id?t.id:n+e)+"-"+t.brushType}function t(t,e){var n,i=r[t];null!=e&&o[e]===l?a[t]=o[e]:(n=a[t]=null!=e?(o[e].__brushOption=i,o[e]):aw(s,ow(s,i)),hw(s,n))}},unmount:function(){if(!C||this._mounted)return this.enableBrush(!1),fw(this),this._zr.remove(this.group),C&&(this._mounted=!1),this},dispose:function(){this.unmount(),this.off()}},S(rw,It);var kw={mousedown:function(t){var e;this._dragging?Pw(this,t):t.target&&t.target.draggable||(Cw(t),e=this.group.transformCoordToLocal(t.offsetX,t.offsetY),this._creatingCover=null,(this._creatingPanel=cw(this,t,e))&&(this._dragging=!0,this._track=[e.slice()]))},mousemove:function(t){var e,n=t.offsetX,i=t.offsetY,r=this.group.transformCoordToLocal(n,i);!function(t,e,n){if(t._brushType&&(i=e,o=t._zr,!(i<0||i>o.getWidth()||r<0||r>o.getHeight()))){var i,r,o,a=t._zr,s=t._covers,l=cw(t,e,n);if(!t._dragging)for(var h=0;h<s.length;h++){var u=s[h].__brushOption;if(l&&(!0===l||u.panelId===l.panelId)&&Lw[u.brushType].contain(s[h],n[0],n[1]))return}l&&a.setCursorStyle("crosshair")}}(this,t,r),this._dragging&&(Cw(t),(e=Aw(this,t,r,!1))&&pw(this,e))},mouseup:function(t){Pw(this,t)}};function Pw(t,e){var n,i,r,o;t._dragging&&(Cw(e),n=e.offsetX,i=e.offsetY,r=t.group.transformCoordToLocal(n,i),o=Aw(t,e,r,!0),t._dragging=!1,t._track=[],t._creatingCover=null,o&&pw(t,o))}var Lw={lineX:Ow(0),lineY:Ow(1),rect:{createCover:function(t,e){return mw(Zx(bw,function(t){return t},function(t){return t}),t,e,["w","e","n","s","se","sw","ne","nw"])},getCreatingRange:function(t){var e=gw(t);return ww(e[1][0],e[1][1],e[0][0],e[0][1])},updateCoverShape:function(t,e,n,i){vw(t,e,n,i)},updateCommon:yw,contain:Tw},polygon:{createCover:function(t,e){var n=new _n;return n.add(new Ra({name:"main",style:xw(e),silent:!0})),n},getCreatingRange:function(t){return t},endCreating:function(t,e){e.remove(e.childAt(0)),e.add(new Na({name:"main",draggable:!0,drift:Zx(Sw,t,e),ondragend:Zx(pw,t,{isEnd:!0})}))},updateCoverShape:function(t,e,n){e.childAt(0).setShape({points:Iw(t,e,n)})},updateCommon:yw,contain:Tw}};function Ow(s){return{createCover:function(t,e){return mw(Zx(bw,function(t){var e=[t,[0,100]];return s&&e.reverse(),e},function(t){return t[s]}),t,e,[["w","e"],["n","s"]][s])},getCreatingRange:function(t){var e=gw(t);return[Yx(e[0][s],e[1][s]),jx(e[0][s],e[1][s])]},updateCoverShape:function(t,e,n,i){var r,o=dw(t,e),a=[n,!0!==o&&o.getLinearBrushOtherExtent?o.getLinearBrushOtherExtent(s,t._transform):[0,[(r=t._zr).getWidth(),r.getHeight()][1-s]]];s&&a.reverse(),vw(t,e,a,i)},updateCommon:yw,contain:Tw}}var Ew={axisPointer:1,tooltip:1,brush:1};function zw(s,l,h){return s=Nw(s),function(t,e,n){return s.contain(e[0],e[1])&&(i=t,r=h,o=l.getComponentByElement(i.topTarget),a=o&&o.coordinateSystem,!(o&&o!==r&&!Ew[o.mainType]&&a&&a.model!==r));var i,r,o,a}}function Nw(t){return yn.create(t)}var Rw=D,Bw=w,Vw=O,Fw=["dataToPoint","pointToData"],Hw=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"];function Ww(t,e,n){var i=this._targetInfoList=[],r={},o=Uw(e,t);Rw(Xw,function(t,e){n&&n.include&&!(0<=Bw(n.include,e))||t(o,i,r)})}var Gw=Ww.prototype;function Zw(t){return t[0]>t[1]&&t.reverse(),t}function Uw(t,e){return Rr(t,e,{includeMainTypes:Hw})}Gw.setOutputRanges=function(t,e){this.matchOutputRanges(t,e,function(t,e,n){var i;(t.coordRanges||(t.coordRanges=[])).push(e),t.coordRange||(t.coordRange=e,i=qw[t.brushType](0,n,e),t.__rangeOffset={offset:Kw[t.brushType](i.values,t.range,[1,1]),xyMinMax:i.xyMinMax})})},Gw.matchOutputRanges=function(t,i,r){Rw(t,function(n){var t=this.findTargetInfo(n,i);t&&!0!==t&&D(t.coordSyses,function(t){var e=qw[n.brushType](1,t,n.range);r(n,e.values,t,i)})},this)},Gw.setInputRanges=function(t,h){Rw(t,function(t){var e,n,i,r,o,a,s,l=this.findTargetInfo(t,h);C&&(j(!l||!0===l||t.coordRange,"coordRange must be specified when coord index specified."),j(!l||!0!==l||t.range,"range must be specified in global brush.")),t.range=t.range||[],l&&!0!==l&&(t.panelId=l.panelId,e=qw[t.brushType](0,l.coordSys,t.coordRange),n=t.__rangeOffset,t.range=n?Kw[t.brushType](e.values,n.offset,(i=e.xyMinMax,r=n.xyMinMax,o=Jw(i),a=Jw(r),s=[o[0]/a[0],o[1]/a[1]],isNaN(s[0])&&(s[0]=1),isNaN(s[1])&&(s[1]=1),s)):e.values)},this)},Gw.makePanelOpts=function(i,a){return P(this._targetInfoList,function(t){var r,o,n,e=t.getPanelRect();return{panelId:t.panelId,defaultBrushType:a&&a(t),clipPath:(n=Nw(n=e),function(t,e){return Qs(t,n)}),isTargetByCursor:zw(e,i,t.coordSysModel),getLinearBrushOtherExtent:(r=Nw(r=e),function(t){var e=null!=o?o:t,n=e?r.width:r.height,i=e?r.x:r.y;return[i,i+(n||0)]})}})},Gw.controlSeries=function(t,e,n){var i=this.findTargetInfo(t,n);return!0===i||i&&0<=Bw(i.coordSyses,e.coordinateSystem)},Gw.findTargetInfo=function(t,e){for(var n=this._targetInfoList,i=Uw(e,t),r=0;r<n.length;r++){var o=n[r],a=t.panelId;if(a){if(o.panelId===a)return o}else for(r=0;r<Yw.length;r++)if(Yw[r](i,o))return o}return!0};var Xw={grid:function(t,i){var r=t.xAxisModels,o=t.yAxisModels,e=t.gridModels,n=tt(),a={},s={};(r||o||e)&&(Rw(r,function(t){var e=t.axis.grid.model;n.set(e.id,e),a[e.id]=!0}),Rw(o,function(t){var e=t.axis.grid.model;n.set(e.id,e),s[e.id]=!0}),Rw(e,function(t){n.set(t.id,t),a[t.id]=!0,s[t.id]=!0}),n.each(function(t){var e=t.coordinateSystem,n=[];Rw(e.getCartesians(),function(t,e){(0<=Bw(r,t.getAxis("x").model)||0<=Bw(o,t.getAxis("y").model))&&n.push(t)}),i.push({panelId:"grid--"+t.id,gridModel:t,coordSysModel:t,coordSys:n[0],coordSyses:n,getPanelRect:jw.grid,xAxisDeclared:a[t.id],yAxisDeclared:s[t.id]})}))},geo:function(t,n){Rw(t.geoModels,function(t){var e=t.coordinateSystem;n.push({panelId:"geo--"+t.id,geoModel:t,coordSysModel:t,coordSys:e,coordSyses:[e],getPanelRect:jw.geo})})}},Yw=[function(t,e){var n=t.xAxisModel,i=t.yAxisModel,r=t.gridModel;return!r&&n&&(r=n.axis.grid.model),!r&&i&&(r=i.axis.grid.model),r&&r===e.gridModel},function(t,e){var n=t.geoModel;return n&&n===e.geoModel}],jw={grid:function(){return this.coordSys.grid.getRect().clone()},geo:function(){var t=this.coordSys,e=t.getBoundingRect().clone();return e.applyTransform(js(t)),e}},qw={lineX:Vw($w,0),lineY:Vw($w,1),rect:function(t,e,n){var i=e[Fw[t]]([n[0][0],n[1][0]]),r=e[Fw[t]]([n[0][1],n[1][1]]),o=[Zw([i[0],r[0]]),Zw([i[1],r[1]])];return{values:o,xyMinMax:o}},polygon:function(n,i,t){var r=[[1/0,-1/0],[1/0,-1/0]];return{values:P(t,function(t){var e=i[Fw[n]](t);return r[0][0]=Math.min(r[0][0],e[0]),r[1][0]=Math.min(r[1][0],e[1]),r[0][1]=Math.max(r[0][1],e[0]),r[1][1]=Math.max(r[1][1],e[1]),e}),xyMinMax:r}}};function $w(t,e,n,i){C&&j("cartesian2d"===n.type,"lineX/lineY brush is available only in cartesian2d.");var r=n.getAxis(["x","y"][t]),o=Zw(P([0,1],function(t){return e?r.coordToData(r.toLocalCoord(i[t])):r.toGlobalCoord(r.dataToCoord(i[t]))})),a=[];return a[t]=o,a[1-t]=[NaN,NaN],{values:o,xyMinMax:a}}var Kw={lineX:Vw(Qw,0),lineY:Vw(Qw,1),rect:function(t,e,n){return[[t[0][0]-n[0]*e[0][0],t[0][1]-n[0]*e[0][1]],[t[1][0]-n[1]*e[1][0],t[1][1]-n[1]*e[1][1]]]},polygon:function(t,n,i){return P(t,function(t,e){return[t[0]-i[0]*n[e][0],t[1]-i[1]*n[e][1]]})}};function Qw(t,e,n,i){return[e[0]-i[t]*n[0],e[1]-i[t]*n[1]]}function Jw(t){return t?[t[0][1]-t[0][0],t[1][1]-t[1][0]]:[NaN,NaN]}var tb=D,eb="\0_ec_hist_store";function nb(t){return t[eb]||(t[eb]=[{}])}F_.extend({type:"dataZoom.select"}),G_.extend({type:"dataZoom.select"});var ib=lc.toolbox.dataZoom,rb=D;function ob(t,e,n){(this._brushController=new rw(n.getZr())).on("brush",L(this._onBrush,this)).mount(),this._isZoomActive}ob.defaultOption={show:!0,filterMode:"filter",icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:T(ib.title),brushStyle:{borderWidth:0,color:"rgba(0,0,0,0.2)"}};var ab=ob.prototype;ab.render=function(t,e,n,i){var r;this.model=t,this.ecModel=e,this.api=n,function(t,e,n,i,r){var o=n._isZoomActive;i&&"takeGlobalCursor"===i.type&&(o="dataZoomSelect"===i.key&&i.dataZoomSelectActive);n._isZoomActive=o,t.setIconStatus("zoom",o?"emphasis":"normal");var a=new Ww(lb(t.option),e,{include:["grid"]});n._brushController.setPanels(a.makePanelOpts(r,function(t){return t.xAxisDeclared&&!t.yAxisDeclared?"lineX":!t.xAxisDeclared&&t.yAxisDeclared?"lineY":"rect"})).enableBrush(!!o&&{brushType:"auto",brushStyle:t.getModel("brushStyle").getItemStyle()})}(t,e,this,i,n),r=e,t.setIconStatus("back",1<function(t){return nb(t).length}(r)?"emphasis":"normal")},ab.onclick=function(t,e,n){sb[n].call(this)},ab.remove=function(t,e){this._brushController.unmount()},ab.dispose=function(t,e){this._brushController.dispose()};var sb={zoom:function(){var t=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:t})},back:function(){this._dispatchZoomAction(function(t){var i=nb(t),e=i[i.length-1];1<i.length&&i.pop();var r={};return tb(e,function(t,e){for(var n=i.length-1;0<=n;n--){if(t=i[n][e]){r[e]=t;break}}}),r}(this.ecModel))}};function lb(e){var n={};return D(["xAxisIndex","yAxisIndex"],function(t){n[t]=e[t],null==n[t]&&(n[t]="all"),!1!==n[t]&&"none"!==n[t]||(n[t]=[])}),n}ab._onBrush=function(t,e){var u,c,o,n,a;function r(t,e,n){var i,r,o,a=e.getAxis(t),s=a.model,l=(i=t,r=s,c.eachComponent({mainType:"dataZoom",subType:"select"},function(t){t.getAxisModel(i,r.componentIndex)&&(o=t)}),o),h=l.findRepresentativeAxisProxy(s).getMinMaxSpan();null==h.minValueSpan&&null==h.maxValueSpan||(n=P_(0,n.slice(),a.scale.getExtent(),0,h.minValueSpan,h.maxValueSpan)),l&&(u[l.id]={dataZoomId:l.id,startValue:n[0],endValue:n[1]})}e.isEnd&&t.length&&(u={},c=this.ecModel,this._brushController.updateCovers([]),new Ww(lb(this.model.option),c,{include:["grid"]}).matchOutputRanges(t,c,function(t,e,n){var i;"cartesian2d"===n.type&&("rect"===(i=t.brushType)?(r("x",n,e[0]),r("y",n,e[1])):r({lineX:"x",lineY:"y"}[i],n,e))}),n=u,a=nb(o=c),tb(n,function(t,e){for(var n,i,r=a.length-1;0<=r;r--){if(a[r][e])break}r<0&&((n=o.queryComponents({mainType:"dataZoom",subType:"select",id:e})[0])&&(i=n.getPercentRange(),a[0][e]={dataZoomId:e,start:i[0],end:i[1]}))}),a.push(n),this._dispatchZoomAction(u))},ab._dispatchZoomAction=function(t){var n=[];rb(t,function(t,e){n.push(T(t))}),n.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:n})},Ix("dataZoom",ob),Ed(function(s){var l,t,e;function n(i,r){var o,a;r&&(null==(a=r[o=i+"Index"])||"all"===a||z(a)||(a=!1===a||"none"===a?[]:[a]),function(t,e){var n=s[t];z(n)||(n=n?[n]:[]);rb(n,e)}(i,function(t,e){var n;null!=a&&"all"!==a&&-1===w(a,e)||((n={type:"select",$fromToolbox:!0,filterMode:r.filterMode||"filter",id:"\0_ec_\0toolbox-dataZoom_"+i+e})[o]=e,l.push(n))}))}s&&(z(l=s.dataZoom||(s.dataZoom=[]))||(s.dataZoom=l=[l]),(t=s.toolbox)&&(z(t)&&(t=t[0]),t&&t.feature&&(n("xAxis",e=t.feature.dataZoom),n("yAxis",e))))});var hb=lc.toolbox.restore;function ub(t){this.model=t}ub.defaultOption={show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:hb.title},ub.prototype.onclick=function(t,e,n){t[eb]=null,e.dispatchAction({type:"restore",from:this.uid})},Ix("restore",ub),Nd({type:"restore",event:"restore",update:"prepareAndUpdate"},function(t,e){e.resetOption("recreate")});var cb,db="urn:schemas-microsoft-com:vml",fb="undefined"==typeof window?null:window,pb=!1,gb=fb&&fb.document;function mb(t){return cb(t)}if(gb&&!v.canvasSupported)try{gb.namespaces.zrvml||gb.namespaces.add("zrvml",db),cb=function(t){return gb.createElement("<zrvml:"+t+' class="zrvml">')}}catch(t){cb=function(t){return gb.createElement("<"+t+' xmlns="'+db+'" class="zrvml">')}}var vb=Uo.CMD,yb=Math.round,_b=Math.sqrt,xb=Math.abs,wb=Math.cos,bb=Math.sin,Sb=Math.max;if(!v.canvasSupported){var Mb=",",Ib="progid:DXImageTransform.Microsoft",Cb=21600,Tb=Cb/2,Ab=function(t){t.style.cssText="position:absolute;left:0;top:0;width:1px;height:1px;",t.coordsize=Cb+","+Cb,t.coordorigin="0,0"},Db=function(t,e,n){return"rgb("+[t,e,n].join(",")+")"},kb=function(t,e){e&&t&&e.parentNode!==t&&t.appendChild(e)},Pb=function(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)},Lb=function(t,e,n){return 1e5*(parseFloat(t)||0)+1e3*(parseFloat(e)||0)+n},Ob=Ei,Eb=function(t,e,n){var i=ze(e);n=+n,isNaN(n)&&(n=1),i&&(t.color=Db(i[0],i[1],i[2]),t.opacity=n*i[3])},zb=function(t,e,n){var i,r,o=e.fill;if(null!=o)if(o instanceof ja){var a,s,l,h,u,c,d,f,p,g,m=0,v=[0,0],y=0,_=1,x=n.getBoundingRect(),w=x.width,b=x.height;"linear"===o.type?(a="gradient",c=n.transform,u=[o.x*w,o.y*b],s=[o.x2*w,o.y2*b],c&&(yt(u,u,c),yt(s,s,c)),l=s[0]-u[0],h=s[1]-u[1],(m=180*Math.atan2(l,h)/Math.PI)<0&&(m+=360),m<1e-6&&(m=0)):(a="gradientradial",u=[o.x*w,o.y*b],c=n.transform,d=n.scale,f=w,p=b,v=[(u[0]-x.x)/f,(u[1]-x.y)/p],c&&yt(u,u,c),f/=d[0]*Cb,p/=d[1]*Cb,y=0/(g=Sb(f,p)),_=2*o.r/g-y);var S=o.colorStops.slice();S.sort(function(t,e){return t.offset-e.offset});for(var M,I,C,T,A=S.length,D=[],k=[],P=0;P<A;P++){var L=S[P],O=(i=L.color,r=void 0,r=ze(i),[Db(r[0],r[1],r[2]),r[3]]);k.push(L.offset*_+y+" "+O[0]),0!==P&&P!==A-1||D.push(O)}2<=A&&(M=D[0][0],I=D[1][0],C=D[0][1]*e.opacity,T=D[1][1]*e.opacity,t.type=a,t.method="none",t.focus="100%",t.angle=m,t.color=M,t.color2=I,t.colors=k.join(","),t.opacity=T,t.opacity2=C),"radial"===a&&(t.focusposition=v.join(","))}else Eb(t,o,e.opacity)},Nb=function(t,e,n,i){var r,o,a="fill"===e,s=t.getElementsByTagName(e)[0];null!=n[e]&&"none"!==n[e]&&(a||!a&&n.lineWidth)?(t[a?"filled":"stroked"]="true",n[e]instanceof ja&&Pb(t,s),s=s||mb(e),a?zb(s,n,i):(r=s,(o=n).lineDash&&(r.dashstyle=o.lineDash.join(" ")),null==o.stroke||o.stroke instanceof ja||Eb(r,o.stroke,o.opacity)),kb(t,s)):(t[a?"filled":"stroked"]="false",Pb(t,s))},Rb=[[],[],[]];ua.prototype.brushVML=function(t){var e=this.style,n=this._vmlEl;n||(n=mb("shape"),Ab(n),this._vmlEl=n),Nb(n,"fill",e,this),Nb(n,"stroke",e,this);var i,r,o=this.transform,a=null!=o,s=n.getElementsByTagName("stroke")[0];s&&(i=e.lineWidth,a&&!e.strokeNoScale&&(r=o[0]*o[3]-o[1]*o[2],i*=_b(xb(r))),s.weight=i+"px");var l=this.path||(this.path=new Uo);this.__dirtyPath&&(l.beginPath(),l.subPixelOptimize=!1,this.buildPath(l,this.shape),l.toStatic(),this.__dirtyPath=!1),n.path=function(t,e){for(var n,i,r,o,a,s=vb.M,l=vb.C,h=vb.L,u=vb.A,c=vb.Q,d=[],f=t.data,p=t.len(),g=0;g<p;){switch(i="",n=0,r=f[g++]){case s:i=" m ",n=1,o=f[g++],a=f[g++],Rb[0][0]=o,Rb[0][1]=a;break;case h:i=" l ",n=1,o=f[g++],a=f[g++],Rb[0][0]=o,Rb[0][1]=a;break;case c:case l:i=" c ",n=3;var m,v,y=f[g++],_=f[g++],x=f[g++],w=f[g++];r===c?(x=((m=x)+2*y)/3,w=((v=w)+2*_)/3,y=(o+2*y)/3,_=(a+2*_)/3):(m=f[g++],v=f[g++]),Rb[0][0]=y,Rb[0][1]=_,Rb[1][0]=x,Rb[1][1]=w,o=Rb[2][0]=m,a=Rb[2][1]=v;break;case u:var b=0,S=0,M=1,I=1,C=0;e&&(b=e[4],S=e[5],M=_b(e[0]*e[0]+e[1]*e[1]),I=_b(e[2]*e[2]+e[3]*e[3]),C=Math.atan2(-e[1]/I,e[0]/M));var T=f[g++],A=f[g++],D=f[g++],k=f[g++],P=f[g++]+C,L=f[g++]+P+C;g++;var O=f[g++],E=T+wb(P)*D,z=A+bb(P)*k,y=T+wb(L)*D,_=A+bb(L)*k,N=O?" wa ":" at ";Math.abs(E-y)<1e-4&&(.01<Math.abs(L-P)?O&&(E+=.0125):Math.abs(z-A)<1e-4?O&&E<T||!O&&T<E?_-=.0125:_+=.0125:O&&z<A||!O&&A<z?y+=.0125:y-=.0125),d.push(N,yb(((T-D)*M+b)*Cb-Tb),Mb,yb(((A-k)*I+S)*Cb-Tb),Mb,yb(((T+D)*M+b)*Cb-Tb),Mb,yb(((A+k)*I+S)*Cb-Tb),Mb,yb((E*M+b)*Cb-Tb),Mb,yb((z*I+S)*Cb-Tb),Mb,yb((y*M+b)*Cb-Tb),Mb,yb((_*I+S)*Cb-Tb)),o=y,a=_;break;case vb.R:var R=Rb[0],B=Rb[1];R[0]=f[g++],R[1]=f[g++],B[0]=R[0]+f[g++],B[1]=R[1]+f[g++],e&&(yt(R,R,e),yt(B,B,e)),R[0]=yb(R[0]*Cb-Tb),B[0]=yb(B[0]*Cb-Tb),R[1]=yb(R[1]*Cb-Tb),B[1]=yb(B[1]*Cb-Tb),d.push(" m ",R[0],Mb,R[1]," l ",B[0],Mb,R[1]," l ",B[0],Mb,B[1]," l ",R[0],Mb,B[1]);break;case vb.Z:d.push(" x ")}if(0<n){d.push(i);for(var V=0;V<n;V++){var F=Rb[V];e&&yt(F,F,e),d.push(yb(F[0]*Cb-Tb),Mb,yb(F[1]*Cb-Tb),V<n-1?Mb:"")}}}return d.join("")}(l,this.transform),n.style.zIndex=Lb(this.zlevel,this.z,this.z2),kb(t,n),null!=e.text?this.drawRectText(t,this.getBoundingRect()):this.removeRectText(t)},ua.prototype.onRemove=function(t){Pb(t,this._vmlEl),this.removeRectText(t)},ua.prototype.onAdd=function(t){kb(t,this._vmlEl),this.appendRectText(t)};Fi.prototype.brushVML=function(t){var e,n,i,r,o,a,s,l,h,u,c,d,f,p,g,m,v,y,_,x,w,b,S,M,I,C,T,A,D,k,P,L,O,E,z,N,R,B=this.style,V=B.image;"object"===FS(s=V)&&s.tagName&&"IMG"===s.tagName.toUpperCase()?((i=V.src)===this._imageSrc?(e=this._imageWidth,n=this._imageHeight):(o=(r=V.runtimeStyle).width,a=r.height,r.width="auto",r.height="auto",e=V.width,n=V.height,r.width=o,r.height=a,this._imageSrc=i,this._imageWidth=e,this._imageHeight=n),V=i):V===this._imageSrc&&(e=this._imageWidth,n=this._imageHeight),V&&(l=B.x||0,h=B.y||0,u=B.width,c=B.height,d=B.sWidth,f=B.sHeight,p=B.sx||0,g=B.sy||0,m=d&&f,(v=this._vmlEl)||(v=gb.createElement("div"),Ab(v),this._vmlEl=v),y=v.style,_=!1,b=w=1,this.transform&&(x=this.transform,w=_b(x[0]*x[0]+x[1]*x[1]),b=_b(x[2]*x[2]+x[3]*x[3]),_=x[1]||x[2]),_?(M=[l+u,h],I=[l,h+c],C=[l+u,h+c],yt(S=[l,h],S,x),yt(M,M,x),yt(I,I,x),yt(C,C,x),T=Sb(S[0],M[0],I[0],C[0]),A=Sb(S[1],M[1],I[1],C[1]),(D=[]).push("M11=",x[0]/w,Mb,"M12=",x[2]/b,Mb,"M21=",x[1]/w,Mb,"M22=",x[3]/b,Mb,"Dx=",yb(l*w+x[4]),Mb,"Dy=",yb(h*b+x[5])),y.padding="0 "+yb(T)+"px "+yb(A)+"px 0",y.filter=Ib+".Matrix("+D.join("")+", SizingMethod=clip)"):(x&&(l=l*w+x[4],h=h*b+x[5]),y.filter="",y.left=yb(l)+"px",y.top=yb(h)+"px"),k=this._imageEl,P=this._cropEl,k||(k=gb.createElement("div"),this._imageEl=k),L=k.style,m?(e&&n?(L.width=yb(w*e*u/d)+"px",L.height=yb(b*n*c/f)+"px"):(O=new Image,E=this,O.onload=function(){O.onload=null,e=O.width,n=O.height,L.width=yb(w*e*u/d)+"px",L.height=yb(b*n*c/f)+"px",E._imageWidth=e,E._imageHeight=n,E._imageSrc=V},O.src=V),P||((P=gb.createElement("div")).style.overflow="hidden",this._cropEl=P),(z=P.style).width=yb((u+p*u/d)*w),z.height=yb((c+g*c/f)*b),z.filter=Ib+".Matrix(Dx="+-p*u/d*w+",Dy="+-g*c/f*b+")",P.parentNode||v.appendChild(P),k.parentNode!==P&&P.appendChild(k)):(L.width=yb(w*u)+"px",L.height=yb(b*c)+"px",v.appendChild(k),P&&P.parentNode&&(v.removeChild(P),this._cropEl=null)),N="",(R=B.opacity)<1&&(N+=".Alpha(opacity="+yb(100*R)+") "),N+=Ib+".AlphaImageLoader(src="+V+", SizingMethod=scale)",L.filter=N,v.style.zIndex=Lb(this.zlevel,this.z,this.z2),kb(t,v),null!=B.text&&this.drawRectText(t,this.getBoundingRect()))},Fi.prototype.onRemove=function(t){Pb(t,this._vmlEl),this._vmlEl=null,this._cropEl=null,this._imageEl=null,this.removeRectText(t)},Fi.prototype.onAdd=function(t){kb(t,this._vmlEl),this.appendRectText(t)};var Bb,Vb="normal",Fb={},Hb=0,Wb=document.createElement("div");ei.measureText=function(t,e){var n=gb;Bb||((Bb=n.createElement("div")).style.cssText="position:absolute;top:-20000px;left:0;padding:0;margin:0;border:none;white-space:pre;",gb.body.appendChild(Bb));try{Bb.style.font=e}catch(t){}return Bb.innerHTML="",Bb.appendChild(n.createTextNode(t)),{width:Bb.offsetWidth}};for(var Gb=new yn,Zb=function(t,e,n,i){var r=this.style;if(this.__dirty&&bi(r),null!=(a=r.text)&&(a+=""),a){if(r.rich){for(var o=di(a,r),a=[],s=0;s<o.lines.length;s++){for(var l=o.lines[s].tokens,h=[],u=0;u<l.length;u++)h.push(l[u].text);a.push(h.join(""))}a=a.join("\n")}var c=r.textAlign,d=r.textVerticalAlign,f=function(t){var e=Fb[t];if(!e){100<Hb&&(Hb=0,Fb={});var n,i=Wb.style;try{i.font=t,n=i.fontFamily.split(",")[0]}catch(t){}e={style:i.fontStyle||Vb,variant:i.fontVariant||Vb,weight:i.fontWeight||Vb,size:0|parseFloat(i.fontSize||12),family:n||"Microsoft YaHei"},Fb[t]=e,Hb++}return e}(r.font),p=f.style+" "+f.variant+" "+f.weight+" "+f.size+'px "'+f.family+'"';n=n||ii(a,p,c,d,r.textPadding,r.textLineHeight);var g,m,v,y,_=this.transform;_&&!i&&(Gb.copy(e),Gb.applyTransform(_),e=Gb),i?(v=e.x,y=e.y):(g=r.textPosition)instanceof Array?(v=e.x+Ob(g[0],e.width),y=e.y+Ob(g[1],e.height),c=c||"left"):(v=(m=this.calculateTextPosition?this.calculateTextPosition({},r,e):ai({},r,e)).x,y=m.y,c=c||m.textAlign,d=d||m.textVerticalAlign),v=ri(v,n.width,c),y=oi(y,n.height,d),y+=n.height/2;var x,w,b,S=mb,M=this._textVmlEl;M?w=(x=(b=M.firstChild).nextSibling).nextSibling:(M=S("line"),x=S("path"),w=S("textpath"),b=S("skew"),w.style["v-text-align"]="left",Ab(M),x.textpathok=!0,w.on=!0,M.from="0 0",M.to="1000 0.05",kb(M,b),kb(M,x),kb(M,w),this._textVmlEl=M);var I=[v,y],C=M.style;_&&i?(yt(I,I,_),b.on=!0,b.matrix=_[0].toFixed(3)+Mb+_[2].toFixed(3)+Mb+_[1].toFixed(3)+Mb+_[3].toFixed(3)+",0,0",b.offset=(yb(I[0])||0)+","+(yb(I[1])||0),b.origin="0 0",C.left="0px",C.top="0px"):(b.on=!1,C.left=yb(v)+"px",C.top=yb(y)+"px"),w.string=String(a).replace(/&/g,"&amp;").replace(/"/g,"&quot;");try{w.style.font=p}catch(t){}Nb(M,"fill",{fill:r.textFill,opacity:r.opacity},this),Nb(M,"stroke",{stroke:r.textStroke,opacity:r.opacity,lineDash:r.lineDash||null},this),M.style.zIndex=Lb(this.zlevel,this.z,this.z2),kb(t,M)}},Ub=function(t){Pb(t,this._textVmlEl),this._textVmlEl=null},Xb=function(t){kb(t,this._textVmlEl)},Yb=[Ri,Vi,Fi,ua,Aa],jb=0;jb<Yb.length;jb++){var qb=Yb[jb].prototype;qb.drawRectText=Zb,qb.removeRectText=Ub,qb.appendRectText=Xb}Aa.prototype.brushVML=function(t){var e=this.style;null!=e.text?this.drawRectText(t,{x:e.x||0,y:e.y||0,width:0,height:0},this.getBoundingRect(),!0):this.removeRectText(t)},Aa.prototype.onRemove=function(t){this.removeRectText(t)},Aa.prototype.onAdd=function(t){this.appendRectText(t)}}function $b(t){return parseInt(t,10)}function Kb(t,e){var n;!pb&&gb&&(pb=!0,(n=gb.styleSheets).length<31?gb.createStyleSheet().addRule(".zrvml","behavior:url(#default#VML)"):n[0].addRule(".zrvml","behavior:url(#default#VML)")),this.root=t,this.storage=e;var i=document.createElement("div"),r=document.createElement("div");i.style.cssText="display:inline-block;overflow:hidden;position:relative;width:300px;height:150px;",r.style.cssText="position:absolute;left:0;top:0;",t.appendChild(i),this._vmlRoot=r,this._vmlViewport=i,this.resize();var o=e.delFromStorage,a=e.addToStorage;e.delFromStorage=function(t){o.call(e,t),t&&t.onRemove&&t.onRemove(r)},e.addToStorage=function(t){t.onAdd&&t.onAdd(r),a.call(e,t)},this._firstPaint=!0}Kb.prototype={constructor:Kb,getType:function(){return"vml"},getViewportRoot:function(){return this._vmlViewport},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(){var t=this.storage.getDisplayList(!0,!0);this._paintList(t)},_paintList:function(t){for(var e=this._vmlRoot,n=0;n<t.length;n++){var i=t[n];i.invisible||i.ignore?(i.__alreadyNotVisible||i.onRemove(e),i.__alreadyNotVisible=!0):(i.__alreadyNotVisible&&i.onAdd(e),i.__alreadyNotVisible=!1,i.__dirty&&(i.beforeBrush&&i.beforeBrush(),(i.brushVML||i.brush).call(i,e),i.afterBrush&&i.afterBrush())),i.__dirty=!1}this._firstPaint&&(this._vmlViewport.appendChild(e),this._firstPaint=!1)},resize:function(t,e){var n,t=null==t?this._getWidth():t,e=null==e?this._getHeight():e;this._width===t&&this._height===e||(this._width=t,this._height=e,(n=this._vmlViewport.style).width=t+"px",n.height=e+"px")},dispose:function(){this.root.innerHTML="",this._vmlRoot=this._vmlViewport=this.storage=null},getWidth:function(){return this._width},getHeight:function(){return this._height},clear:function(){this._vmlViewport&&this.root.removeChild(this._vmlViewport)},_getWidth:function(){var t=this.root,e=t.currentStyle;return(t.clientWidth||$b(e.width))-$b(e.paddingLeft)-$b(e.paddingRight)|0},_getHeight:function(){var t=this.root,e=t.currentStyle;return(t.clientHeight||$b(e.height))-$b(e.paddingTop)-$b(e.paddingBottom)|0}},D(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","toDataURL","pathToImage"],function(t){var e;Kb.prototype[t]=(e=t,function(){sn('In IE8.0 VML mode painter not support method "'+e+'"')})}),_r("vml",Kb);function Qb(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}var Jb=Uo.CMD,tS=Array.prototype.join,eS="none",nS=Math.round,iS=Math.sin,rS=Math.cos,oS=Math.PI,aS=2*Math.PI,sS=180/oS,lS=1e-4;function hS(t){return nS(1e4*t)/1e4}function uS(t){return t<lS&&-lS<t}function cS(t,e){e&&dS(t,"transform","matrix("+tS.call(e,",")+")")}function dS(t,e,n){n&&("linear"===n.type||"radial"===n.type)||t.setAttribute(e,n)}function fS(t,e,n,i){var r,o,a,s,l,h;o=e,null!=(a=n?o.textFill:o.fill)&&a!==eS?(dS(t,"fill",r="transparent"===(r=n?e.textFill:e.fill)?eS:r),dS(t,"fill-opacity",null!=e.fillOpacity?e.fillOpacity*e.opacity:e.opacity)):dS(t,"fill",eS),l=e,null!=(h=n?l.textStroke:l.stroke)&&h!==eS?(dS(t,"stroke",s="transparent"===(s=n?e.textStroke:e.stroke)?eS:s),dS(t,"stroke-width",(n?e.textStrokeWidth:e.lineWidth)/(!n&&e.strokeNoScale?i.getLineScale():1)),dS(t,"paint-order",n?"stroke":"fill"),dS(t,"stroke-opacity",null!=e.strokeOpacity?e.strokeOpacity:e.opacity),e.lineDash?(dS(t,"stroke-dasharray",e.lineDash.join(",")),dS(t,"stroke-dashoffset",nS(e.lineDashOffset||0))):dS(t,"stroke-dasharray",""),e.lineCap&&dS(t,"stroke-linecap",e.lineCap),e.lineJoin&&dS(t,"stroke-linejoin",e.lineJoin),e.miterLimit&&dS(t,"stroke-miterlimit",e.miterLimit)):dS(t,"stroke",eS)}var pS={};pS.brush=function(t){var e=t.style,n=t.__svgEl;n||(n=Qb("path"),t.__svgEl=n),t.path||t.createPathProxy();var i,r=t.path;t.__dirtyPath&&(r.beginPath(),r.subPixelOptimize=!1,t.buildPath(r,t.shape),t.__dirtyPath=!1,(i=function(t){for(var e=[],n=t.data,i=t.len(),r=0;r<i;){var o="",a=0;switch(n[r++]){case Jb.M:o="M",a=2;break;case Jb.L:o="L",a=2;break;case Jb.Q:o="Q",a=4;break;case Jb.C:o="C",a=6;break;case Jb.A:var s=n[r++],l=n[r++],h=n[r++],u=n[r++],c=n[r++],d=n[r++],f=n[r++],p=n[r++],g=Math.abs(d),m=uS(g-aS)||(p?aS<=d:aS<=-d),v=!1,v=!!m||!uS(g)&&oS<=(0<d?d%aS:d%aS+aS)==!!p,y=hS(s+h*rS(c)),_=hS(l+u*iS(c));m&&(d=p?aS-1e-4:1e-4-aS,v=!0,9===r&&e.push("M",y,_));var x=hS(s+h*rS(c+d)),w=hS(l+u*iS(c+d));e.push("A",hS(h),hS(u),nS(f*sS),+v,+p,x,w);break;case Jb.Z:o="Z";break;case Jb.R:var x=hS(n[r++]),w=hS(n[r++]),b=hS(n[r++]),S=hS(n[r++]);e.push("M",x,w,"L",x+b,w,"L",x+b,w+S,"L",x,w+S,"L",x,w)}o&&e.push(o);for(var M=0;M<a;M++)e.push(hS(n[r++]))}return e.join(" ")}(r)).indexOf("NaN")<0&&dS(n,"d",i)),fS(n,e,!1,t),cS(n,t.transform),null!=e.text?wS(t,t.getBoundingRect()):SS(t)};var gS={brush:function(t){var e,n,i,r,o,a,s,l=t.style,h=l.image;h instanceof HTMLImageElement&&(h=h.src),h&&(e=l.x||0,n=l.y||0,i=l.width,r=l.height,(o=t.__svgEl)||(o=Qb("image"),t.__svgEl=o),h!==t.__imageSrc&&(a="href",s=h,o.setAttributeNS("http://www.w3.org/1999/xlink",a,s),t.__imageSrc=h),dS(o,"width",i),dS(o,"height",r),dS(o,"x",e),dS(o,"y",n),cS(o,t.transform),null!=l.text?wS(t,t.getBoundingRect()):SS(t))}},mS={},vS=new yn,yS={},_S=[],xS={left:"start",right:"end",center:"middle",middle:"middle"},wS=function(t,e){var n=t.style,i=t.transform,r=t instanceof Aa||n.transformText;t.__dirty&&bi(n);var o=n.text;if(null!=o&&(o+=""),Ni(o,n)){null==o&&(o=""),!r&&i&&(vS.copy(e),vS.applyTransform(i),e=vS);var a=t.__textSvgEl;a||(a=Qb("text"),t.__textSvgEl=a);var s=a.style,l=n.font||ti,h=a.__computedFont;l!==a.__styleFont&&(s.font=a.__styleFont=l,h=a.__computedFont=s.font);var u=n.textPadding,c=n.textLineHeight,d=t.__textCotentBlock;d&&!t.__dirtyText||(d=t.__textCotentBlock=ci(o,h,u,c,n.truncate));var f=d.outerHeight,p=d.lineHeight;ki(yS,t,n,e);var g=yS.baseX,m=yS.baseY,v=yS.textAlign||"left",y=yS.textVerticalAlign;!function(t,e,n,i,r,o,a){ee(_S),e&&n&&ne(_S,n);var s=i.textRotation;{var l;r&&s&&("center"===(l=i.textOrigin)?(o=r.width/2+r.x,a=r.height/2+r.y):l&&(o=l[0]+r.x,a=l[1]+r.y),_S[4]-=o,_S[5]-=a,oe(_S,_S,s),_S[4]+=o,_S[5]+=a)}cS(t,_S)}(a,r,i,n,e,g,m);var _,x,w,b=g,S=oi(m,f,y);u&&(_=g,w=u,b="right"===(x=v)?_-w[1]:"center"===x?_+w[3]/2-w[1]/2:_+w[3],S+=u[0]),S+=p/2,fS(a,n,!0,t);var M=d.canCacheByTextString,I=t.__tspanList||(t.__tspanList=[]),C=I.length;if(M&&t.__canCacheByTextString&&t.__text===o){if(t.__dirtyText&&C)for(var T=0;T<C;++T)bS(I[T],v,b,S+T*p)}else{t.__text=o,t.__canCacheByTextString=M;for(var A=d.lines,D=A.length,T=0;T<D;T++){var k=I[T],P=A[T];k?k.__zrText!==P&&(k.innerHTML="",k.appendChild(document.createTextNode(P))):(k=I[T]=Qb("tspan"),a.appendChild(k),k.appendChild(document.createTextNode(P))),bS(k,v,b,S+T*p)}if(D<C){for(;T<C;T++)a.removeChild(I[T]);I.length=D}}}};function bS(t,e,n,i){dS(t,"dominant-baseline","middle"),dS(t,"text-anchor",xS[e]),dS(t,"x",n),dS(t,"y",i)}function SS(t){t&&t.__textSvgEl&&(t.__textSvgEl.parentNode&&t.__textSvgEl.parentNode.removeChild(t.__textSvgEl),t.__textSvgEl=null,t.__tspanList=[],t.__text=null)}function MS(){}mS.drawRectText=wS,mS.brush=function(t){null!=t.style.text?wS(t,!1):SS(t)},MS.prototype={diff:function(l,h,t){t=t||function(t,e){return t===e},this.equals=t;var u=this;l=l.slice();var c=(h=h.slice()).length,d=l.length,f=1,e=c+d,p=[{newPos:-1,components:[]}],n=this.extractCommon(p[0],h,l,0);if(p[0].newPos+1>=c&&d<=n+1){for(var i=[],r=0;r<h.length;r++)i.push(r);return[{indices:i,count:h.length}]}for(;f<=e;){var o=function(){for(var t,e=-1*f;e<=f;e+=2){var n,i=p[e-1],r=p[e+1],o=(r?r.newPos:0)-e;i&&(p[e-1]=void 0);var a=i&&i.newPos+1<c,s=r&&0<=o&&o<d;if(a||s){if(!a||s&&i.newPos<r.newPos?(n={newPos:(t=r).newPos,components:t.components.slice(0)},u.pushComponent(n.components,void 0,!0)):((n=i).newPos++,u.pushComponent(n.components,!0,void 0)),o=u.extractCommon(n,h,l,e),n.newPos+1>=c&&d<=o+1)return function(t){for(var e=0,n=t.length,i=0,r=0;e<n;e++){var o=t[e];if(o.removed){for(a=[],s=r;s<r+o.count;s++)a.push(s);o.indices=a,r+=o.count}else{for(var a=[],s=i;s<i+o.count;s++)a.push(s);o.indices=a,i+=o.count,o.added||(r+=o.count)}}return t}(n.components);p[e]=n}else p[e]=void 0}f++}();if(o)return o}},pushComponent:function(t,e,n){var i=t[t.length-1];i&&i.added===e&&i.removed===n?t[t.length-1]={count:i.count+1,added:e,removed:n}:t.push({count:1,added:e,removed:n})},extractCommon:function(t,e,n,i){for(var r=e.length,o=n.length,a=t.newPos,s=a-i,l=0;a+1<r&&s+1<o&&this.equals(e[a+1],n[s+1]);)a++,s++,l++;return l&&t.components.push({count:l}),t.newPos=a,s},tokenize:function(t){return t.slice()},join:function(t){return t.slice()}};var IS=new MS;function CS(t,e,n,i,r){this._zrId=t,this._svgRoot=e,this._tagNames="string"==typeof n?[n]:n,this._markLabel=i,this._domName=r||"_dom",this.nextId=0}function TS(t,e){CS.call(this,t,e,["linearGradient","radialGradient"],"__gradient_in_use__")}function AS(t,e){CS.call(this,t,e,"clipPath","__clippath_in_use__")}function DS(t,e){CS.call(this,t,e,["filter"],"__filter_in_use__","_shadowDom")}function kS(t){return t&&(t.shadowBlur||t.shadowOffsetX||t.shadowOffsetY||t.textShadowBlur||t.textShadowOffsetX||t.textShadowOffsetY)}function PS(t){return parseInt(t,10)}function LS(t,e){return e&&t&&e.parentNode!==t}function OS(t,e,n){var i;LS(t,e)&&n&&((i=n.nextSibling)?t.insertBefore(e,i):t.appendChild(e))}function ES(t,e){var n;LS(t,e)&&((n=t.firstChild)?t.insertBefore(e,n):t.appendChild(e))}function zS(t,e){e&&t&&e.parentNode===t&&t.removeChild(e)}function NS(t){return t.__textSvgEl}function RS(t){return t.__svgEl}CS.prototype.createElement=Qb,CS.prototype.getDefs=function(t){var e=this._svgRoot,i=this._svgRoot.getElementsByTagName("defs");return 0===i.length?t?((i=e.insertBefore(this.createElement("defs"),e.firstChild)).contains||(i.contains=function(t){var e=i.children;if(!e)return!1;for(var n=e.length-1;0<=n;--n)if(e[n]===t)return!0;return!1}),i):null:i[0]},CS.prototype.update=function(t,e){var n,i;t&&(n=this.getDefs(!1),t[this._domName]&&n.contains(t[this._domName])?"function"==typeof e&&e(t):(i=this.add(t))&&(t[this._domName]=i))},CS.prototype.addDom=function(t){this.getDefs(!0).appendChild(t)},CS.prototype.removeDom=function(t){var e=this.getDefs(!1);e&&t[this._domName]&&(e.removeChild(t[this._domName]),t[this._domName]=null)},CS.prototype.getDoms=function(){var n=this.getDefs(!1);if(!n)return[];var i=[];return D(this._tagNames,function(t){var e=n.getElementsByTagName(t);i=i.concat([].slice.call(e))}),i},CS.prototype.markAllUnused=function(){var t=this.getDoms(),e=this;D(t,function(t){t[e._markLabel]="0"})},CS.prototype.markUsed=function(t){t&&(t[this._markLabel]="1")},CS.prototype.removeUnused=function(){var t,e,n=this.getDefs(!1);n&&(t=this.getDoms(),e=this,D(t,function(t){"1"!==t[e._markLabel]&&n.removeChild(t)}))},CS.prototype.getSvgProxy=function(t){return t instanceof ua?pS:t instanceof Fi?gS:t instanceof Aa?mS:pS},CS.prototype.getTextSvgElement=function(t){return t.__textSvgEl},CS.prototype.getSvgElement=function(t){return t.__svgEl},b(TS,CS),TS.prototype.addWithoutUpdate=function(o,a){var s;a&&a.style&&(s=this,D(["fill","stroke"],function(t){var e,n,i,r;!a.style[t]||"linear"!==a.style[t].type&&"radial"!==a.style[t].type||(e=a.style[t],n=s.getDefs(!0),e._dom?(i=e._dom,n.contains(e._dom)||s.addDom(i)):i=s.add(e),s.markUsed(a),r=i.getAttribute("id"),o.setAttribute(t,"url(#"+r+")"))}))},TS.prototype.add=function(t){var e;if("linear"===t.type)e=this.createElement("linearGradient");else{if("radial"!==t.type)return sn("Illegal gradient type."),null;e=this.createElement("radialGradient")}return t.id=t.id||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-gradient-"+t.id),this.updateDom(t,e),this.addDom(e),e},TS.prototype.update=function(n){var i=this;CS.prototype.update.call(this,n,function(){var t=n.type,e=n._dom.tagName;"linear"===t&&"linearGradient"===e||"radial"===t&&"radialGradient"===e?i.updateDom(n,n._dom):(i.removeDom(n),i.add(n))})},TS.prototype.updateDom=function(t,e){if("linear"===t.type)e.setAttribute("x1",t.x),e.setAttribute("y1",t.y),e.setAttribute("x2",t.x2),e.setAttribute("y2",t.y2);else{if("radial"!==t.type)return void sn("Illegal gradient type.");e.setAttribute("cx",t.x),e.setAttribute("cy",t.y),e.setAttribute("r",t.r)}t.global?e.setAttribute("gradientUnits","userSpaceOnUse"):e.setAttribute("gradientUnits","objectBoundingBox"),e.innerHTML="";for(var n=t.colorStops,i=0,r=n.length;i<r;++i){var o=this.createElement("stop");o.setAttribute("offset",100*n[i].offset+"%");var a,s,l=n[i].color;-1<l.indexOf("rgba")?(a=ze(l)[3],s=Be(l),o.setAttribute("stop-color","#"+s),o.setAttribute("stop-opacity",a)):o.setAttribute("stop-color",n[i].color),e.appendChild(o)}t._dom=e},TS.prototype.markUsed=function(t){var e;t.style&&((e=t.style.fill)&&e._dom&&CS.prototype.markUsed.call(this,e._dom),(e=t.style.stroke)&&e._dom&&CS.prototype.markUsed.call(this,e._dom))},b(AS,CS),AS.prototype.update=function(t){var e=this.getSvgElement(t);e&&this.updateDom(e,t.__clipPaths,!1);var n=this.getTextSvgElement(t);n&&this.updateDom(n,t.__clipPaths,!0),this.markUsed(t)},AS.prototype.updateDom=function(t,e,n){var i,r,o,a,s,l,h,u;e&&0<e.length?(i=this.getDefs(!0),(r=e[0])[s=n?"_textDom":"_dom"]?(a=r[s].getAttribute("id"),o=r[s],i.contains(o)||i.appendChild(o)):(a="zr"+this._zrId+"-clip-"+this.nextId,++this.nextId,(o=this.createElement("clipPath")).setAttribute("id",a),i.appendChild(o),r[s]=o),l=this.getSvgProxy(r),r.transform&&r.parent.invTransform&&!n?(h=Array.prototype.slice.call(r.transform),ie(r.transform,r.parent.invTransform,r.transform),l.brush(r),r.transform=h):l.brush(r),u=this.getSvgElement(r),o.innerHTML="",o.appendChild(u.cloneNode()),t.setAttribute("clip-path","url(#"+a+")"),1<e.length&&this.updateDom(o,e.slice(1),n)):t&&t.setAttribute("clip-path","none")},AS.prototype.markUsed=function(t){var e=this;t.__clipPaths&&D(t.__clipPaths,function(t){t._dom&&CS.prototype.markUsed.call(e,t._dom),t._textDom&&CS.prototype.markUsed.call(e,t._textDom)})},b(DS,CS),DS.prototype.addWithoutUpdate=function(t,e){var n,i;e&&kS(e.style)&&(e._shadowDom?(n=e._shadowDom,this.getDefs(!0).contains(e._shadowDom)||this.addDom(n)):n=this.add(e),this.markUsed(e),i=n.getAttribute("id"),t.style.filter="url(#"+i+")")},DS.prototype.add=function(t){var e=this.createElement("filter");return t._shadowDomId=t._shadowDomId||this.nextId++,e.setAttribute("id","zr"+this._zrId+"-shadow-"+t._shadowDomId),this.updateDom(t,e),this.addDom(e),e},DS.prototype.update=function(t,e){var n;kS(e.style)?(n=this,CS.prototype.update.call(this,e,function(){n.updateDom(e,e._shadowDom)})):this.remove(t,e)},DS.prototype.remove=function(t,e){null!=e._shadowDomId&&(this.removeDom(t),t.style.filter="")},DS.prototype.updateDom=function(t,e){var n,i,r,o,a=0===(a=e.getElementsByTagName("feDropShadow")).length?this.createElement("feDropShadow"):a[0],s=t.style,l=t.scale&&t.scale[0]||1,h=t.scale&&t.scale[1]||1;if(s.shadowBlur||s.shadowOffsetX||s.shadowOffsetY)n=s.shadowOffsetX||0,i=s.shadowOffsetY||0,r=s.shadowBlur,o=s.shadowColor;else{if(!s.textShadowBlur)return void this.removeDom(e,s);n=s.textShadowOffsetX||0,i=s.textShadowOffsetY||0,r=s.textShadowBlur,o=s.textShadowColor}a.setAttribute("dx",n/l),a.setAttribute("dy",i/h),a.setAttribute("flood-color",o);var u=r/2/l+" "+r/2/h;a.setAttribute("stdDeviation",u),e.setAttribute("x","-100%"),e.setAttribute("y","-100%"),e.setAttribute("width",Math.ceil(r/2*200)+"%"),e.setAttribute("height",Math.ceil(r/2*200)+"%"),e.appendChild(a),t._shadowDom=e},DS.prototype.markUsed=function(t){t._shadowDom&&CS.prototype.markUsed.call(this,t._shadowDom)};function BS(t,e,n,i){this.root=t,this.storage=e,this._opts=n=k({},n||{});var r=Qb("svg");r.setAttribute("xmlns","http://www.w3.org/2000/svg"),r.setAttribute("version","1.1"),r.setAttribute("baseProfile","full"),r.style.cssText="user-select:none;position:absolute;left:0;top:0;";var o=Qb("g");r.appendChild(o);var a=Qb("g");r.appendChild(a),this.gradientManager=new TS(i,a),this.clipPathManager=new AS(i,a),this.shadowManager=new DS(i,a);var s=document.createElement("div");s.style.cssText="overflow:hidden;position:relative",this._svgDom=r,this._svgRoot=a,this._backgroundRoot=o,this._viewport=s,t.appendChild(s),s.appendChild(r),this.resize(n.width,n.height),this._visibleList=[]}BS.prototype={constructor:BS,getType:function(){return"svg"},getViewportRoot:function(){return this._viewport},getSvgDom:function(){return this._svgDom},getSvgRoot:function(){return this._svgRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(){var t=this.storage.getDisplayList(!0);this._paintList(t)},setBackgroundColor:function(t){this._backgroundRoot&&this._backgroundNode&&this._backgroundRoot.removeChild(this._backgroundNode);var e=Qb("rect");e.setAttribute("width",this.getWidth()),e.setAttribute("height",this.getHeight()),e.setAttribute("x",0),e.setAttribute("y",0),e.setAttribute("id",0),e.style.fill=t,this._backgroundRoot.appendChild(e),this._backgroundNode=e},_paintList:function(t){this.gradientManager.markAllUnused(),this.clipPathManager.markAllUnused(),this.shadowManager.markAllUnused();for(var e,n=this._svgRoot,i=this._visibleList,r=t.length,o=[],a=0;a<r;a++){var s=t[a],l=(e=s)instanceof ua?pS:e instanceof Fi?gS:e instanceof Aa?mS:pS,h=RS(s)||NS(s);s.invisible||(s.__dirty&&(l&&l.brush(s),this.clipPathManager.update(s),s.style&&(this.gradientManager.update(s.style.fill),this.gradientManager.update(s.style.stroke),this.shadowManager.update(h,s)),s.__dirty=!1),o.push(s))}var u,c,d,f,p,g=(u=i,c=o,IS.diff(u,c,d));for(a=0;a<g.length;a++){if((p=g[a]).removed)for(var m=0;m<p.count;m++){var h=RS(s=i[p.indices[m]]),v=NS(s);zS(n,h),zS(n,v)}}for(a=0;a<g.length;a++){if((p=g[a]).added)for(m=0;m<p.count;m++){h=RS(s=o[p.indices[m]]),v=NS(s);f?OS(n,h,f):ES(n,h),h?OS(n,v,h):f?OS(n,v,f):ES(n,v),OS(n,v,h),f=v||h||f,this.gradientManager.addWithoutUpdate(h||v,s),this.shadowManager.addWithoutUpdate(h||v,s),this.clipPathManager.markUsed(s)}else if(!p.removed)for(m=0;m<p.count;m++){h=RS(s=o[p.indices[m]]),v=NS(s),h=RS(s),v=NS(s);this.gradientManager.markUsed(s),this.gradientManager.addWithoutUpdate(h||v,s),this.shadowManager.markUsed(s),this.shadowManager.addWithoutUpdate(h||v,s),this.clipPathManager.markUsed(s),v&&OS(n,v,h),f=h||v||f}}this.gradientManager.removeUnused(),this.clipPathManager.removeUnused(),this.shadowManager.removeUnused(),this._visibleList=o},_getDefs:function(t){var e=this._svgDom,i=e.getElementsByTagName("defs");return 0!==i.length?i[0]:t?((i=e.insertBefore(Qb("defs"),e.firstChild)).contains||(i.contains=function(t){var e=i.children;if(!e)return!1;for(var n=e.length-1;0<=n;--n)if(e[n]===t)return!0;return!1}),i):null},resize:function(t,e){var n=this._viewport;n.style.display="none";var i,r,o=this._opts;null!=t&&(o.width=t),null!=e&&(o.height=e),t=this._getSize(0),e=this._getSize(1),n.style.display="",this._width===t&&this._height===e||(this._width=t,this._height=e,(i=n.style).width=t+"px",i.height=e+"px",(r=this._svgDom).setAttribute("width",t),r.setAttribute("height",e)),this._backgroundNode&&(this._backgroundNode.setAttribute("width",t),this._backgroundNode.setAttribute("height",e))},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,n=["width","height"][t],i=["clientWidth","clientHeight"][t],r=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=e[n]&&"auto"!==e[n])return parseFloat(e[n]);var a=this.root,s=document.defaultView.getComputedStyle(a);return(a[i]||PS(s[n])||PS(a.style[n]))-(PS(s[r])||0)-(PS(s[o])||0)|0},dispose:function(){this.root.innerHTML="",this._svgRoot=this._backgroundRoot=this._svgDom=this._backgroundNode=this._viewport=this.storage=null},clear:function(){this._viewport&&this.root.removeChild(this._viewport)},toDataURL:function(){return this.refresh(),"data:image/svg+xml;charset=UTF-8,"+encodeURIComponent(this._svgDom.outerHTML.replace(/></g,">\n\r<"))}},D(["getLayer","insertLayer","eachLayer","eachBuiltinLayer","eachOtherLayer","getLayers","modLayer","delLayer","clearLayer","pathToImage"],function(t){var e;BS.prototype[t]=(e=t,function(){sn('In SVG mode painter not support method "'+e+'"')})}),_r("svg",BS),t.version="4.9.0",t.dependencies=Uc,t.PRIORITY=Xc,t.init=function(t,e,n){if(C){if(+vr.replace(".","")<+Uc.zrender.replace(".",""))throw new Error("zrender/src 4.3.2 is too old for ECharts 4.9.0. Current version need ZRender "+Uc.zrender+"+");if(!t)throw new Error("Initialize failed: invalid dom.")}var i=Ld(t);if(i)return i;C&&(!H(t)||"CANVAS"===t.nodeName.toUpperCase()||(t.clientWidth||n&&null!=n.width)&&(t.clientHeight||n&&n.height));var r,o,a=new Qc(t,e,n);function s(t,e){for(var n=0;n<t.length;n++){t[n][o]=e}}return a.id="ec_"+Td++,Id[a.id]=a,Vr(t,Dd,a.id),r=a,o="__connectUpdateStatus",Hc(yd,function(t,e){r._messageCenter.on(e,function(t){if(Cd[r.group]&&0!==r[o]){if(t&&t.escapeConnect)return;var e=r.makeActionFromEvent(t),n=[];Hc(Id,function(t){t!==r&&t.group===r.group&&n.push(t)}),s(n,0),Hc(n,function(t){1!==t[o]&&t.dispatchAction(e)}),s(n,2)}})}),a},t.connect=function(e){var t;return z(e)&&(t=e,e=null,Hc(t,function(t){null!=t.group&&(e=t.group)}),e=e||"g_"+Ad++,Hc(t,function(t){t.group=e})),Cd[e]=!0,e},t.disConnect=kd,t.disconnect=Pd,t.dispose=function(t){"string"==typeof t?t=Id[t]:t instanceof Qc||(t=Ld(t)),t instanceof Qc&&!t.isDisposed()&&t.dispose()},t.getInstanceByDom=Ld,t.getInstanceById=function(t){return Id[t]},t.registerTheme=Od,t.registerPreprocessor=Ed,t.registerProcessor=zd,t.registerPostUpdate=function(t){wd.push(t)},t.registerAction=Nd,t.registerCoordinateSystem=function(t,e){Bh.register(t,e)},t.getCoordinateSystemDimensions=function(t){var e=Bh.get(t);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()},t.registerLayout=Rd,t.registerVisual=Bd,t.registerLoading=Fd,t.extendComponentModel=Hd,t.extendComponentView=Wd,t.extendSeriesModel=Gd,t.extendChartView=Zd,t.setCanvasCreator=function(t){p("createCanvas",t)},t.registerMap=function(t,e,n){Rc(t,e,n)},t.getMap=function(t){var e=Bc(t);return e&&e[0]&&{geoJson:e[0].geoJSON,specialAreas:e[0].specialAreas}},t.dataTool={},t.zrender=wr,t.number=Dl,t.format=Ul,t.throttle=rc,t.helper=Bp,t.matrix=le,t.vector=wt,t.color=Ge,t.parseGeoJSON=Zp,t.parseGeoJson=ig,t.util=rg,t.graphic=og,t.List=hf,t.Model=ll,t.Axis=tg,t.env=v},"object"===FS(o)&&void 0!==r?i(o):(e=[o],void 0===(n="function"==typeof(t=i)?t.apply(o,e):t)||(r.exports=n))}).call(this,t("./node_modules/webpack/buildin/global.js"))}}]);