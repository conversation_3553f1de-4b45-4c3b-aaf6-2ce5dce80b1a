{% extends "base.html" %}

{% block stylesheets %}
	<link rel="stylesheet" href="{{ url_for('views.themes', path='css/challenge-board.css') }}">
{% endblock %}

{% block content %}
<div class="jumbotron">
	<div class="container">
		<h1>Challenges</h1>
	</div>
</div>

<div class="modal fade" id="challenge-window" tabindex="-1" role="dialog">
</div>

<div class="container">
	<div class="row">
		<div class="col-md-12">
			{% include "components/errors.html" %}
		</div>
	</div>

	<div id='challenges-board'>
		<div class="min-vh-50 d-flex align-items-center">
			<div class="text-center w-100">
				<i class="fas fa-circle-notch fa-spin fa-3x fa-fw spinner"></i>
			</div>
		</div>
	</div>
</div>
{% endblock %}

{% block scripts %}
{% endblock %}

{% block entrypoint %}
	<script defer src="{{ url_for('views.themes', path='js/pages/challenges.js') }}"></script>
{% endblock %}
