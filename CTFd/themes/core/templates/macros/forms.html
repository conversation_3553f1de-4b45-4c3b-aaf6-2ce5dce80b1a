{% macro render_extra_fields(fields, show_labels=True, show_optionals=True, show_descriptions=True) -%}
    {% for field in fields %}
    <div class="form-group">
        {% if field.field_type == "text" %}
            {% if show_labels %}
                <b>{{ field.label }}</b>
            {% endif %}

            {% if show_optionals %}
                {% if field.flags.required is false %}
                    <small class="float-right text-muted">Optional</small>
                {% endif %}
            {% endif %}

            {{ field(class="form-control") }}

            {% if show_descriptions %}
                {% if field.description %}
                <small class="form-text text-muted">
                    {{ field.description }}
                </small>
                {% endif %}
            {% endif %}
        {% elif field.field_type == "boolean" %}
            <div class="form-check">
                {{ field(class="form-check-input") }}
                {{ field.label(class="form-check-label") }}

                {% if show_optionals %}
                    {% if field.flags.required is false %}
                        <sup class="text-muted">Optional</sup>
                    {% endif %}
                {% endif %}
            </div>

            {% if show_descriptions %}
                {% if field.description %}
                <small class="form-text text-muted">
                    {{ field.description }}
                </small>
                {% endif %}
            {% endif %}
        {% endif %}
    </div>
    {% endfor %}
{%- endmacro %}