{% extends "base.html" %}

{% block stylesheets %}
{% endblock %}

{% block content %}
<div class="jumbotron">
	<div class="container">
		<h1>Login</h1>
	</div>
</div>
<div class="container">
	<div class="row">
		<div class="col-md-6 offset-md-3">
			{% include "components/errors.html" %}

			{% if integrations.mlc() %}
			<a class="btn btn-secondary btn-lg btn-block" href="{{ url_for('auth.oauth_login') }}">
				Login with Major League Cyber
			</a>

			<hr>
			{% endif %}

			{% with form = Forms.auth.LoginForm() %}
			<form method="post" accept-charset="utf-8" autocomplete="off">
				<div class="form-group">
					<b>{{ form.name.label }}</b>
					{{ form.name(class="form-control", value=name) }}
				</div>
				<div class="form-group">
					<b>{{ form.password.label }}</b>
					{{ form.password(class="form-control", value=password) }}
				</div>
				<div class="row pt-3">
					<div class="col-md-6">
						<a class="float-left align-text-to-button" href="{{ url_for('auth.reset_password') }}">
							Forgot your password?
						</a>
					</div>
					<div class="col-md-6">
						{{ form.submit(class="btn btn-md btn-primary btn-outlined float-right") }}
					</div>
				</div>
				{{ form.nonce() }}
			</form>
			{% endwith %}
		</div>
	</div>
</div>
{% endblock %}

{% block scripts %}
{% endblock %}

