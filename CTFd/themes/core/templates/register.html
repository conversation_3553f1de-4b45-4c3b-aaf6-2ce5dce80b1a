{% extends "base.html" %}

{% block stylesheets %}
{% endblock %}

{% block content %}
<div class="jumbotron">
	<div class="container">
		<h1>Register</h1>
	</div>
</div>
<div class="container">
	<div class="row">
		<div class="col-md-6 offset-md-3">
			{% include "components/errors.html" %}

			{% if integrations.mlc() %}
			<a class="btn btn-secondary btn-lg btn-block" href="{{ url_for('auth.oauth_login') }}">
				Login with Major League Cyber
			</a>

			<hr>
			{% endif %}

			{% with form = Forms.auth.RegistrationForm() %}
			{% from "macros/forms.html" import render_extra_fields %}
			<form method="post" accept-charset="utf-8" autocomplete="off" role="form">
				<div class="form-group">
					<b>{{ form.name.label }}</b>
					{{ form.name(class="form-control", value=name) }}
					<small class="form-text text-muted">
						Your username on the site
					</small>
				</div>
				<div class="form-group">
					<b>{{ form.email.label }}</b>
					{{ form.email(class="form-control", value=email) }}
					<small class="form-text text-muted">
						Never shown to the public
					</small>
				</div>
				<div class="form-group">
					<b>{{ form.password.label }}</b>
					{{ form.password(class="form-control", value=password) }}
					<small class="form-text text-muted">
						Password used to log into your account
					</small>
				</div>
				{{ form.nonce() }}

				{{ render_extra_fields(form.extra) }}

				<div class="row pt-3">
					<div class="col-md-12">
						{{ form.submit(class="btn btn-md btn-primary btn-outlined float-right") }}
					</div>
				</div>

				{% if Configs.tos_or_privacy %}
				<div class="row pt-3">
					<div class="col-md-12 text-center">
						<small class="text-muted text-center">
							By registering, you agree to the
							<a href="{{ Configs.privacy_link }}" rel="noopener" target="_blank">privacy policy</a>
							and <a href="{{ Configs.tos_link }}" rel="noopener" target="_blank">terms of service</a>
						</small>
					</div>
				</div>
				{% endif %}
			</form>
			{% endwith %}
		</div>
	</div>
</div>
{% endblock %}

{% block scripts %}
{% endblock %}
