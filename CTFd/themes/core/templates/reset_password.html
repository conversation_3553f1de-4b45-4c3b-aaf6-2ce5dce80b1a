{% extends "base.html" %}

{% block stylesheets %}
{% endblock %}

{% block content %}
<div class="jumbotron">
	<div class="container">
		<h1>Reset Password</h1>
	</div>
</div>
<div class="container">
	<div class="row">
		<div class="col-md-6 offset-md-3">
			{% include "components/errors.html" %}

			{% if mode == "set" %}
				{% with form = Forms.auth.ResetPasswordForm() %}
				<form method="post" accept-charset="utf-8" autocomplete="off" role="form" class="form-horizontal">
					<div class="row">
						<div class="col-md-12">
							<p>You can now reset the password for your account and log in. Please enter in a new password below.</p>
						</div>
					</div>
					<div class="form-group">
						{{ form.password.label }}
						{{ form.password(class="form-control") }}
					</div>
					<div class="row">
						<div class="col-md-6 offset-md-6">
							{{ form.submit(class="btn btn-md btn-primary float-right") }}
						</div>
					</div>
					{{ form.nonce() }}
				</form>
				{% endwith %}
			{% else %}
				{% with form = Forms.auth.ResetPasswordRequestForm() %}
				<form method="post" accept-charset="utf-8" autocomplete="off" role="form" class="form-horizontal">
					<div class="row">
						<div class="col-md-12">
							<p>Please provide the email address associated with your account below.</p>
						</div>
					</div>
					<div class="form-group">
						{{ form.email.label }}
						{{ form.email(class="form-control") }}
					</div>
					<div class="row">
						<div class="col-md-6 offset-md-6">
							{{ form.submit(class="btn btn-md btn-primary float-right") }}
						</div>
					</div>
					{{ form.nonce() }}
				</form>
				{% endwith %}
			{% endif %}
		</div>
	</div>
</div>
{% endblock %}

{% block scripts %}
{% endblock %}
