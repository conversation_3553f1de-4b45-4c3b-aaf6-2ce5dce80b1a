import{m as o,c as n,C as a}from"./index.993fb568.js";import{g as l}from"./userscore.43bb0844.js";import{e as u}from"./index.1cf73b05.js";import"./echarts.128204f2.js";window.Alpine=o;o.data("UserGraphs",()=>({solves:null,fails:null,awards:null,solveCount:0,failCount:0,awardCount:0,getSolvePercentage(){return(this.solveCount/(this.solveCount+this.failCount)*100).toFixed(2)},getFailPercentage(){return(this.failCount/(this.solveCount+this.failCount)*100).toFixed(2)},getCategoryBreakdown(){const e=[],t={};this.solves.data.map(s=>{e.push(s.challenge.category)}),e.forEach(s=>{s in t?t[s]+=1:t[s]=1});const i=[];for(const s in t){const r=Number(t[s]/e.length*100).toFixed(2);i.push({name:s,count:t[s],color:n(s),percent:r})}return i},async init(){this.solves=await a.pages.users.userSolves("me"),this.fails=await a.pages.users.userFails("me"),this.awards=await a.pages.users.userAwards("me"),this.solveCount=this.solves.meta.count,this.failCount=this.fails.meta.count,this.awardCount=this.awards.meta.count;let e=window.userScoreGraphChartOptions;u(this.$refs.scoregraph,l(a.user.id,a.user.name,this.solves.data,this.awards.data,e))}}));o.start();
