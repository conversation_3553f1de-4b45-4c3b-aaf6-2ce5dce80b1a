/* Challenge styles
-------------------------------------------------- */

.challenge-desc {
  overflow-wrap: anywhere;

  img {
    max-width: 100%;
  }
}

[data-bs-theme="light"] .challenge-button {
  border: none;
  box-shadow: 0 0 15px var(--bs-secondary);

  &.challenge-solved {
    background-color: #29c830;

    &:hover {
      background-color: #37d63e;
    }
  }
}

[data-bs-theme="dark"] .challenge-button {
  border: none;
  background-color: var(--bs-gray-dark);

  &:hover {
    background-color: var(--bs-gray);
  }

  &.challenge-solved {
    background-color: #29c830;

    &:hover {
      background-color: #37d63e;
    }
  }
}
