{% extends "base.html" %}

{% block content %}
  <div class="jumbotron">
    <div class="container">
      <h1 id="team-id" data-ctfd-team-id="{{ team.id }}">{{ team.name }}</h1>

      {% if team.oauth_id %}
        <a href="https://majorleaguecyber.org/t/{{ team.name }}">
          <h3 class="d-inline-block mx-1"><span class="badge bg-primary rounded-pill">{% trans %}Official{% endtrans %}</span></h3>
        </a>
      {% endif %}

      {% if team.affiliation %}
        <h3 class="d-inline-block mx-1">
          <span class="badge bg-primary rounded-pill">{{ team.affiliation }}</span>
        </h3>
      {% endif %}

      {% if team.country %}
        <h3 class="d-inline-block mx-1">
          <span class="badge bg-primary rounded-pill">
            <i class="flag-{{ team.country.lower() }}"></i>
            {{ lookup_country_code(team.country) }}
          </span>
        </h3>
      {% endif %}

      {% if team.bracket_id %}
        <div class="pt-2">
          <h2>
            <span class="badge text-bg-light">
              {{ team.bracket.name }}
            </span>
          </h2>
        </div>
      {% endif %}

      <div class="pt-2">
        {% for field in team.fields %}
          <h3 class="d-block">
            {{ field.name }}: {{ field.value }}
          </h3>
        {% endfor %}
      </div>

      <div class="pt-2">
        {% if team.website and (team.website.startswith('http://') or team.website.startswith('https://')) %}
          <a href="{{ team.website }}" target="_blank" style="color: inherit;" rel="noopener">
            <i
                class="fas fa-external-link-alt fa-2x px-2" data-toggle="tooltip" data-placement="top"
                title="{{ team.website }}"
            ></i>
          </a>
        {% endif %}
      </div>

      {% if team.fields or team.website %}
        <hr class="w-50 mx-auto">
      {% endif %}

      <h2 id="team-place" class="text-center">
        {# This intentionally hides the team's place when scores are hidden because this can be
          their internal profile and we don't want to leak their place in the CTF. #}

        {# Public page hiding is done at the route level #}
        {% if scores_visible() %}
          {% if place %}
            {{ place }}
            <small>place</small>
          {% endif %}
        {% endif %}
      </h2>

      <h2 id="team-score" class="text-center">
        {% if score %}
          {{ score }}
          <small>points</small>
        {% endif %}
      </h2>
    </div>
  </div>

  <div class="container">
    {% include "components/errors.html" %}

    <br>

    <div class="row">
      <div class="col-md-12">
        <h3>{% trans %}Members{% endtrans %}</h3>
        <table class="table table-striped align-middle">
          <thead>
          <tr>
            <th>{% trans %}User Name{% endtrans %}</th>
            <th>{% trans %}Score{% endtrans %}</th>
          </tr>
          </thead>
          <tbody>
          {% for member in team.members %}
            <tr>
              <td>
                <a href="{{ url_for('users.public', user_id=member.id) }}">
                  {{ member.name }}
                </a>
                {% if member.bracket_id %}
                  <span class="badge bg-secondary ms-2">{{ member.bracket.name }}</span>
                {% endif %}
                {% if team.captain_id == member.id %}
                  <span class="badge bg-primary ms-2">{% trans %}Captain{% endtrans %}</span>
                {% endif %}
              </td>
              <td>{{ member.score }}</td>
            </tr>
          {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    {% if solves or awards %}
      {% if awards %}
        <div class="row">
          <div class="col-md-12">
            <h3>{% trans %}Awards{% endtrans %}</h3>
          </div>

          {% for award in awards %}
            <div class="col-md-3 col-sm-6">
              <p class="text-center">
                <i class="award-icon award-{{ award.icon }} fa-2x"></i> <br>
                <strong>{{ award.name }}</strong>
              </p>

              {% if award.category %}
                <p class="text-center">{{ award.category }}</p>
              {% endif %}

              {% if award.description %}
                <p class="text-center">{{ award.description }}</p>
              {% endif %}

              <p class="text-center">{{ award.value }}</p>
            </div>
          {% endfor %}
        </div>

        <br>
      {% endif %}

      <div class="row">
        <div class="col-md-12">
          <h3>{% trans %}Solves{% endtrans %}</h3>
          <table class="table table-striped align-middle">
            <thead>
            <tr>
              <th class="text-center">{% trans %}Challenge{% endtrans %}</th>
              <th class="d-none d-md-block d-lg-block">{% trans %}Category{% endtrans %}</th>
              <th>{% trans %}Value{% endtrans %}</th>
              <th>{% trans %}Time{% endtrans %}</th>
            </tr>
            </thead>
            <tbody>
            {% for solve in solves %}
              <tr>
                <td class="text-center">
                  <a href="{{ url_for('challenges.listing') }}#{{ solve.challenge.name }}-{{ solve.challenge.id }}">
                    {{ solve.challenge.name }}
                  </a>
                </td>
                <td class="d-none d-md-block d-lg-block">{{ solve.challenge.category }}</td>
                <td>{{ solve.challenge.value }}</td>
                <td class="solve-time">
                  <span data-time="{{ solve.date | isoformat }}"></span>
                </td>
              </tr>
            {% endfor %}
            </tbody>
          </table>
        </div>
      </div>

      <div class="clearfix"></div>

      <div class="row" x-data="TeamGraphs">
        <div class="col-md-6 d-none d-md-block d-lg-block py-4">
          <div class="progress">
            <div
              class="progress-bar"
              role="progressbar"
              :style="{ width: `${getSolvePercentage()}%`, 'background-color': 'rgb(0, 209, 64)' }"
            >
            </div>
            <div
              class="progress-bar"
              role="progressbar"
              :style="{ width: `${getFailPercentage()}%`, 'background-color': 'rgb(207, 38, 0)' }"
            >
            </div>
          </div>
          <div class="ps-2 float-start">
            <svg height="16" width="16">
              <circle cx="8" cy="8" r="5" fill="rgb(0, 209, 64)" />
            </svg>
            <small x-text="`Solves (${getSolvePercentage()}%)`"></small>
          </div>
          <div class="ps-2 float-start">
            <svg height="16" width="16">
              <circle cx="8" cy="8" r="5" fill="rgb(207, 38, 0)" />
            </svg>
            <small x-text="`Fails (${getFailPercentage()}%)`"></small>
          </div>
        </div>
        <div class="col-md-6 d-none d-md-block d-lg-block py-4">
          <div class="progress">
            <template x-for="category in getCategoryBreakdown()" :key="category.name">
              <div
                class="progress-bar"
                role="progressbar"
                :style="{ width: `${category.percent}%`, 'background-color': category.color }"
              >
              </div>
            </template>
          </div>
          <template x-for="category in getCategoryBreakdown()" :key="category.name">
            <div class="ps-2 float-start">
              <svg height="16" width="16">
                <circle cx="8" cy="8" r="5" :fill="category.color" />
              </svg>
              <small x-text="`${category.name} (${category.percent}%)`"></small>
            </div>
          </template>
        </div>

        <br class="clearfix">

        <div class="col-md-12 d-none d-md-block d-lg-block">
          <div id="score-graph" x-ref="scoregraph" class="w-100 d-flex align-items-center">
            <div class="text-center w-100">
              <i class="fas fa-circle-notch fa-spin fa-3x fa-fw spinner"></i>
            </div>
          </div>
        </div>
      </div>
    {% else %}
      <div class="row min-vh-25">
        <h3 class="opacity-50 text-center w-100 justify-content-center align-self-center">
          {% trans %}No solves yet{% endtrans %}
        </h3>
      </div>
    {% endif %}
  </div>
{% endblock %}

{% block scripts %}
  <script>
      window.TEAM =
      {{
        {
           'type': 'team',
           'id': team.id,
           'name': team.name,
           'account_id': team.id,
        } | tojson
      }}
  </script>

  {{ Assets.js("assets/js/teams/public.js") }}
{% endblock %}
