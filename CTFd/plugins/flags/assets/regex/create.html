<label>
	Regex Flag<br>
	<small>Enter regex flag data</small>
</label>
<div class="form-group">
	<input type="text" class="form-control" name="content" value="{{ content }}">
</div>
<div class="form-group">
	<select class="form-control custom-select col-md-6" name="data">
		<option value="">Case Sensitive</option>
		<option value="case_insensitive">Case Insensitive</option>
	</select>
</div>
<div class="form-group">
	<label>Examples</label>
	<table class="table small">
		<thead>
			<tr>
				<th scope="col">Description</th>
				<th scope="col">Flag</th>
			</tr>
		</thead>
		<tr>
			<td>Match any content inside of flag{}:</td>
			<td><code>flag{.*}</code></td>
		</tr>
		<tr>
			<td>Match numeric flags:</td>
			<td><code>flag{(\d+)}</code></td>
		</tr>
		<tr>
			<td>Accept flags with or without the flag format prefix:</td>
			<td><code>(flag{)?this_is_a_flag(})?</code></td>
		</tr>
	</table>
</div>
<input type="hidden" name="type" value="regex">