<label>
	Regex<br>
	<small>Enter regex key data</small>
</label>
<div class="form-group">
	<input type="text" class="form-control" name="content" value="{{ content }}">
</div>
<div class="form-group">
	<select class="form-control custom-select col-md-6" name="data">
		<option value="">Case Sensitive</option>
		<option value="case_insensitive" {% if data %}selected{% endif %}>Case Insensitive</option>
	</select>
</div>
<input type="hidden" name="type" value="regex">
<input type="hidden" name="id" value="{{ id }}">
<hr>
<div class="form-group">
	<button class="btn btn-success float-right">Update</button>
</div>
