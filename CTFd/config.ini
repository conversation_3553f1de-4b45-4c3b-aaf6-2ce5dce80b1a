# CTFd Configuration File
#
# Use this file to configure aspects of how CTFd behaves. Additional attributes can be specified for
# plugins and other additional behavior.
#
# If a configuration item is specified but left empty, CTFd will do the following:
#
#   1. Look for an environment variable under the same name and use that value if found
#   2. Use a default value specified in it's own internal configuration
#   3. Use a null value (i.e. None) or empty string for the configuration value


[server]
# SECRET_KEY:
# The secret value used to creation sessions and sign strings. This should be set to a random string. In the
# interest of ease, CTFd will automatically create a secret key file for you. If you wish to add this secret key
# to your instance you should hard code this value to a random static value.
#
# You can also remove .ctfd_secret_key from the .gitignore file and commit this file into whatever repository
# you are using.
#
# http://flask.pocoo.org/docs/latest/quickstart/#sessions
SECRET_KEY =

# DATABASE_URL
# The URI that specifies the username, password, hostname, port, and database of the server
# used to hold the CTFd database.
#
# If neither this setting nor `DATABASE_HOST` is specified, CTFd will automatically create a SQLite database for you to use
# e.g. mysql+pymysql://root:<YOUR_PASSWORD_HERE>@localhost/ctfd
DATABASE_URL = mysql+pymysql://root:toor@localhost/ctfd

# DATABASE_HOST
# The hostname of the database server used to hold the CTFd database.
# If `DATABASE_URL` is set, this setting will have no effect.
#
# This option, along with the other `DATABASE_*` options, are an alternative to specifying all connection details in the single `DATABASE_URL`.
# If neither this setting nor `DATABASE_URL` is specified, CTFd will automatically create a SQLite database for you to use.
DATABASE_HOST =

# DATABASE_PROTOCOL
# The protocol used to access the database server, if `DATABASE_HOST` is set. Defaults to `mysql+pymysql`.
DATABASE_PROTOCOL =

# DATABASE_USER
# The username used to access the database server, if `DATABASE_HOST` is set. Defaults to `ctfd`.
DATABASE_USER =

# DATABASE_PASSWORD
# The password used to access the database server, if `DATABASE_HOST` is set.
DATABASE_PASSWORD =

# DATABASE_PORT
# The port used to access the database server, if `DATABASE_HOST` is set.
DATABASE_PORT =

# DATABASE_NAME
# The name of the database to access on the database server, if `DATABASE_HOST` is set. Defaults to `ctfd`.
DATABASE_NAME =

# REDIS_URL
# The URL to connect to a Redis server. If neither this setting nor `REDIS_HOST` is specified,
# CTFd will use the .data folder as a filesystem cache.
#
# e.g. redis://user:password@localhost:6379
# http://pythonhosted.org/Flask-Caching/#configuring-flask-caching
REDIS_URL =

# REDIS_HOST
# The hostname of the Redis server to connect to.
# If `REDIS_URL` is set, this setting will have no effect.
#
# This option, along with the other `REDIS_*` options, are an alternative to specifying all connection details in the single `REDIS_URL`.
# If neither this setting nor `REDIS_URL` is specified, CTFd will use the .data folder as a filesystem cache.
REDIS_HOST =

# REDIS_PROTOCOL
# The protocol used to access the Redis server, if `REDIS_HOST` is set. Defaults to `redis`.
#
# Note that the `unix` protocol is not supported here; use `REDIS_URL` instead.
REDIS_PROTOCOL =

# REDIS_USER
# The username used to access the Redis server, if `REDIS_HOST` is set.
REDIS_USER =

# REDIS_PASSWORD
# The password used to access the Redis server, if `REDIS_HOST` is set.
REDIS_PASSWORD =

# REDIS_PORT
# The port used to access the Redis server, if `REDIS_HOST` is set.
REDIS_PORT =

# REDIS_DB
# The index of the Redis database to access, if `REDIS_HOST` is set.
REDIS_DB =

[security]
# TRUSTED_HOSTS
# Comma seperated string containing valid host names for CTFd to respond to. If not specified, 
# CTFd will respond to requests for any host unless otherwise restricted in an upstream proxy.
# Each value is either an exact match, or can start with a dot . to match any subdomain.
#
# It is recommended that most users set this to the server name that they expect to be using
# 
# Example: example.com,ctfd.io,.ctfd.io
# See https://flask.palletsprojects.com/en/stable/config/#TRUSTED_HOSTS
TRUSTED_HOSTS =

# SESSION_COOKIE_HTTPONLY
# Controls if cookies should be set with the HttpOnly flag. Defaults to True.
SESSION_COOKIE_HTTPONLY = true

# SESSION_COOKIE_SAMESITE
# Controls the SameSite attribute on session cookies. Can be Lax or Strict.
# Should be left as Lax unless the implications are well understood
SESSION_COOKIE_SAMESITE = Lax

# PERMANENT_SESSION_LIFETIME
# The lifetime of a session. The default is 604800 seconds (7 days).
PERMANENT_SESSION_LIFETIME = 604800

# CROSS_ORIGIN_OPENER_POLICY
# Setting for the Cross-Origin-Opener-Policy response header. Defaults to same-origin-allow-popups
CROSS_ORIGIN_OPENER_POLICY =

[email]
# MAILFROM_ADDR
# The email address that emails are sent from if not overridden in the configuration panel.
MAILFROM_ADDR =

# MAIL_SERVER
# The mail server that emails are sent from if not overriden in the configuration panel.
MAIL_SERVER =

# MAIL_PORT
# The mail port that emails are sent from if not overriden in the configuration panel.
MAIL_PORT =

# MAIL_USEAUTH
# Whether or not to use username and password to authenticate to the SMTP server
MAIL_USEAUTH =

# MAIL_USERNAME
# The username used to authenticate to the SMTP server if MAIL_USEAUTH is defined
MAIL_USERNAME =

# MAIL_PASSWORD
# The password used to authenticate to the SMTP server if MAIL_USEAUTH is defined
MAIL_PASSWORD =

# MAIL_TLS
# Whether to connect to the SMTP server over TLS
MAIL_TLS =

# MAIL_SSL
# Whether to connect to the SMTP server over SSL
MAIL_SSL =

# MAILSENDER_ADDR
# The email address that is responsible for the transmission of emails.
# This is very often the MAILFROM_ADDR value but can be specified if your email
# is delivered by a different domain than what's specified in your MAILFROM_ADDR.
# If this isn't specified, the MAILFROM_ADDR value is used.
# It is fairly rare to need to set this value.
MAILSENDER_ADDR =

# MAILGUN_API_KEY
# Mailgun API key to send email over Mailgun. As of CTFd v3, Mailgun integration is deprecated.
# Installations using the Mailgun API should migrate over to SMTP settings.
MAILGUN_API_KEY =

# MAILGUN_BASE_URL
# Mailgun base url to send email over Mailgun. As of CTFd v3, Mailgun integration is deprecated.
# Installations using the Mailgun API should migrate over to SMTP settings.
MAILGUN_BASE_URL =

# MAIL_PROVIDER
# Specifies the email provider that CTFd will use to send email.
# By default CTFd will automatically detect the correct email provider based on the other settings
# specified here or in the configuration panel. This setting can be used to force a specific provider.
MAIL_PROVIDER =

[uploads]
# UPLOAD_PROVIDER
# Specifies the service that CTFd should use to store files.
# Can be set to filesystem or s3
UPLOAD_PROVIDER =

# UPLOAD_FOLDER
# The location where files are uploaded under the filesystem uploader.
# The default destination is the CTFd/uploads folder.
UPLOAD_FOLDER =

# AWS_ACCESS_KEY_ID
# AWS access token used to authenticate to the S3 bucket. Only used under the s3 uploader.
AWS_ACCESS_KEY_ID =

# AWS_SECRET_ACCESS_KEY
# AWS secret token used to authenticate to the S3 bucket. Only used under the s3 uploader.
AWS_SECRET_ACCESS_KEY =

# AWS_S3_BUCKET
# The unique identifier for your S3 bucket. Only used under the s3 uploader.
AWS_S3_BUCKET =

# AWS_S3_ENDPOINT_URL
# A URL pointing to a custom S3 implementation. Only used under the s3 uploader.
AWS_S3_ENDPOINT_URL =

# AWS_S3_REGION
# The aws region that hosts your bucket. Only used in the s3 uploader.
AWS_S3_REGION =

# AWS_S3_ADDRESSING_STYLE
# The S3 addressing style to use for URLs. Only used under the s3 uploader.
# Defaults to auto; can be set to virtual or path.
# See https://boto3.amazonaws.com/v1/documentation/api/latest/guide/configuration.html
AWS_S3_ADDRESSING_STYLE =

# AWS_S3_CUSTOM_DOMAIN
# A hostname that replaces the default hostname in the generated S3 download URLs. Required by some S3 providers or CDNs.
# Only used under the s3 uploader.
AWS_S3_CUSTOM_DOMAIN =

# AWS_S3_CUSTOM_PREFIX
# Add a prefix to the file path to be placed in S3.
# Only used under the s3 uploader.
AWS_S3_CUSTOM_PREFIX =

[logs]
# LOG_FOLDER
# The location where logs are written. These are the logs for CTFd key submissions, registrations, and logins. The default location is the CTFd/logs folder.
LOG_FOLDER =

[optional]
# REVERSE_PROXY
# Specifies whether CTFd is behind a reverse proxy or not. Set to true if using a reverse proxy like nginx.
# You can also specify a comma seperated set of numbers specifying the reverse proxy configuration settings.
# See https://werkzeug.palletsprojects.com/en/0.15.x/middleware/proxy_fix/#werkzeug.middleware.proxy_fix.ProxyFix.
# For example to configure `x_for=1, x_proto=1, x_host=1, x_port=1, x_prefix=1` specify `1,1,1,1,1`.
# If you specify `true` CTFd will default to the above behavior with all proxy settings set to 1.
REVERSE_PROXY =

# THEME_FALLBACK
# Specifies whether CTFd will fallback to the default "core" theme for missing pages/content. Useful for developing themes or using incomplete themes.
# Defaults to true.
THEME_FALLBACK =

# TEMPLATES_AUTO_RELOAD
# Specifies whether Flask should check for modifications to templates and reload them automatically. Defaults to true.
TEMPLATES_AUTO_RELOAD =

# SQLALCHEMY_TRACK_MODIFICATIONS
# Automatically disabled to suppress warnings and save memory.
# You should only enable this if you need it.
# Defaults to false.
SQLALCHEMY_TRACK_MODIFICATIONS =

# SWAGGER_UI
# Enable the Swagger UI endpoint at /api/v1/
SWAGGER_UI =

# UPDATE_CHECK
# Specifies whether or not CTFd will check whether or not there is a new version of CTFd. Defaults True.
UPDATE_CHECK =

# APPLICATION_ROOT
# Specifies what path CTFd is mounted under. It can be used to run CTFd in a subdirectory.
# Example: /ctfd
APPLICATION_ROOT =

# SERVER_SENT_EVENTS
# Specifies whether or not to enable the Server-Sent Events based Notifications system.
# Defaults to true
SERVER_SENT_EVENTS =

# HTML_SANITIZATION
# Specifies whether CTFd should sanitize HTML content
# Defaults to false
HTML_SANITIZATION =

# SQLALCHEMY_MAX_OVERFLOW
# Specifies the max_overflow setting for SQLAlchemy's Engine
# https://docs.sqlalchemy.org/en/13/core/engines.html#sqlalchemy.create_engine
# https://flask-sqlalchemy.palletsprojects.com/en/2.x/config/#configuration-keys
SQLALCHEMY_MAX_OVERFLOW =

# SQLALCHEMY_POOL_PRE_PING
# Specifies the pool_pre_ping setting for SQLAlchemy's Engine
# https://docs.sqlalchemy.org/en/13/core/engines.html#sqlalchemy.create_engine
# https://flask-sqlalchemy.palletsprojects.com/en/2.x/config/#configuration-keys
SQLALCHEMY_POOL_PRE_PING =

# SAFE_MODE
# If SAFE_MODE is enabled, CTFd will not load any plugins which may alleviate issues preventing CTFd from starting
# Defaults to false
SAFE_MODE =

[oauth]
# OAUTH_CLIENT_ID
# Register an event at https://majorleaguecyber.org/ and use the Client ID here
OAUTH_CLIENT_ID =

# OAUTH_CLIENT_ID
# Register an event at https://majorleaguecyber.org/ and use the Client Secret here
OAUTH_CLIENT_SECRET =

[extra]
# The extra section can be used to specify additional values to be loaded into CTFd's configuration
