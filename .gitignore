# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]

# C extensions
*.so

# Distribution / packaging
.Python
env/
venv*
.venv*
build/
develop-eggs/
dist/
downloads/
eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml

# Translations
# TODO: CTFd 4.0 We should consider generating .mo files in a Docker image instead of saving them in git
# *.mo

# Django stuff:
*.log

# Sphinx documentation
docs/_build/

# PyBuilder
target/

.DS_Store

*.db
*.log
*.log.*
.idea/
.vscode/
CTFd/static/uploads
CTFd/uploads
.data/
.ctfd_secret_key
.*.swp

# Vagrant
.vagrant

# CTFd Exports
*.zip

# JS
node_modules/

# Flask Profiler files
flask_profiler.sql
