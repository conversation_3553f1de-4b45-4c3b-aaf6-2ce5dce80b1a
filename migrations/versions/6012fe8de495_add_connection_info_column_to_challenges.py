"""Add connection_info column to Challenges

Revision ID: 6012fe8de495
Revises: ef87d69ec29a
Create Date: 2021-07-30 03:50:54.219124

"""
from alembic import op  # noqa: I001
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "6012fe8de495"
down_revision = "ef87d69ec29a"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("challenges", sa.Column("connection_info", sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("challenges", "connection_info")
    # ### end Alembic commands ###
