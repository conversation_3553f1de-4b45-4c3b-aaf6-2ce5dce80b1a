"""Add type to awards

Revision ID: 4e4d5a9ea000
Revises: 8369118943a1
Create Date: 2019-04-07 19:37:17.872128

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "4e4d5a9ea000"
down_revision = "8369118943a1"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "awards",
        sa.Column(
            "type", sa.String(length=80), nullable=True, server_default="standard"
        ),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("awards", "type")
    # ### end Alembic commands ###
