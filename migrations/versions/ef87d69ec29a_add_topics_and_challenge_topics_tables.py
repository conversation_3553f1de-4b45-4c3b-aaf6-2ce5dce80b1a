"""Add topics and challenge_topics tables

Revision ID: ef87d69ec29a
Revises: 07dfbe5e1edc
Create Date: 2021-07-29 23:22:39.345426

"""
from alembic import op  # noqa: I001
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "ef87d69ec29a"
down_revision = "07dfbe5e1edc"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "topics",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("value", sa.String(length=255), nullable=True),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("value"),
    )
    op.create_table(
        "challenge_topics",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("challenge_id", sa.Integer(), nullable=True),
        sa.<PERSON>umn("topic_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["challenge_id"], ["challenges.id"], ondelete="CASCADE"
        ),
        sa.ForeignKeyConstraint(["topic_id"], ["topics.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("challenge_topics")
    op.drop_table("topics")
    # ### end Alembic commands ###
