"""Add Solutions table

Revision ID: 62bf576b2cd3
Revises: a49ad66aa0f1
Create Date: 2025-07-28 20:04:45.082529

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "62bf576b2cd3"
down_revision = "a49ad66aa0f1"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "solutions",
        sa.<PERSON>umn("id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("challenge_id", sa.Integer(), nullable=True),
        sa.<PERSON>umn("content", sa.Text(), nullable=True),
        sa.Column("state", sa.String(length=80), nullable=False),
        sa.ForeignKeyConstraint(
            ["challenge_id"], ["challenges.id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("challenge_id"),
    )
    op.add_column("files", sa.Column("solution_id", sa.Integer(), nullable=True))
    op.create_foreign_key(None, "files", "solutions", ["solution_id"], ["id"])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "files", type_="foreignkey")
    op.drop_column("files", "solution_id")
    op.drop_table("solutions")
    # ### end Alembic commands ###
