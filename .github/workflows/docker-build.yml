name: Docker build image on release

on:
  release:
    types: [published]

jobs:
  docker:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - name: Set repo name lowercase
        id: repo
        uses: ASzc/change-string-case-action@v2
        with:
          string: ${{ github.repository }}
      - name: Checkout
        uses: actions/checkout@v3
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Build and push
        uses: docker/build-push-action@v3
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            ${{ steps.repo.outputs.lowercase }}:latest
            ghcr.io/${{ steps.repo.outputs.lowercase }}:latest
            ${{ steps.repo.outputs.lowercase }}:${{ github.event.release.tag_name }}
            ghcr.io/${{ steps.repo.outputs.lowercase }}:${{ github.event.release.tag_name }}
