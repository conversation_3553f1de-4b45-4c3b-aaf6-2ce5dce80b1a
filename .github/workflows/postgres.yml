---
name: CTFd Postgres CI

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  build:

    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres
        ports:
          - 5432:5432
        env:
          POSTGRES_HOST_AUTH_METHOD: trust
          POSTGRES_DB: ctfd
          POSTGRES_PASSWORD: password
        # Set health checks to wait until postg<PERSON> has started
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis
        ports:
          - 6379:6379

    strategy:
      matrix:
        python-version: ['3.11']

    name: Python ${{ matrix.python-version }}
    steps:
      - uses: actions/checkout@v2
      - name: Setup python
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}
          architecture: x64
      - name: Install dependencies
        run: |
            python -m pip install --upgrade pip
            python -m pip install -r development.txt
            sudo yarn install --non-interactive

      - name: Test
        run: |
            sudo rm -f /etc/boto.cfg
            make test
        env:
            AWS_ACCESS_KEY_ID: AKIAIOSFODNN7EXAMPLE
            AWS_SECRET_ACCESS_KEY: wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
            TESTING_DATABASE_URL: postgres://postgres:password@localhost:${{ job.services.postgres.ports[5432] }}/ctfd

      - name: Codecov
        uses: codecov/codecov-action@v5
        with:
          file: ./coverage.xml

