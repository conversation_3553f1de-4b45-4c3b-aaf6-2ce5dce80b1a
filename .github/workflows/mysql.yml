---
name: CTFd MySQL CI

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  build:

    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:5.7
        env:
          MYSQL_ROOT_PASSWORD: password
        ports:
          - 3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
      redis:
        image: redis
        ports:
          - 6379:6379

    strategy:
      matrix:
        python-version: ['3.11']

    name: Python ${{ matrix.python-version }}
    steps:
      - uses: actions/checkout@v2
      - name: Setup python
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}
          architecture: x64
      - name: Install dependencies
        run: |
            python -m pip install --upgrade pip
            python -m pip install -r development.txt
            sudo yarn install --non-interactive

      - name: Test
        run: |
            sudo rm -f /etc/boto.cfg
            make test
        env:
            AWS_ACCESS_KEY_ID: AKIAIOSFODNN7EXAMPLE
            AWS_SECRET_ACCESS_KEY: wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
            TESTING_DATABASE_URL: mysql+pymysql://root:password@localhost:${{ job.services.mysql.ports[3306] }}/ctfd

      - name: Codecov
        uses: codecov/codecov-action@v5
        with:
          file: ./coverage.xml
