---
name: Linting

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        python-version: ['3.11']

    name: Linting
    steps:
      - uses: actions/checkout@v2
      - name: Setup python
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}
          architecture: x64
      - name: Install dependencies
        run: |
            python -m pip install --upgrade pip
            python -m pip install -r linting.txt
            sudo yarn --cwd CTFd/themes/admin install --non-interactive
            sudo yarn global add prettier@3.2.5

      - name: Lint
        run: make lint
        env:
            TESTING_DATABASE_URL: 'sqlite://'

      - name: <PERSON><PERSON> Dockerfile
        uses: brpaz/hadolint-action@master
        with:
          dockerfile: "Dockerfile"

      - name: Lint docker-compose
        run: |
            docker compose -f docker-compose.yml config

      - name: Lint translations
        run: |
          make translations-lint

