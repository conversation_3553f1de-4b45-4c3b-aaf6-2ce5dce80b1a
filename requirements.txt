#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    ./scripts/pip-compile.sh
#
alembic==1.4.3
    # via
    #   dataset
    #   flask-migrate
aniso8601==8.0.0
    # via flask-restx
attrs==20.3.0
    # via jsonschema
babel==2.12.1
    # via flask-babel
banal==1.0.6
    # via dataset
bcrypt==4.0.1
    # via -r requirements.in
boto3==1.35.27
    # via -r requirements.in
botocore==1.35.27
    # via
    #   boto3
    #   s3transfer
cachelib==0.9.0
    # via flask-caching
certifi==2024.7.4
    # via requests
cffi==1.17.1
    # via
    #   -r requirements.in
    #   cmarkgfm
    #   cryptography
charset-normalizer==2.0.12
    # via requests
click==7.1.2
    # via flask
cmarkgfm==2024.1.14
    # via -r requirements.in
cryptography==40.0.2
    # via pymysql
dataset==1.6.2
    # via -r requirements.in
flask==2.0.3
    # via
    #   -r requirements.in
    #   flask-babel
    #   flask-caching
    #   flask-marshmallow
    #   flask-migrate
    #   flask-restx
    #   flask-script
    #   flask-sqlalchemy
flask-babel==2.0.0
    # via -r requirements.in
flask-caching==2.0.2
    # via -r requirements.in
flask-marshmallow==0.10.1
    # via -r requirements.in
flask-migrate==2.5.3
    # via -r requirements.in
flask-restx==1.1.0
    # via -r requirements.in
flask-script==2.0.6
    # via -r requirements.in
flask-sqlalchemy==2.5.1
    # via
    #   -r requirements.in
    #   flask-migrate
freezegun==1.2.2
    # via -r requirements.in
gevent==23.9.1
    # via -r requirements.in
greenlet==3.0.3
    # via
    #   gevent
    #   sqlalchemy
gunicorn==23.0.0
    # via -r requirements.in
idna==2.10
    # via requests
itsdangerous==2.1.2
    # via flask
jinja2==3.1.6
    # via
    #   -r requirements.in
    #   flask
    #   flask-babel
jmespath==0.10.0
    # via
    #   boto3
    #   botocore
jsonschema==3.2.0
    # via flask-restx
mako==1.1.3
    # via alembic
markupsafe==2.1.3
    # via
    #   jinja2
    #   mako
    #   wtforms
marshmallow==2.20.2
    # via
    #   -r requirements.in
    #   flask-marshmallow
    #   marshmallow-sqlalchemy
marshmallow-sqlalchemy==0.17.0
    # via -r requirements.in
maxminddb==1.5.4
    # via
    #   -r requirements.in
    #   python-geoacumen-city
nh3==0.3.0
    # via -r requirements.in
packaging==24.2
    # via gunicorn
passlib==1.7.4
    # via -r requirements.in
pillow==10.1.0
    # via -r requirements.in
pycparser==2.20
    # via cffi
pydantic==1.6.2
    # via -r requirements.in
pymysql[rsa]==1.0.2
    # via
    #   -r requirements.in
    #   pymysql
pyrsistent==0.17.3
    # via jsonschema
python-dateutil==2.8.1
    # via
    #   alembic
    #   botocore
    #   freezegun
python-dotenv==0.13.0
    # via -r requirements.in
python-editor==1.0.4
    # via alembic
python-geoacumen-city==2023.4.15
    # via -r requirements.in
pytz==2020.4
    # via
    #   flask-babel
    #   flask-restx
redis==4.5.5
    # via -r requirements.in
requests==2.32.4
    # via -r requirements.in
s3transfer==0.10.0
    # via boto3
six==1.16.0
    # via
    #   -r requirements.in
    #   flask-marshmallow
    #   jsonschema
    #   python-dateutil
    #   tenacity
sqlalchemy==1.4.48
    # via
    #   -r requirements.in
    #   alembic
    #   dataset
    #   flask-sqlalchemy
    #   marshmallow-sqlalchemy
    #   sqlalchemy-utils
sqlalchemy-utils==0.41.1
    # via -r requirements.in
tenacity==6.2.0
    # via -r requirements.in
urllib3==1.26.20
    # via
    #   -r requirements.in
    #   botocore
    #   requests
werkzeug==2.0.3
    # via
    #   -r requirements.in
    #   flask
    #   flask-restx
wtforms==2.3.1
    # via -r requirements.in
zope-event==4.5.0
    # via gevent
zope-interface==5.2.0
    # via gevent

# The following packages are considered to be unsafe in a requirements file:
# setuptools
