#!/usr/bin/env python
# -*- coding: utf-8 -*-

from tests.helpers import (
    create_ctfd,
    destroy_ctfd,
    gen_tracking,
    gen_user,
    login_as_user,
)


def test_admin_user_ip_search():
    """Can an admin search user IPs"""
    app = create_ctfd()
    with app.app_context():
        u1 = gen_user(app.db, name="user1", email="<EMAIL>")
        gen_tracking(app.db, user_id=u1.id, ip="*******")

        u2 = gen_user(app.db, name="user2", email="<EMAIL>")
        gen_tracking(app.db, user_id=u2.id, ip="*******")

        u3 = gen_user(app.db, name="user3", email="<EMAIL>")
        gen_tracking(app.db, user_id=u3.id, ip="*******")

        u4 = gen_user(app.db, name="user4", email="<EMAIL>")
        gen_tracking(app.db, user_id=u4.id, ip="*******")
        gen_tracking(app.db, user_id=u4.id, ip="*******")

        with login_as_user(app, name="admin", password="password") as admin:
            r = admin.get("/admin/users?field=ip&q=*******")
            resp = r.get_data(as_text=True)
            assert "user1" in resp
            assert "user2" not in resp
            assert "user3" not in resp

            r = admin.get("/admin/users?field=ip&q=*******")
            resp = r.get_data(as_text=True)
            assert "user1" not in resp
            assert "user2" in resp
            assert "user3" not in resp

            r = admin.get("/admin/users?field=ip&q=*******")
            resp = r.get_data(as_text=True)
            assert "user1" not in resp
            assert "user2" not in resp
            assert "user3" in resp
            assert "user4" in resp
    destroy_ctfd(app)
